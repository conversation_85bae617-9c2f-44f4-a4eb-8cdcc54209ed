var c=(e,t)=>{e.componentOnReady?e.componentOnReady().then(a=>t(a)):s(()=>t(e))},d=e=>e.componentOnReady!==void 0,r=(e,t=[])=>{let a={};return t.forEach(n=>{e.hasAttribute(n)&&(e.getAttribute(n)!==null&&(a[n]=e.getAttribute(n)),e.removeAttribute(n))}),a},i=["role","aria-activedescendant","aria-atomic","aria-autocomplete","aria-braillelabel","aria-brailleroledescription","aria-busy","aria-checked","aria-colcount","aria-colindex","aria-colindextext","aria-colspan","aria-controls","aria-current","aria-describedby","aria-description","aria-details","aria-disabled","aria-errormessage","aria-expanded","aria-flowto","aria-haspopup","aria-hidden","aria-invalid","aria-keyshortcuts","aria-label","aria-labelledby","aria-level","aria-live","aria-multiline","aria-multiselectable","aria-orientation","aria-owns","aria-placeholder","aria-posinset","aria-pressed","aria-readonly","aria-relevant","aria-required","aria-roledescription","aria-rowcount","aria-rowindex","aria-rowindextext","aria-rowspan","aria-selected","aria-setsize","aria-sort","aria-valuemax","aria-valuemin","aria-valuenow","aria-valuetext"],l=(e,t)=>r(e,i),f=(e,t,a,n)=>e.addEventListener(t,a,n),m=(e,t,a,n)=>e.removeEventListener(t,a,n),b=(e,t=e)=>e.shadowRoot||t,s=e=>typeof __zone_symbol__requestAnimationFrame=="function"?__zone_symbol__requestAnimationFrame(e):typeof requestAnimationFrame=="function"?requestAnimationFrame(e):setTimeout(e),h=e=>!!e.shadowRoot&&!!e.attachShadow,p=e=>{if(e.focus(),e.classList.contains("ion-focusable")){let t=e.closest("ion-app");t&&t.setFocus([e])}};var y=(e,t,a)=>Math.max(e,Math.min(t,a));var E=e=>{if(e){let t=e.changedTouches;if(t&&t.length>0){let a=t[0];return{x:a.clientX,y:a.clientY}}if(e.pageX!==void 0)return{x:e.pageX,y:e.pageY}}return{x:0,y:0}};var g=(e,t)=>{let a=e._original||e;return{_original:e,emit:o(a.emit.bind(a),t)}},o=(e,t=0)=>{let a;return(...n)=>{clearTimeout(a),a=setTimeout(e,t,...n)}},v=(e,t)=>{if(e??(e={}),t??(t={}),e===t)return!0;let a=Object.keys(e);if(a.length!==Object.keys(t).length)return!1;for(let n of a)if(!(n in t)||e[n]!==t[n])return!1;return!0};export{c as a,d as b,r as c,l as d,f as e,m as f,b as g,s as h,h as i,p as j,y as k,E as l,g as m,v as n};
