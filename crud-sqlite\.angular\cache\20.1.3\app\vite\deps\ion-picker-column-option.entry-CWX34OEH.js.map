{"version": 3, "sources": ["../../../../../../node_modules/@ionic/core/dist/esm/ion-picker-column-option.entry.js"], "sourcesContent": ["/*!\n * (C) Ionic http://ionicframework.com - MIT License\n */\nimport { r as registerInstance, e as getIonMode, h, j as Host, k as getElement } from './index-B_U9CtaY.js';\nimport { b as inheritAttributes } from './helpers-1O4D2b7y.js';\nimport { c as createColorClasses } from './theme-DiVJyqlX.js';\n\nconst pickerColumnOptionIosCss = \"button{padding-left:0;padding-right:0;padding-top:0;padding-bottom:0;margin-left:0;margin-right:0;margin-top:0;margin-bottom:0;width:100%;height:34px;border:0px;outline:none;background:transparent;color:inherit;font-family:var(--ion-font-family, inherit);font-size:inherit;line-height:34px;text-align:inherit;text-overflow:ellipsis;white-space:nowrap;cursor:pointer;overflow:hidden}:host(.option-disabled){opacity:0.4}:host(.option-disabled) button{cursor:default}\";\n\nconst pickerColumnOptionMdCss = \"button{padding-left:0;padding-right:0;padding-top:0;padding-bottom:0;margin-left:0;margin-right:0;margin-top:0;margin-bottom:0;width:100%;height:34px;border:0px;outline:none;background:transparent;color:inherit;font-family:var(--ion-font-family, inherit);font-size:inherit;line-height:34px;text-align:inherit;text-overflow:ellipsis;white-space:nowrap;cursor:pointer;overflow:hidden}:host(.option-disabled){opacity:0.4}:host(.option-disabled) button{cursor:default}:host(.option-active){color:var(--ion-color-base)}\";\n\nconst PickerColumnOption = class {\n    constructor(hostRef) {\n        registerInstance(this, hostRef);\n        /**\n         * We keep track of the parent picker column\n         * so we can update the value of it when\n         * clicking an enable option.\n         */\n        this.pickerColumn = null;\n        /**\n         * The aria-label of the option.\n         *\n         * If the value changes, then it will trigger a\n         * re-render of the picker since it's a @State variable.\n         * Otherwise, the `aria-label` attribute cannot be updated\n         * after the component is loaded.\n         */\n        this.ariaLabel = null;\n        /**\n         * If `true`, the user cannot interact with the picker column option.\n         */\n        this.disabled = false;\n        /**\n         * The color to use from your application's color palette.\n         * Default options are: `\"primary\"`, `\"secondary\"`, `\"tertiary\"`, `\"success\"`, `\"warning\"`, `\"danger\"`, `\"light\"`, `\"medium\"`, and `\"dark\"`.\n         * For more information on colors, see [theming](/docs/theming/basics).\n         */\n        this.color = 'primary';\n    }\n    /**\n     * The aria-label of the option has changed after the\n     * first render and needs to be updated within the component.\n     *\n     * @param ariaLbl The new aria-label value.\n     */\n    onAriaLabelChange(ariaLbl) {\n        this.ariaLabel = ariaLbl;\n    }\n    componentWillLoad() {\n        const inheritedAttributes = inheritAttributes(this.el, ['aria-label']);\n        /**\n         * The initial value of `aria-label` needs to be set for\n         * the first render.\n    \n         */\n        this.ariaLabel = inheritedAttributes['aria-label'] || null;\n    }\n    connectedCallback() {\n        this.pickerColumn = this.el.closest('ion-picker-column');\n    }\n    disconnectedCallback() {\n        this.pickerColumn = null;\n    }\n    /**\n     * The column options can load at any time\n     * so the options needs to tell the\n     * parent picker column when it is loaded\n     * so the picker column can ensure it is\n     * centered in the view.\n     *\n     * We intentionally run this for every\n     * option. If we only ran this from\n     * the selected option then if the newly\n     * loaded options were not selected then\n     * scrollActiveItemIntoView would not be called.\n     */\n    componentDidLoad() {\n        const { pickerColumn } = this;\n        if (pickerColumn !== null) {\n            pickerColumn.scrollActiveItemIntoView();\n        }\n    }\n    /**\n     * When an option is clicked, update the\n     * parent picker column value. This\n     * component will handle centering the option\n     * in the column view.\n     */\n    onClick() {\n        const { pickerColumn } = this;\n        if (pickerColumn !== null) {\n            pickerColumn.setValue(this.value);\n        }\n    }\n    render() {\n        const { color, disabled, ariaLabel } = this;\n        const mode = getIonMode(this);\n        return (h(Host, { key: 'f816729941aabcb31ddfdce3ffe2e2139030d715', class: createColorClasses(color, {\n                [mode]: true,\n                ['option-disabled']: disabled,\n            }) }, h(\"button\", { key: '48dff7833bb60fc8331cd353a0885e6affa683d1', tabindex: \"-1\", \"aria-label\": ariaLabel, disabled: disabled, onClick: () => this.onClick() }, h(\"slot\", { key: 'f9224d0e7b7aa6c05b29abfdcfe0f30ad6ee3141' }))));\n    }\n    get el() { return getElement(this); }\n    static get watchers() { return {\n        \"aria-label\": [\"onAriaLabelChange\"]\n    }; }\n};\nPickerColumnOption.style = {\n    ios: pickerColumnOptionIosCss,\n    md: pickerColumnOptionMdCss\n};\n\nexport { PickerColumnOption as ion_picker_column_option };\n"], "mappings": ";;;;;;;;;;;;;;;;AAOA,IAAM,2BAA2B;AAEjC,IAAM,0BAA0B;AAEhC,IAAM,qBAAqB,MAAM;AAAA,EAC7B,YAAY,SAAS;AACjB,qBAAiB,MAAM,OAAO;AAM9B,SAAK,eAAe;AASpB,SAAK,YAAY;AAIjB,SAAK,WAAW;AAMhB,SAAK,QAAQ;AAAA,EACjB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,kBAAkB,SAAS;AACvB,SAAK,YAAY;AAAA,EACrB;AAAA,EACA,oBAAoB;AAChB,UAAM,sBAAsB,kBAAkB,KAAK,IAAI,CAAC,YAAY,CAAC;AAMrE,SAAK,YAAY,oBAAoB,YAAY,KAAK;AAAA,EAC1D;AAAA,EACA,oBAAoB;AAChB,SAAK,eAAe,KAAK,GAAG,QAAQ,mBAAmB;AAAA,EAC3D;AAAA,EACA,uBAAuB;AACnB,SAAK,eAAe;AAAA,EACxB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAcA,mBAAmB;AACf,UAAM,EAAE,aAAa,IAAI;AACzB,QAAI,iBAAiB,MAAM;AACvB,mBAAa,yBAAyB;AAAA,IAC1C;AAAA,EACJ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,UAAU;AACN,UAAM,EAAE,aAAa,IAAI;AACzB,QAAI,iBAAiB,MAAM;AACvB,mBAAa,SAAS,KAAK,KAAK;AAAA,IACpC;AAAA,EACJ;AAAA,EACA,SAAS;AACL,UAAM,EAAE,OAAO,UAAU,UAAU,IAAI;AACvC,UAAM,OAAO,WAAW,IAAI;AAC5B,WAAQ,EAAE,MAAM,EAAE,KAAK,4CAA4C,OAAO,mBAAmB,OAAO;AAAA,MAC5F,CAAC,IAAI,GAAG;AAAA,MACR,CAAC,iBAAiB,GAAG;AAAA,IACzB,CAAC,EAAE,GAAG,EAAE,UAAU,EAAE,KAAK,4CAA4C,UAAU,MAAM,cAAc,WAAW,UAAoB,SAAS,MAAM,KAAK,QAAQ,EAAE,GAAG,EAAE,QAAQ,EAAE,KAAK,2CAA2C,CAAC,CAAC,CAAC;AAAA,EAC1O;AAAA,EACA,IAAI,KAAK;AAAE,WAAO,WAAW,IAAI;AAAA,EAAG;AAAA,EACpC,WAAW,WAAW;AAAE,WAAO;AAAA,MAC3B,cAAc,CAAC,mBAAmB;AAAA,IACtC;AAAA,EAAG;AACP;AACA,mBAAmB,QAAQ;AAAA,EACvB,KAAK;AAAA,EACL,IAAI;AACR;", "names": []}