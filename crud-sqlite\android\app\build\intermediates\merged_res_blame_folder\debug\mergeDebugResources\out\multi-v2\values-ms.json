{"logs": [{"outputFile": "io.ionic.starter.app-mergeDebugResources-31:/values-ms/values-ms.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\e2542f07e03e83966d61d2ce34c14b7c\\transformed\\appcompat-1.7.0\\res\\values-ms\\values-ms.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,216,321,429,516,620,731,810,888,979,1072,1167,1261,1359,1452,1547,1641,1732,1823,1903,2015,2123,2220,2329,2433,2540,2699,2800", "endColumns": "110,104,107,86,103,110,78,77,90,92,94,93,97,92,94,93,90,90,79,111,107,96,108,103,106,158,100,80", "endOffsets": "211,316,424,511,615,726,805,883,974,1067,1162,1256,1354,1447,1542,1636,1727,1818,1898,2010,2118,2215,2324,2428,2535,2694,2795,2876"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,48", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,216,321,429,516,620,731,810,888,979,1072,1167,1261,1359,1452,1547,1641,1732,1823,1903,2015,2123,2220,2329,2433,2540,2699,5018", "endColumns": "110,104,107,86,103,110,78,77,90,92,94,93,97,92,94,93,90,90,79,111,107,96,108,103,106,158,100,80", "endOffsets": "211,316,424,511,615,726,805,883,974,1067,1162,1256,1354,1447,1542,1636,1727,1818,1898,2010,2118,2215,2324,2428,2535,2694,2795,5094"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\ea82f0e4740fea90ca38a885280cdee8\\transformed\\core-1.15.0\\res\\values-ms\\values-ms.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,150,252,349,459,565,683,798", "endColumns": "94,101,96,109,105,117,114,100", "endOffsets": "145,247,344,454,560,678,793,894"}, "to": {"startLines": "29,30,31,32,33,34,35,49", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "2800,2895,2997,3094,3204,3310,3428,5099", "endColumns": "94,101,96,109,105,117,114,100", "endOffsets": "2890,2992,3089,3199,3305,3423,3538,5195"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\eaaca94d50affd3c3a86a8212751e60b\\transformed\\biometric-1.1.0\\res\\values-ms\\values-ms.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,165,263,378,507,634,769,889,1027,1127,1266,1407", "endColumns": "109,97,114,128,126,134,119,137,99,138,140,122", "endOffsets": "160,258,373,502,629,764,884,1022,1122,1261,1402,1525"}, "to": {"startLines": "36,37,38,39,40,41,42,43,44,45,46,47", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3543,3653,3751,3866,3995,4122,4257,4377,4515,4615,4754,4895", "endColumns": "109,97,114,128,126,134,119,137,99,138,140,122", "endOffsets": "3648,3746,3861,3990,4117,4252,4372,4510,4610,4749,4890,5013"}}]}]}