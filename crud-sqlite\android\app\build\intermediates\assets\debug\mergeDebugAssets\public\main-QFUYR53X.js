import{D as h,E as v,F as g,O as C,Q as y,d as n,e as p,h as a,i as m,j as s,v as l,w as c,x as d,y as u,z as f}from"./chunk-JE2PG4HF.js";import"./chunk-QUM6RZVN.js";import"./chunk-6WAW2KHA.js";import"./chunk-YJ7VB3TT.js";import"./chunk-QDU6VP7L.js";import"./chunk-ICSGBKZQ.js";import"./chunk-YSN7XVTD.js";import"./chunk-R5HL6L5F.js";import"./chunk-4WFVMWDK.js";import"./chunk-M2X7KQLB.js";import"./chunk-2YSZFPCQ.js";import"./chunk-57YRIO75.js";import"./chunk-REYR55MP.js";import"./chunk-C5RQ2IC2.js";import"./chunk-42C7ZIID.js";import{e as r}from"./chunk-JHI3MBHO.js";var I=[{path:"home",loadComponent:()=>import("./chunk-7ZT7QFGG.js").then(t=>t.HomePage)},{path:"",redirectTo:"home",pathMatch:"full"},{path:"sqlite-crudop",loadComponent:()=>import("./chunk-K7XANT4P.js").then(t=>t.SqliteCrudopPage)}];var S=(()=>{let e=class e{constructor(o){this.sqlite=o}ngOnInit(){return r(this,null,function*(){yield this.sqlite.initDB()})}};e.\u0275fac=function(i){return new(i||e)(n(y))},e.\u0275cmp=p({type:e,selectors:[["app-root"]],decls:2,vars:0,template:function(i,q){i&1&&(a(0,"ion-app"),s(1,"ion-router-outlet"),m())},dependencies:[g,v],encapsulation:2});let t=e;return t})();l(S,{providers:[{provide:c,useClass:h},C(),u(I,f(d))]});
