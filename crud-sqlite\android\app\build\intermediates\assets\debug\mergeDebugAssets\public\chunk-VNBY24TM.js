import{b as n}from"./chunk-6WAW2KHA.js";import{e as s}from"./chunk-JHI3MBHO.js";var r=class extends n{constructor(){super(...arguments),this.jeepSqliteElement=null,this.isWebStoreOpen=!1}initWebStore(){return s(this,null,function*(){yield customElements.whenDefined("jeep-sqlite"),this.jeepSqliteElement=document.querySelector("jeep-sqlite"),this.ensureJeepSqliteIsAvailable(),this.jeepSqliteElement.addEventListener("jeepSqliteImportProgress",e=>{this.notifyListeners("sqliteImportProgressEvent",e.detail)}),this.jeepSqliteElement.addEventListener("jeepSqliteExportProgress",e=>{this.notifyListeners("sqliteExportProgressEvent",e.detail)}),this.jeepSqliteElement.addEventListener("jeepSqliteHTTPRequestEnded",e=>{this.notifyListeners("sqliteHTTPRequestEndedEvent",e.detail)}),this.jeepSqliteElement.addEventListener("jeepSqlitePickDatabaseEnded",e=>{this.notifyListeners("sqlitePickDatabaseEndedEvent",e.detail)}),this.jeepSqliteElement.addEventListener("jeepSqliteSaveDatabaseToDisk",e=>{this.notifyListeners("sqliteSaveDatabaseToDiskEvent",e.detail)}),this.isWebStoreOpen||(this.isWebStoreOpen=yield this.jeepSqliteElement.isStoreOpen())})}saveToStore(e){return s(this,null,function*(){this.ensureJeepSqliteIsAvailable(),this.ensureWebstoreIsOpen();try{yield this.jeepSqliteElement.saveToStore(e);return}catch(t){throw new Error(`${t}`)}})}getFromLocalDiskToStore(e){return s(this,null,function*(){this.ensureJeepSqliteIsAvailable(),this.ensureWebstoreIsOpen();try{yield this.jeepSqliteElement.getFromLocalDiskToStore(e);return}catch(t){throw new Error(`${t}`)}})}saveToLocalDisk(e){return s(this,null,function*(){this.ensureJeepSqliteIsAvailable(),this.ensureWebstoreIsOpen();try{yield this.jeepSqliteElement.saveToLocalDisk(e);return}catch(t){throw new Error(`${t}`)}})}echo(e){return s(this,null,function*(){return this.ensureJeepSqliteIsAvailable(),yield this.jeepSqliteElement.echo(e)})}createConnection(e){return s(this,null,function*(){this.ensureJeepSqliteIsAvailable(),this.ensureWebstoreIsOpen();try{yield this.jeepSqliteElement.createConnection(e);return}catch(t){throw new Error(`${t}`)}})}open(e){return s(this,null,function*(){this.ensureJeepSqliteIsAvailable(),this.ensureWebstoreIsOpen();try{yield this.jeepSqliteElement.open(e);return}catch(t){throw new Error(`${t}`)}})}closeConnection(e){return s(this,null,function*(){this.ensureJeepSqliteIsAvailable(),this.ensureWebstoreIsOpen();try{yield this.jeepSqliteElement.closeConnection(e);return}catch(t){throw new Error(`${t}`)}})}getVersion(e){return s(this,null,function*(){this.ensureJeepSqliteIsAvailable(),this.ensureWebstoreIsOpen();try{return yield this.jeepSqliteElement.getVersion(e)}catch(t){throw new Error(`${t}`)}})}checkConnectionsConsistency(e){return s(this,null,function*(){this.ensureJeepSqliteIsAvailable();try{return yield this.jeepSqliteElement.checkConnectionsConsistency(e)}catch(t){throw new Error(`${t}`)}})}close(e){return s(this,null,function*(){this.ensureJeepSqliteIsAvailable(),this.ensureWebstoreIsOpen();try{yield this.jeepSqliteElement.close(e);return}catch(t){throw new Error(`${t}`)}})}beginTransaction(e){return s(this,null,function*(){this.ensureJeepSqliteIsAvailable(),this.ensureWebstoreIsOpen();try{return yield this.jeepSqliteElement.beginTransaction(e)}catch(t){throw new Error(`${t}`)}})}commitTransaction(e){return s(this,null,function*(){this.ensureJeepSqliteIsAvailable(),this.ensureWebstoreIsOpen();try{return yield this.jeepSqliteElement.commitTransaction(e)}catch(t){throw new Error(`${t}`)}})}rollbackTransaction(e){return s(this,null,function*(){this.ensureJeepSqliteIsAvailable(),this.ensureWebstoreIsOpen();try{return yield this.jeepSqliteElement.rollbackTransaction(e)}catch(t){throw new Error(`${t}`)}})}isTransactionActive(e){return s(this,null,function*(){this.ensureJeepSqliteIsAvailable(),this.ensureWebstoreIsOpen();try{return yield this.jeepSqliteElement.isTransactionActive(e)}catch(t){throw new Error(`${t}`)}})}getTableList(e){return s(this,null,function*(){this.ensureJeepSqliteIsAvailable(),this.ensureWebstoreIsOpen();try{return yield this.jeepSqliteElement.getTableList(e)}catch(t){throw new Error(`${t}`)}})}execute(e){return s(this,null,function*(){this.ensureJeepSqliteIsAvailable(),this.ensureWebstoreIsOpen();try{return yield this.jeepSqliteElement.execute(e)}catch(t){throw new Error(`${t}`)}})}executeSet(e){return s(this,null,function*(){this.ensureJeepSqliteIsAvailable(),this.ensureWebstoreIsOpen();try{return yield this.jeepSqliteElement.executeSet(e)}catch(t){throw new Error(`${t}`)}})}run(e){return s(this,null,function*(){this.ensureJeepSqliteIsAvailable(),this.ensureWebstoreIsOpen();try{return yield this.jeepSqliteElement.run(e)}catch(t){throw new Error(`${t}`)}})}query(e){return s(this,null,function*(){this.ensureJeepSqliteIsAvailable(),this.ensureWebstoreIsOpen();try{return yield this.jeepSqliteElement.query(e)}catch(t){throw new Error(`${t}`)}})}isDBExists(e){return s(this,null,function*(){this.ensureJeepSqliteIsAvailable(),this.ensureWebstoreIsOpen();try{return yield this.jeepSqliteElement.isDBExists(e)}catch(t){throw new Error(`${t}`)}})}isDBOpen(e){return s(this,null,function*(){this.ensureJeepSqliteIsAvailable(),this.ensureWebstoreIsOpen();try{return yield this.jeepSqliteElement.isDBOpen(e)}catch(t){throw new Error(`${t}`)}})}isDatabase(e){return s(this,null,function*(){this.ensureJeepSqliteIsAvailable(),this.ensureWebstoreIsOpen();try{return yield this.jeepSqliteElement.isDatabase(e)}catch(t){throw new Error(`${t}`)}})}isTableExists(e){return s(this,null,function*(){this.ensureJeepSqliteIsAvailable(),this.ensureWebstoreIsOpen();try{return yield this.jeepSqliteElement.isTableExists(e)}catch(t){throw new Error(`${t}`)}})}deleteDatabase(e){return s(this,null,function*(){this.ensureJeepSqliteIsAvailable(),this.ensureWebstoreIsOpen();try{yield this.jeepSqliteElement.deleteDatabase(e);return}catch(t){throw new Error(`${t}`)}})}isJsonValid(e){return s(this,null,function*(){this.ensureJeepSqliteIsAvailable(),this.ensureWebstoreIsOpen();try{return yield this.jeepSqliteElement.isJsonValid(e)}catch(t){throw new Error(`${t}`)}})}importFromJson(e){return s(this,null,function*(){this.ensureJeepSqliteIsAvailable(),this.ensureWebstoreIsOpen();try{return yield this.jeepSqliteElement.importFromJson(e)}catch(t){throw new Error(`${t}`)}})}exportToJson(e){return s(this,null,function*(){this.ensureJeepSqliteIsAvailable(),this.ensureWebstoreIsOpen();try{return yield this.jeepSqliteElement.exportToJson(e)}catch(t){throw new Error(`${t}`)}})}createSyncTable(e){return s(this,null,function*(){this.ensureJeepSqliteIsAvailable(),this.ensureWebstoreIsOpen();try{return yield this.jeepSqliteElement.createSyncTable(e)}catch(t){throw new Error(`${t}`)}})}setSyncDate(e){return s(this,null,function*(){this.ensureJeepSqliteIsAvailable(),this.ensureWebstoreIsOpen();try{yield this.jeepSqliteElement.setSyncDate(e);return}catch(t){throw new Error(`${t}`)}})}getSyncDate(e){return s(this,null,function*(){this.ensureJeepSqliteIsAvailable(),this.ensureWebstoreIsOpen();try{return yield this.jeepSqliteElement.getSyncDate(e)}catch(t){throw new Error(`${t}`)}})}deleteExportedRows(e){return s(this,null,function*(){this.ensureJeepSqliteIsAvailable(),this.ensureWebstoreIsOpen();try{yield this.jeepSqliteElement.deleteExportedRows(e);return}catch(t){throw new Error(`${t}`)}})}addUpgradeStatement(e){return s(this,null,function*(){this.ensureJeepSqliteIsAvailable(),this.ensureWebstoreIsOpen();try{yield this.jeepSqliteElement.addUpgradeStatement(e);return}catch(t){throw new Error(`${t}`)}})}copyFromAssets(e){return s(this,null,function*(){this.ensureJeepSqliteIsAvailable(),this.ensureWebstoreIsOpen();try{yield this.jeepSqliteElement.copyFromAssets(e);return}catch(t){throw new Error(`${t}`)}})}getFromHTTPRequest(e){return s(this,null,function*(){this.ensureJeepSqliteIsAvailable(),this.ensureWebstoreIsOpen();try{yield this.jeepSqliteElement.getFromHTTPRequest(e);return}catch(t){throw new Error(`${t}`)}})}getDatabaseList(){return s(this,null,function*(){this.ensureJeepSqliteIsAvailable(),this.ensureWebstoreIsOpen();try{return yield this.jeepSqliteElement.getDatabaseList()}catch(e){throw new Error(`${e}`)}})}ensureJeepSqliteIsAvailable(){if(this.jeepSqliteElement===null)throw new Error("The jeep-sqlite element is not present in the DOM! Please check the @capacitor-community/sqlite documentation for instructions regarding the web platform.")}ensureWebstoreIsOpen(){if(!this.isWebStoreOpen)throw new Error('WebStore is not open yet. You have to call "initWebStore()" first.')}getUrl(){return s(this,null,function*(){throw this.unimplemented("Not implemented on web.")})}getMigratableDbList(e){return s(this,null,function*(){throw console.log("getMigratableDbList",e),this.unimplemented("Not implemented on web.")})}addSQLiteSuffix(e){return s(this,null,function*(){throw console.log("addSQLiteSuffix",e),this.unimplemented("Not implemented on web.")})}deleteOldDatabases(e){return s(this,null,function*(){throw console.log("deleteOldDatabases",e),this.unimplemented("Not implemented on web.")})}moveDatabasesAndAddSuffix(e){return s(this,null,function*(){throw console.log("moveDatabasesAndAddSuffix",e),this.unimplemented("Not implemented on web.")})}isSecretStored(){return s(this,null,function*(){throw this.unimplemented("Not implemented on web.")})}setEncryptionSecret(e){return s(this,null,function*(){throw console.log("setEncryptionSecret",e),this.unimplemented("Not implemented on web.")})}changeEncryptionSecret(e){return s(this,null,function*(){throw console.log("changeEncryptionSecret",e),this.unimplemented("Not implemented on web.")})}clearEncryptionSecret(){return s(this,null,function*(){throw console.log("clearEncryptionSecret"),this.unimplemented("Not implemented on web.")})}checkEncryptionSecret(e){return s(this,null,function*(){throw console.log("checkEncryptionPassPhrase",e),this.unimplemented("Not implemented on web.")})}getNCDatabasePath(e){return s(this,null,function*(){throw console.log("getNCDatabasePath",e),this.unimplemented("Not implemented on web.")})}createNCConnection(e){return s(this,null,function*(){throw console.log("createNCConnection",e),this.unimplemented("Not implemented on web.")})}closeNCConnection(e){return s(this,null,function*(){throw console.log("closeNCConnection",e),this.unimplemented("Not implemented on web.")})}isNCDatabase(e){return s(this,null,function*(){throw console.log("isNCDatabase",e),this.unimplemented("Not implemented on web.")})}isDatabaseEncrypted(e){return s(this,null,function*(){throw console.log("isDatabaseEncrypted",e),this.unimplemented("Not implemented on web.")})}isInConfigEncryption(){return s(this,null,function*(){throw this.unimplemented("Not implemented on web.")})}isInConfigBiometricAuth(){return s(this,null,function*(){throw this.unimplemented("Not implemented on web.")})}loadExtension(e){return s(this,null,function*(){throw console.log("loadExtension",e),this.unimplemented("Not implemented on web.")})}enableLoadExtension(e){return s(this,null,function*(){throw console.log("enableLoadExtension",e),this.unimplemented("Not implemented on web.")})}};export{r as CapacitorSQLiteWeb};
