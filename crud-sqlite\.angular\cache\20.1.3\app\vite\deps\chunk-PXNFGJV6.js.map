{"version": 3, "sources": ["../../../../../../node_modules/@ionic/core/dist/esm/input.utils-zWijNCrx.js"], "sourcesContent": ["/*!\n * (C) Ionic http://ionicframework.com - MIT License\n */\nimport { w as win } from './index-ZjP4CjeZ.js';\nimport { r as raf } from './helpers-1O4D2b7y.js';\nimport { o as printIonError } from './index-B_U9CtaY.js';\n\n/**\n * Used to update a scoped component that uses emulated slots. This fires when\n * content is passed into the slot or when the content inside of a slot changes.\n * This is not needed for components using native slots in the Shadow DOM.\n * @internal\n * @param el The host element to observe\n * @param slotName mutationCallback will fire when nodes on these slot(s) change\n * @param mutationCallback The callback to fire whenever the slotted content changes\n */\nconst createSlotMutationController = (el, slotName, mutationCallback) => {\n    let hostMutationObserver;\n    let slottedContentMutationObserver;\n    if (win !== undefined && 'MutationObserver' in win) {\n        const slots = Array.isArray(slotName) ? slotName : [slotName];\n        hostMutationObserver = new MutationObserver((entries) => {\n            for (const entry of entries) {\n                for (const node of entry.addedNodes) {\n                    /**\n                     * Check to see if the added node\n                     *  is our slotted content.\n                     */\n                    if (node.nodeType === Node.ELEMENT_NODE && slots.includes(node.slot)) {\n                        /**\n                         * If so, we want to watch the slotted\n                         * content itself for changes. This lets us\n                         * detect when content inside of the slot changes.\n                         */\n                        mutationCallback();\n                        /**\n                         * Adding the listener in an raf\n                         * waits until Stencil moves the slotted element\n                         * into the correct place in the event that\n                         * slotted content is being added.\n                         */\n                        raf(() => watchForSlotChange(node));\n                        return;\n                    }\n                }\n            }\n        });\n        hostMutationObserver.observe(el, {\n            childList: true,\n            /**\n             * This fixes an issue with the `ion-input` and\n             * `ion-textarea` not re-rendering in some cases\n             * when using the label slot functionality.\n             *\n             * HTML element patches in Stencil that are enabled\n             * by the `experimentalSlotFixes` flag in Stencil v4\n             * result in DOM manipulations that won't trigger\n             * the current mutation observer configuration and\n             * callback.\n             */\n            subtree: true,\n        });\n    }\n    /**\n     * Listen for changes inside of the slotted content.\n     * We can listen for subtree changes here to be\n     * informed of text within the slotted content\n     * changing. Doing this on the host is possible\n     * but it is much more expensive to do because\n     * it also listens for changes to the internals\n     * of the component.\n     */\n    const watchForSlotChange = (slottedEl) => {\n        var _a;\n        if (slottedContentMutationObserver) {\n            slottedContentMutationObserver.disconnect();\n            slottedContentMutationObserver = undefined;\n        }\n        slottedContentMutationObserver = new MutationObserver((entries) => {\n            mutationCallback();\n            for (const entry of entries) {\n                for (const node of entry.removedNodes) {\n                    /**\n                     * If the element was removed then we\n                     * need to destroy the MutationObserver\n                     * so the element can be garbage collected.\n                     */\n                    if (node.nodeType === Node.ELEMENT_NODE && node.slot === slotName) {\n                        destroySlottedContentObserver();\n                    }\n                }\n            }\n        });\n        /**\n         * Listen for changes inside of the element\n         * as well as anything deep in the tree.\n         * We listen on the parentElement so that we can\n         * detect when slotted element itself is removed.\n         */\n        slottedContentMutationObserver.observe((_a = slottedEl.parentElement) !== null && _a !== void 0 ? _a : slottedEl, { subtree: true, childList: true });\n    };\n    const destroy = () => {\n        if (hostMutationObserver) {\n            hostMutationObserver.disconnect();\n            hostMutationObserver = undefined;\n        }\n        destroySlottedContentObserver();\n    };\n    const destroySlottedContentObserver = () => {\n        if (slottedContentMutationObserver) {\n            slottedContentMutationObserver.disconnect();\n            slottedContentMutationObserver = undefined;\n        }\n    };\n    return {\n        destroy,\n    };\n};\n\nconst getCounterText = (value, maxLength, counterFormatter) => {\n    const valueLength = value == null ? 0 : value.toString().length;\n    const defaultCounterText = defaultCounterFormatter(valueLength, maxLength);\n    /**\n     * If developers did not pass a custom formatter,\n     * use the default one.\n     */\n    if (counterFormatter === undefined) {\n        return defaultCounterText;\n    }\n    /**\n     * Otherwise, try to use the custom formatter\n     * and fallback to the default formatter if\n     * there was an error.\n     */\n    try {\n        return counterFormatter(valueLength, maxLength);\n    }\n    catch (e) {\n        printIonError('[ion-input] - Exception in provided `counterFormatter`:', e);\n        return defaultCounterText;\n    }\n};\nconst defaultCounterFormatter = (length, maxlength) => {\n    return `${length} / ${maxlength}`;\n};\n\nexport { createSlotMutationController as c, getCounterText as g };\n"], "mappings": ";;;;;;;;;;;AAgBA,IAAM,+BAA+B,CAAC,IAAI,UAAU,qBAAqB;AACrE,MAAI;AACJ,MAAI;AACJ,MAAI,QAAQ,UAAa,sBAAsB,KAAK;AAChD,UAAM,QAAQ,MAAM,QAAQ,QAAQ,IAAI,WAAW,CAAC,QAAQ;AAC5D,2BAAuB,IAAI,iBAAiB,CAAC,YAAY;AACrD,iBAAW,SAAS,SAAS;AACzB,mBAAW,QAAQ,MAAM,YAAY;AAKjC,cAAI,KAAK,aAAa,KAAK,gBAAgB,MAAM,SAAS,KAAK,IAAI,GAAG;AAMlE,6BAAiB;AAOjB,gBAAI,MAAM,mBAAmB,IAAI,CAAC;AAClC;AAAA,UACJ;AAAA,QACJ;AAAA,MACJ;AAAA,IACJ,CAAC;AACD,yBAAqB,QAAQ,IAAI;AAAA,MAC7B,WAAW;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAYX,SAAS;AAAA,IACb,CAAC;AAAA,EACL;AAUA,QAAM,qBAAqB,CAAC,cAAc;AACtC,QAAI;AACJ,QAAI,gCAAgC;AAChC,qCAA+B,WAAW;AAC1C,uCAAiC;AAAA,IACrC;AACA,qCAAiC,IAAI,iBAAiB,CAAC,YAAY;AAC/D,uBAAiB;AACjB,iBAAW,SAAS,SAAS;AACzB,mBAAW,QAAQ,MAAM,cAAc;AAMnC,cAAI,KAAK,aAAa,KAAK,gBAAgB,KAAK,SAAS,UAAU;AAC/D,0CAA8B;AAAA,UAClC;AAAA,QACJ;AAAA,MACJ;AAAA,IACJ,CAAC;AAOD,mCAA+B,SAAS,KAAK,UAAU,mBAAmB,QAAQ,OAAO,SAAS,KAAK,WAAW,EAAE,SAAS,MAAM,WAAW,KAAK,CAAC;AAAA,EACxJ;AACA,QAAM,UAAU,MAAM;AAClB,QAAI,sBAAsB;AACtB,2BAAqB,WAAW;AAChC,6BAAuB;AAAA,IAC3B;AACA,kCAA8B;AAAA,EAClC;AACA,QAAM,gCAAgC,MAAM;AACxC,QAAI,gCAAgC;AAChC,qCAA+B,WAAW;AAC1C,uCAAiC;AAAA,IACrC;AAAA,EACJ;AACA,SAAO;AAAA,IACH;AAAA,EACJ;AACJ;AAEA,IAAM,iBAAiB,CAAC,OAAO,WAAW,qBAAqB;AAC3D,QAAM,cAAc,SAAS,OAAO,IAAI,MAAM,SAAS,EAAE;AACzD,QAAM,qBAAqB,wBAAwB,aAAa,SAAS;AAKzE,MAAI,qBAAqB,QAAW;AAChC,WAAO;AAAA,EACX;AAMA,MAAI;AACA,WAAO,iBAAiB,aAAa,SAAS;AAAA,EAClD,SACO,GAAG;AACN,kBAAc,2DAA2D,CAAC;AAC1E,WAAO;AAAA,EACX;AACJ;AACA,IAAM,0BAA0B,CAAC,QAAQ,cAAc;AACnD,SAAO,GAAG,MAAM,MAAM,SAAS;AACnC;", "names": []}