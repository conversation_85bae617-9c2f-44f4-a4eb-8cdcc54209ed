{"version": 3, "sources": ["../../../../../../node_modules/@ionic/core/dist/esm/ion-col_3.entry.js"], "sourcesContent": ["/*!\n * (C) Ionic http://ionicframework.com - MIT License\n */\nimport { r as registerInstance, n as forceUpdate, e as getIonMode, h, j as Host } from './index-B_U9CtaY.js';\n\nconst SIZE_TO_MEDIA = {\n    xs: '(min-width: 0px)',\n    sm: '(min-width: 576px)',\n    md: '(min-width: 768px)',\n    lg: '(min-width: 992px)',\n    xl: '(min-width: 1200px)',\n};\n// Check if the window matches the media query\n// at the breakpoint passed\n// e.g. matchBreakpoint('sm') => true if screen width exceeds 576px\nconst matchBreakpoint = (breakpoint) => {\n    if (breakpoint === undefined || breakpoint === '') {\n        return true;\n    }\n    if (window.matchMedia) {\n        const mediaQuery = SIZE_TO_MEDIA[breakpoint];\n        return window.matchMedia(mediaQuery).matches;\n    }\n    return false;\n};\n\nconst colCss = \":host{-webkit-padding-start:var(--ion-grid-column-padding-xs, var(--ion-grid-column-padding, 5px));padding-inline-start:var(--ion-grid-column-padding-xs, var(--ion-grid-column-padding, 5px));-webkit-padding-end:var(--ion-grid-column-padding-xs, var(--ion-grid-column-padding, 5px));padding-inline-end:var(--ion-grid-column-padding-xs, var(--ion-grid-column-padding, 5px));padding-top:var(--ion-grid-column-padding-xs, var(--ion-grid-column-padding, 5px));padding-bottom:var(--ion-grid-column-padding-xs, var(--ion-grid-column-padding, 5px));margin-left:0;margin-right:0;margin-top:0;margin-bottom:0;-webkit-box-sizing:border-box;box-sizing:border-box;position:relative;-ms-flex-preferred-size:0;flex-basis:0;-ms-flex-positive:1;flex-grow:1;width:100%;max-width:100%;min-height:1px}@media (min-width: 576px){:host{-webkit-padding-start:var(--ion-grid-column-padding-sm, var(--ion-grid-column-padding, 5px));padding-inline-start:var(--ion-grid-column-padding-sm, var(--ion-grid-column-padding, 5px));-webkit-padding-end:var(--ion-grid-column-padding-sm, var(--ion-grid-column-padding, 5px));padding-inline-end:var(--ion-grid-column-padding-sm, var(--ion-grid-column-padding, 5px));padding-top:var(--ion-grid-column-padding-sm, var(--ion-grid-column-padding, 5px));padding-bottom:var(--ion-grid-column-padding-sm, var(--ion-grid-column-padding, 5px))}}@media (min-width: 768px){:host{-webkit-padding-start:var(--ion-grid-column-padding-md, var(--ion-grid-column-padding, 5px));padding-inline-start:var(--ion-grid-column-padding-md, var(--ion-grid-column-padding, 5px));-webkit-padding-end:var(--ion-grid-column-padding-md, var(--ion-grid-column-padding, 5px));padding-inline-end:var(--ion-grid-column-padding-md, var(--ion-grid-column-padding, 5px));padding-top:var(--ion-grid-column-padding-md, var(--ion-grid-column-padding, 5px));padding-bottom:var(--ion-grid-column-padding-md, var(--ion-grid-column-padding, 5px))}}@media (min-width: 992px){:host{-webkit-padding-start:var(--ion-grid-column-padding-lg, var(--ion-grid-column-padding, 5px));padding-inline-start:var(--ion-grid-column-padding-lg, var(--ion-grid-column-padding, 5px));-webkit-padding-end:var(--ion-grid-column-padding-lg, var(--ion-grid-column-padding, 5px));padding-inline-end:var(--ion-grid-column-padding-lg, var(--ion-grid-column-padding, 5px));padding-top:var(--ion-grid-column-padding-lg, var(--ion-grid-column-padding, 5px));padding-bottom:var(--ion-grid-column-padding-lg, var(--ion-grid-column-padding, 5px))}}@media (min-width: 1200px){:host{-webkit-padding-start:var(--ion-grid-column-padding-xl, var(--ion-grid-column-padding, 5px));padding-inline-start:var(--ion-grid-column-padding-xl, var(--ion-grid-column-padding, 5px));-webkit-padding-end:var(--ion-grid-column-padding-xl, var(--ion-grid-column-padding, 5px));padding-inline-end:var(--ion-grid-column-padding-xl, var(--ion-grid-column-padding, 5px));padding-top:var(--ion-grid-column-padding-xl, var(--ion-grid-column-padding, 5px));padding-bottom:var(--ion-grid-column-padding-xl, var(--ion-grid-column-padding, 5px))}}\";\n\nconst win = typeof window !== 'undefined' ? window : undefined;\n// eslint-disable-next-line @typescript-eslint/prefer-optional-chain\nconst SUPPORTS_VARS = win && !!(win.CSS && win.CSS.supports && win.CSS.supports('--a: 0'));\nconst BREAKPOINTS = ['', 'xs', 'sm', 'md', 'lg', 'xl'];\nconst Col = class {\n    constructor(hostRef) {\n        registerInstance(this, hostRef);\n    }\n    onResize() {\n        forceUpdate(this);\n    }\n    // Loop through all of the breakpoints to see if the media query\n    // matches and grab the column value from the relevant prop if so\n    getColumns(property) {\n        let matched;\n        for (const breakpoint of BREAKPOINTS) {\n            const matches = matchBreakpoint(breakpoint);\n            // Grab the value of the property, if it exists and our\n            // media query matches we return the value\n            const columns = this[property + breakpoint.charAt(0).toUpperCase() + breakpoint.slice(1)];\n            if (matches && columns !== undefined) {\n                matched = columns;\n            }\n        }\n        // Return the last matched columns since the breakpoints\n        // increase in size and we want to return the largest match\n        return matched;\n    }\n    calculateSize() {\n        const columns = this.getColumns('size');\n        // If size wasn't set for any breakpoint\n        // or if the user set the size without a value\n        // it means we need to stick with the default and return\n        // e.g. <ion-col size-md>\n        if (!columns || columns === '') {\n            return;\n        }\n        // If the size is set to auto then don't calculate a size\n        const colSize = columns === 'auto'\n            ? 'auto'\n            : // If CSS supports variables we should use the grid columns var\n                SUPPORTS_VARS\n                    ? `calc(calc(${columns} / var(--ion-grid-columns, 12)) * 100%)`\n                    : // Convert the columns to a percentage by dividing by the total number\n                        // of columns (12) and then multiplying by 100\n                        (columns / 12) * 100 + '%';\n        return {\n            flex: `0 0 ${colSize}`,\n            width: `${colSize}`,\n            'max-width': `${colSize}`,\n        };\n    }\n    // Called by push, pull, and offset since they use the same calculations\n    calculatePosition(property, modifier) {\n        const columns = this.getColumns(property);\n        if (!columns) {\n            return;\n        }\n        // If the number of columns passed are greater than 0 and less than\n        // 12 we can position the column, else default to auto\n        const amount = SUPPORTS_VARS\n            ? // If CSS supports variables we should use the grid columns var\n                `calc(calc(${columns} / var(--ion-grid-columns, 12)) * 100%)`\n            : // Convert the columns to a percentage by dividing by the total number\n                // of columns (12) and then multiplying by 100\n                columns > 0 && columns < 12\n                    ? (columns / 12) * 100 + '%'\n                    : 'auto';\n        return {\n            [modifier]: amount,\n        };\n    }\n    calculateOffset(isRTL) {\n        return this.calculatePosition('offset', isRTL ? 'margin-right' : 'margin-left');\n    }\n    calculatePull(isRTL) {\n        return this.calculatePosition('pull', isRTL ? 'left' : 'right');\n    }\n    calculatePush(isRTL) {\n        return this.calculatePosition('push', isRTL ? 'right' : 'left');\n    }\n    render() {\n        const isRTL = document.dir === 'rtl';\n        const mode = getIonMode(this);\n        return (h(Host, { key: '32ed75d81dd09d9bc8999f6d42e5b3cb99c84d91', class: {\n                [mode]: true,\n            }, style: Object.assign(Object.assign(Object.assign(Object.assign({}, this.calculateOffset(isRTL)), this.calculatePull(isRTL)), this.calculatePush(isRTL)), this.calculateSize()) }, h(\"slot\", { key: '38f8d0440c20cc6d1b1d6a654d07f16de61d8134' })));\n    }\n};\nCol.style = colCss;\n\nconst gridCss = \":host{-webkit-padding-start:var(--ion-grid-padding-xs, var(--ion-grid-padding, 5px));padding-inline-start:var(--ion-grid-padding-xs, var(--ion-grid-padding, 5px));-webkit-padding-end:var(--ion-grid-padding-xs, var(--ion-grid-padding, 5px));padding-inline-end:var(--ion-grid-padding-xs, var(--ion-grid-padding, 5px));padding-top:var(--ion-grid-padding-xs, var(--ion-grid-padding, 5px));padding-bottom:var(--ion-grid-padding-xs, var(--ion-grid-padding, 5px));-webkit-margin-start:auto;margin-inline-start:auto;-webkit-margin-end:auto;margin-inline-end:auto;display:block;-ms-flex:1;flex:1}@media (min-width: 576px){:host{-webkit-padding-start:var(--ion-grid-padding-sm, var(--ion-grid-padding, 5px));padding-inline-start:var(--ion-grid-padding-sm, var(--ion-grid-padding, 5px));-webkit-padding-end:var(--ion-grid-padding-sm, var(--ion-grid-padding, 5px));padding-inline-end:var(--ion-grid-padding-sm, var(--ion-grid-padding, 5px));padding-top:var(--ion-grid-padding-sm, var(--ion-grid-padding, 5px));padding-bottom:var(--ion-grid-padding-sm, var(--ion-grid-padding, 5px))}}@media (min-width: 768px){:host{-webkit-padding-start:var(--ion-grid-padding-md, var(--ion-grid-padding, 5px));padding-inline-start:var(--ion-grid-padding-md, var(--ion-grid-padding, 5px));-webkit-padding-end:var(--ion-grid-padding-md, var(--ion-grid-padding, 5px));padding-inline-end:var(--ion-grid-padding-md, var(--ion-grid-padding, 5px));padding-top:var(--ion-grid-padding-md, var(--ion-grid-padding, 5px));padding-bottom:var(--ion-grid-padding-md, var(--ion-grid-padding, 5px))}}@media (min-width: 992px){:host{-webkit-padding-start:var(--ion-grid-padding-lg, var(--ion-grid-padding, 5px));padding-inline-start:var(--ion-grid-padding-lg, var(--ion-grid-padding, 5px));-webkit-padding-end:var(--ion-grid-padding-lg, var(--ion-grid-padding, 5px));padding-inline-end:var(--ion-grid-padding-lg, var(--ion-grid-padding, 5px));padding-top:var(--ion-grid-padding-lg, var(--ion-grid-padding, 5px));padding-bottom:var(--ion-grid-padding-lg, var(--ion-grid-padding, 5px))}}@media (min-width: 1200px){:host{-webkit-padding-start:var(--ion-grid-padding-xl, var(--ion-grid-padding, 5px));padding-inline-start:var(--ion-grid-padding-xl, var(--ion-grid-padding, 5px));-webkit-padding-end:var(--ion-grid-padding-xl, var(--ion-grid-padding, 5px));padding-inline-end:var(--ion-grid-padding-xl, var(--ion-grid-padding, 5px));padding-top:var(--ion-grid-padding-xl, var(--ion-grid-padding, 5px));padding-bottom:var(--ion-grid-padding-xl, var(--ion-grid-padding, 5px))}}:host(.grid-fixed){width:var(--ion-grid-width-xs, var(--ion-grid-width, 100%));max-width:100%}@media (min-width: 576px){:host(.grid-fixed){width:var(--ion-grid-width-sm, var(--ion-grid-width, 540px))}}@media (min-width: 768px){:host(.grid-fixed){width:var(--ion-grid-width-md, var(--ion-grid-width, 720px))}}@media (min-width: 992px){:host(.grid-fixed){width:var(--ion-grid-width-lg, var(--ion-grid-width, 960px))}}@media (min-width: 1200px){:host(.grid-fixed){width:var(--ion-grid-width-xl, var(--ion-grid-width, 1140px))}}:host(.ion-no-padding){--ion-grid-column-padding:0;--ion-grid-column-padding-xs:0;--ion-grid-column-padding-sm:0;--ion-grid-column-padding-md:0;--ion-grid-column-padding-lg:0;--ion-grid-column-padding-xl:0}\";\n\nconst Grid = class {\n    constructor(hostRef) {\n        registerInstance(this, hostRef);\n        /**\n         * If `true`, the grid will have a fixed width based on the screen size.\n         */\n        this.fixed = false;\n    }\n    render() {\n        const mode = getIonMode(this);\n        return (h(Host, { key: '617127ecfabf9bf615bef1dda1be3fed5a065949', class: {\n                [mode]: true,\n                'grid-fixed': this.fixed,\n            } }, h(\"slot\", { key: 'c781fff853b093d8f44bdb7943bbc4f17c903803' })));\n    }\n};\nGrid.style = gridCss;\n\nconst rowCss = \":host{display:-ms-flexbox;display:flex;-ms-flex-wrap:wrap;flex-wrap:wrap}\";\n\nconst Row = class {\n    constructor(hostRef) {\n        registerInstance(this, hostRef);\n    }\n    render() {\n        return (h(Host, { key: '65592a79621bd8f75f9566db3e8c05a4b8fc6048', class: getIonMode(this) }, h(\"slot\", { key: '56f09784db7a0299c9ce76dfcede185b295251ff' })));\n    }\n};\nRow.style = rowCss;\n\nexport { Col as ion_col, Grid as ion_grid, Row as ion_row };\n"], "mappings": ";;;;;;;;;;AAKA,IAAM,gBAAgB;AAAA,EAClB,IAAI;AAAA,EACJ,IAAI;AAAA,EACJ,IAAI;AAAA,EACJ,IAAI;AAAA,EACJ,IAAI;AACR;AAIA,IAAM,kBAAkB,CAAC,eAAe;AACpC,MAAI,eAAe,UAAa,eAAe,IAAI;AAC/C,WAAO;AAAA,EACX;AACA,MAAI,OAAO,YAAY;AACnB,UAAM,aAAa,cAAc,UAAU;AAC3C,WAAO,OAAO,WAAW,UAAU,EAAE;AAAA,EACzC;AACA,SAAO;AACX;AAEA,IAAM,SAAS;AAEf,IAAM,MAAM,OAAO,WAAW,cAAc,SAAS;AAErD,IAAM,gBAAgB,OAAO,CAAC,EAAE,IAAI,OAAO,IAAI,IAAI,YAAY,IAAI,IAAI,SAAS,QAAQ;AACxF,IAAM,cAAc,CAAC,IAAI,MAAM,MAAM,MAAM,MAAM,IAAI;AACrD,IAAM,MAAM,MAAM;AAAA,EACd,YAAY,SAAS;AACjB,qBAAiB,MAAM,OAAO;AAAA,EAClC;AAAA,EACA,WAAW;AACP,gBAAY,IAAI;AAAA,EACpB;AAAA;AAAA;AAAA,EAGA,WAAW,UAAU;AACjB,QAAI;AACJ,eAAW,cAAc,aAAa;AAClC,YAAM,UAAU,gBAAgB,UAAU;AAG1C,YAAM,UAAU,KAAK,WAAW,WAAW,OAAO,CAAC,EAAE,YAAY,IAAI,WAAW,MAAM,CAAC,CAAC;AACxF,UAAI,WAAW,YAAY,QAAW;AAClC,kBAAU;AAAA,MACd;AAAA,IACJ;AAGA,WAAO;AAAA,EACX;AAAA,EACA,gBAAgB;AACZ,UAAM,UAAU,KAAK,WAAW,MAAM;AAKtC,QAAI,CAAC,WAAW,YAAY,IAAI;AAC5B;AAAA,IACJ;AAEA,UAAM,UAAU,YAAY,SACtB;AAAA;AAAA,MAEE,gBACM,aAAa,OAAO;AAAA;AAAA;AAAA,QAGjB,UAAU,KAAM,MAAM;AAAA;AAAA;AACvC,WAAO;AAAA,MACH,MAAM,OAAO,OAAO;AAAA,MACpB,OAAO,GAAG,OAAO;AAAA,MACjB,aAAa,GAAG,OAAO;AAAA,IAC3B;AAAA,EACJ;AAAA;AAAA,EAEA,kBAAkB,UAAU,UAAU;AAClC,UAAM,UAAU,KAAK,WAAW,QAAQ;AACxC,QAAI,CAAC,SAAS;AACV;AAAA,IACJ;AAGA,UAAM,SAAS;AAAA;AAAA,MAEP,aAAa,OAAO;AAAA;AAAA;AAAA;AAAA,MAGpB,UAAU,KAAK,UAAU,KAClB,UAAU,KAAM,MAAM,MACvB;AAAA;AACd,WAAO;AAAA,MACH,CAAC,QAAQ,GAAG;AAAA,IAChB;AAAA,EACJ;AAAA,EACA,gBAAgB,OAAO;AACnB,WAAO,KAAK,kBAAkB,UAAU,QAAQ,iBAAiB,aAAa;AAAA,EAClF;AAAA,EACA,cAAc,OAAO;AACjB,WAAO,KAAK,kBAAkB,QAAQ,QAAQ,SAAS,OAAO;AAAA,EAClE;AAAA,EACA,cAAc,OAAO;AACjB,WAAO,KAAK,kBAAkB,QAAQ,QAAQ,UAAU,MAAM;AAAA,EAClE;AAAA,EACA,SAAS;AACL,UAAM,QAAQ,SAAS,QAAQ;AAC/B,UAAM,OAAO,WAAW,IAAI;AAC5B,WAAQ,EAAE,MAAM,EAAE,KAAK,4CAA4C,OAAO;AAAA,MAClE,CAAC,IAAI,GAAG;AAAA,IACZ,GAAG,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,CAAC,GAAG,KAAK,gBAAgB,KAAK,CAAC,GAAG,KAAK,cAAc,KAAK,CAAC,GAAG,KAAK,cAAc,KAAK,CAAC,GAAG,KAAK,cAAc,CAAC,EAAE,GAAG,EAAE,QAAQ,EAAE,KAAK,2CAA2C,CAAC,CAAC;AAAA,EAC3P;AACJ;AACA,IAAI,QAAQ;AAEZ,IAAM,UAAU;AAEhB,IAAM,OAAO,MAAM;AAAA,EACf,YAAY,SAAS;AACjB,qBAAiB,MAAM,OAAO;AAI9B,SAAK,QAAQ;AAAA,EACjB;AAAA,EACA,SAAS;AACL,UAAM,OAAO,WAAW,IAAI;AAC5B,WAAQ,EAAE,MAAM,EAAE,KAAK,4CAA4C,OAAO;AAAA,MAClE,CAAC,IAAI,GAAG;AAAA,MACR,cAAc,KAAK;AAAA,IACvB,EAAE,GAAG,EAAE,QAAQ,EAAE,KAAK,2CAA2C,CAAC,CAAC;AAAA,EAC3E;AACJ;AACA,KAAK,QAAQ;AAEb,IAAM,SAAS;AAEf,IAAM,MAAM,MAAM;AAAA,EACd,YAAY,SAAS;AACjB,qBAAiB,MAAM,OAAO;AAAA,EAClC;AAAA,EACA,SAAS;AACL,WAAQ,EAAE,MAAM,EAAE,KAAK,4CAA4C,OAAO,WAAW,IAAI,EAAE,GAAG,EAAE,QAAQ,EAAE,KAAK,2CAA2C,CAAC,CAAC;AAAA,EAChK;AACJ;AACA,IAAI,QAAQ;", "names": []}