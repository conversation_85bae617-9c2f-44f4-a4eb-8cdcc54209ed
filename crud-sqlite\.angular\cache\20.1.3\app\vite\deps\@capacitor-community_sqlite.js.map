{"version": 3, "sources": ["../../../../../../node_modules/@capacitor-community/sqlite/dist/esm/definitions.js", "../../../../../../node_modules/@capacitor-community/sqlite/dist/esm/index.js"], "sourcesContent": ["//import { Capacitor } from '@capacitor/core';\n/**\n * SQLiteConnection Class\n */\nexport class SQLiteConnection {\n    constructor(sqlite) {\n        this.sqlite = sqlite;\n        this._connectionDict = new Map();\n    }\n    async initWebStore() {\n        try {\n            await this.sqlite.initWebStore();\n            return Promise.resolve();\n        }\n        catch (err) {\n            return Promise.reject(err);\n        }\n    }\n    async saveToStore(database) {\n        try {\n            await this.sqlite.saveToStore({ database });\n            return Promise.resolve();\n        }\n        catch (err) {\n            return Promise.reject(err);\n        }\n    }\n    async saveToLocalDisk(database) {\n        try {\n            await this.sqlite.saveToLocalDisk({ database });\n            return Promise.resolve();\n        }\n        catch (err) {\n            return Promise.reject(err);\n        }\n    }\n    async getFromLocalDiskToStore(overwrite) {\n        const mOverwrite = overwrite != null ? overwrite : true;\n        try {\n            await this.sqlite.getFromLocalDiskToStore({ overwrite: mOverwrite });\n            return Promise.resolve();\n        }\n        catch (err) {\n            return Promise.reject(err);\n        }\n    }\n    async echo(value) {\n        try {\n            const res = await this.sqlite.echo({ value });\n            return Promise.resolve(res);\n        }\n        catch (err) {\n            return Promise.reject(err);\n        }\n    }\n    async isSecretStored() {\n        try {\n            const res = await this.sqlite.isSecretStored();\n            return Promise.resolve(res);\n        }\n        catch (err) {\n            return Promise.reject(err);\n        }\n    }\n    async setEncryptionSecret(passphrase) {\n        try {\n            await this.sqlite.setEncryptionSecret({ passphrase: passphrase });\n            return Promise.resolve();\n        }\n        catch (err) {\n            return Promise.reject(err);\n        }\n    }\n    async changeEncryptionSecret(passphrase, oldpassphrase) {\n        try {\n            await this.sqlite.changeEncryptionSecret({\n                passphrase: passphrase,\n                oldpassphrase: oldpassphrase,\n            });\n            return Promise.resolve();\n        }\n        catch (err) {\n            return Promise.reject(err);\n        }\n    }\n    async clearEncryptionSecret() {\n        try {\n            await this.sqlite.clearEncryptionSecret();\n            return Promise.resolve();\n        }\n        catch (err) {\n            return Promise.reject(err);\n        }\n    }\n    async checkEncryptionSecret(passphrase) {\n        try {\n            const res = await this.sqlite.checkEncryptionSecret({\n                passphrase: passphrase,\n            });\n            return Promise.resolve(res);\n        }\n        catch (err) {\n            return Promise.reject(err);\n        }\n    }\n    async addUpgradeStatement(database, upgrade) {\n        try {\n            if (database.endsWith('.db'))\n                database = database.slice(0, -3);\n            await this.sqlite.addUpgradeStatement({\n                database,\n                upgrade,\n            });\n            return Promise.resolve();\n        }\n        catch (err) {\n            return Promise.reject(err);\n        }\n    }\n    async createConnection(database, encrypted, mode, version, readonly) {\n        try {\n            if (database.endsWith('.db'))\n                database = database.slice(0, -3);\n            await this.sqlite.createConnection({\n                database,\n                encrypted,\n                mode,\n                version,\n                readonly,\n            });\n            const conn = new SQLiteDBConnection(database, readonly, this.sqlite);\n            const connName = readonly ? `RO_${database}` : `RW_${database}`;\n            this._connectionDict.set(connName, conn);\n            /*\n            console.log(`*** in createConnection connectionDict: ***`)\n            this._connectionDict.forEach((connection, key) => {\n              console.log(`Key: ${key}, Value: ${connection}`);\n            });\n      */\n            return Promise.resolve(conn);\n        }\n        catch (err) {\n            return Promise.reject(err);\n        }\n    }\n    async closeConnection(database, readonly) {\n        try {\n            if (database.endsWith('.db'))\n                database = database.slice(0, -3);\n            await this.sqlite.closeConnection({ database, readonly });\n            const connName = readonly ? `RO_${database}` : `RW_${database}`;\n            this._connectionDict.delete(connName);\n            /*      console.log(`*** in closeConnection connectionDict: ***`)\n            this._connectionDict.forEach((connection, key) => {\n              console.log(`Key: ${key}, Value: ${connection}`);\n            });\n      */\n            return Promise.resolve();\n        }\n        catch (err) {\n            return Promise.reject(err);\n        }\n    }\n    async isConnection(database, readonly) {\n        const res = {};\n        if (database.endsWith('.db'))\n            database = database.slice(0, -3);\n        const connName = readonly ? `RO_${database}` : `RW_${database}`;\n        res.result = this._connectionDict.has(connName);\n        return Promise.resolve(res);\n    }\n    async retrieveConnection(database, readonly) {\n        if (database.endsWith('.db'))\n            database = database.slice(0, -3);\n        const connName = readonly ? `RO_${database}` : `RW_${database}`;\n        if (this._connectionDict.has(connName)) {\n            const conn = this._connectionDict.get(connName);\n            if (typeof conn != 'undefined')\n                return Promise.resolve(conn);\n            else {\n                return Promise.reject(`Connection ${database} is undefined`);\n            }\n        }\n        else {\n            return Promise.reject(`Connection ${database} does not exist`);\n        }\n    }\n    async getNCDatabasePath(path, database) {\n        try {\n            const databasePath = await this.sqlite.getNCDatabasePath({\n                path,\n                database,\n            });\n            return Promise.resolve(databasePath);\n        }\n        catch (err) {\n            return Promise.reject(err);\n        }\n    }\n    async createNCConnection(databasePath, version) {\n        try {\n            await this.sqlite.createNCConnection({\n                databasePath,\n                version,\n            });\n            const conn = new SQLiteDBConnection(databasePath, true, this.sqlite);\n            const connName = `RO_${databasePath})`;\n            this._connectionDict.set(connName, conn);\n            return Promise.resolve(conn);\n        }\n        catch (err) {\n            return Promise.reject(err);\n        }\n    }\n    async closeNCConnection(databasePath) {\n        try {\n            await this.sqlite.closeNCConnection({ databasePath });\n            const connName = `RO_${databasePath})`;\n            this._connectionDict.delete(connName);\n            return Promise.resolve();\n        }\n        catch (err) {\n            return Promise.reject(err);\n        }\n    }\n    async isNCConnection(databasePath) {\n        const res = {};\n        const connName = `RO_${databasePath})`;\n        res.result = this._connectionDict.has(connName);\n        return Promise.resolve(res);\n    }\n    async retrieveNCConnection(databasePath) {\n        if (this._connectionDict.has(databasePath)) {\n            const connName = `RO_${databasePath})`;\n            const conn = this._connectionDict.get(connName);\n            if (typeof conn != 'undefined')\n                return Promise.resolve(conn);\n            else {\n                return Promise.reject(`Connection ${databasePath} is undefined`);\n            }\n        }\n        else {\n            return Promise.reject(`Connection ${databasePath} does not exist`);\n        }\n    }\n    async isNCDatabase(databasePath) {\n        try {\n            const res = await this.sqlite.isNCDatabase({ databasePath });\n            return Promise.resolve(res);\n        }\n        catch (err) {\n            return Promise.reject(err);\n        }\n    }\n    async retrieveAllConnections() {\n        return this._connectionDict;\n    }\n    async closeAllConnections() {\n        const delDict = new Map();\n        try {\n            /*      console.log(`*** in closeAllConnections connectionDict: ***`)\n            this._connectionDict.forEach((connection, key) => {\n              console.log(`Key: ${key}, Value: ${connection}`);\n            });\n      */\n            for (const key of this._connectionDict.keys()) {\n                const database = key.substring(3);\n                const readonly = key.substring(0, 3) === 'RO_' ? true : false;\n                await this.sqlite.closeConnection({ database, readonly });\n                delDict.set(key, null);\n            }\n            for (const key of delDict.keys()) {\n                this._connectionDict.delete(key);\n            }\n            return Promise.resolve();\n        }\n        catch (err) {\n            return Promise.reject(err);\n        }\n    }\n    async checkConnectionsConsistency() {\n        try {\n            const keys = [...this._connectionDict.keys()];\n            const openModes = [];\n            const dbNames = [];\n            for (const key of keys) {\n                openModes.push(key.substring(0, 2));\n                dbNames.push(key.substring(3));\n            }\n            const res = await this.sqlite.checkConnectionsConsistency({\n                dbNames: dbNames,\n                openModes: openModes,\n            });\n            if (!res.result)\n                this._connectionDict = new Map();\n            return Promise.resolve(res);\n        }\n        catch (err) {\n            this._connectionDict = new Map();\n            return Promise.reject(err);\n        }\n    }\n    async importFromJson(jsonstring) {\n        try {\n            const ret = await this.sqlite.importFromJson({ jsonstring: jsonstring });\n            return Promise.resolve(ret);\n        }\n        catch (err) {\n            return Promise.reject(err);\n        }\n    }\n    async isJsonValid(jsonstring) {\n        try {\n            const ret = await this.sqlite.isJsonValid({ jsonstring: jsonstring });\n            return Promise.resolve(ret);\n        }\n        catch (err) {\n            return Promise.reject(err);\n        }\n    }\n    async copyFromAssets(overwrite) {\n        const mOverwrite = overwrite != null ? overwrite : true;\n        try {\n            await this.sqlite.copyFromAssets({ overwrite: mOverwrite });\n            return Promise.resolve();\n        }\n        catch (err) {\n            return Promise.reject(err);\n        }\n    }\n    async getFromHTTPRequest(url, overwrite) {\n        const mOverwrite = overwrite != null ? overwrite : true;\n        try {\n            await this.sqlite.getFromHTTPRequest({ url, overwrite: mOverwrite });\n            return Promise.resolve();\n        }\n        catch (err) {\n            return Promise.reject(err);\n        }\n    }\n    async isDatabaseEncrypted(database) {\n        if (database.endsWith('.db'))\n            database = database.slice(0, -3);\n        try {\n            const res = await this.sqlite.isDatabaseEncrypted({ database: database });\n            return Promise.resolve(res);\n        }\n        catch (err) {\n            return Promise.reject(err);\n        }\n    }\n    async isInConfigEncryption() {\n        try {\n            const res = await this.sqlite.isInConfigEncryption();\n            return Promise.resolve(res);\n        }\n        catch (err) {\n            return Promise.reject(err);\n        }\n    }\n    async isInConfigBiometricAuth() {\n        try {\n            const res = await this.sqlite.isInConfigBiometricAuth();\n            return Promise.resolve(res);\n        }\n        catch (err) {\n            return Promise.reject(err);\n        }\n    }\n    async isDatabase(database) {\n        if (database.endsWith('.db'))\n            database = database.slice(0, -3);\n        try {\n            const res = await this.sqlite.isDatabase({ database: database });\n            return Promise.resolve(res);\n        }\n        catch (err) {\n            return Promise.reject(err);\n        }\n    }\n    async getDatabaseList() {\n        try {\n            const res = await this.sqlite.getDatabaseList();\n            const values = res.values;\n            values.sort();\n            const ret = { values: values };\n            return Promise.resolve(ret);\n        }\n        catch (err) {\n            return Promise.reject(err);\n        }\n    }\n    async getMigratableDbList(folderPath) {\n        const path = folderPath ? folderPath : 'default';\n        try {\n            const res = await this.sqlite.getMigratableDbList({\n                folderPath: path,\n            });\n            return Promise.resolve(res);\n        }\n        catch (err) {\n            return Promise.reject(err);\n        }\n    }\n    async addSQLiteSuffix(folderPath, dbNameList) {\n        const path = folderPath ? folderPath : 'default';\n        const dbList = dbNameList ? dbNameList : [];\n        try {\n            const res = await this.sqlite.addSQLiteSuffix({\n                folderPath: path,\n                dbNameList: dbList,\n            });\n            return Promise.resolve(res);\n        }\n        catch (err) {\n            return Promise.reject(err);\n        }\n    }\n    async deleteOldDatabases(folderPath, dbNameList) {\n        const path = folderPath ? folderPath : 'default';\n        const dbList = dbNameList ? dbNameList : [];\n        try {\n            const res = await this.sqlite.deleteOldDatabases({\n                folderPath: path,\n                dbNameList: dbList,\n            });\n            return Promise.resolve(res);\n        }\n        catch (err) {\n            return Promise.reject(err);\n        }\n    }\n    async moveDatabasesAndAddSuffix(folderPath, dbNameList) {\n        const path = folderPath ? folderPath : 'default';\n        const dbList = dbNameList ? dbNameList : [];\n        return this.sqlite.moveDatabasesAndAddSuffix({\n            folderPath: path,\n            dbNameList: dbList,\n        });\n    }\n}\n/**\n * SQLiteDBConnection Class\n */\nexport class SQLiteDBConnection {\n    constructor(dbName, readonly, sqlite) {\n        this.dbName = dbName;\n        this.readonly = readonly;\n        this.sqlite = sqlite;\n    }\n    getConnectionDBName() {\n        return this.dbName;\n    }\n    getConnectionReadOnly() {\n        return this.readonly;\n    }\n    async open() {\n        try {\n            await this.sqlite.open({\n                database: this.dbName,\n                readonly: this.readonly,\n            });\n            return Promise.resolve();\n        }\n        catch (err) {\n            return Promise.reject(err);\n        }\n    }\n    async close() {\n        try {\n            await this.sqlite.close({\n                database: this.dbName,\n                readonly: this.readonly,\n            });\n            return Promise.resolve();\n        }\n        catch (err) {\n            return Promise.reject(err);\n        }\n    }\n    async beginTransaction() {\n        try {\n            const changes = await this.sqlite.beginTransaction({\n                database: this.dbName,\n            });\n            return Promise.resolve(changes);\n        }\n        catch (err) {\n            return Promise.reject(err);\n        }\n    }\n    async commitTransaction() {\n        try {\n            const changes = await this.sqlite.commitTransaction({\n                database: this.dbName,\n            });\n            return Promise.resolve(changes);\n        }\n        catch (err) {\n            return Promise.reject(err);\n        }\n    }\n    async rollbackTransaction() {\n        try {\n            const changes = await this.sqlite.rollbackTransaction({\n                database: this.dbName,\n            });\n            return Promise.resolve(changes);\n        }\n        catch (err) {\n            return Promise.reject(err);\n        }\n    }\n    async isTransactionActive() {\n        try {\n            const result = await this.sqlite.isTransactionActive({\n                database: this.dbName,\n            });\n            return Promise.resolve(result);\n        }\n        catch (err) {\n            return Promise.reject(err);\n        }\n    }\n    async loadExtension(path) {\n        try {\n            await this.sqlite.loadExtension({\n                database: this.dbName,\n                path: path,\n                readonly: this.readonly,\n            });\n            return Promise.resolve();\n        }\n        catch (err) {\n            return Promise.reject(err);\n        }\n    }\n    async enableLoadExtension(toggle) {\n        try {\n            await this.sqlite.enableLoadExtension({\n                database: this.dbName,\n                toggle: toggle,\n                readonly: this.readonly,\n            });\n            return Promise.resolve();\n        }\n        catch (err) {\n            return Promise.reject(err);\n        }\n    }\n    async getUrl() {\n        try {\n            const res = await this.sqlite.getUrl({\n                database: this.dbName,\n                readonly: this.readonly,\n            });\n            return Promise.resolve(res);\n        }\n        catch (err) {\n            return Promise.reject(err);\n        }\n    }\n    async getVersion() {\n        try {\n            const version = await this.sqlite.getVersion({\n                database: this.dbName,\n                readonly: this.readonly,\n            });\n            return Promise.resolve(version);\n        }\n        catch (err) {\n            return Promise.reject(err);\n        }\n    }\n    async getTableList() {\n        try {\n            const res = await this.sqlite.getTableList({\n                database: this.dbName,\n                readonly: this.readonly,\n            });\n            return Promise.resolve(res);\n        }\n        catch (err) {\n            return Promise.reject(err);\n        }\n    }\n    async execute(statements, transaction = true, isSQL92 = true) {\n        try {\n            if (!this.readonly) {\n                const res = await this.sqlite.execute({\n                    database: this.dbName,\n                    statements: statements,\n                    transaction: transaction,\n                    readonly: false,\n                    isSQL92: isSQL92,\n                });\n                return Promise.resolve(res);\n            }\n            else {\n                return Promise.reject('not allowed in read-only mode');\n            }\n        }\n        catch (err) {\n            return Promise.reject(err);\n        }\n    }\n    async query(statement, values, isSQL92 = true) {\n        let res;\n        try {\n            if (values && values.length > 0) {\n                res = await this.sqlite.query({\n                    database: this.dbName,\n                    statement: statement,\n                    values: values,\n                    readonly: this.readonly,\n                    isSQL92: true,\n                });\n            }\n            else {\n                res = await this.sqlite.query({\n                    database: this.dbName,\n                    statement: statement,\n                    values: [],\n                    readonly: this.readonly,\n                    isSQL92: isSQL92,\n                });\n            }\n            // reorder rows for ios\n            res = await this.reorderRows(res);\n            return Promise.resolve(res);\n        }\n        catch (err) {\n            return Promise.reject(err);\n        }\n    }\n    async run(statement, values, transaction = true, returnMode = 'no', isSQL92 = true) {\n        let res;\n        try {\n            if (!this.readonly) {\n                if (values && values.length > 0) {\n                    res = await this.sqlite.run({\n                        database: this.dbName,\n                        statement: statement,\n                        values: values,\n                        transaction: transaction,\n                        readonly: false,\n                        returnMode: returnMode,\n                        isSQL92: true,\n                    });\n                }\n                else {\n                    res = await this.sqlite.run({\n                        database: this.dbName,\n                        statement: statement,\n                        values: [],\n                        transaction: transaction,\n                        readonly: false,\n                        returnMode: returnMode,\n                        isSQL92: isSQL92,\n                    });\n                }\n                // reorder rows for ios\n                res.changes = await this.reorderRows(res.changes);\n                return Promise.resolve(res);\n            }\n            else {\n                return Promise.reject('not allowed in read-only mode');\n            }\n        }\n        catch (err) {\n            return Promise.reject(err);\n        }\n    }\n    async executeSet(set, transaction = true, returnMode = 'no', isSQL92 = true) {\n        let res;\n        try {\n            if (!this.readonly) {\n                res = await this.sqlite.executeSet({\n                    database: this.dbName,\n                    set: set,\n                    transaction: transaction,\n                    readonly: false,\n                    returnMode: returnMode,\n                    isSQL92: isSQL92,\n                });\n                //      }\n                // reorder rows for ios\n                res.changes = await this.reorderRows(res.changes);\n                return Promise.resolve(res);\n            }\n            else {\n                return Promise.reject('not allowed in read-only mode');\n            }\n        }\n        catch (err) {\n            return Promise.reject(err);\n        }\n    }\n    async isExists() {\n        try {\n            const res = await this.sqlite.isDBExists({\n                database: this.dbName,\n                readonly: this.readonly,\n            });\n            return Promise.resolve(res);\n        }\n        catch (err) {\n            return Promise.reject(err);\n        }\n    }\n    async isTable(table) {\n        try {\n            const res = await this.sqlite.isTableExists({\n                database: this.dbName,\n                table: table,\n                readonly: this.readonly,\n            });\n            return Promise.resolve(res);\n        }\n        catch (err) {\n            return Promise.reject(err);\n        }\n    }\n    async isDBOpen() {\n        try {\n            const res = await this.sqlite.isDBOpen({\n                database: this.dbName,\n                readonly: this.readonly,\n            });\n            return Promise.resolve(res);\n        }\n        catch (err) {\n            return Promise.reject(err);\n        }\n    }\n    async delete() {\n        try {\n            if (!this.readonly) {\n                await this.sqlite.deleteDatabase({\n                    database: this.dbName,\n                    readonly: false,\n                });\n                return Promise.resolve();\n            }\n            else {\n                return Promise.reject('not allowed in read-only mode');\n            }\n        }\n        catch (err) {\n            return Promise.reject(err);\n        }\n    }\n    async createSyncTable() {\n        try {\n            if (!this.readonly) {\n                const res = await this.sqlite.createSyncTable({\n                    database: this.dbName,\n                    readonly: false,\n                });\n                return Promise.resolve(res);\n            }\n            else {\n                return Promise.reject('not allowed in read-only mode');\n            }\n        }\n        catch (err) {\n            return Promise.reject(err);\n        }\n    }\n    async setSyncDate(syncdate) {\n        try {\n            if (!this.readonly) {\n                await this.sqlite.setSyncDate({\n                    database: this.dbName,\n                    syncdate: syncdate,\n                    readonly: false,\n                });\n                return Promise.resolve();\n            }\n            else {\n                return Promise.reject('not allowed in read-only mode');\n            }\n        }\n        catch (err) {\n            return Promise.reject(err);\n        }\n    }\n    async getSyncDate() {\n        try {\n            const res = await this.sqlite.getSyncDate({\n                database: this.dbName,\n                readonly: this.readonly,\n            });\n            let retDate = '';\n            if (res.syncDate > 0)\n                retDate = new Date(res.syncDate * 1000).toISOString();\n            return Promise.resolve(retDate);\n        }\n        catch (err) {\n            return Promise.reject(err);\n        }\n    }\n    async exportToJson(mode, encrypted = false) {\n        try {\n            const res = await this.sqlite.exportToJson({\n                database: this.dbName,\n                jsonexportmode: mode,\n                readonly: this.readonly,\n                encrypted: encrypted,\n            });\n            return Promise.resolve(res);\n        }\n        catch (err) {\n            return Promise.reject(err);\n        }\n    }\n    async deleteExportedRows() {\n        try {\n            if (!this.readonly) {\n                await this.sqlite.deleteExportedRows({\n                    database: this.dbName,\n                    readonly: false,\n                });\n                return Promise.resolve();\n            }\n            else {\n                return Promise.reject('not allowed in read-only mode');\n            }\n        }\n        catch (err) {\n            return Promise.reject(err);\n        }\n    }\n    async executeTransaction(txn, isSQL92 = true) {\n        let changes = 0;\n        let isActive = false;\n        if (!this.readonly) {\n            await this.sqlite.beginTransaction({\n                database: this.dbName,\n            });\n            isActive = await this.sqlite.isTransactionActive({\n                database: this.dbName,\n            });\n            if (!isActive) {\n                return Promise.reject('After Begin Transaction, no transaction active');\n            }\n            try {\n                for (const task of txn) {\n                    if (typeof task !== 'object' || !('statement' in task)) {\n                        throw new Error('Error a task.statement must be provided');\n                    }\n                    if ('values' in task && task.values && task.values.length > 0) {\n                        const retMode = task.statement.toUpperCase().includes('RETURNING') ? 'all' : 'no';\n                        const ret = await this.sqlite.run({\n                            database: this.dbName,\n                            statement: task.statement,\n                            values: task.values,\n                            transaction: false,\n                            readonly: false,\n                            returnMode: retMode,\n                            isSQL92: isSQL92,\n                        });\n                        if (ret.changes.changes < 0) {\n                            throw new Error('Error in transaction method run ');\n                        }\n                        changes += ret.changes.changes;\n                    }\n                    else {\n                        const ret = await this.sqlite.execute({\n                            database: this.dbName,\n                            statements: task.statement,\n                            transaction: false,\n                            readonly: false,\n                        });\n                        if (ret.changes.changes < 0) {\n                            throw new Error('Error in transaction method execute ');\n                        }\n                        changes += ret.changes.changes;\n                    }\n                }\n                // commit\n                const retC = await this.sqlite.commitTransaction({\n                    database: this.dbName,\n                });\n                changes += retC.changes.changes;\n                const retChanges = { changes: { changes: changes } };\n                return Promise.resolve(retChanges);\n            }\n            catch (err) {\n                // rollback\n                const msg = err.message ? err.message : err;\n                await this.sqlite.rollbackTransaction({\n                    database: this.dbName,\n                });\n                return Promise.reject(msg);\n            }\n        }\n        else {\n            return Promise.reject('not allowed in read-only mode');\n        }\n    }\n    async reorderRows(res) {\n        const retRes = res;\n        if (res?.values && typeof res.values[0] === 'object') {\n            if (Object.keys(res.values[0]).includes('ios_columns')) {\n                const columnList = res.values[0]['ios_columns'];\n                const iosRes = [];\n                for (let i = 1; i < res.values.length; i++) {\n                    const rowJson = res.values[i];\n                    const resRowJson = {};\n                    for (const item of columnList) {\n                        resRowJson[item] = rowJson[item];\n                    }\n                    iosRes.push(resRowJson);\n                }\n                retRes['values'] = iosRes;\n            }\n        }\n        return Promise.resolve(retRes);\n    }\n}\n", "import { registerPlugin } from '@capacitor/core';\nconst CapacitorSQLite = registerPlugin('CapacitorSQLite', {\n    web: () => import('./web').then((m) => new m.CapacitorSQLiteWeb()),\n    electron: () => window.CapacitorCustomPlatform.plugins.CapacitorSQLite,\n});\nexport { CapacitorSQLite };\nexport * from './definitions';\n"], "mappings": ";;;;;;;;AAIO,IAAM,mBAAN,MAAuB;AAAA,EAC1B,YAAY,QAAQ;AAChB,SAAK,SAAS;AACd,SAAK,kBAAkB,oBAAI,IAAI;AAAA,EACnC;AAAA,EACM,eAAe;AAAA;AACjB,UAAI;AACA,cAAM,KAAK,OAAO,aAAa;AAC/B,eAAO,QAAQ,QAAQ;AAAA,MAC3B,SACO,KAAK;AACR,eAAO,QAAQ,OAAO,GAAG;AAAA,MAC7B;AAAA,IACJ;AAAA;AAAA,EACM,YAAY,UAAU;AAAA;AACxB,UAAI;AACA,cAAM,KAAK,OAAO,YAAY,EAAE,SAAS,CAAC;AAC1C,eAAO,QAAQ,QAAQ;AAAA,MAC3B,SACO,KAAK;AACR,eAAO,QAAQ,OAAO,GAAG;AAAA,MAC7B;AAAA,IACJ;AAAA;AAAA,EACM,gBAAgB,UAAU;AAAA;AAC5B,UAAI;AACA,cAAM,KAAK,OAAO,gBAAgB,EAAE,SAAS,CAAC;AAC9C,eAAO,QAAQ,QAAQ;AAAA,MAC3B,SACO,KAAK;AACR,eAAO,QAAQ,OAAO,GAAG;AAAA,MAC7B;AAAA,IACJ;AAAA;AAAA,EACM,wBAAwB,WAAW;AAAA;AACrC,YAAM,aAAa,aAAa,OAAO,YAAY;AACnD,UAAI;AACA,cAAM,KAAK,OAAO,wBAAwB,EAAE,WAAW,WAAW,CAAC;AACnE,eAAO,QAAQ,QAAQ;AAAA,MAC3B,SACO,KAAK;AACR,eAAO,QAAQ,OAAO,GAAG;AAAA,MAC7B;AAAA,IACJ;AAAA;AAAA,EACM,KAAK,OAAO;AAAA;AACd,UAAI;AACA,cAAM,MAAM,MAAM,KAAK,OAAO,KAAK,EAAE,MAAM,CAAC;AAC5C,eAAO,QAAQ,QAAQ,GAAG;AAAA,MAC9B,SACO,KAAK;AACR,eAAO,QAAQ,OAAO,GAAG;AAAA,MAC7B;AAAA,IACJ;AAAA;AAAA,EACM,iBAAiB;AAAA;AACnB,UAAI;AACA,cAAM,MAAM,MAAM,KAAK,OAAO,eAAe;AAC7C,eAAO,QAAQ,QAAQ,GAAG;AAAA,MAC9B,SACO,KAAK;AACR,eAAO,QAAQ,OAAO,GAAG;AAAA,MAC7B;AAAA,IACJ;AAAA;AAAA,EACM,oBAAoB,YAAY;AAAA;AAClC,UAAI;AACA,cAAM,KAAK,OAAO,oBAAoB,EAAE,WAAuB,CAAC;AAChE,eAAO,QAAQ,QAAQ;AAAA,MAC3B,SACO,KAAK;AACR,eAAO,QAAQ,OAAO,GAAG;AAAA,MAC7B;AAAA,IACJ;AAAA;AAAA,EACM,uBAAuB,YAAY,eAAe;AAAA;AACpD,UAAI;AACA,cAAM,KAAK,OAAO,uBAAuB;AAAA,UACrC;AAAA,UACA;AAAA,QACJ,CAAC;AACD,eAAO,QAAQ,QAAQ;AAAA,MAC3B,SACO,KAAK;AACR,eAAO,QAAQ,OAAO,GAAG;AAAA,MAC7B;AAAA,IACJ;AAAA;AAAA,EACM,wBAAwB;AAAA;AAC1B,UAAI;AACA,cAAM,KAAK,OAAO,sBAAsB;AACxC,eAAO,QAAQ,QAAQ;AAAA,MAC3B,SACO,KAAK;AACR,eAAO,QAAQ,OAAO,GAAG;AAAA,MAC7B;AAAA,IACJ;AAAA;AAAA,EACM,sBAAsB,YAAY;AAAA;AACpC,UAAI;AACA,cAAM,MAAM,MAAM,KAAK,OAAO,sBAAsB;AAAA,UAChD;AAAA,QACJ,CAAC;AACD,eAAO,QAAQ,QAAQ,GAAG;AAAA,MAC9B,SACO,KAAK;AACR,eAAO,QAAQ,OAAO,GAAG;AAAA,MAC7B;AAAA,IACJ;AAAA;AAAA,EACM,oBAAoB,UAAU,SAAS;AAAA;AACzC,UAAI;AACA,YAAI,SAAS,SAAS,KAAK;AACvB,qBAAW,SAAS,MAAM,GAAG,EAAE;AACnC,cAAM,KAAK,OAAO,oBAAoB;AAAA,UAClC;AAAA,UACA;AAAA,QACJ,CAAC;AACD,eAAO,QAAQ,QAAQ;AAAA,MAC3B,SACO,KAAK;AACR,eAAO,QAAQ,OAAO,GAAG;AAAA,MAC7B;AAAA,IACJ;AAAA;AAAA,EACM,iBAAiB,UAAU,WAAW,MAAM,SAAS,UAAU;AAAA;AACjE,UAAI;AACA,YAAI,SAAS,SAAS,KAAK;AACvB,qBAAW,SAAS,MAAM,GAAG,EAAE;AACnC,cAAM,KAAK,OAAO,iBAAiB;AAAA,UAC/B;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,QACJ,CAAC;AACD,cAAM,OAAO,IAAI,mBAAmB,UAAU,UAAU,KAAK,MAAM;AACnE,cAAM,WAAW,WAAW,MAAM,QAAQ,KAAK,MAAM,QAAQ;AAC7D,aAAK,gBAAgB,IAAI,UAAU,IAAI;AAOvC,eAAO,QAAQ,QAAQ,IAAI;AAAA,MAC/B,SACO,KAAK;AACR,eAAO,QAAQ,OAAO,GAAG;AAAA,MAC7B;AAAA,IACJ;AAAA;AAAA,EACM,gBAAgB,UAAU,UAAU;AAAA;AACtC,UAAI;AACA,YAAI,SAAS,SAAS,KAAK;AACvB,qBAAW,SAAS,MAAM,GAAG,EAAE;AACnC,cAAM,KAAK,OAAO,gBAAgB,EAAE,UAAU,SAAS,CAAC;AACxD,cAAM,WAAW,WAAW,MAAM,QAAQ,KAAK,MAAM,QAAQ;AAC7D,aAAK,gBAAgB,OAAO,QAAQ;AAMpC,eAAO,QAAQ,QAAQ;AAAA,MAC3B,SACO,KAAK;AACR,eAAO,QAAQ,OAAO,GAAG;AAAA,MAC7B;AAAA,IACJ;AAAA;AAAA,EACM,aAAa,UAAU,UAAU;AAAA;AACnC,YAAM,MAAM,CAAC;AACb,UAAI,SAAS,SAAS,KAAK;AACvB,mBAAW,SAAS,MAAM,GAAG,EAAE;AACnC,YAAM,WAAW,WAAW,MAAM,QAAQ,KAAK,MAAM,QAAQ;AAC7D,UAAI,SAAS,KAAK,gBAAgB,IAAI,QAAQ;AAC9C,aAAO,QAAQ,QAAQ,GAAG;AAAA,IAC9B;AAAA;AAAA,EACM,mBAAmB,UAAU,UAAU;AAAA;AACzC,UAAI,SAAS,SAAS,KAAK;AACvB,mBAAW,SAAS,MAAM,GAAG,EAAE;AACnC,YAAM,WAAW,WAAW,MAAM,QAAQ,KAAK,MAAM,QAAQ;AAC7D,UAAI,KAAK,gBAAgB,IAAI,QAAQ,GAAG;AACpC,cAAM,OAAO,KAAK,gBAAgB,IAAI,QAAQ;AAC9C,YAAI,OAAO,QAAQ;AACf,iBAAO,QAAQ,QAAQ,IAAI;AAAA,aAC1B;AACD,iBAAO,QAAQ,OAAO,cAAc,QAAQ,eAAe;AAAA,QAC/D;AAAA,MACJ,OACK;AACD,eAAO,QAAQ,OAAO,cAAc,QAAQ,iBAAiB;AAAA,MACjE;AAAA,IACJ;AAAA;AAAA,EACM,kBAAkB,MAAM,UAAU;AAAA;AACpC,UAAI;AACA,cAAM,eAAe,MAAM,KAAK,OAAO,kBAAkB;AAAA,UACrD;AAAA,UACA;AAAA,QACJ,CAAC;AACD,eAAO,QAAQ,QAAQ,YAAY;AAAA,MACvC,SACO,KAAK;AACR,eAAO,QAAQ,OAAO,GAAG;AAAA,MAC7B;AAAA,IACJ;AAAA;AAAA,EACM,mBAAmB,cAAc,SAAS;AAAA;AAC5C,UAAI;AACA,cAAM,KAAK,OAAO,mBAAmB;AAAA,UACjC;AAAA,UACA;AAAA,QACJ,CAAC;AACD,cAAM,OAAO,IAAI,mBAAmB,cAAc,MAAM,KAAK,MAAM;AACnE,cAAM,WAAW,MAAM,YAAY;AACnC,aAAK,gBAAgB,IAAI,UAAU,IAAI;AACvC,eAAO,QAAQ,QAAQ,IAAI;AAAA,MAC/B,SACO,KAAK;AACR,eAAO,QAAQ,OAAO,GAAG;AAAA,MAC7B;AAAA,IACJ;AAAA;AAAA,EACM,kBAAkB,cAAc;AAAA;AAClC,UAAI;AACA,cAAM,KAAK,OAAO,kBAAkB,EAAE,aAAa,CAAC;AACpD,cAAM,WAAW,MAAM,YAAY;AACnC,aAAK,gBAAgB,OAAO,QAAQ;AACpC,eAAO,QAAQ,QAAQ;AAAA,MAC3B,SACO,KAAK;AACR,eAAO,QAAQ,OAAO,GAAG;AAAA,MAC7B;AAAA,IACJ;AAAA;AAAA,EACM,eAAe,cAAc;AAAA;AAC/B,YAAM,MAAM,CAAC;AACb,YAAM,WAAW,MAAM,YAAY;AACnC,UAAI,SAAS,KAAK,gBAAgB,IAAI,QAAQ;AAC9C,aAAO,QAAQ,QAAQ,GAAG;AAAA,IAC9B;AAAA;AAAA,EACM,qBAAqB,cAAc;AAAA;AACrC,UAAI,KAAK,gBAAgB,IAAI,YAAY,GAAG;AACxC,cAAM,WAAW,MAAM,YAAY;AACnC,cAAM,OAAO,KAAK,gBAAgB,IAAI,QAAQ;AAC9C,YAAI,OAAO,QAAQ;AACf,iBAAO,QAAQ,QAAQ,IAAI;AAAA,aAC1B;AACD,iBAAO,QAAQ,OAAO,cAAc,YAAY,eAAe;AAAA,QACnE;AAAA,MACJ,OACK;AACD,eAAO,QAAQ,OAAO,cAAc,YAAY,iBAAiB;AAAA,MACrE;AAAA,IACJ;AAAA;AAAA,EACM,aAAa,cAAc;AAAA;AAC7B,UAAI;AACA,cAAM,MAAM,MAAM,KAAK,OAAO,aAAa,EAAE,aAAa,CAAC;AAC3D,eAAO,QAAQ,QAAQ,GAAG;AAAA,MAC9B,SACO,KAAK;AACR,eAAO,QAAQ,OAAO,GAAG;AAAA,MAC7B;AAAA,IACJ;AAAA;AAAA,EACM,yBAAyB;AAAA;AAC3B,aAAO,KAAK;AAAA,IAChB;AAAA;AAAA,EACM,sBAAsB;AAAA;AACxB,YAAM,UAAU,oBAAI,IAAI;AACxB,UAAI;AAMA,mBAAW,OAAO,KAAK,gBAAgB,KAAK,GAAG;AAC3C,gBAAM,WAAW,IAAI,UAAU,CAAC;AAChC,gBAAM,WAAW,IAAI,UAAU,GAAG,CAAC,MAAM,QAAQ,OAAO;AACxD,gBAAM,KAAK,OAAO,gBAAgB,EAAE,UAAU,SAAS,CAAC;AACxD,kBAAQ,IAAI,KAAK,IAAI;AAAA,QACzB;AACA,mBAAW,OAAO,QAAQ,KAAK,GAAG;AAC9B,eAAK,gBAAgB,OAAO,GAAG;AAAA,QACnC;AACA,eAAO,QAAQ,QAAQ;AAAA,MAC3B,SACO,KAAK;AACR,eAAO,QAAQ,OAAO,GAAG;AAAA,MAC7B;AAAA,IACJ;AAAA;AAAA,EACM,8BAA8B;AAAA;AAChC,UAAI;AACA,cAAM,OAAO,CAAC,GAAG,KAAK,gBAAgB,KAAK,CAAC;AAC5C,cAAM,YAAY,CAAC;AACnB,cAAM,UAAU,CAAC;AACjB,mBAAW,OAAO,MAAM;AACpB,oBAAU,KAAK,IAAI,UAAU,GAAG,CAAC,CAAC;AAClC,kBAAQ,KAAK,IAAI,UAAU,CAAC,CAAC;AAAA,QACjC;AACA,cAAM,MAAM,MAAM,KAAK,OAAO,4BAA4B;AAAA,UACtD;AAAA,UACA;AAAA,QACJ,CAAC;AACD,YAAI,CAAC,IAAI;AACL,eAAK,kBAAkB,oBAAI,IAAI;AACnC,eAAO,QAAQ,QAAQ,GAAG;AAAA,MAC9B,SACO,KAAK;AACR,aAAK,kBAAkB,oBAAI,IAAI;AAC/B,eAAO,QAAQ,OAAO,GAAG;AAAA,MAC7B;AAAA,IACJ;AAAA;AAAA,EACM,eAAe,YAAY;AAAA;AAC7B,UAAI;AACA,cAAM,MAAM,MAAM,KAAK,OAAO,eAAe,EAAE,WAAuB,CAAC;AACvE,eAAO,QAAQ,QAAQ,GAAG;AAAA,MAC9B,SACO,KAAK;AACR,eAAO,QAAQ,OAAO,GAAG;AAAA,MAC7B;AAAA,IACJ;AAAA;AAAA,EACM,YAAY,YAAY;AAAA;AAC1B,UAAI;AACA,cAAM,MAAM,MAAM,KAAK,OAAO,YAAY,EAAE,WAAuB,CAAC;AACpE,eAAO,QAAQ,QAAQ,GAAG;AAAA,MAC9B,SACO,KAAK;AACR,eAAO,QAAQ,OAAO,GAAG;AAAA,MAC7B;AAAA,IACJ;AAAA;AAAA,EACM,eAAe,WAAW;AAAA;AAC5B,YAAM,aAAa,aAAa,OAAO,YAAY;AACnD,UAAI;AACA,cAAM,KAAK,OAAO,eAAe,EAAE,WAAW,WAAW,CAAC;AAC1D,eAAO,QAAQ,QAAQ;AAAA,MAC3B,SACO,KAAK;AACR,eAAO,QAAQ,OAAO,GAAG;AAAA,MAC7B;AAAA,IACJ;AAAA;AAAA,EACM,mBAAmB,KAAK,WAAW;AAAA;AACrC,YAAM,aAAa,aAAa,OAAO,YAAY;AACnD,UAAI;AACA,cAAM,KAAK,OAAO,mBAAmB,EAAE,KAAK,WAAW,WAAW,CAAC;AACnE,eAAO,QAAQ,QAAQ;AAAA,MAC3B,SACO,KAAK;AACR,eAAO,QAAQ,OAAO,GAAG;AAAA,MAC7B;AAAA,IACJ;AAAA;AAAA,EACM,oBAAoB,UAAU;AAAA;AAChC,UAAI,SAAS,SAAS,KAAK;AACvB,mBAAW,SAAS,MAAM,GAAG,EAAE;AACnC,UAAI;AACA,cAAM,MAAM,MAAM,KAAK,OAAO,oBAAoB,EAAE,SAAmB,CAAC;AACxE,eAAO,QAAQ,QAAQ,GAAG;AAAA,MAC9B,SACO,KAAK;AACR,eAAO,QAAQ,OAAO,GAAG;AAAA,MAC7B;AAAA,IACJ;AAAA;AAAA,EACM,uBAAuB;AAAA;AACzB,UAAI;AACA,cAAM,MAAM,MAAM,KAAK,OAAO,qBAAqB;AACnD,eAAO,QAAQ,QAAQ,GAAG;AAAA,MAC9B,SACO,KAAK;AACR,eAAO,QAAQ,OAAO,GAAG;AAAA,MAC7B;AAAA,IACJ;AAAA;AAAA,EACM,0BAA0B;AAAA;AAC5B,UAAI;AACA,cAAM,MAAM,MAAM,KAAK,OAAO,wBAAwB;AACtD,eAAO,QAAQ,QAAQ,GAAG;AAAA,MAC9B,SACO,KAAK;AACR,eAAO,QAAQ,OAAO,GAAG;AAAA,MAC7B;AAAA,IACJ;AAAA;AAAA,EACM,WAAW,UAAU;AAAA;AACvB,UAAI,SAAS,SAAS,KAAK;AACvB,mBAAW,SAAS,MAAM,GAAG,EAAE;AACnC,UAAI;AACA,cAAM,MAAM,MAAM,KAAK,OAAO,WAAW,EAAE,SAAmB,CAAC;AAC/D,eAAO,QAAQ,QAAQ,GAAG;AAAA,MAC9B,SACO,KAAK;AACR,eAAO,QAAQ,OAAO,GAAG;AAAA,MAC7B;AAAA,IACJ;AAAA;AAAA,EACM,kBAAkB;AAAA;AACpB,UAAI;AACA,cAAM,MAAM,MAAM,KAAK,OAAO,gBAAgB;AAC9C,cAAM,SAAS,IAAI;AACnB,eAAO,KAAK;AACZ,cAAM,MAAM,EAAE,OAAe;AAC7B,eAAO,QAAQ,QAAQ,GAAG;AAAA,MAC9B,SACO,KAAK;AACR,eAAO,QAAQ,OAAO,GAAG;AAAA,MAC7B;AAAA,IACJ;AAAA;AAAA,EACM,oBAAoB,YAAY;AAAA;AAClC,YAAM,OAAO,aAAa,aAAa;AACvC,UAAI;AACA,cAAM,MAAM,MAAM,KAAK,OAAO,oBAAoB;AAAA,UAC9C,YAAY;AAAA,QAChB,CAAC;AACD,eAAO,QAAQ,QAAQ,GAAG;AAAA,MAC9B,SACO,KAAK;AACR,eAAO,QAAQ,OAAO,GAAG;AAAA,MAC7B;AAAA,IACJ;AAAA;AAAA,EACM,gBAAgB,YAAY,YAAY;AAAA;AAC1C,YAAM,OAAO,aAAa,aAAa;AACvC,YAAM,SAAS,aAAa,aAAa,CAAC;AAC1C,UAAI;AACA,cAAM,MAAM,MAAM,KAAK,OAAO,gBAAgB;AAAA,UAC1C,YAAY;AAAA,UACZ,YAAY;AAAA,QAChB,CAAC;AACD,eAAO,QAAQ,QAAQ,GAAG;AAAA,MAC9B,SACO,KAAK;AACR,eAAO,QAAQ,OAAO,GAAG;AAAA,MAC7B;AAAA,IACJ;AAAA;AAAA,EACM,mBAAmB,YAAY,YAAY;AAAA;AAC7C,YAAM,OAAO,aAAa,aAAa;AACvC,YAAM,SAAS,aAAa,aAAa,CAAC;AAC1C,UAAI;AACA,cAAM,MAAM,MAAM,KAAK,OAAO,mBAAmB;AAAA,UAC7C,YAAY;AAAA,UACZ,YAAY;AAAA,QAChB,CAAC;AACD,eAAO,QAAQ,QAAQ,GAAG;AAAA,MAC9B,SACO,KAAK;AACR,eAAO,QAAQ,OAAO,GAAG;AAAA,MAC7B;AAAA,IACJ;AAAA;AAAA,EACM,0BAA0B,YAAY,YAAY;AAAA;AACpD,YAAM,OAAO,aAAa,aAAa;AACvC,YAAM,SAAS,aAAa,aAAa,CAAC;AAC1C,aAAO,KAAK,OAAO,0BAA0B;AAAA,QACzC,YAAY;AAAA,QACZ,YAAY;AAAA,MAChB,CAAC;AAAA,IACL;AAAA;AACJ;AAIO,IAAM,qBAAN,MAAyB;AAAA,EAC5B,YAAY,QAAQ,UAAU,QAAQ;AAClC,SAAK,SAAS;AACd,SAAK,WAAW;AAChB,SAAK,SAAS;AAAA,EAClB;AAAA,EACA,sBAAsB;AAClB,WAAO,KAAK;AAAA,EAChB;AAAA,EACA,wBAAwB;AACpB,WAAO,KAAK;AAAA,EAChB;AAAA,EACM,OAAO;AAAA;AACT,UAAI;AACA,cAAM,KAAK,OAAO,KAAK;AAAA,UACnB,UAAU,KAAK;AAAA,UACf,UAAU,KAAK;AAAA,QACnB,CAAC;AACD,eAAO,QAAQ,QAAQ;AAAA,MAC3B,SACO,KAAK;AACR,eAAO,QAAQ,OAAO,GAAG;AAAA,MAC7B;AAAA,IACJ;AAAA;AAAA,EACM,QAAQ;AAAA;AACV,UAAI;AACA,cAAM,KAAK,OAAO,MAAM;AAAA,UACpB,UAAU,KAAK;AAAA,UACf,UAAU,KAAK;AAAA,QACnB,CAAC;AACD,eAAO,QAAQ,QAAQ;AAAA,MAC3B,SACO,KAAK;AACR,eAAO,QAAQ,OAAO,GAAG;AAAA,MAC7B;AAAA,IACJ;AAAA;AAAA,EACM,mBAAmB;AAAA;AACrB,UAAI;AACA,cAAM,UAAU,MAAM,KAAK,OAAO,iBAAiB;AAAA,UAC/C,UAAU,KAAK;AAAA,QACnB,CAAC;AACD,eAAO,QAAQ,QAAQ,OAAO;AAAA,MAClC,SACO,KAAK;AACR,eAAO,QAAQ,OAAO,GAAG;AAAA,MAC7B;AAAA,IACJ;AAAA;AAAA,EACM,oBAAoB;AAAA;AACtB,UAAI;AACA,cAAM,UAAU,MAAM,KAAK,OAAO,kBAAkB;AAAA,UAChD,UAAU,KAAK;AAAA,QACnB,CAAC;AACD,eAAO,QAAQ,QAAQ,OAAO;AAAA,MAClC,SACO,KAAK;AACR,eAAO,QAAQ,OAAO,GAAG;AAAA,MAC7B;AAAA,IACJ;AAAA;AAAA,EACM,sBAAsB;AAAA;AACxB,UAAI;AACA,cAAM,UAAU,MAAM,KAAK,OAAO,oBAAoB;AAAA,UAClD,UAAU,KAAK;AAAA,QACnB,CAAC;AACD,eAAO,QAAQ,QAAQ,OAAO;AAAA,MAClC,SACO,KAAK;AACR,eAAO,QAAQ,OAAO,GAAG;AAAA,MAC7B;AAAA,IACJ;AAAA;AAAA,EACM,sBAAsB;AAAA;AACxB,UAAI;AACA,cAAM,SAAS,MAAM,KAAK,OAAO,oBAAoB;AAAA,UACjD,UAAU,KAAK;AAAA,QACnB,CAAC;AACD,eAAO,QAAQ,QAAQ,MAAM;AAAA,MACjC,SACO,KAAK;AACR,eAAO,QAAQ,OAAO,GAAG;AAAA,MAC7B;AAAA,IACJ;AAAA;AAAA,EACM,cAAc,MAAM;AAAA;AACtB,UAAI;AACA,cAAM,KAAK,OAAO,cAAc;AAAA,UAC5B,UAAU,KAAK;AAAA,UACf;AAAA,UACA,UAAU,KAAK;AAAA,QACnB,CAAC;AACD,eAAO,QAAQ,QAAQ;AAAA,MAC3B,SACO,KAAK;AACR,eAAO,QAAQ,OAAO,GAAG;AAAA,MAC7B;AAAA,IACJ;AAAA;AAAA,EACM,oBAAoB,QAAQ;AAAA;AAC9B,UAAI;AACA,cAAM,KAAK,OAAO,oBAAoB;AAAA,UAClC,UAAU,KAAK;AAAA,UACf;AAAA,UACA,UAAU,KAAK;AAAA,QACnB,CAAC;AACD,eAAO,QAAQ,QAAQ;AAAA,MAC3B,SACO,KAAK;AACR,eAAO,QAAQ,OAAO,GAAG;AAAA,MAC7B;AAAA,IACJ;AAAA;AAAA,EACM,SAAS;AAAA;AACX,UAAI;AACA,cAAM,MAAM,MAAM,KAAK,OAAO,OAAO;AAAA,UACjC,UAAU,KAAK;AAAA,UACf,UAAU,KAAK;AAAA,QACnB,CAAC;AACD,eAAO,QAAQ,QAAQ,GAAG;AAAA,MAC9B,SACO,KAAK;AACR,eAAO,QAAQ,OAAO,GAAG;AAAA,MAC7B;AAAA,IACJ;AAAA;AAAA,EACM,aAAa;AAAA;AACf,UAAI;AACA,cAAM,UAAU,MAAM,KAAK,OAAO,WAAW;AAAA,UACzC,UAAU,KAAK;AAAA,UACf,UAAU,KAAK;AAAA,QACnB,CAAC;AACD,eAAO,QAAQ,QAAQ,OAAO;AAAA,MAClC,SACO,KAAK;AACR,eAAO,QAAQ,OAAO,GAAG;AAAA,MAC7B;AAAA,IACJ;AAAA;AAAA,EACM,eAAe;AAAA;AACjB,UAAI;AACA,cAAM,MAAM,MAAM,KAAK,OAAO,aAAa;AAAA,UACvC,UAAU,KAAK;AAAA,UACf,UAAU,KAAK;AAAA,QACnB,CAAC;AACD,eAAO,QAAQ,QAAQ,GAAG;AAAA,MAC9B,SACO,KAAK;AACR,eAAO,QAAQ,OAAO,GAAG;AAAA,MAC7B;AAAA,IACJ;AAAA;AAAA,EACM,QAAQ,YAAY,cAAc,MAAM,UAAU,MAAM;AAAA;AAC1D,UAAI;AACA,YAAI,CAAC,KAAK,UAAU;AAChB,gBAAM,MAAM,MAAM,KAAK,OAAO,QAAQ;AAAA,YAClC,UAAU,KAAK;AAAA,YACf;AAAA,YACA;AAAA,YACA,UAAU;AAAA,YACV;AAAA,UACJ,CAAC;AACD,iBAAO,QAAQ,QAAQ,GAAG;AAAA,QAC9B,OACK;AACD,iBAAO,QAAQ,OAAO,+BAA+B;AAAA,QACzD;AAAA,MACJ,SACO,KAAK;AACR,eAAO,QAAQ,OAAO,GAAG;AAAA,MAC7B;AAAA,IACJ;AAAA;AAAA,EACM,MAAM,WAAW,QAAQ,UAAU,MAAM;AAAA;AAC3C,UAAI;AACJ,UAAI;AACA,YAAI,UAAU,OAAO,SAAS,GAAG;AAC7B,gBAAM,MAAM,KAAK,OAAO,MAAM;AAAA,YAC1B,UAAU,KAAK;AAAA,YACf;AAAA,YACA;AAAA,YACA,UAAU,KAAK;AAAA,YACf,SAAS;AAAA,UACb,CAAC;AAAA,QACL,OACK;AACD,gBAAM,MAAM,KAAK,OAAO,MAAM;AAAA,YAC1B,UAAU,KAAK;AAAA,YACf;AAAA,YACA,QAAQ,CAAC;AAAA,YACT,UAAU,KAAK;AAAA,YACf;AAAA,UACJ,CAAC;AAAA,QACL;AAEA,cAAM,MAAM,KAAK,YAAY,GAAG;AAChC,eAAO,QAAQ,QAAQ,GAAG;AAAA,MAC9B,SACO,KAAK;AACR,eAAO,QAAQ,OAAO,GAAG;AAAA,MAC7B;AAAA,IACJ;AAAA;AAAA,EACM,IAAI,WAAW,QAAQ,cAAc,MAAM,aAAa,MAAM,UAAU,MAAM;AAAA;AAChF,UAAI;AACJ,UAAI;AACA,YAAI,CAAC,KAAK,UAAU;AAChB,cAAI,UAAU,OAAO,SAAS,GAAG;AAC7B,kBAAM,MAAM,KAAK,OAAO,IAAI;AAAA,cACxB,UAAU,KAAK;AAAA,cACf;AAAA,cACA;AAAA,cACA;AAAA,cACA,UAAU;AAAA,cACV;AAAA,cACA,SAAS;AAAA,YACb,CAAC;AAAA,UACL,OACK;AACD,kBAAM,MAAM,KAAK,OAAO,IAAI;AAAA,cACxB,UAAU,KAAK;AAAA,cACf;AAAA,cACA,QAAQ,CAAC;AAAA,cACT;AAAA,cACA,UAAU;AAAA,cACV;AAAA,cACA;AAAA,YACJ,CAAC;AAAA,UACL;AAEA,cAAI,UAAU,MAAM,KAAK,YAAY,IAAI,OAAO;AAChD,iBAAO,QAAQ,QAAQ,GAAG;AAAA,QAC9B,OACK;AACD,iBAAO,QAAQ,OAAO,+BAA+B;AAAA,QACzD;AAAA,MACJ,SACO,KAAK;AACR,eAAO,QAAQ,OAAO,GAAG;AAAA,MAC7B;AAAA,IACJ;AAAA;AAAA,EACM,WAAW,KAAK,cAAc,MAAM,aAAa,MAAM,UAAU,MAAM;AAAA;AACzE,UAAI;AACJ,UAAI;AACA,YAAI,CAAC,KAAK,UAAU;AAChB,gBAAM,MAAM,KAAK,OAAO,WAAW;AAAA,YAC/B,UAAU,KAAK;AAAA,YACf;AAAA,YACA;AAAA,YACA,UAAU;AAAA,YACV;AAAA,YACA;AAAA,UACJ,CAAC;AAGD,cAAI,UAAU,MAAM,KAAK,YAAY,IAAI,OAAO;AAChD,iBAAO,QAAQ,QAAQ,GAAG;AAAA,QAC9B,OACK;AACD,iBAAO,QAAQ,OAAO,+BAA+B;AAAA,QACzD;AAAA,MACJ,SACO,KAAK;AACR,eAAO,QAAQ,OAAO,GAAG;AAAA,MAC7B;AAAA,IACJ;AAAA;AAAA,EACM,WAAW;AAAA;AACb,UAAI;AACA,cAAM,MAAM,MAAM,KAAK,OAAO,WAAW;AAAA,UACrC,UAAU,KAAK;AAAA,UACf,UAAU,KAAK;AAAA,QACnB,CAAC;AACD,eAAO,QAAQ,QAAQ,GAAG;AAAA,MAC9B,SACO,KAAK;AACR,eAAO,QAAQ,OAAO,GAAG;AAAA,MAC7B;AAAA,IACJ;AAAA;AAAA,EACM,QAAQ,OAAO;AAAA;AACjB,UAAI;AACA,cAAM,MAAM,MAAM,KAAK,OAAO,cAAc;AAAA,UACxC,UAAU,KAAK;AAAA,UACf;AAAA,UACA,UAAU,KAAK;AAAA,QACnB,CAAC;AACD,eAAO,QAAQ,QAAQ,GAAG;AAAA,MAC9B,SACO,KAAK;AACR,eAAO,QAAQ,OAAO,GAAG;AAAA,MAC7B;AAAA,IACJ;AAAA;AAAA,EACM,WAAW;AAAA;AACb,UAAI;AACA,cAAM,MAAM,MAAM,KAAK,OAAO,SAAS;AAAA,UACnC,UAAU,KAAK;AAAA,UACf,UAAU,KAAK;AAAA,QACnB,CAAC;AACD,eAAO,QAAQ,QAAQ,GAAG;AAAA,MAC9B,SACO,KAAK;AACR,eAAO,QAAQ,OAAO,GAAG;AAAA,MAC7B;AAAA,IACJ;AAAA;AAAA,EACM,SAAS;AAAA;AACX,UAAI;AACA,YAAI,CAAC,KAAK,UAAU;AAChB,gBAAM,KAAK,OAAO,eAAe;AAAA,YAC7B,UAAU,KAAK;AAAA,YACf,UAAU;AAAA,UACd,CAAC;AACD,iBAAO,QAAQ,QAAQ;AAAA,QAC3B,OACK;AACD,iBAAO,QAAQ,OAAO,+BAA+B;AAAA,QACzD;AAAA,MACJ,SACO,KAAK;AACR,eAAO,QAAQ,OAAO,GAAG;AAAA,MAC7B;AAAA,IACJ;AAAA;AAAA,EACM,kBAAkB;AAAA;AACpB,UAAI;AACA,YAAI,CAAC,KAAK,UAAU;AAChB,gBAAM,MAAM,MAAM,KAAK,OAAO,gBAAgB;AAAA,YAC1C,UAAU,KAAK;AAAA,YACf,UAAU;AAAA,UACd,CAAC;AACD,iBAAO,QAAQ,QAAQ,GAAG;AAAA,QAC9B,OACK;AACD,iBAAO,QAAQ,OAAO,+BAA+B;AAAA,QACzD;AAAA,MACJ,SACO,KAAK;AACR,eAAO,QAAQ,OAAO,GAAG;AAAA,MAC7B;AAAA,IACJ;AAAA;AAAA,EACM,YAAY,UAAU;AAAA;AACxB,UAAI;AACA,YAAI,CAAC,KAAK,UAAU;AAChB,gBAAM,KAAK,OAAO,YAAY;AAAA,YAC1B,UAAU,KAAK;AAAA,YACf;AAAA,YACA,UAAU;AAAA,UACd,CAAC;AACD,iBAAO,QAAQ,QAAQ;AAAA,QAC3B,OACK;AACD,iBAAO,QAAQ,OAAO,+BAA+B;AAAA,QACzD;AAAA,MACJ,SACO,KAAK;AACR,eAAO,QAAQ,OAAO,GAAG;AAAA,MAC7B;AAAA,IACJ;AAAA;AAAA,EACM,cAAc;AAAA;AAChB,UAAI;AACA,cAAM,MAAM,MAAM,KAAK,OAAO,YAAY;AAAA,UACtC,UAAU,KAAK;AAAA,UACf,UAAU,KAAK;AAAA,QACnB,CAAC;AACD,YAAI,UAAU;AACd,YAAI,IAAI,WAAW;AACf,oBAAU,IAAI,KAAK,IAAI,WAAW,GAAI,EAAE,YAAY;AACxD,eAAO,QAAQ,QAAQ,OAAO;AAAA,MAClC,SACO,KAAK;AACR,eAAO,QAAQ,OAAO,GAAG;AAAA,MAC7B;AAAA,IACJ;AAAA;AAAA,EACM,aAAa,MAAM,YAAY,OAAO;AAAA;AACxC,UAAI;AACA,cAAM,MAAM,MAAM,KAAK,OAAO,aAAa;AAAA,UACvC,UAAU,KAAK;AAAA,UACf,gBAAgB;AAAA,UAChB,UAAU,KAAK;AAAA,UACf;AAAA,QACJ,CAAC;AACD,eAAO,QAAQ,QAAQ,GAAG;AAAA,MAC9B,SACO,KAAK;AACR,eAAO,QAAQ,OAAO,GAAG;AAAA,MAC7B;AAAA,IACJ;AAAA;AAAA,EACM,qBAAqB;AAAA;AACvB,UAAI;AACA,YAAI,CAAC,KAAK,UAAU;AAChB,gBAAM,KAAK,OAAO,mBAAmB;AAAA,YACjC,UAAU,KAAK;AAAA,YACf,UAAU;AAAA,UACd,CAAC;AACD,iBAAO,QAAQ,QAAQ;AAAA,QAC3B,OACK;AACD,iBAAO,QAAQ,OAAO,+BAA+B;AAAA,QACzD;AAAA,MACJ,SACO,KAAK;AACR,eAAO,QAAQ,OAAO,GAAG;AAAA,MAC7B;AAAA,IACJ;AAAA;AAAA,EACM,mBAAmB,KAAK,UAAU,MAAM;AAAA;AAC1C,UAAI,UAAU;AACd,UAAI,WAAW;AACf,UAAI,CAAC,KAAK,UAAU;AAChB,cAAM,KAAK,OAAO,iBAAiB;AAAA,UAC/B,UAAU,KAAK;AAAA,QACnB,CAAC;AACD,mBAAW,MAAM,KAAK,OAAO,oBAAoB;AAAA,UAC7C,UAAU,KAAK;AAAA,QACnB,CAAC;AACD,YAAI,CAAC,UAAU;AACX,iBAAO,QAAQ,OAAO,gDAAgD;AAAA,QAC1E;AACA,YAAI;AACA,qBAAW,QAAQ,KAAK;AACpB,gBAAI,OAAO,SAAS,YAAY,EAAE,eAAe,OAAO;AACpD,oBAAM,IAAI,MAAM,yCAAyC;AAAA,YAC7D;AACA,gBAAI,YAAY,QAAQ,KAAK,UAAU,KAAK,OAAO,SAAS,GAAG;AAC3D,oBAAM,UAAU,KAAK,UAAU,YAAY,EAAE,SAAS,WAAW,IAAI,QAAQ;AAC7E,oBAAM,MAAM,MAAM,KAAK,OAAO,IAAI;AAAA,gBAC9B,UAAU,KAAK;AAAA,gBACf,WAAW,KAAK;AAAA,gBAChB,QAAQ,KAAK;AAAA,gBACb,aAAa;AAAA,gBACb,UAAU;AAAA,gBACV,YAAY;AAAA,gBACZ;AAAA,cACJ,CAAC;AACD,kBAAI,IAAI,QAAQ,UAAU,GAAG;AACzB,sBAAM,IAAI,MAAM,kCAAkC;AAAA,cACtD;AACA,yBAAW,IAAI,QAAQ;AAAA,YAC3B,OACK;AACD,oBAAM,MAAM,MAAM,KAAK,OAAO,QAAQ;AAAA,gBAClC,UAAU,KAAK;AAAA,gBACf,YAAY,KAAK;AAAA,gBACjB,aAAa;AAAA,gBACb,UAAU;AAAA,cACd,CAAC;AACD,kBAAI,IAAI,QAAQ,UAAU,GAAG;AACzB,sBAAM,IAAI,MAAM,sCAAsC;AAAA,cAC1D;AACA,yBAAW,IAAI,QAAQ;AAAA,YAC3B;AAAA,UACJ;AAEA,gBAAM,OAAO,MAAM,KAAK,OAAO,kBAAkB;AAAA,YAC7C,UAAU,KAAK;AAAA,UACnB,CAAC;AACD,qBAAW,KAAK,QAAQ;AACxB,gBAAM,aAAa,EAAE,SAAS,EAAE,QAAiB,EAAE;AACnD,iBAAO,QAAQ,QAAQ,UAAU;AAAA,QACrC,SACO,KAAK;AAER,gBAAM,MAAM,IAAI,UAAU,IAAI,UAAU;AACxC,gBAAM,KAAK,OAAO,oBAAoB;AAAA,YAClC,UAAU,KAAK;AAAA,UACnB,CAAC;AACD,iBAAO,QAAQ,OAAO,GAAG;AAAA,QAC7B;AAAA,MACJ,OACK;AACD,eAAO,QAAQ,OAAO,+BAA+B;AAAA,MACzD;AAAA,IACJ;AAAA;AAAA,EACM,YAAY,KAAK;AAAA;AACnB,YAAM,SAAS;AACf,UAAI,KAAK,UAAU,OAAO,IAAI,OAAO,CAAC,MAAM,UAAU;AAClD,YAAI,OAAO,KAAK,IAAI,OAAO,CAAC,CAAC,EAAE,SAAS,aAAa,GAAG;AACpD,gBAAM,aAAa,IAAI,OAAO,CAAC,EAAE,aAAa;AAC9C,gBAAM,SAAS,CAAC;AAChB,mBAAS,IAAI,GAAG,IAAI,IAAI,OAAO,QAAQ,KAAK;AACxC,kBAAM,UAAU,IAAI,OAAO,CAAC;AAC5B,kBAAM,aAAa,CAAC;AACpB,uBAAW,QAAQ,YAAY;AAC3B,yBAAW,IAAI,IAAI,QAAQ,IAAI;AAAA,YACnC;AACA,mBAAO,KAAK,UAAU;AAAA,UAC1B;AACA,iBAAO,QAAQ,IAAI;AAAA,QACvB;AAAA,MACJ;AACA,aAAO,QAAQ,QAAQ,MAAM;AAAA,IACjC;AAAA;AACJ;;;ACv5BA,IAAM,kBAAkB,eAAe,mBAAmB;AAAA,EACtD,KAAK,MAAM,OAAO,mBAAO,EAAE,KAAK,CAAC,MAAM,IAAI,EAAE,mBAAmB,CAAC;AAAA,EACjE,UAAU,MAAM,OAAO,wBAAwB,QAAQ;AAC3D,CAAC;", "names": []}