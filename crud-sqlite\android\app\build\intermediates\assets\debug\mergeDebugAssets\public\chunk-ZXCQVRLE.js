import{A as k,B as x,C as T,G as P,H as F,I as W,J as L,K as V,L as D,M as N,N as O,P as B,Q as z,a as u,b as _,c as r,d as w,e as I,f as U,g as y,h as i,i as n,k as q,l as p,m as h,n as s,o as g,p as E,q as f,r as C,s as S,t as b,u as v}from"./chunk-JE2PG4HF.js";import{e as c}from"./chunk-JHI3MBHO.js";function A(d,o){if(d&1){let m=q();i(0,"ion-item")(1,"ion-label")(2,"h2"),s(3),n(),i(4,"p"),s(5),n()(),i(6,"ion-button",5),p("click",function(){let a=u(m).$implicit,e=h();return _(e.editUser(a))}),s(7,"Edit"),n(),i(8,"ion-button",6),p("click",function(){let a=u(m).$implicit,e=h();return _(e.deleteUser(a.id))}),s(9,"Delete"),n()()}if(d&2){let m=o.$implicit;r(3),g(m.name),r(2),g(m.email)}}var X=(()=>{let o=class o{constructor(t){this.sqlite=t,this.users=[],this.name="",this.email="",this.editMode=!1}ngOnInit(){return c(this,null,function*(){yield this.loadUsers()})}loadUsers(){return c(this,null,function*(){this.users=yield this.sqlite.getAllUsers()})}saveUser(){return c(this,null,function*(){this.editMode?(yield this.sqlite.updateUser(this.editId,this.name,this.email),this.editMode=!1):yield this.sqlite.addUser(this.name,this.email),this.name="",this.email="",yield this.loadUsers()})}editUser(t){this.editMode=!0,this.editId=t.id,this.name=t.name,this.email=t.email}deleteUser(t){return c(this,null,function*(){yield this.sqlite.deleteUser(t),yield this.loadUsers()})}};o.\u0275fac=function(a){return new(a||o)(w(z))},o.\u0275cmp=I({type:o,selectors:[["app-sqlite-crudop"]],decls:13,vars:4,consts:[[1,"ion-padding"],["placeholder","Name",3,"ngModelChange","ngModel"],["placeholder","Email",3,"ngModelChange","ngModel"],["expand","full",3,"click"],[4,"ngFor","ngForOf"],["size","small",3,"click"],["size","small","color","danger",3,"click"]],template:function(a,e){a&1&&(i(0,"ion-header")(1,"ion-toolbar")(2,"ion-title"),s(3,"SQLite CRUD"),n()()(),i(4,"ion-content",0)(5,"ion-item")(6,"ion-input",1),S("ngModelChange",function(l){return C(e.name,l)||(e.name=l),l}),n()(),i(7,"ion-item")(8,"ion-input",2),S("ngModelChange",function(l){return C(e.email,l)||(e.email=l),l}),n()(),i(9,"ion-button",3),p("click",function(){return e.saveUser()}),s(10),n(),i(11,"ion-list"),U(12,A,10,2,"ion-item",4),n()()),a&2&&(r(6),f("ngModel",e.name),r(2),f("ngModel",e.email),r(2),E(" ",e.editMode?"Update":"Add"," User "),r(2),y("ngForOf",e.users))},dependencies:[v,b,T,k,x,F,W,N,O,L,B,P,D,V],encapsulation:2});let d=o;return d})();export{X as a};
