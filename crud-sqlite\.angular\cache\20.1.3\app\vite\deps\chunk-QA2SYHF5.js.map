{"version": 3, "sources": ["../../../../../../node_modules/@ionic/core/dist/esm/gesture-controller-BTEOs1at.js"], "sourcesContent": ["/*!\n * (C) Ionic http://ionicframework.com - MIT License\n */\nclass GestureController {\n    constructor() {\n        this.gestureId = 0;\n        this.requestedStart = new Map();\n        this.disabledGestures = new Map();\n        this.disabledScroll = new Set();\n    }\n    /**\n     * Creates a gesture delegate based on the GestureConfig passed\n     */\n    createGesture(config) {\n        var _a;\n        return new GestureDelegate(this, this.newID(), config.name, (_a = config.priority) !== null && _a !== void 0 ? _a : 0, !!config.disableScroll);\n    }\n    /**\n     * Creates a blocker that will block any other gesture events from firing. Set in the ion-gesture component.\n     */\n    createBlocker(opts = {}) {\n        return new BlockerDelegate(this, this.newID(), opts.disable, !!opts.disableScroll);\n    }\n    start(gestureName, id, priority) {\n        if (!this.canStart(gestureName)) {\n            this.requestedStart.delete(id);\n            return false;\n        }\n        this.requestedStart.set(id, priority);\n        return true;\n    }\n    capture(gestureName, id, priority) {\n        if (!this.start(gestureName, id, priority)) {\n            return false;\n        }\n        const requestedStart = this.requestedStart;\n        let maxPriority = -1e4;\n        requestedStart.forEach((value) => {\n            maxPriority = Math.max(maxPriority, value);\n        });\n        if (maxPriority === priority) {\n            this.capturedId = id;\n            requestedStart.clear();\n            const event = new CustomEvent('ionGestureCaptured', { detail: { gestureName } });\n            document.dispatchEvent(event);\n            return true;\n        }\n        requestedStart.delete(id);\n        return false;\n    }\n    release(id) {\n        this.requestedStart.delete(id);\n        if (this.capturedId === id) {\n            this.capturedId = undefined;\n        }\n    }\n    disableGesture(gestureName, id) {\n        let set = this.disabledGestures.get(gestureName);\n        if (set === undefined) {\n            set = new Set();\n            this.disabledGestures.set(gestureName, set);\n        }\n        set.add(id);\n    }\n    enableGesture(gestureName, id) {\n        const set = this.disabledGestures.get(gestureName);\n        if (set !== undefined) {\n            set.delete(id);\n        }\n    }\n    disableScroll(id) {\n        this.disabledScroll.add(id);\n        if (this.disabledScroll.size === 1) {\n            document.body.classList.add(BACKDROP_NO_SCROLL);\n        }\n    }\n    enableScroll(id) {\n        this.disabledScroll.delete(id);\n        if (this.disabledScroll.size === 0) {\n            document.body.classList.remove(BACKDROP_NO_SCROLL);\n        }\n    }\n    canStart(gestureName) {\n        if (this.capturedId !== undefined) {\n            // a gesture already captured\n            return false;\n        }\n        if (this.isDisabled(gestureName)) {\n            return false;\n        }\n        return true;\n    }\n    isCaptured() {\n        return this.capturedId !== undefined;\n    }\n    isScrollDisabled() {\n        return this.disabledScroll.size > 0;\n    }\n    isDisabled(gestureName) {\n        const disabled = this.disabledGestures.get(gestureName);\n        if (disabled && disabled.size > 0) {\n            return true;\n        }\n        return false;\n    }\n    newID() {\n        this.gestureId++;\n        return this.gestureId;\n    }\n}\nclass GestureDelegate {\n    constructor(ctrl, id, name, priority, disableScroll) {\n        this.id = id;\n        this.name = name;\n        this.disableScroll = disableScroll;\n        this.priority = priority * 1000000 + id;\n        this.ctrl = ctrl;\n    }\n    canStart() {\n        if (!this.ctrl) {\n            return false;\n        }\n        return this.ctrl.canStart(this.name);\n    }\n    start() {\n        if (!this.ctrl) {\n            return false;\n        }\n        return this.ctrl.start(this.name, this.id, this.priority);\n    }\n    capture() {\n        if (!this.ctrl) {\n            return false;\n        }\n        const captured = this.ctrl.capture(this.name, this.id, this.priority);\n        if (captured && this.disableScroll) {\n            this.ctrl.disableScroll(this.id);\n        }\n        return captured;\n    }\n    release() {\n        if (this.ctrl) {\n            this.ctrl.release(this.id);\n            if (this.disableScroll) {\n                this.ctrl.enableScroll(this.id);\n            }\n        }\n    }\n    destroy() {\n        this.release();\n        this.ctrl = undefined;\n    }\n}\nclass BlockerDelegate {\n    constructor(ctrl, id, disable, disableScroll) {\n        this.id = id;\n        this.disable = disable;\n        this.disableScroll = disableScroll;\n        this.ctrl = ctrl;\n    }\n    block() {\n        if (!this.ctrl) {\n            return;\n        }\n        if (this.disable) {\n            for (const gesture of this.disable) {\n                this.ctrl.disableGesture(gesture, this.id);\n            }\n        }\n        if (this.disableScroll) {\n            this.ctrl.disableScroll(this.id);\n        }\n    }\n    unblock() {\n        if (!this.ctrl) {\n            return;\n        }\n        if (this.disable) {\n            for (const gesture of this.disable) {\n                this.ctrl.enableGesture(gesture, this.id);\n            }\n        }\n        if (this.disableScroll) {\n            this.ctrl.enableScroll(this.id);\n        }\n    }\n    destroy() {\n        this.unblock();\n        this.ctrl = undefined;\n    }\n}\nconst BACKDROP_NO_SCROLL = 'backdrop-no-scroll';\nconst GESTURE_CONTROLLER = new GestureController();\n\nexport { BACKDROP_NO_SCROLL as B, GESTURE_CONTROLLER as G };\n"], "mappings": ";AAGA,IAAM,oBAAN,MAAwB;AAAA,EACpB,cAAc;AACV,SAAK,YAAY;AACjB,SAAK,iBAAiB,oBAAI,IAAI;AAC9B,SAAK,mBAAmB,oBAAI,IAAI;AAChC,SAAK,iBAAiB,oBAAI,IAAI;AAAA,EAClC;AAAA;AAAA;AAAA;AAAA,EAIA,cAAc,QAAQ;AAClB,QAAI;AACJ,WAAO,IAAI,gBAAgB,MAAM,KAAK,MAAM,GAAG,OAAO,OAAO,KAAK,OAAO,cAAc,QAAQ,OAAO,SAAS,KAAK,GAAG,CAAC,CAAC,OAAO,aAAa;AAAA,EACjJ;AAAA;AAAA;AAAA;AAAA,EAIA,cAAc,OAAO,CAAC,GAAG;AACrB,WAAO,IAAI,gBAAgB,MAAM,KAAK,MAAM,GAAG,KAAK,SAAS,CAAC,CAAC,KAAK,aAAa;AAAA,EACrF;AAAA,EACA,MAAM,aAAa,IAAI,UAAU;AAC7B,QAAI,CAAC,KAAK,SAAS,WAAW,GAAG;AAC7B,WAAK,eAAe,OAAO,EAAE;AAC7B,aAAO;AAAA,IACX;AACA,SAAK,eAAe,IAAI,IAAI,QAAQ;AACpC,WAAO;AAAA,EACX;AAAA,EACA,QAAQ,aAAa,IAAI,UAAU;AAC/B,QAAI,CAAC,KAAK,MAAM,aAAa,IAAI,QAAQ,GAAG;AACxC,aAAO;AAAA,IACX;AACA,UAAM,iBAAiB,KAAK;AAC5B,QAAI,cAAc;AAClB,mBAAe,QAAQ,CAAC,UAAU;AAC9B,oBAAc,KAAK,IAAI,aAAa,KAAK;AAAA,IAC7C,CAAC;AACD,QAAI,gBAAgB,UAAU;AAC1B,WAAK,aAAa;AAClB,qBAAe,MAAM;AACrB,YAAM,QAAQ,IAAI,YAAY,sBAAsB,EAAE,QAAQ,EAAE,YAAY,EAAE,CAAC;AAC/E,eAAS,cAAc,KAAK;AAC5B,aAAO;AAAA,IACX;AACA,mBAAe,OAAO,EAAE;AACxB,WAAO;AAAA,EACX;AAAA,EACA,QAAQ,IAAI;AACR,SAAK,eAAe,OAAO,EAAE;AAC7B,QAAI,KAAK,eAAe,IAAI;AACxB,WAAK,aAAa;AAAA,IACtB;AAAA,EACJ;AAAA,EACA,eAAe,aAAa,IAAI;AAC5B,QAAI,MAAM,KAAK,iBAAiB,IAAI,WAAW;AAC/C,QAAI,QAAQ,QAAW;AACnB,YAAM,oBAAI,IAAI;AACd,WAAK,iBAAiB,IAAI,aAAa,GAAG;AAAA,IAC9C;AACA,QAAI,IAAI,EAAE;AAAA,EACd;AAAA,EACA,cAAc,aAAa,IAAI;AAC3B,UAAM,MAAM,KAAK,iBAAiB,IAAI,WAAW;AACjD,QAAI,QAAQ,QAAW;AACnB,UAAI,OAAO,EAAE;AAAA,IACjB;AAAA,EACJ;AAAA,EACA,cAAc,IAAI;AACd,SAAK,eAAe,IAAI,EAAE;AAC1B,QAAI,KAAK,eAAe,SAAS,GAAG;AAChC,eAAS,KAAK,UAAU,IAAI,kBAAkB;AAAA,IAClD;AAAA,EACJ;AAAA,EACA,aAAa,IAAI;AACb,SAAK,eAAe,OAAO,EAAE;AAC7B,QAAI,KAAK,eAAe,SAAS,GAAG;AAChC,eAAS,KAAK,UAAU,OAAO,kBAAkB;AAAA,IACrD;AAAA,EACJ;AAAA,EACA,SAAS,aAAa;AAClB,QAAI,KAAK,eAAe,QAAW;AAE/B,aAAO;AAAA,IACX;AACA,QAAI,KAAK,WAAW,WAAW,GAAG;AAC9B,aAAO;AAAA,IACX;AACA,WAAO;AAAA,EACX;AAAA,EACA,aAAa;AACT,WAAO,KAAK,eAAe;AAAA,EAC/B;AAAA,EACA,mBAAmB;AACf,WAAO,KAAK,eAAe,OAAO;AAAA,EACtC;AAAA,EACA,WAAW,aAAa;AACpB,UAAM,WAAW,KAAK,iBAAiB,IAAI,WAAW;AACtD,QAAI,YAAY,SAAS,OAAO,GAAG;AAC/B,aAAO;AAAA,IACX;AACA,WAAO;AAAA,EACX;AAAA,EACA,QAAQ;AACJ,SAAK;AACL,WAAO,KAAK;AAAA,EAChB;AACJ;AACA,IAAM,kBAAN,MAAsB;AAAA,EAClB,YAAY,MAAM,IAAI,MAAM,UAAU,eAAe;AACjD,SAAK,KAAK;AACV,SAAK,OAAO;AACZ,SAAK,gBAAgB;AACrB,SAAK,WAAW,WAAW,MAAU;AACrC,SAAK,OAAO;AAAA,EAChB;AAAA,EACA,WAAW;AACP,QAAI,CAAC,KAAK,MAAM;AACZ,aAAO;AAAA,IACX;AACA,WAAO,KAAK,KAAK,SAAS,KAAK,IAAI;AAAA,EACvC;AAAA,EACA,QAAQ;AACJ,QAAI,CAAC,KAAK,MAAM;AACZ,aAAO;AAAA,IACX;AACA,WAAO,KAAK,KAAK,MAAM,KAAK,MAAM,KAAK,IAAI,KAAK,QAAQ;AAAA,EAC5D;AAAA,EACA,UAAU;AACN,QAAI,CAAC,KAAK,MAAM;AACZ,aAAO;AAAA,IACX;AACA,UAAM,WAAW,KAAK,KAAK,QAAQ,KAAK,MAAM,KAAK,IAAI,KAAK,QAAQ;AACpE,QAAI,YAAY,KAAK,eAAe;AAChC,WAAK,KAAK,cAAc,KAAK,EAAE;AAAA,IACnC;AACA,WAAO;AAAA,EACX;AAAA,EACA,UAAU;AACN,QAAI,KAAK,MAAM;AACX,WAAK,KAAK,QAAQ,KAAK,EAAE;AACzB,UAAI,KAAK,eAAe;AACpB,aAAK,KAAK,aAAa,KAAK,EAAE;AAAA,MAClC;AAAA,IACJ;AAAA,EACJ;AAAA,EACA,UAAU;AACN,SAAK,QAAQ;AACb,SAAK,OAAO;AAAA,EAChB;AACJ;AACA,IAAM,kBAAN,MAAsB;AAAA,EAClB,YAAY,MAAM,IAAI,SAAS,eAAe;AAC1C,SAAK,KAAK;AACV,SAAK,UAAU;AACf,SAAK,gBAAgB;AACrB,SAAK,OAAO;AAAA,EAChB;AAAA,EACA,QAAQ;AACJ,QAAI,CAAC,KAAK,MAAM;AACZ;AAAA,IACJ;AACA,QAAI,KAAK,SAAS;AACd,iBAAW,WAAW,KAAK,SAAS;AAChC,aAAK,KAAK,eAAe,SAAS,KAAK,EAAE;AAAA,MAC7C;AAAA,IACJ;AACA,QAAI,KAAK,eAAe;AACpB,WAAK,KAAK,cAAc,KAAK,EAAE;AAAA,IACnC;AAAA,EACJ;AAAA,EACA,UAAU;AACN,QAAI,CAAC,KAAK,MAAM;AACZ;AAAA,IACJ;AACA,QAAI,KAAK,SAAS;AACd,iBAAW,WAAW,KAAK,SAAS;AAChC,aAAK,KAAK,cAAc,SAAS,KAAK,EAAE;AAAA,MAC5C;AAAA,IACJ;AACA,QAAI,KAAK,eAAe;AACpB,WAAK,KAAK,aAAa,KAAK,EAAE;AAAA,IAClC;AAAA,EACJ;AAAA,EACA,UAAU;AACN,SAAK,QAAQ;AACb,SAAK,OAAO;AAAA,EAChB;AACJ;AACA,IAAM,qBAAqB;AAC3B,IAAM,qBAAqB,IAAI,kBAAkB;", "names": []}