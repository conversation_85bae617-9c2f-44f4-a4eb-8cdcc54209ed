{"version": 3, "sources": ["../../../../../../node_modules/@ionic/core/dist/esm/ion-modal.entry.js"], "sourcesContent": ["/*!\n * (C) Ionic http://ionicframework.com - MIT License\n */\nimport { r as registerInstance, d as createEvent, e as getIonMode, m as printIonWarning, w as writeTask, l as config, h, j as Host, k as getElement } from './index-B_U9CtaY.js';\nimport { f as findClosestIonContent, i as isIonContent, d as disableContentScrollY, r as resetContentScrollY, a as findIonContent, p as printIonContentErrorMsg } from './index-BlJTBdxG.js';\nimport { C as CoreDelegate, a as attachComponent, d as detachComponent } from './framework-delegate-DxcnWic_.js';\nimport { e as clamp, g as getElementRoot, r as raf, b as inheritAttributes, h as hasLazyBuild } from './helpers-1O4D2b7y.js';\nimport { c as createLockController } from './lock-controller-B-hirT0v.js';\nimport { g as getCapacitor } from './capacitor-CFERIeaU.js';\nimport { G as GESTURE, O as OVERLAY_GESTURE_PRIORITY, F as FOCUS_TRAP_DISABLE_CLASS, e as createTriggerController, B as BACKDROP, j as prepareOverlay, k as setOverlayId, f as present, g as dismiss, h as eventMethod } from './overlays-8Y2rA-ps.js';\nimport { g as getClassMap } from './theme-DiVJyqlX.js';\nimport { e as deepReady, w as waitForMount } from './index-DfBA5ztX.js';\nimport { KEYBOARD_DID_OPEN } from './keyboard-ywgs5efA.js';\nimport { c as createAnimation } from './animation-BWcUKtbn.js';\nimport { g as getTimeGivenProgression } from './cubic-bezier-hHmYLOfE.js';\nimport { createGesture } from './index-CfgBF1SE.js';\nimport { w as win } from './index-ZjP4CjeZ.js';\nimport './hardware-back-button-DcH0BbDp.js';\nimport './gesture-controller-BTEOs1at.js';\nimport './keyboard-CUw4ekVy.js';\n\nvar Style;\n(function (Style) {\n    Style[\"Dark\"] = \"DARK\";\n    Style[\"Light\"] = \"LIGHT\";\n    Style[\"Default\"] = \"DEFAULT\";\n})(Style || (Style = {}));\nconst StatusBar = {\n    getEngine() {\n        const capacitor = getCapacitor();\n        if (capacitor === null || capacitor === void 0 ? void 0 : capacitor.isPluginAvailable('StatusBar')) {\n            return capacitor.Plugins.StatusBar;\n        }\n        return undefined;\n    },\n    setStyle(options) {\n        const engine = this.getEngine();\n        if (!engine) {\n            return;\n        }\n        engine.setStyle(options);\n    },\n    getStyle: async function () {\n        const engine = this.getEngine();\n        if (!engine) {\n            return Style.Default;\n        }\n        const { style } = await engine.getInfo();\n        return style;\n    },\n};\n\n/**\n * Use y = mx + b to\n * figure out the backdrop value\n * at a particular x coordinate. This\n * is useful when the backdrop does\n * not begin to fade in until after\n * the 0 breakpoint.\n */\nconst getBackdropValueForSheet = (x, backdropBreakpoint) => {\n    /**\n     * We will use these points:\n     * (backdropBreakpoint, 0)\n     * (maxBreakpoint, 1)\n     * We know that at the beginning breakpoint,\n     * the backdrop will be hidden. We also\n     * know that at the maxBreakpoint, the backdrop\n     * must be fully visible. maxBreakpoint should\n     * always be 1 even if the maximum value\n     * of the breakpoints array is not 1 since\n     * the animation runs from a progress of 0\n     * to a progress of 1.\n     * m = (y2 - y1) / (x2 - x1)\n     *\n     * This is simplified from:\n     * m = (1 - 0) / (maxBreakpoint - backdropBreakpoint)\n     *\n     * If the backdropBreakpoint is 1, we return 0 as the\n     * backdrop is completely hidden.\n     *\n     */\n    if (backdropBreakpoint === 1) {\n        return 0;\n    }\n    const slope = 1 / (1 - backdropBreakpoint);\n    /**\n     * From here, compute b which is\n     * the backdrop opacity if the offset\n     * is 0. If the backdrop does not\n     * begin to fade in until after the\n     * 0 breakpoint, this b value will be\n     * negative. This is fine as we never pass\n     * b directly into the animation keyframes.\n     * b = y - mx\n     * Use a known point: (backdropBreakpoint, 0)\n     * This is simplified from:\n     * b = 0 - (backdropBreakpoint * slope)\n     */\n    const b = -(backdropBreakpoint * slope);\n    /**\n     * Finally, we can now determine the\n     * backdrop offset given an arbitrary\n     * gesture offset.\n     */\n    return x * slope + b;\n};\n/**\n * The tablet/desktop card modal activates\n * when the window width is >= 768.\n * At that point, the presenting element\n * is not transformed, so we do not need to\n * adjust the status bar color.\n *\n */\nconst setCardStatusBarDark = () => {\n    if (!win || win.innerWidth >= 768) {\n        return;\n    }\n    StatusBar.setStyle({ style: Style.Dark });\n};\nconst setCardStatusBarDefault = (defaultStyle = Style.Default) => {\n    if (!win || win.innerWidth >= 768) {\n        return;\n    }\n    StatusBar.setStyle({ style: defaultStyle });\n};\n\nconst handleCanDismiss = async (el, animation) => {\n    /**\n     * If canDismiss is not a function\n     * then we can return early. If canDismiss is `true`,\n     * then canDismissBlocksGesture is `false` as canDismiss\n     * will never interrupt the gesture. As a result,\n     * this code block is never reached. If canDismiss is `false`,\n     * then we never dismiss.\n     */\n    if (typeof el.canDismiss !== 'function') {\n        return;\n    }\n    /**\n     * Run the canDismiss callback.\n     * If the function returns `true`,\n     * then we can proceed with dismiss.\n     */\n    const shouldDismiss = await el.canDismiss(undefined, GESTURE);\n    if (!shouldDismiss) {\n        return;\n    }\n    /**\n     * If canDismiss resolved after the snap\n     * back animation finished, we can\n     * dismiss immediately.\n     *\n     * If canDismiss resolved before the snap\n     * back animation finished, we need to\n     * wait until the snap back animation is\n     * done before dismissing.\n     */\n    if (animation.isRunning()) {\n        animation.onFinish(() => {\n            el.dismiss(undefined, 'handler');\n        }, { oneTimeCallback: true });\n    }\n    else {\n        el.dismiss(undefined, 'handler');\n    }\n};\n/**\n * This function lets us simulate a realistic spring-like animation\n * when swiping down on the modal.\n * There are two forces that we need to use to compute the spring physics:\n *\n * 1. Stiffness, k: This is a measure of resistance applied a spring.\n * 2. Dampening, c: This value has the effect of reducing or preventing oscillation.\n *\n * Using these two values, we can calculate the Spring Force and the Dampening Force\n * to compute the total force applied to a spring.\n *\n * Spring Force: This force pulls a spring back into its equilibrium position.\n * Hooke's Law tells us that that spring force (FS) = kX.\n * k is the stiffness of a spring, and X is the displacement of the spring from its\n * equilibrium position. In this case, it is the amount by which the free end\n * of a spring was displaced (stretched/pushed) from its \"relaxed\" position.\n *\n * Dampening Force: This force slows down motion. Without it, a spring would oscillate forever.\n * The dampening force, FD, can be found via this formula: FD = -cv\n * where c the dampening value and v is velocity.\n *\n * Therefore, the resulting force that is exerted on the block is:\n * F = FS + FD = -kX - cv\n *\n * Newton's 2nd Law tells us that F = ma:\n * ma = -kX - cv.\n *\n * For Ionic's purposes, we can assume that m = 1:\n * a = -kX - cv\n *\n * Imagine a block attached to the end of a spring. At equilibrium\n * the block is at position x = 1.\n * Pressing on the block moves it to position x = 0;\n * So, to calculate the displacement, we need to take the\n * current position and subtract the previous position from it.\n * X = x - x0 = 0 - 1 = -1.\n *\n * For Ionic's purposes, we are only pushing on the spring modal\n * so we have a max position of 1.\n * As a result, we can expand displacement to this formula:\n * X = x - 1\n *\n * a = -k(x - 1) - cv\n *\n * We can represent the motion of something as a function of time: f(t) = x.\n * The derivative of position gives us the velocity: f'(t)\n * The derivative of the velocity gives us the acceleration: f''(t)\n *\n * We can substitute the formula above with these values:\n *\n * f\"(t) = -k * (f(t) - 1) - c * f'(t)\n *\n * This is called a differential equation.\n *\n * We know that at t = 0, we are at x = 0 because the modal does not move: f(0) = 0\n * This means our velocity is also zero: f'(0) = 0.\n *\n * We can cheat a bit and plug the formula into Wolfram Alpha.\n * However, we need to pick stiffness and dampening values:\n * k = 0.57\n * c = 15\n *\n * I picked these as they are fairly close to native iOS's spring effect\n * with the modal.\n *\n * What we plug in is this: f(0) = 0; f'(0) = 0; f''(t) = -0.57(f(t) - 1) - 15f'(t)\n *\n * The result is a formula that lets us calculate the acceleration\n * for a given time t.\n * Note: This is the approximate form of the solution. Wolfram Alpha will\n * give you a complex differential equation too.\n */\nconst calculateSpringStep = (t) => {\n    return 0.00255275 * 2.71828 ** (-14.9619 * t) - 1.00255 * 2.71828 ** (-0.0380968 * t) + 1;\n};\n\n// Defaults for the card swipe animation\nconst SwipeToCloseDefaults = {\n    MIN_PRESENTING_SCALE: 0.915,\n};\nconst createSwipeToCloseGesture = (el, animation, statusBarStyle, onDismiss) => {\n    /**\n     * The step value at which a card modal\n     * is eligible for dismissing via gesture.\n     */\n    const DISMISS_THRESHOLD = 0.5;\n    const height = el.offsetHeight;\n    let isOpen = false;\n    let canDismissBlocksGesture = false;\n    let contentEl = null;\n    let scrollEl = null;\n    const canDismissMaxStep = 0.2;\n    let initialScrollY = true;\n    let lastStep = 0;\n    const getScrollY = () => {\n        if (contentEl && isIonContent(contentEl)) {\n            return contentEl.scrollY;\n            /**\n             * Custom scroll containers are intended to be\n             * used with virtual scrolling, so we assume\n             * there is scrolling in this case.\n             */\n        }\n        else {\n            return true;\n        }\n    };\n    const canStart = (detail) => {\n        const target = detail.event.target;\n        if (target === null || !target.closest) {\n            return true;\n        }\n        /**\n         * If we are swiping on the content,\n         * swiping should only be possible if\n         * the content is scrolled all the way\n         * to the top so that we do not interfere\n         * with scrolling.\n         *\n         * We cannot assume that the `ion-content`\n         * target will remain consistent between\n         * swipes. For example, when using\n         * ion-nav within a card modal it is\n         * possible to swipe, push a view, and then\n         * swipe again. The target content will not\n         * be the same between swipes.\n         */\n        contentEl = findClosestIonContent(target);\n        if (contentEl) {\n            /**\n             * The card should never swipe to close\n             * on the content with a refresher.\n             * Note: We cannot solve this by making the\n             * swipeToClose gesture have a higher priority\n             * than the refresher gesture as the iOS native\n             * refresh gesture uses a scroll listener in\n             * addition to a gesture.\n             *\n             * Note: Do not use getScrollElement here\n             * because we need this to be a synchronous\n             * operation, and getScrollElement is\n             * asynchronous.\n             */\n            if (isIonContent(contentEl)) {\n                const root = getElementRoot(contentEl);\n                scrollEl = root.querySelector('.inner-scroll');\n            }\n            else {\n                scrollEl = contentEl;\n            }\n            const hasRefresherInContent = !!contentEl.querySelector('ion-refresher');\n            return !hasRefresherInContent && scrollEl.scrollTop === 0;\n        }\n        /**\n         * Card should be swipeable on all\n         * parts of the modal except for the footer.\n         */\n        const footer = target.closest('ion-footer');\n        if (footer === null) {\n            return true;\n        }\n        return false;\n    };\n    const onStart = (detail) => {\n        const { deltaY } = detail;\n        /**\n         * Get the initial scrollY value so\n         * that we can correctly reset the scrollY\n         * prop when the gesture ends.\n         */\n        initialScrollY = getScrollY();\n        /**\n         * If canDismiss is anything other than `true`\n         * then users should be able to swipe down\n         * until a threshold is hit. At that point,\n         * the card modal should not proceed any further.\n         * TODO (FW-937)\n         * Remove undefined check\n         */\n        canDismissBlocksGesture = el.canDismiss !== undefined && el.canDismiss !== true;\n        /**\n         * If we are pulling down, then\n         * it is possible we are pulling on the\n         * content. We do not want scrolling to\n         * happen at the same time as the gesture.\n         */\n        if (deltaY > 0 && contentEl) {\n            disableContentScrollY(contentEl);\n        }\n        animation.progressStart(true, isOpen ? 1 : 0);\n    };\n    const onMove = (detail) => {\n        const { deltaY } = detail;\n        /**\n         * If we are pulling down, then\n         * it is possible we are pulling on the\n         * content. We do not want scrolling to\n         * happen at the same time as the gesture.\n         */\n        if (deltaY > 0 && contentEl) {\n            disableContentScrollY(contentEl);\n        }\n        /**\n         * If we are swiping on the content\n         * then the swipe gesture should only\n         * happen if we are pulling down.\n         *\n         * However, if we pull up and\n         * then down such that the scroll position\n         * returns to 0, we should be able to swipe\n         * the card.\n         */\n        const step = detail.deltaY / height;\n        /**\n         * Check if user is swiping down and\n         * if we have a canDismiss value that\n         * should block the gesture from\n         * proceeding,\n         */\n        const isAttemptingDismissWithCanDismiss = step >= 0 && canDismissBlocksGesture;\n        /**\n         * If we are blocking the gesture from dismissing,\n         * set the max step value so that the sheet cannot be\n         * completely hidden.\n         */\n        const maxStep = isAttemptingDismissWithCanDismiss ? canDismissMaxStep : 0.9999;\n        /**\n         * If we are blocking the gesture from\n         * dismissing, calculate the spring modifier value\n         * this will be added to the starting breakpoint\n         * value to give the gesture a spring-like feeling.\n         * Note that the starting breakpoint is always 0,\n         * so we omit adding 0 to the result.\n         */\n        const processedStep = isAttemptingDismissWithCanDismiss ? calculateSpringStep(step / maxStep) : step;\n        const clampedStep = clamp(0.0001, processedStep, maxStep);\n        animation.progressStep(clampedStep);\n        /**\n         * When swiping down half way, the status bar style\n         * should be reset to its default value.\n         *\n         * We track lastStep so that we do not fire these\n         * functions on every onMove, only when the user has\n         * crossed a certain threshold.\n         */\n        if (clampedStep >= DISMISS_THRESHOLD && lastStep < DISMISS_THRESHOLD) {\n            setCardStatusBarDefault(statusBarStyle);\n            /**\n             * However, if we swipe back up, then the\n             * status bar style should be set to have light\n             * text on a dark background.\n             */\n        }\n        else if (clampedStep < DISMISS_THRESHOLD && lastStep >= DISMISS_THRESHOLD) {\n            setCardStatusBarDark();\n        }\n        lastStep = clampedStep;\n    };\n    const onEnd = (detail) => {\n        const velocity = detail.velocityY;\n        const step = detail.deltaY / height;\n        const isAttemptingDismissWithCanDismiss = step >= 0 && canDismissBlocksGesture;\n        const maxStep = isAttemptingDismissWithCanDismiss ? canDismissMaxStep : 0.9999;\n        const processedStep = isAttemptingDismissWithCanDismiss ? calculateSpringStep(step / maxStep) : step;\n        const clampedStep = clamp(0.0001, processedStep, maxStep);\n        const threshold = (detail.deltaY + velocity * 1000) / height;\n        /**\n         * If canDismiss blocks\n         * the swipe gesture, then the\n         * animation can never complete until\n         * canDismiss is checked.\n         */\n        const shouldComplete = !isAttemptingDismissWithCanDismiss && threshold >= DISMISS_THRESHOLD;\n        let newStepValue = shouldComplete ? -1e-3 : 0.001;\n        if (!shouldComplete) {\n            animation.easing('cubic-bezier(1, 0, 0.68, 0.28)');\n            newStepValue += getTimeGivenProgression([0, 0], [1, 0], [0.68, 0.28], [1, 1], clampedStep)[0];\n        }\n        else {\n            animation.easing('cubic-bezier(0.32, 0.72, 0, 1)');\n            newStepValue += getTimeGivenProgression([0, 0], [0.32, 0.72], [0, 1], [1, 1], clampedStep)[0];\n        }\n        const duration = shouldComplete\n            ? computeDuration(step * height, velocity)\n            : computeDuration((1 - clampedStep) * height, velocity);\n        isOpen = shouldComplete;\n        gesture.enable(false);\n        if (contentEl) {\n            resetContentScrollY(contentEl, initialScrollY);\n        }\n        animation\n            .onFinish(() => {\n            if (!shouldComplete) {\n                gesture.enable(true);\n            }\n        })\n            .progressEnd(shouldComplete ? 1 : 0, newStepValue, duration);\n        /**\n         * If the canDismiss value blocked the gesture\n         * from proceeding, then we should ignore whatever\n         * shouldComplete is. Whether or not the modal\n         * animation should complete is now determined by\n         * canDismiss.\n         *\n         * If the user swiped >25% of the way\n         * to the max step, then we should\n         * check canDismiss. 25% was chosen\n         * to avoid accidental swipes.\n         */\n        if (isAttemptingDismissWithCanDismiss && clampedStep > maxStep / 4) {\n            handleCanDismiss(el, animation);\n        }\n        else if (shouldComplete) {\n            onDismiss();\n        }\n    };\n    const gesture = createGesture({\n        el,\n        gestureName: 'modalSwipeToClose',\n        gesturePriority: OVERLAY_GESTURE_PRIORITY,\n        direction: 'y',\n        threshold: 10,\n        canStart,\n        onStart,\n        onMove,\n        onEnd,\n    });\n    return gesture;\n};\nconst computeDuration = (remaining, velocity) => {\n    return clamp(400, remaining / Math.abs(velocity * 1.1), 500);\n};\n\nconst createSheetEnterAnimation = (opts) => {\n    const { currentBreakpoint, backdropBreakpoint, expandToScroll } = opts;\n    /**\n     * If the backdropBreakpoint is undefined, then the backdrop\n     * should always fade in. If the backdropBreakpoint came before the\n     * current breakpoint, then the backdrop should be fading in.\n     */\n    const shouldShowBackdrop = backdropBreakpoint === undefined || backdropBreakpoint < currentBreakpoint;\n    const initialBackdrop = shouldShowBackdrop ? `calc(var(--backdrop-opacity) * ${currentBreakpoint})` : '0';\n    const backdropAnimation = createAnimation('backdropAnimation').fromTo('opacity', 0, initialBackdrop);\n    if (shouldShowBackdrop) {\n        backdropAnimation\n            .beforeStyles({\n            'pointer-events': 'none',\n        })\n            .afterClearStyles(['pointer-events']);\n    }\n    const wrapperAnimation = createAnimation('wrapperAnimation').keyframes([\n        { offset: 0, opacity: 1, transform: 'translateY(100%)' },\n        { offset: 1, opacity: 1, transform: `translateY(${100 - currentBreakpoint * 100}%)` },\n    ]);\n    /**\n     * This allows the content to be scrollable at any breakpoint.\n     */\n    const contentAnimation = !expandToScroll\n        ? createAnimation('contentAnimation').keyframes([\n            { offset: 0, opacity: 1, maxHeight: `${(1 - currentBreakpoint) * 100}%` },\n            { offset: 1, opacity: 1, maxHeight: `${currentBreakpoint * 100}%` },\n        ])\n        : undefined;\n    return { wrapperAnimation, backdropAnimation, contentAnimation };\n};\nconst createSheetLeaveAnimation = (opts) => {\n    const { currentBreakpoint, backdropBreakpoint } = opts;\n    /**\n     * Backdrop does not always fade in from 0 to 1 if backdropBreakpoint\n     * is defined, so we need to account for that offset by figuring out\n     * what the current backdrop value should be.\n     */\n    const backdropValue = `calc(var(--backdrop-opacity) * ${getBackdropValueForSheet(currentBreakpoint, backdropBreakpoint)})`;\n    const defaultBackdrop = [\n        { offset: 0, opacity: backdropValue },\n        { offset: 1, opacity: 0 },\n    ];\n    const customBackdrop = [\n        { offset: 0, opacity: backdropValue },\n        { offset: backdropBreakpoint, opacity: 0 },\n        { offset: 1, opacity: 0 },\n    ];\n    const backdropAnimation = createAnimation('backdropAnimation').keyframes(backdropBreakpoint !== 0 ? customBackdrop : defaultBackdrop);\n    const wrapperAnimation = createAnimation('wrapperAnimation').keyframes([\n        { offset: 0, opacity: 1, transform: `translateY(${100 - currentBreakpoint * 100}%)` },\n        { offset: 1, opacity: 1, transform: `translateY(100%)` },\n    ]);\n    return { wrapperAnimation, backdropAnimation };\n};\n\nconst createEnterAnimation$1 = () => {\n    const backdropAnimation = createAnimation()\n        .fromTo('opacity', 0.01, 'var(--backdrop-opacity)')\n        .beforeStyles({\n        'pointer-events': 'none',\n    })\n        .afterClearStyles(['pointer-events']);\n    const wrapperAnimation = createAnimation().fromTo('transform', 'translateY(100vh)', 'translateY(0vh)');\n    return { backdropAnimation, wrapperAnimation, contentAnimation: undefined };\n};\n/**\n * iOS Modal Enter Animation for the Card presentation style\n */\nconst iosEnterAnimation = (baseEl, opts) => {\n    const { presentingEl, currentBreakpoint, expandToScroll } = opts;\n    const root = getElementRoot(baseEl);\n    const { wrapperAnimation, backdropAnimation, contentAnimation } = currentBreakpoint !== undefined ? createSheetEnterAnimation(opts) : createEnterAnimation$1();\n    backdropAnimation.addElement(root.querySelector('ion-backdrop'));\n    wrapperAnimation.addElement(root.querySelectorAll('.modal-wrapper, .modal-shadow')).beforeStyles({ opacity: 1 });\n    // The content animation is only added if scrolling is enabled for\n    // all the breakpoints.\n    !expandToScroll && (contentAnimation === null || contentAnimation === void 0 ? void 0 : contentAnimation.addElement(baseEl.querySelector('.ion-page')));\n    const baseAnimation = createAnimation('entering-base')\n        .addElement(baseEl)\n        .easing('cubic-bezier(0.32,0.72,0,1)')\n        .duration(500)\n        .addAnimation([wrapperAnimation]);\n    if (contentAnimation) {\n        baseAnimation.addAnimation(contentAnimation);\n    }\n    if (presentingEl) {\n        const isPortrait = window.innerWidth < 768;\n        const hasCardModal = presentingEl.tagName === 'ION-MODAL' && presentingEl.presentingElement !== undefined;\n        const presentingElRoot = getElementRoot(presentingEl);\n        const presentingAnimation = createAnimation().beforeStyles({\n            transform: 'translateY(0)',\n            'transform-origin': 'top center',\n            overflow: 'hidden',\n        });\n        const bodyEl = document.body;\n        if (isPortrait) {\n            /**\n             * Fallback for browsers that does not support `max()` (ex: Firefox)\n             * No need to worry about statusbar padding since engines like Gecko\n             * are not used as the engine for standalone Cordova/Capacitor apps\n             */\n            const transformOffset = !CSS.supports('width', 'max(0px, 1px)') ? '30px' : 'max(30px, var(--ion-safe-area-top))';\n            const modalTransform = hasCardModal ? '-10px' : transformOffset;\n            const toPresentingScale = SwipeToCloseDefaults.MIN_PRESENTING_SCALE;\n            const finalTransform = `translateY(${modalTransform}) scale(${toPresentingScale})`;\n            presentingAnimation\n                .afterStyles({\n                transform: finalTransform,\n            })\n                .beforeAddWrite(() => bodyEl.style.setProperty('background-color', 'black'))\n                .addElement(presentingEl)\n                .keyframes([\n                { offset: 0, filter: 'contrast(1)', transform: 'translateY(0px) scale(1)', borderRadius: '0px' },\n                { offset: 1, filter: 'contrast(0.85)', transform: finalTransform, borderRadius: '10px 10px 0 0' },\n            ]);\n            baseAnimation.addAnimation(presentingAnimation);\n        }\n        else {\n            baseAnimation.addAnimation(backdropAnimation);\n            if (!hasCardModal) {\n                wrapperAnimation.fromTo('opacity', '0', '1');\n            }\n            else {\n                const toPresentingScale = hasCardModal ? SwipeToCloseDefaults.MIN_PRESENTING_SCALE : 1;\n                const finalTransform = `translateY(-10px) scale(${toPresentingScale})`;\n                presentingAnimation\n                    .afterStyles({\n                    transform: finalTransform,\n                })\n                    .addElement(presentingElRoot.querySelector('.modal-wrapper'))\n                    .keyframes([\n                    { offset: 0, filter: 'contrast(1)', transform: 'translateY(0) scale(1)' },\n                    { offset: 1, filter: 'contrast(0.85)', transform: finalTransform },\n                ]);\n                const shadowAnimation = createAnimation()\n                    .afterStyles({\n                    transform: finalTransform,\n                })\n                    .addElement(presentingElRoot.querySelector('.modal-shadow'))\n                    .keyframes([\n                    { offset: 0, opacity: '1', transform: 'translateY(0) scale(1)' },\n                    { offset: 1, opacity: '0', transform: finalTransform },\n                ]);\n                baseAnimation.addAnimation([presentingAnimation, shadowAnimation]);\n            }\n        }\n    }\n    else {\n        baseAnimation.addAnimation(backdropAnimation);\n    }\n    return baseAnimation;\n};\n\nconst createLeaveAnimation$1 = () => {\n    const backdropAnimation = createAnimation().fromTo('opacity', 'var(--backdrop-opacity)', 0);\n    const wrapperAnimation = createAnimation().fromTo('transform', 'translateY(0vh)', 'translateY(100vh)');\n    return { backdropAnimation, wrapperAnimation };\n};\n/**\n * iOS Modal Leave Animation\n */\nconst iosLeaveAnimation = (baseEl, opts, duration = 500) => {\n    const { presentingEl, currentBreakpoint } = opts;\n    const root = getElementRoot(baseEl);\n    const { wrapperAnimation, backdropAnimation } = currentBreakpoint !== undefined ? createSheetLeaveAnimation(opts) : createLeaveAnimation$1();\n    backdropAnimation.addElement(root.querySelector('ion-backdrop'));\n    wrapperAnimation.addElement(root.querySelectorAll('.modal-wrapper, .modal-shadow')).beforeStyles({ opacity: 1 });\n    const baseAnimation = createAnimation('leaving-base')\n        .addElement(baseEl)\n        .easing('cubic-bezier(0.32,0.72,0,1)')\n        .duration(duration)\n        .addAnimation(wrapperAnimation);\n    if (presentingEl) {\n        const isPortrait = window.innerWidth < 768;\n        const hasCardModal = presentingEl.tagName === 'ION-MODAL' && presentingEl.presentingElement !== undefined;\n        const presentingElRoot = getElementRoot(presentingEl);\n        const presentingAnimation = createAnimation()\n            .beforeClearStyles(['transform'])\n            .afterClearStyles(['transform'])\n            .onFinish((currentStep) => {\n            // only reset background color if this is the last card-style modal\n            if (currentStep !== 1) {\n                return;\n            }\n            presentingEl.style.setProperty('overflow', '');\n            const numModals = Array.from(bodyEl.querySelectorAll('ion-modal:not(.overlay-hidden)')).filter((m) => m.presentingElement !== undefined).length;\n            if (numModals <= 1) {\n                bodyEl.style.setProperty('background-color', '');\n            }\n        });\n        const bodyEl = document.body;\n        if (isPortrait) {\n            const transformOffset = !CSS.supports('width', 'max(0px, 1px)') ? '30px' : 'max(30px, var(--ion-safe-area-top))';\n            const modalTransform = hasCardModal ? '-10px' : transformOffset;\n            const toPresentingScale = SwipeToCloseDefaults.MIN_PRESENTING_SCALE;\n            const finalTransform = `translateY(${modalTransform}) scale(${toPresentingScale})`;\n            presentingAnimation.addElement(presentingEl).keyframes([\n                { offset: 0, filter: 'contrast(0.85)', transform: finalTransform, borderRadius: '10px 10px 0 0' },\n                { offset: 1, filter: 'contrast(1)', transform: 'translateY(0px) scale(1)', borderRadius: '0px' },\n            ]);\n            baseAnimation.addAnimation(presentingAnimation);\n        }\n        else {\n            baseAnimation.addAnimation(backdropAnimation);\n            if (!hasCardModal) {\n                wrapperAnimation.fromTo('opacity', '1', '0');\n            }\n            else {\n                const toPresentingScale = hasCardModal ? SwipeToCloseDefaults.MIN_PRESENTING_SCALE : 1;\n                const finalTransform = `translateY(-10px) scale(${toPresentingScale})`;\n                presentingAnimation\n                    .addElement(presentingElRoot.querySelector('.modal-wrapper'))\n                    .afterStyles({\n                    transform: 'translate3d(0, 0, 0)',\n                })\n                    .keyframes([\n                    { offset: 0, filter: 'contrast(0.85)', transform: finalTransform },\n                    { offset: 1, filter: 'contrast(1)', transform: 'translateY(0) scale(1)' },\n                ]);\n                const shadowAnimation = createAnimation()\n                    .addElement(presentingElRoot.querySelector('.modal-shadow'))\n                    .afterStyles({\n                    transform: 'translateY(0) scale(1)',\n                })\n                    .keyframes([\n                    { offset: 0, opacity: '0', transform: finalTransform },\n                    { offset: 1, opacity: '1', transform: 'translateY(0) scale(1)' },\n                ]);\n                baseAnimation.addAnimation([presentingAnimation, shadowAnimation]);\n            }\n        }\n    }\n    else {\n        baseAnimation.addAnimation(backdropAnimation);\n    }\n    return baseAnimation;\n};\n\n/**\n * Transition animation from portrait view to landscape view\n * This handles the case where a card modal is open in portrait view\n * and the user switches to landscape view\n */\nconst portraitToLandscapeTransition = (baseEl, opts, duration = 300) => {\n    const { presentingEl } = opts;\n    if (!presentingEl) {\n        // No transition needed for non-card modals\n        return createAnimation('portrait-to-landscape-transition');\n    }\n    const presentingElIsCardModal = presentingEl.tagName === 'ION-MODAL' && presentingEl.presentingElement !== undefined;\n    const presentingElRoot = getElementRoot(presentingEl);\n    const bodyEl = document.body;\n    const baseAnimation = createAnimation('portrait-to-landscape-transition')\n        .addElement(baseEl)\n        .easing('cubic-bezier(0.32,0.72,0,1)')\n        .duration(duration);\n    const presentingAnimation = createAnimation().beforeStyles({\n        transform: 'translateY(0)',\n        'transform-origin': 'top center',\n        overflow: 'hidden',\n    });\n    if (!presentingElIsCardModal) {\n        // The presenting element is not a card modal, so we do not\n        // need to care about layering and modal-specific styles.\n        const root = getElementRoot(baseEl);\n        const wrapperAnimation = createAnimation()\n            .addElement(root.querySelectorAll('.modal-wrapper, .modal-shadow'))\n            .fromTo('opacity', '1', '1'); // Keep wrapper visible in landscape\n        const backdropAnimation = createAnimation()\n            .addElement(root.querySelector('ion-backdrop'))\n            .fromTo('opacity', 'var(--backdrop-opacity)', 'var(--backdrop-opacity)'); // Keep backdrop visible\n        // Animate presentingEl from portrait state back to normal\n        const transformOffset = !CSS.supports('width', 'max(0px, 1px)') ? '30px' : 'max(30px, var(--ion-safe-area-top))';\n        const toPresentingScale = SwipeToCloseDefaults.MIN_PRESENTING_SCALE;\n        const fromTransform = `translateY(${transformOffset}) scale(${toPresentingScale})`;\n        presentingAnimation\n            .addElement(presentingEl)\n            .afterStyles({\n            transform: 'translateY(0px) scale(1)',\n            'border-radius': '0px',\n        })\n            .beforeAddWrite(() => bodyEl.style.setProperty('background-color', ''))\n            .fromTo('transform', fromTransform, 'translateY(0px) scale(1)')\n            .fromTo('filter', 'contrast(0.85)', 'contrast(1)')\n            .fromTo('border-radius', '10px 10px 0 0', '0px');\n        baseAnimation.addAnimation([presentingAnimation, wrapperAnimation, backdropAnimation]);\n    }\n    else {\n        // The presenting element is a card modal, so we do\n        // need to care about layering and modal-specific styles.\n        const toPresentingScale = SwipeToCloseDefaults.MIN_PRESENTING_SCALE;\n        const fromTransform = `translateY(-10px) scale(${toPresentingScale})`;\n        const toTransform = `translateY(0px) scale(1)`;\n        presentingAnimation\n            .addElement(presentingEl)\n            .afterStyles({\n            transform: toTransform,\n        })\n            .fromTo('transform', fromTransform, toTransform)\n            .fromTo('filter', 'contrast(0.85)', 'contrast(1)');\n        const shadowAnimation = createAnimation()\n            .addElement(presentingElRoot.querySelector('.modal-shadow'))\n            .afterStyles({\n            transform: toTransform,\n            opacity: '0',\n        })\n            .fromTo('transform', fromTransform, toTransform);\n        baseAnimation.addAnimation([presentingAnimation, shadowAnimation]);\n    }\n    return baseAnimation;\n};\n/**\n * Transition animation from landscape view to portrait view\n * This handles the case where a card modal is open in landscape view\n * and the user switches to portrait view\n */\nconst landscapeToPortraitTransition = (baseEl, opts, duration = 300) => {\n    const { presentingEl } = opts;\n    if (!presentingEl) {\n        // No transition needed for non-card modals\n        return createAnimation('landscape-to-portrait-transition');\n    }\n    const presentingElIsCardModal = presentingEl.tagName === 'ION-MODAL' && presentingEl.presentingElement !== undefined;\n    const presentingElRoot = getElementRoot(presentingEl);\n    const bodyEl = document.body;\n    const baseAnimation = createAnimation('landscape-to-portrait-transition')\n        .addElement(baseEl)\n        .easing('cubic-bezier(0.32,0.72,0,1)')\n        .duration(duration);\n    const presentingAnimation = createAnimation().beforeStyles({\n        transform: 'translateY(0)',\n        'transform-origin': 'top center',\n        overflow: 'hidden',\n    });\n    if (!presentingElIsCardModal) {\n        // The presenting element is not a card modal, so we do not\n        // need to care about layering and modal-specific styles.\n        const root = getElementRoot(baseEl);\n        const wrapperAnimation = createAnimation()\n            .addElement(root.querySelectorAll('.modal-wrapper, .modal-shadow'))\n            .fromTo('opacity', '1', '1'); // Keep wrapper visible\n        const backdropAnimation = createAnimation()\n            .addElement(root.querySelector('ion-backdrop'))\n            .fromTo('opacity', 'var(--backdrop-opacity)', 'var(--backdrop-opacity)'); // Keep backdrop visible\n        // Animate presentingEl from normal state to portrait state\n        const transformOffset = !CSS.supports('width', 'max(0px, 1px)') ? '30px' : 'max(30px, var(--ion-safe-area-top))';\n        const toPresentingScale = SwipeToCloseDefaults.MIN_PRESENTING_SCALE;\n        const toTransform = `translateY(${transformOffset}) scale(${toPresentingScale})`;\n        presentingAnimation\n            .addElement(presentingEl)\n            .afterStyles({\n            transform: toTransform,\n        })\n            .beforeAddWrite(() => bodyEl.style.setProperty('background-color', 'black'))\n            .keyframes([\n            { offset: 0, transform: 'translateY(0px) scale(1)', filter: 'contrast(1)', borderRadius: '0px' },\n            { offset: 0.2, transform: 'translateY(0px) scale(1)', filter: 'contrast(1)', borderRadius: '10px 10px 0 0' },\n            { offset: 1, transform: toTransform, filter: 'contrast(0.85)', borderRadius: '10px 10px 0 0' },\n        ]);\n        baseAnimation.addAnimation([presentingAnimation, wrapperAnimation, backdropAnimation]);\n    }\n    else {\n        // The presenting element is also a card modal, so we need\n        // to handle layering and modal-specific styles.\n        const toPresentingScale = SwipeToCloseDefaults.MIN_PRESENTING_SCALE;\n        const fromTransform = `translateY(-10px) scale(${toPresentingScale})`;\n        const toTransform = `translateY(0) scale(1)`;\n        presentingAnimation\n            .addElement(presentingEl)\n            .afterStyles({\n            transform: toTransform,\n        })\n            .fromTo('transform', fromTransform, toTransform);\n        const shadowAnimation = createAnimation()\n            .addElement(presentingElRoot.querySelector('.modal-shadow'))\n            .afterStyles({\n            transform: toTransform,\n            opacity: '0',\n        })\n            .fromTo('transform', fromTransform, toTransform);\n        baseAnimation.addAnimation([presentingAnimation, shadowAnimation]);\n    }\n    return baseAnimation;\n};\n\nconst createEnterAnimation = () => {\n    const backdropAnimation = createAnimation()\n        .fromTo('opacity', 0.01, 'var(--backdrop-opacity)')\n        .beforeStyles({\n        'pointer-events': 'none',\n    })\n        .afterClearStyles(['pointer-events']);\n    const wrapperAnimation = createAnimation().keyframes([\n        { offset: 0, opacity: 0.01, transform: 'translateY(40px)' },\n        { offset: 1, opacity: 1, transform: `translateY(0px)` },\n    ]);\n    return { backdropAnimation, wrapperAnimation, contentAnimation: undefined };\n};\n/**\n * Md Modal Enter Animation\n */\nconst mdEnterAnimation = (baseEl, opts) => {\n    const { currentBreakpoint, expandToScroll } = opts;\n    const root = getElementRoot(baseEl);\n    const { wrapperAnimation, backdropAnimation, contentAnimation } = currentBreakpoint !== undefined ? createSheetEnterAnimation(opts) : createEnterAnimation();\n    backdropAnimation.addElement(root.querySelector('ion-backdrop'));\n    wrapperAnimation.addElement(root.querySelector('.modal-wrapper'));\n    // The content animation is only added if scrolling is enabled for\n    // all the breakpoints.\n    !expandToScroll && (contentAnimation === null || contentAnimation === void 0 ? void 0 : contentAnimation.addElement(baseEl.querySelector('.ion-page')));\n    const baseAnimation = createAnimation()\n        .addElement(baseEl)\n        .easing('cubic-bezier(0.36,0.66,0.04,1)')\n        .duration(280)\n        .addAnimation([backdropAnimation, wrapperAnimation]);\n    if (contentAnimation) {\n        baseAnimation.addAnimation(contentAnimation);\n    }\n    return baseAnimation;\n};\n\nconst createLeaveAnimation = () => {\n    const backdropAnimation = createAnimation().fromTo('opacity', 'var(--backdrop-opacity)', 0);\n    const wrapperAnimation = createAnimation().keyframes([\n        { offset: 0, opacity: 0.99, transform: `translateY(0px)` },\n        { offset: 1, opacity: 0, transform: 'translateY(40px)' },\n    ]);\n    return { backdropAnimation, wrapperAnimation };\n};\n/**\n * Md Modal Leave Animation\n */\nconst mdLeaveAnimation = (baseEl, opts) => {\n    const { currentBreakpoint } = opts;\n    const root = getElementRoot(baseEl);\n    const { wrapperAnimation, backdropAnimation } = currentBreakpoint !== undefined ? createSheetLeaveAnimation(opts) : createLeaveAnimation();\n    backdropAnimation.addElement(root.querySelector('ion-backdrop'));\n    wrapperAnimation.addElement(root.querySelector('.modal-wrapper'));\n    const baseAnimation = createAnimation()\n        .easing('cubic-bezier(0.47,0,0.745,0.715)')\n        .duration(200)\n        .addAnimation([backdropAnimation, wrapperAnimation]);\n    return baseAnimation;\n};\n\nconst createSheetGesture = (baseEl, backdropEl, wrapperEl, initialBreakpoint, backdropBreakpoint, animation, breakpoints = [], expandToScroll, getCurrentBreakpoint, onDismiss, onBreakpointChange) => {\n    // Defaults for the sheet swipe animation\n    const defaultBackdrop = [\n        { offset: 0, opacity: 'var(--backdrop-opacity)' },\n        { offset: 1, opacity: 0.01 },\n    ];\n    const customBackdrop = [\n        { offset: 0, opacity: 'var(--backdrop-opacity)' },\n        { offset: 1 - backdropBreakpoint, opacity: 0 },\n        { offset: 1, opacity: 0 },\n    ];\n    const SheetDefaults = {\n        WRAPPER_KEYFRAMES: [\n            { offset: 0, transform: 'translateY(0%)' },\n            { offset: 1, transform: 'translateY(100%)' },\n        ],\n        BACKDROP_KEYFRAMES: backdropBreakpoint !== 0 ? customBackdrop : defaultBackdrop,\n        CONTENT_KEYFRAMES: [\n            { offset: 0, maxHeight: '100%' },\n            { offset: 1, maxHeight: '0%' },\n        ],\n    };\n    const contentEl = baseEl.querySelector('ion-content');\n    const height = wrapperEl.clientHeight;\n    let currentBreakpoint = initialBreakpoint;\n    let offset = 0;\n    let canDismissBlocksGesture = false;\n    let cachedScrollEl = null;\n    let cachedFooterEls = null;\n    let cachedFooterYPosition = null;\n    let currentFooterState = null;\n    const canDismissMaxStep = 0.95;\n    const maxBreakpoint = breakpoints[breakpoints.length - 1];\n    const minBreakpoint = breakpoints[0];\n    const wrapperAnimation = animation.childAnimations.find((ani) => ani.id === 'wrapperAnimation');\n    const backdropAnimation = animation.childAnimations.find((ani) => ani.id === 'backdropAnimation');\n    const contentAnimation = animation.childAnimations.find((ani) => ani.id === 'contentAnimation');\n    const enableBackdrop = () => {\n        baseEl.style.setProperty('pointer-events', 'auto');\n        backdropEl.style.setProperty('pointer-events', 'auto');\n        /**\n         * When the backdrop is enabled, elements such\n         * as inputs should not be focusable outside\n         * the sheet.\n         */\n        baseEl.classList.remove(FOCUS_TRAP_DISABLE_CLASS);\n    };\n    const disableBackdrop = () => {\n        baseEl.style.setProperty('pointer-events', 'none');\n        backdropEl.style.setProperty('pointer-events', 'none');\n        /**\n         * When the backdrop is enabled, elements such\n         * as inputs should not be focusable outside\n         * the sheet.\n         * Adding this class disables focus trapping\n         * for the sheet temporarily.\n         */\n        baseEl.classList.add(FOCUS_TRAP_DISABLE_CLASS);\n    };\n    /**\n     * Toggles the footer to an absolute position while moving to prevent\n     * it from shaking while the sheet is being dragged.\n     * @param newPosition Whether the footer is in a moving or stationary position.\n     */\n    const swapFooterPosition = (newPosition) => {\n        if (!cachedFooterEls) {\n            cachedFooterEls = Array.from(baseEl.querySelectorAll('ion-footer'));\n            if (!cachedFooterEls.length) {\n                return;\n            }\n        }\n        const page = baseEl.querySelector('.ion-page');\n        currentFooterState = newPosition;\n        if (newPosition === 'stationary') {\n            cachedFooterEls.forEach((cachedFooterEl) => {\n                // Reset positioning styles to allow normal document flow\n                cachedFooterEl.classList.remove('modal-footer-moving');\n                cachedFooterEl.style.removeProperty('position');\n                cachedFooterEl.style.removeProperty('width');\n                cachedFooterEl.style.removeProperty('height');\n                cachedFooterEl.style.removeProperty('top');\n                cachedFooterEl.style.removeProperty('left');\n                page === null || page === void 0 ? void 0 : page.style.removeProperty('padding-bottom');\n                // Move to page\n                page === null || page === void 0 ? void 0 : page.appendChild(cachedFooterEl);\n            });\n        }\n        else {\n            let footerHeights = 0;\n            cachedFooterEls.forEach((cachedFooterEl, index) => {\n                // Get both the footer and document body positions\n                const cachedFooterElRect = cachedFooterEl.getBoundingClientRect();\n                const bodyRect = document.body.getBoundingClientRect();\n                // Calculate the total height of all footers\n                // so we can add padding to the page element\n                footerHeights += cachedFooterEl.clientHeight;\n                // Calculate absolute position relative to body\n                // We need to subtract the body's offsetTop to get true position within document.body\n                const absoluteTop = cachedFooterElRect.top - bodyRect.top;\n                const absoluteLeft = cachedFooterElRect.left - bodyRect.left;\n                // Capture the footer's current dimensions and store them in CSS variables for\n                // later use when applying absolute positioning.\n                cachedFooterEl.style.setProperty('--pinned-width', `${cachedFooterEl.clientWidth}px`);\n                cachedFooterEl.style.setProperty('--pinned-height', `${cachedFooterEl.clientHeight}px`);\n                cachedFooterEl.style.setProperty('--pinned-top', `${absoluteTop}px`);\n                cachedFooterEl.style.setProperty('--pinned-left', `${absoluteLeft}px`);\n                // Only cache the first footer's Y position\n                // This is used to determine if the sheet has been moved below the footer\n                // and needs to be swapped back to stationary so it collapses correctly.\n                if (index === 0) {\n                    cachedFooterYPosition = absoluteTop;\n                    // If there's a header, we need to combine the header height with the footer position\n                    // because the header moves with the drag handle, so when it starts overlapping the footer,\n                    // we need to account for that.\n                    const header = baseEl.querySelector('ion-header');\n                    if (header) {\n                        cachedFooterYPosition -= header.clientHeight;\n                    }\n                }\n            });\n            // Apply the pinning of styles after we've calculated everything\n            // so that we don't cause layouts to shift while calculating the footer positions.\n            // Otherwise, with multiple footers we'll end up capturing the wrong positions.\n            cachedFooterEls.forEach((cachedFooterEl) => {\n                // Add padding to the parent element to prevent content from being hidden\n                // when the footer is positioned absolutely. This has to be done before we\n                // make the footer absolutely positioned or we may accidentally cause the\n                // sheet to scroll.\n                page === null || page === void 0 ? void 0 : page.style.setProperty('padding-bottom', `${footerHeights}px`);\n                // Apply positioning styles to keep footer at bottom\n                cachedFooterEl.classList.add('modal-footer-moving');\n                // Apply our preserved styles to pin the footer\n                cachedFooterEl.style.setProperty('position', 'absolute');\n                cachedFooterEl.style.setProperty('width', 'var(--pinned-width)');\n                cachedFooterEl.style.setProperty('height', 'var(--pinned-height)');\n                cachedFooterEl.style.setProperty('top', 'var(--pinned-top)');\n                cachedFooterEl.style.setProperty('left', 'var(--pinned-left)');\n                // Move the element to the body when everything else is done\n                document.body.appendChild(cachedFooterEl);\n            });\n        }\n    };\n    /**\n     * After the entering animation completes,\n     * we need to set the animation to go from\n     * offset 0 to offset 1 so that users can\n     * swipe in any direction. We then set the\n     * animation offset to the current breakpoint\n     * so there is no flickering.\n     */\n    if (wrapperAnimation && backdropAnimation) {\n        wrapperAnimation.keyframes([...SheetDefaults.WRAPPER_KEYFRAMES]);\n        backdropAnimation.keyframes([...SheetDefaults.BACKDROP_KEYFRAMES]);\n        contentAnimation === null || contentAnimation === void 0 ? void 0 : contentAnimation.keyframes([...SheetDefaults.CONTENT_KEYFRAMES]);\n        animation.progressStart(true, 1 - currentBreakpoint);\n        /**\n         * If backdrop is not enabled, then content\n         * behind modal should be clickable. To do this, we need\n         * to remove pointer-events from ion-modal as a whole.\n         * ion-backdrop and .modal-wrapper always have pointer-events: auto\n         * applied, so the modal content can still be interacted with.\n         */\n        const shouldEnableBackdrop = currentBreakpoint > backdropBreakpoint;\n        if (shouldEnableBackdrop) {\n            enableBackdrop();\n        }\n        else {\n            disableBackdrop();\n        }\n    }\n    if (contentEl && currentBreakpoint !== maxBreakpoint && expandToScroll) {\n        contentEl.scrollY = false;\n    }\n    const canStart = (detail) => {\n        /**\n         * If we are swiping on the content, swiping should only be possible if the content\n         * is scrolled all the way to the top so that we do not interfere with scrolling.\n         *\n         * We cannot assume that the `ion-content` target will remain consistent between swipes.\n         * For example, when using ion-nav within a modal it is possible to swipe, push a view,\n         * and then swipe again. The target content will not be the same between swipes.\n         */\n        const contentEl = findClosestIonContent(detail.event.target);\n        currentBreakpoint = getCurrentBreakpoint();\n        /**\n         * If `expandToScroll` is disabled, we should not allow the swipe gesture\n         * to start if the content is not scrolled to the top.\n         */\n        if (!expandToScroll && contentEl) {\n            const scrollEl = isIonContent(contentEl) ? getElementRoot(contentEl).querySelector('.inner-scroll') : contentEl;\n            return scrollEl.scrollTop === 0;\n        }\n        if (currentBreakpoint === 1 && contentEl) {\n            /**\n             * The modal should never swipe to close on the content with a refresher.\n             * Note 1: We cannot solve this by making this gesture have a higher priority than\n             * the refresher gesture as the iOS native refresh gesture uses a scroll listener in\n             * addition to a gesture.\n             *\n             * Note 2: Do not use getScrollElement here because we need this to be a synchronous\n             * operation, and getScrollElement is asynchronous.\n             */\n            const scrollEl = isIonContent(contentEl) ? getElementRoot(contentEl).querySelector('.inner-scroll') : contentEl;\n            const hasRefresherInContent = !!contentEl.querySelector('ion-refresher');\n            return !hasRefresherInContent && scrollEl.scrollTop === 0;\n        }\n        return true;\n    };\n    const onStart = (detail) => {\n        /**\n         * If canDismiss is anything other than `true`\n         * then users should be able to swipe down\n         * until a threshold is hit. At that point,\n         * the card modal should not proceed any further.\n         *\n         * canDismiss is never fired via gesture if there is\n         * no 0 breakpoint. However, it can be fired if the user\n         * presses Esc or the hardware back button.\n         * TODO (FW-937)\n         * Remove undefined check\n         */\n        canDismissBlocksGesture = baseEl.canDismiss !== undefined && baseEl.canDismiss !== true && minBreakpoint === 0;\n        /**\n         * Cache the scroll element reference when the gesture starts,\n         * this allows us to avoid querying the DOM for the target in onMove,\n         * which would impact performance significantly.\n         */\n        if (!expandToScroll) {\n            const targetEl = findClosestIonContent(detail.event.target);\n            cachedScrollEl =\n                targetEl && isIonContent(targetEl) ? getElementRoot(targetEl).querySelector('.inner-scroll') : targetEl;\n        }\n        /**\n         * If expandToScroll is disabled, we need to swap\n         * the footer position to moving so that it doesn't shake\n         * while the sheet is being dragged.\n         */\n        if (!expandToScroll) {\n            swapFooterPosition('moving');\n        }\n        /**\n         * If we are pulling down, then it is possible we are pulling on the content.\n         * We do not want scrolling to happen at the same time as the gesture.\n         */\n        if (detail.deltaY > 0 && contentEl) {\n            contentEl.scrollY = false;\n        }\n        raf(() => {\n            /**\n             * Dismisses the open keyboard when the sheet drag gesture is started.\n             * Sets the focus onto the modal element.\n             */\n            baseEl.focus();\n        });\n        animation.progressStart(true, 1 - currentBreakpoint);\n    };\n    const onMove = (detail) => {\n        /**\n         * If `expandToScroll` is disabled, we need to see if we're currently below\n         * the footer element and the footer is in a stationary position. If so,\n         * we need to make the stationary the original position so that the footer\n         * collapses with the sheet.\n         */\n        if (!expandToScroll && cachedFooterYPosition !== null && currentFooterState !== null) {\n            // Check if we need to swap the footer position\n            if (detail.currentY >= cachedFooterYPosition && currentFooterState === 'moving') {\n                swapFooterPosition('stationary');\n            }\n            else if (detail.currentY < cachedFooterYPosition && currentFooterState === 'stationary') {\n                swapFooterPosition('moving');\n            }\n        }\n        /**\n         * If `expandToScroll` is disabled, and an upwards swipe gesture is done within\n         * the scrollable content, we should not allow the swipe gesture to continue.\n         */\n        if (!expandToScroll && detail.deltaY <= 0 && cachedScrollEl) {\n            return;\n        }\n        /**\n         * If we are pulling down, then it is possible we are pulling on the content.\n         * We do not want scrolling to happen at the same time as the gesture.\n         * This accounts for when the user scrolls down, scrolls all the way up, and then\n         * pulls down again such that the modal should start to move.\n         */\n        if (detail.deltaY > 0 && contentEl) {\n            contentEl.scrollY = false;\n        }\n        /**\n         * Given the change in gesture position on the Y axis,\n         * compute where the offset of the animation should be\n         * relative to where the user dragged.\n         */\n        const initialStep = 1 - currentBreakpoint;\n        const secondToLastBreakpoint = breakpoints.length > 1 ? 1 - breakpoints[1] : undefined;\n        const step = initialStep + detail.deltaY / height;\n        const isAttemptingDismissWithCanDismiss = secondToLastBreakpoint !== undefined && step >= secondToLastBreakpoint && canDismissBlocksGesture;\n        /**\n         * If we are blocking the gesture from dismissing,\n         * set the max step value so that the sheet cannot be\n         * completely hidden.\n         */\n        const maxStep = isAttemptingDismissWithCanDismiss ? canDismissMaxStep : 0.9999;\n        /**\n         * If we are blocking the gesture from\n         * dismissing, calculate the spring modifier value\n         * this will be added to the starting breakpoint\n         * value to give the gesture a spring-like feeling.\n         * Note that when isAttemptingDismissWithCanDismiss is true,\n         * the modifier is always added to the breakpoint that\n         * appears right after the 0 breakpoint.\n         *\n         * Note that this modifier is essentially the progression\n         * between secondToLastBreakpoint and maxStep which is\n         * why we subtract secondToLastBreakpoint. This lets us get\n         * the result as a value from 0 to 1.\n         */\n        const processedStep = isAttemptingDismissWithCanDismiss && secondToLastBreakpoint !== undefined\n            ? secondToLastBreakpoint +\n                calculateSpringStep((step - secondToLastBreakpoint) / (maxStep - secondToLastBreakpoint))\n            : step;\n        offset = clamp(0.0001, processedStep, maxStep);\n        animation.progressStep(offset);\n    };\n    const onEnd = (detail) => {\n        /**\n         * If expandToScroll is disabled, we should not allow the moveSheetToBreakpoint\n         * function to be called if the user is trying to swipe content upwards and the content\n         * is not scrolled to the top.\n         */\n        if (!expandToScroll && detail.deltaY <= 0 && cachedScrollEl && cachedScrollEl.scrollTop > 0) {\n            /**\n             * If expand to scroll is disabled, we need to make sure we swap the footer position\n             * back to stationary so that it will collapse correctly if the modal is dismissed without\n             * dragging (e.g. through a dismiss button).\n             * This can cause issues if the user has a modal with content that can be dragged, as we'll\n             * swap to moving on drag and if we don't swap back here then the footer will get stuck.\n             */\n            swapFooterPosition('stationary');\n            return;\n        }\n        /**\n         * When the gesture releases, we need to determine\n         * the closest breakpoint to snap to.\n         */\n        const velocity = detail.velocityY;\n        const threshold = (detail.deltaY + velocity * 350) / height;\n        const diff = currentBreakpoint - threshold;\n        const closest = breakpoints.reduce((a, b) => {\n            return Math.abs(b - diff) < Math.abs(a - diff) ? b : a;\n        });\n        moveSheetToBreakpoint({\n            breakpoint: closest,\n            breakpointOffset: offset,\n            canDismiss: canDismissBlocksGesture,\n            /**\n             * The swipe is user-driven, so we should\n             * always animate when the gesture ends.\n             */\n            animated: true,\n        });\n    };\n    const moveSheetToBreakpoint = (options) => {\n        const { breakpoint, canDismiss, breakpointOffset, animated } = options;\n        /**\n         * canDismiss should only prevent snapping\n         * when users are trying to dismiss. If canDismiss\n         * is present but the user is trying to swipe upwards,\n         * we should allow that to happen,\n         */\n        const shouldPreventDismiss = canDismiss && breakpoint === 0;\n        const snapToBreakpoint = shouldPreventDismiss ? currentBreakpoint : breakpoint;\n        const shouldRemainOpen = snapToBreakpoint !== 0;\n        currentBreakpoint = 0;\n        /**\n         * Update the animation so that it plays from\n         * the last offset to the closest snap point.\n         */\n        if (wrapperAnimation && backdropAnimation) {\n            wrapperAnimation.keyframes([\n                { offset: 0, transform: `translateY(${breakpointOffset * 100}%)` },\n                { offset: 1, transform: `translateY(${(1 - snapToBreakpoint) * 100}%)` },\n            ]);\n            backdropAnimation.keyframes([\n                {\n                    offset: 0,\n                    opacity: `calc(var(--backdrop-opacity) * ${getBackdropValueForSheet(1 - breakpointOffset, backdropBreakpoint)})`,\n                },\n                {\n                    offset: 1,\n                    opacity: `calc(var(--backdrop-opacity) * ${getBackdropValueForSheet(snapToBreakpoint, backdropBreakpoint)})`,\n                },\n            ]);\n            if (contentAnimation) {\n                /**\n                 * The modal content should scroll at any breakpoint when expandToScroll\n                 * is disabled. In order to do this, the content needs to be completely\n                 * viewable so scrolling can access everything. Otherwise, the default\n                 * behavior would show the content off the screen and only allow\n                 * scrolling when the sheet is fully expanded.\n                 */\n                contentAnimation.keyframes([\n                    { offset: 0, maxHeight: `${(1 - breakpointOffset) * 100}%` },\n                    { offset: 1, maxHeight: `${snapToBreakpoint * 100}%` },\n                ]);\n            }\n            animation.progressStep(0);\n        }\n        /**\n         * Gesture should remain disabled until the\n         * snapping animation completes.\n         */\n        gesture.enable(false);\n        if (shouldPreventDismiss) {\n            handleCanDismiss(baseEl, animation);\n        }\n        else if (!shouldRemainOpen) {\n            onDismiss();\n        }\n        /**\n         * Enables scrolling immediately if the sheet is about to fully expand\n         * or if it allows scrolling at any breakpoint. Without this, there would\n         * be a ~500ms delay while the modal animation completes, causing a\n         * noticeable lag. Native iOS allows scrolling as soon as the gesture is\n         * released, so we align with that behavior.\n         */\n        if (contentEl && (snapToBreakpoint === breakpoints[breakpoints.length - 1] || !expandToScroll)) {\n            contentEl.scrollY = true;\n        }\n        /**\n         * If expandToScroll is disabled and we're animating\n         * to close the sheet, we need to swap\n         * the footer position to stationary so that it\n         * will collapse correctly. We cannot just always swap\n         * here or it'll be jittery while animating movement.\n         */\n        if (!expandToScroll && snapToBreakpoint === 0) {\n            swapFooterPosition('stationary');\n        }\n        return new Promise((resolve) => {\n            animation\n                .onFinish(() => {\n                if (shouldRemainOpen) {\n                    /**\n                     * If expandToScroll is disabled, we need to swap\n                     * the footer position to stationary so that it\n                     * will act as it would by default.\n                     */\n                    if (!expandToScroll) {\n                        swapFooterPosition('stationary');\n                    }\n                    /**\n                     * Once the snapping animation completes,\n                     * we need to reset the animation to go\n                     * from 0 to 1 so users can swipe in any direction.\n                     * We then set the animation offset to the current\n                     * breakpoint so that it starts at the snapped position.\n                     */\n                    if (wrapperAnimation && backdropAnimation) {\n                        raf(() => {\n                            wrapperAnimation.keyframes([...SheetDefaults.WRAPPER_KEYFRAMES]);\n                            backdropAnimation.keyframes([...SheetDefaults.BACKDROP_KEYFRAMES]);\n                            contentAnimation === null || contentAnimation === void 0 ? void 0 : contentAnimation.keyframes([...SheetDefaults.CONTENT_KEYFRAMES]);\n                            animation.progressStart(true, 1 - snapToBreakpoint);\n                            currentBreakpoint = snapToBreakpoint;\n                            onBreakpointChange(currentBreakpoint);\n                            /**\n                             * Backdrop should become enabled\n                             * after the backdropBreakpoint value\n                             */\n                            const shouldEnableBackdrop = currentBreakpoint > backdropBreakpoint;\n                            if (shouldEnableBackdrop) {\n                                enableBackdrop();\n                            }\n                            else {\n                                disableBackdrop();\n                            }\n                            gesture.enable(true);\n                            resolve();\n                        });\n                    }\n                    else {\n                        gesture.enable(true);\n                        resolve();\n                    }\n                }\n                else {\n                    resolve();\n                }\n                /**\n                 * This must be a one time callback\n                 * otherwise a new callback will\n                 * be added every time onEnd runs.\n                 */\n            }, { oneTimeCallback: true })\n                .progressEnd(1, 0, animated ? 500 : 0);\n        });\n    };\n    const gesture = createGesture({\n        el: wrapperEl,\n        gestureName: 'modalSheet',\n        gesturePriority: 40,\n        direction: 'y',\n        threshold: 10,\n        canStart,\n        onStart,\n        onMove,\n        onEnd,\n    });\n    return {\n        gesture,\n        moveSheetToBreakpoint,\n    };\n};\n\nconst modalIosCss = \":host{--width:100%;--min-width:auto;--max-width:auto;--height:100%;--min-height:auto;--max-height:auto;--overflow:hidden;--border-radius:0;--border-width:0;--border-style:none;--border-color:transparent;--background:var(--ion-background-color, #fff);--box-shadow:none;--backdrop-opacity:0;left:0;right:0;top:0;bottom:0;display:-ms-flexbox;display:flex;position:absolute;-ms-flex-align:center;align-items:center;-ms-flex-pack:center;justify-content:center;outline:none;color:var(--ion-text-color, #000);contain:strict}.modal-wrapper,ion-backdrop{pointer-events:auto}:host(.overlay-hidden){display:none}.modal-wrapper,.modal-shadow{border-radius:var(--border-radius);width:var(--width);min-width:var(--min-width);max-width:var(--max-width);height:var(--height);min-height:var(--min-height);max-height:var(--max-height);border-width:var(--border-width);border-style:var(--border-style);border-color:var(--border-color);background:var(--background);-webkit-box-shadow:var(--box-shadow);box-shadow:var(--box-shadow);overflow:var(--overflow);z-index:10}.modal-shadow{position:absolute;background:transparent}@media only screen and (min-width: 768px) and (min-height: 600px){:host{--width:600px;--height:500px;--ion-safe-area-top:0px;--ion-safe-area-bottom:0px;--ion-safe-area-right:0px;--ion-safe-area-left:0px}}@media only screen and (min-width: 768px) and (min-height: 768px){:host{--width:600px;--height:600px}}.modal-handle{left:0px;right:0px;top:5px;border-radius:8px;-webkit-margin-start:auto;margin-inline-start:auto;-webkit-margin-end:auto;margin-inline-end:auto;position:absolute;width:36px;height:5px;-webkit-transform:translateZ(0);transform:translateZ(0);border:0;background:var(--ion-color-step-350, var(--ion-background-color-step-350, #c0c0be));cursor:pointer;z-index:11}.modal-handle::before{-webkit-padding-start:4px;padding-inline-start:4px;-webkit-padding-end:4px;padding-inline-end:4px;padding-top:4px;padding-bottom:4px;position:absolute;width:36px;height:5px;-webkit-transform:translate(-50%, -50%);transform:translate(-50%, -50%);content:\\\"\\\"}:host(.modal-sheet){--height:calc(100% - (var(--ion-safe-area-top) + 10px))}:host(.modal-sheet) .modal-wrapper,:host(.modal-sheet) .modal-shadow{position:absolute;bottom:0}:host(.modal-sheet.modal-no-expand-scroll) ion-footer{position:absolute;bottom:0;width:var(--width)}:host{--backdrop-opacity:var(--ion-backdrop-opacity, 0.4)}:host(.modal-card),:host(.modal-sheet){--border-radius:10px}@media only screen and (min-width: 768px) and (min-height: 600px){:host{--border-radius:10px}}.modal-wrapper{-webkit-transform:translate3d(0,  100%,  0);transform:translate3d(0,  100%,  0)}@media screen and (max-width: 767px){@supports (width: max(0px, 1px)){:host(.modal-card){--height:calc(100% - max(30px, var(--ion-safe-area-top)) - 10px)}}@supports not (width: max(0px, 1px)){:host(.modal-card){--height:calc(100% - 40px)}}:host(.modal-card) .modal-wrapper{border-start-start-radius:var(--border-radius);border-start-end-radius:var(--border-radius);border-end-end-radius:0;border-end-start-radius:0}:host(.modal-card){--backdrop-opacity:0;--width:100%;-ms-flex-align:end;align-items:flex-end}:host(.modal-card) .modal-shadow{display:none}:host(.modal-card) ion-backdrop{pointer-events:none}}@media screen and (min-width: 768px){:host(.modal-card){--width:calc(100% - 120px);--height:calc(100% - (120px + var(--ion-safe-area-top) + var(--ion-safe-area-bottom)));--max-width:720px;--max-height:1000px;--backdrop-opacity:0;--box-shadow:0px 0px 30px 10px rgba(0, 0, 0, 0.1);-webkit-transition:all 0.5s ease-in-out;transition:all 0.5s ease-in-out}:host(.modal-card) .modal-wrapper{-webkit-box-shadow:none;box-shadow:none}:host(.modal-card) .modal-shadow{-webkit-box-shadow:var(--box-shadow);box-shadow:var(--box-shadow)}}:host(.modal-sheet) .modal-wrapper{border-start-start-radius:var(--border-radius);border-start-end-radius:var(--border-radius);border-end-end-radius:0;border-end-start-radius:0}\";\n\nconst modalMdCss = \":host{--width:100%;--min-width:auto;--max-width:auto;--height:100%;--min-height:auto;--max-height:auto;--overflow:hidden;--border-radius:0;--border-width:0;--border-style:none;--border-color:transparent;--background:var(--ion-background-color, #fff);--box-shadow:none;--backdrop-opacity:0;left:0;right:0;top:0;bottom:0;display:-ms-flexbox;display:flex;position:absolute;-ms-flex-align:center;align-items:center;-ms-flex-pack:center;justify-content:center;outline:none;color:var(--ion-text-color, #000);contain:strict}.modal-wrapper,ion-backdrop{pointer-events:auto}:host(.overlay-hidden){display:none}.modal-wrapper,.modal-shadow{border-radius:var(--border-radius);width:var(--width);min-width:var(--min-width);max-width:var(--max-width);height:var(--height);min-height:var(--min-height);max-height:var(--max-height);border-width:var(--border-width);border-style:var(--border-style);border-color:var(--border-color);background:var(--background);-webkit-box-shadow:var(--box-shadow);box-shadow:var(--box-shadow);overflow:var(--overflow);z-index:10}.modal-shadow{position:absolute;background:transparent}@media only screen and (min-width: 768px) and (min-height: 600px){:host{--width:600px;--height:500px;--ion-safe-area-top:0px;--ion-safe-area-bottom:0px;--ion-safe-area-right:0px;--ion-safe-area-left:0px}}@media only screen and (min-width: 768px) and (min-height: 768px){:host{--width:600px;--height:600px}}.modal-handle{left:0px;right:0px;top:5px;border-radius:8px;-webkit-margin-start:auto;margin-inline-start:auto;-webkit-margin-end:auto;margin-inline-end:auto;position:absolute;width:36px;height:5px;-webkit-transform:translateZ(0);transform:translateZ(0);border:0;background:var(--ion-color-step-350, var(--ion-background-color-step-350, #c0c0be));cursor:pointer;z-index:11}.modal-handle::before{-webkit-padding-start:4px;padding-inline-start:4px;-webkit-padding-end:4px;padding-inline-end:4px;padding-top:4px;padding-bottom:4px;position:absolute;width:36px;height:5px;-webkit-transform:translate(-50%, -50%);transform:translate(-50%, -50%);content:\\\"\\\"}:host(.modal-sheet){--height:calc(100% - (var(--ion-safe-area-top) + 10px))}:host(.modal-sheet) .modal-wrapper,:host(.modal-sheet) .modal-shadow{position:absolute;bottom:0}:host(.modal-sheet.modal-no-expand-scroll) ion-footer{position:absolute;bottom:0;width:var(--width)}:host{--backdrop-opacity:var(--ion-backdrop-opacity, 0.32)}@media only screen and (min-width: 768px) and (min-height: 600px){:host{--border-radius:2px;--box-shadow:0 28px 48px rgba(0, 0, 0, 0.4)}}.modal-wrapper{-webkit-transform:translate3d(0,  40px,  0);transform:translate3d(0,  40px,  0);opacity:0.01}\";\n\nconst Modal = class {\n    constructor(hostRef) {\n        registerInstance(this, hostRef);\n        this.didPresent = createEvent(this, \"ionModalDidPresent\", 7);\n        this.willPresent = createEvent(this, \"ionModalWillPresent\", 7);\n        this.willDismiss = createEvent(this, \"ionModalWillDismiss\", 7);\n        this.didDismiss = createEvent(this, \"ionModalDidDismiss\", 7);\n        this.ionBreakpointDidChange = createEvent(this, \"ionBreakpointDidChange\", 7);\n        this.didPresentShorthand = createEvent(this, \"didPresent\", 7);\n        this.willPresentShorthand = createEvent(this, \"willPresent\", 7);\n        this.willDismissShorthand = createEvent(this, \"willDismiss\", 7);\n        this.didDismissShorthand = createEvent(this, \"didDismiss\", 7);\n        this.ionMount = createEvent(this, \"ionMount\", 7);\n        this.lockController = createLockController();\n        this.triggerController = createTriggerController();\n        this.coreDelegate = CoreDelegate();\n        this.isSheetModal = false;\n        this.inheritedAttributes = {};\n        this.inline = false;\n        // Whether or not modal is being dismissed via gesture\n        this.gestureAnimationDismissing = false;\n        this.presented = false;\n        /** @internal */\n        this.hasController = false;\n        /**\n         * If `true`, the keyboard will be automatically dismissed when the overlay is presented.\n         */\n        this.keyboardClose = true;\n        /**\n         * Controls whether scrolling or dragging within the sheet modal expands\n         * it to a larger breakpoint. This only takes effect when `breakpoints`\n         * and `initialBreakpoint` are set.\n         *\n         * If `true`, scrolling or dragging anywhere in the modal will first expand\n         * it to the next breakpoint. Once fully expanded, scrolling will affect the\n         * content.\n         * If `false`, scrolling will always affect the content. The modal will\n         * only expand when dragging the header or handle. The modal will close when\n         * dragging the header or handle. It can also be closed when dragging the\n         * content, but only if the content is scrolled to the top.\n         */\n        this.expandToScroll = true;\n        /**\n         * A decimal value between 0 and 1 that indicates the\n         * point after which the backdrop will begin to fade in\n         * when using a sheet modal. Prior to this point, the\n         * backdrop will be hidden and the content underneath\n         * the sheet can be interacted with. This value is exclusive\n         * meaning the backdrop will become active after the value\n         * specified.\n         */\n        this.backdropBreakpoint = 0;\n        /**\n         * The interaction behavior for the sheet modal when the handle is pressed.\n         *\n         * Defaults to `\"none\"`, which  means the modal will not change size or position when the handle is pressed.\n         * Set to `\"cycle\"` to let the modal cycle between available breakpoints when pressed.\n         *\n         * Handle behavior is unavailable when the `handle` property is set to `false` or\n         * when the `breakpoints` property is not set (using a fullscreen or card modal).\n         */\n        this.handleBehavior = 'none';\n        /**\n         * If `true`, the modal will be dismissed when the backdrop is clicked.\n         */\n        this.backdropDismiss = true;\n        /**\n         * If `true`, a backdrop will be displayed behind the modal.\n         * This property controls whether or not the backdrop\n         * darkens the screen when the modal is presented.\n         * It does not control whether or not the backdrop\n         * is active or present in the DOM.\n         */\n        this.showBackdrop = true;\n        /**\n         * If `true`, the modal will animate.\n         */\n        this.animated = true;\n        /**\n         * If `true`, the modal will open. If `false`, the modal will close.\n         * Use this if you need finer grained control over presentation, otherwise\n         * just use the modalController or the `trigger` property.\n         * Note: `isOpen` will not automatically be set back to `false` when\n         * the modal dismisses. You will need to do that in your code.\n         */\n        this.isOpen = false;\n        /**\n         * If `true`, the component passed into `ion-modal` will\n         * automatically be mounted when the modal is created. The\n         * component will remain mounted even when the modal is dismissed.\n         * However, the component will be destroyed when the modal is\n         * destroyed. This property is not reactive and should only be\n         * used when initially creating a modal.\n         *\n         * Note: This feature only applies to inline modals in JavaScript\n         * frameworks such as Angular, React, and Vue.\n         */\n        this.keepContentsMounted = false;\n        /**\n         * If `true`, focus will not be allowed to move outside of this overlay.\n         * If `false`, focus will be allowed to move outside of the overlay.\n         *\n         * In most scenarios this property should remain set to `true`. Setting\n         * this property to `false` can cause severe accessibility issues as users\n         * relying on assistive technologies may be able to move focus into\n         * a confusing state. We recommend only setting this to `false` when\n         * absolutely necessary.\n         *\n         * Developers may want to consider disabling focus trapping if this\n         * overlay presents a non-Ionic overlay from a 3rd party library.\n         * Developers would disable focus trapping on the Ionic overlay\n         * when presenting the 3rd party overlay and then re-enable\n         * focus trapping when dismissing the 3rd party overlay and moving\n         * focus back to the Ionic overlay.\n         */\n        this.focusTrap = true;\n        /**\n         * Determines whether or not a modal can dismiss\n         * when calling the `dismiss` method.\n         *\n         * If the value is `true` or the value's function returns `true`, the modal will close when trying to dismiss.\n         * If the value is `false` or the value's function returns `false`, the modal will not close when trying to dismiss.\n         *\n         * See https://ionicframework.com/docs/troubleshooting/runtime#accessing-this\n         * if you need to access `this` from within the callback.\n         */\n        this.canDismiss = true;\n        this.onHandleClick = () => {\n            const { sheetTransition, handleBehavior } = this;\n            if (handleBehavior !== 'cycle' || sheetTransition !== undefined) {\n                /**\n                 * The sheet modal should not advance to the next breakpoint\n                 * if the handle behavior is not `cycle` or if the handle\n                 * is clicked while the sheet is moving to a breakpoint.\n                 */\n                return;\n            }\n            this.moveToNextBreakpoint();\n        };\n        this.onBackdropTap = () => {\n            const { sheetTransition } = this;\n            if (sheetTransition !== undefined) {\n                /**\n                 * When the handle is double clicked at the largest breakpoint,\n                 * it will start to move to the first breakpoint. While transitioning,\n                 * the backdrop will often receive the second click. We prevent the\n                 * backdrop from dismissing the modal while moving between breakpoints.\n                 */\n                return;\n            }\n            this.dismiss(undefined, BACKDROP);\n        };\n        this.onLifecycle = (modalEvent) => {\n            const el = this.usersElement;\n            const name = LIFECYCLE_MAP[modalEvent.type];\n            if (el && name) {\n                const ev = new CustomEvent(name, {\n                    bubbles: false,\n                    cancelable: false,\n                    detail: modalEvent.detail,\n                });\n                el.dispatchEvent(ev);\n            }\n        };\n        /**\n         * When the modal receives focus directly, pass focus to the handle\n         * if it exists and is focusable, otherwise let the focus trap handle it.\n         */\n        this.onModalFocus = (ev) => {\n            const { dragHandleEl, el } = this;\n            // Only handle focus if the modal itself was focused (not a child element)\n            if (ev.target === el && dragHandleEl && dragHandleEl.tabIndex !== -1) {\n                dragHandleEl.focus();\n            }\n        };\n        /**\n         * When the slot changes, we need to find all the modals in the slot\n         * and set the data-parent-ion-modal attribute on them so we can find them\n         * and dismiss them when we get dismissed.\n         * We need to do it this way because when a modal is opened, it's moved to\n         * the end of the body and is no longer an actual child of the modal.\n         */\n        this.onSlotChange = ({ target }) => {\n            const slot = target;\n            slot.assignedElements().forEach((el) => {\n                el.querySelectorAll('ion-modal').forEach((childModal) => {\n                    // We don't need to write to the DOM if the modal is already tagged\n                    // If this is a deeply nested modal, this effect should cascade so we don't\n                    // need to worry about another modal claiming the same child.\n                    if (childModal.getAttribute('data-parent-ion-modal') === null) {\n                        childModal.setAttribute('data-parent-ion-modal', this.el.id);\n                    }\n                });\n            });\n        };\n    }\n    onIsOpenChange(newValue, oldValue) {\n        if (newValue === true && oldValue === false) {\n            this.present();\n        }\n        else if (newValue === false && oldValue === true) {\n            this.dismiss();\n        }\n    }\n    triggerChanged() {\n        const { trigger, el, triggerController } = this;\n        if (trigger) {\n            triggerController.addClickListener(el, trigger);\n        }\n    }\n    onWindowResize() {\n        // Only handle resize for iOS card modals when no custom animations are provided\n        if (getIonMode(this) !== 'ios' || !this.presentingElement || this.enterAnimation || this.leaveAnimation) {\n            return;\n        }\n        clearTimeout(this.resizeTimeout);\n        this.resizeTimeout = setTimeout(() => {\n            this.handleViewTransition();\n        }, 50); // Debounce to avoid excessive calls during active resizing\n    }\n    breakpointsChanged(breakpoints) {\n        if (breakpoints !== undefined) {\n            this.sortedBreakpoints = breakpoints.sort((a, b) => a - b);\n        }\n    }\n    connectedCallback() {\n        const { el } = this;\n        prepareOverlay(el);\n        this.triggerChanged();\n    }\n    disconnectedCallback() {\n        this.triggerController.removeClickListener();\n        this.cleanupViewTransitionListener();\n        this.cleanupParentRemovalObserver();\n    }\n    componentWillLoad() {\n        var _a;\n        const { breakpoints, initialBreakpoint, el, htmlAttributes } = this;\n        const isSheetModal = (this.isSheetModal = breakpoints !== undefined && initialBreakpoint !== undefined);\n        const attributesToInherit = ['aria-label', 'role'];\n        this.inheritedAttributes = inheritAttributes(el, attributesToInherit);\n        // Cache original parent before modal gets moved to body during presentation\n        if (el.parentNode) {\n            this.cachedOriginalParent = el.parentNode;\n        }\n        /**\n         * When using a controller modal you can set attributes\n         * using the htmlAttributes property. Since the above attributes\n         * need to be inherited inside of the modal, we need to look\n         * and see if these attributes are being set via htmlAttributes.\n         *\n         * We could alternatively move this to componentDidLoad to simplify the work\n         * here, but we'd then need to make inheritedAttributes a State variable,\n         * thus causing another render to always happen after the first render.\n         */\n        if (htmlAttributes !== undefined) {\n            attributesToInherit.forEach((attribute) => {\n                const attributeValue = htmlAttributes[attribute];\n                if (attributeValue) {\n                    /**\n                     * If an attribute we need to inherit was\n                     * set using htmlAttributes then add it to\n                     * inheritedAttributes and remove it from htmlAttributes.\n                     * This ensures the attribute is inherited and not\n                     * set on the host.\n                     *\n                     * In this case, if an inherited attribute is set\n                     * on the host element and using htmlAttributes then\n                     * htmlAttributes wins, but that's not a pattern that we recommend.\n                     * The only time you'd need htmlAttributes is when using modalController.\n                     */\n                    this.inheritedAttributes = Object.assign(Object.assign({}, this.inheritedAttributes), { [attribute]: htmlAttributes[attribute] });\n                    delete htmlAttributes[attribute];\n                }\n            });\n        }\n        if (isSheetModal) {\n            this.currentBreakpoint = this.initialBreakpoint;\n        }\n        if (breakpoints !== undefined && initialBreakpoint !== undefined && !breakpoints.includes(initialBreakpoint)) {\n            printIonWarning('[ion-modal] - Your breakpoints array must include the initialBreakpoint value.');\n        }\n        if (!((_a = this.htmlAttributes) === null || _a === void 0 ? void 0 : _a.id)) {\n            setOverlayId(this.el);\n        }\n    }\n    componentDidLoad() {\n        /**\n         * If modal was rendered with isOpen=\"true\"\n         * then we should open modal immediately.\n         */\n        if (this.isOpen === true) {\n            raf(() => this.present());\n        }\n        this.breakpointsChanged(this.breakpoints);\n        /**\n         * When binding values in frameworks such as Angular\n         * it is possible for the value to be set after the Web Component\n         * initializes but before the value watcher is set up in Stencil.\n         * As a result, the watcher callback may not be fired.\n         * We work around this by manually calling the watcher\n         * callback when the component has loaded and the watcher\n         * is configured.\n         */\n        this.triggerChanged();\n    }\n    /**\n     * Determines whether or not an overlay\n     * is being used inline or via a controller/JS\n     * and returns the correct delegate.\n     * By default, subsequent calls to getDelegate\n     * will use a cached version of the delegate.\n     * This is useful for calling dismiss after\n     * present so that the correct delegate is given.\n     */\n    getDelegate(force = false) {\n        if (this.workingDelegate && !force) {\n            return {\n                delegate: this.workingDelegate,\n                inline: this.inline,\n            };\n        }\n        /**\n         * If using overlay inline\n         * we potentially need to use the coreDelegate\n         * so that this works in vanilla JS apps.\n         * If a developer has presented this component\n         * via a controller, then we can assume\n         * the component is already in the\n         * correct place.\n         */\n        const parentEl = this.el.parentNode;\n        const inline = (this.inline = parentEl !== null && !this.hasController);\n        const delegate = (this.workingDelegate = inline ? this.delegate || this.coreDelegate : this.delegate);\n        return { inline, delegate };\n    }\n    /**\n     * Determines whether or not the\n     * modal is allowed to dismiss based\n     * on the state of the canDismiss prop.\n     */\n    async checkCanDismiss(data, role) {\n        const { canDismiss } = this;\n        if (typeof canDismiss === 'function') {\n            return canDismiss(data, role);\n        }\n        return canDismiss;\n    }\n    /**\n     * Present the modal overlay after it has been created.\n     */\n    async present() {\n        const unlock = await this.lockController.lock();\n        if (this.presented) {\n            unlock();\n            return;\n        }\n        const { presentingElement, el } = this;\n        /**\n         * If the modal is presented multiple times (inline modals), we\n         * need to reset the current breakpoint to the initial breakpoint.\n         */\n        this.currentBreakpoint = this.initialBreakpoint;\n        const { inline, delegate } = this.getDelegate(true);\n        /**\n         * Emit ionMount so JS Frameworks have an opportunity\n         * to add the child component to the DOM. The child\n         * component will be assigned to this.usersElement below.\n         */\n        this.ionMount.emit();\n        this.usersElement = await attachComponent(delegate, el, this.component, ['ion-page'], this.componentProps, inline);\n        /**\n         * When using the lazy loaded build of Stencil, we need to wait\n         * for every Stencil component instance to be ready before presenting\n         * otherwise there can be a flash of unstyled content. With the\n         * custom elements bundle we need to wait for the JS framework\n         * mount the inner contents of the overlay otherwise WebKit may\n         * get the transition incorrect.\n         */\n        if (hasLazyBuild(el)) {\n            await deepReady(this.usersElement);\n            /**\n             * If keepContentsMounted=\"true\" then the\n             * JS Framework has already mounted the inner\n             * contents so there is no need to wait.\n             * Otherwise, we need to wait for the JS\n             * Framework to mount the inner contents\n             * of this component.\n             */\n        }\n        else if (!this.keepContentsMounted) {\n            await waitForMount();\n        }\n        writeTask(() => this.el.classList.add('show-modal'));\n        const hasCardModal = presentingElement !== undefined;\n        /**\n         * We need to change the status bar at the\n         * start of the animation so that it completes\n         * by the time the card animation is done.\n         */\n        if (hasCardModal && getIonMode(this) === 'ios') {\n            // Cache the original status bar color before the modal is presented\n            this.statusBarStyle = await StatusBar.getStyle();\n            setCardStatusBarDark();\n        }\n        await present(this, 'modalEnter', iosEnterAnimation, mdEnterAnimation, {\n            presentingEl: presentingElement,\n            currentBreakpoint: this.initialBreakpoint,\n            backdropBreakpoint: this.backdropBreakpoint,\n            expandToScroll: this.expandToScroll,\n        });\n        /* tslint:disable-next-line */\n        if (typeof window !== 'undefined') {\n            /**\n             * This needs to be setup before any\n             * non-transition async work so it can be dereferenced\n             * in the dismiss method. The dismiss method\n             * only waits for the entering transition\n             * to finish. It does not wait for all of the `present`\n             * method to resolve.\n             */\n            this.keyboardOpenCallback = () => {\n                if (this.gesture) {\n                    /**\n                     * When the native keyboard is opened and the webview\n                     * is resized, the gesture implementation will become unresponsive\n                     * and enter a free-scroll mode.\n                     *\n                     * When the keyboard is opened, we disable the gesture for\n                     * a single frame and re-enable once the contents have repositioned\n                     * from the keyboard placement.\n                     */\n                    this.gesture.enable(false);\n                    raf(() => {\n                        if (this.gesture) {\n                            this.gesture.enable(true);\n                        }\n                    });\n                }\n            };\n            window.addEventListener(KEYBOARD_DID_OPEN, this.keyboardOpenCallback);\n        }\n        if (this.isSheetModal) {\n            this.initSheetGesture();\n        }\n        else if (hasCardModal) {\n            this.initSwipeToClose();\n        }\n        // Initialize view transition listener for iOS card modals\n        this.initViewTransitionListener();\n        // Initialize parent removal observer\n        this.initParentRemovalObserver();\n        unlock();\n    }\n    initSwipeToClose() {\n        var _a;\n        if (getIonMode(this) !== 'ios') {\n            return;\n        }\n        const { el } = this;\n        // All of the elements needed for the swipe gesture\n        // should be in the DOM and referenced by now, except\n        // for the presenting el\n        const animationBuilder = this.leaveAnimation || config.get('modalLeave', iosLeaveAnimation);\n        const ani = (this.animation = animationBuilder(el, {\n            presentingEl: this.presentingElement,\n            expandToScroll: this.expandToScroll,\n        }));\n        const contentEl = findIonContent(el);\n        if (!contentEl) {\n            printIonContentErrorMsg(el);\n            return;\n        }\n        const statusBarStyle = (_a = this.statusBarStyle) !== null && _a !== void 0 ? _a : Style.Default;\n        this.gesture = createSwipeToCloseGesture(el, ani, statusBarStyle, () => {\n            /**\n             * While the gesture animation is finishing\n             * it is possible for a user to tap the backdrop.\n             * This would result in the dismiss animation\n             * being played again. Typically this is avoided\n             * by setting `presented = false` on the overlay\n             * component; however, we cannot do that here as\n             * that would prevent the element from being\n             * removed from the DOM.\n             */\n            this.gestureAnimationDismissing = true;\n            /**\n             * Reset the status bar style as the dismiss animation\n             * starts otherwise the status bar will be the wrong\n             * color for the duration of the dismiss animation.\n             * The dismiss method does this as well, but\n             * in this case it's only called once the animation\n             * has finished.\n             */\n            setCardStatusBarDefault(this.statusBarStyle);\n            this.animation.onFinish(async () => {\n                await this.dismiss(undefined, GESTURE);\n                this.gestureAnimationDismissing = false;\n            });\n        });\n        this.gesture.enable(true);\n    }\n    initSheetGesture() {\n        const { wrapperEl, initialBreakpoint, backdropBreakpoint } = this;\n        if (!wrapperEl || initialBreakpoint === undefined) {\n            return;\n        }\n        const animationBuilder = this.enterAnimation || config.get('modalEnter', iosEnterAnimation);\n        const ani = (this.animation = animationBuilder(this.el, {\n            presentingEl: this.presentingElement,\n            currentBreakpoint: initialBreakpoint,\n            backdropBreakpoint,\n            expandToScroll: this.expandToScroll,\n        }));\n        ani.progressStart(true, 1);\n        const { gesture, moveSheetToBreakpoint } = createSheetGesture(this.el, this.backdropEl, wrapperEl, initialBreakpoint, backdropBreakpoint, ani, this.sortedBreakpoints, this.expandToScroll, () => { var _a; return (_a = this.currentBreakpoint) !== null && _a !== void 0 ? _a : 0; }, () => this.sheetOnDismiss(), (breakpoint) => {\n            if (this.currentBreakpoint !== breakpoint) {\n                this.currentBreakpoint = breakpoint;\n                this.ionBreakpointDidChange.emit({ breakpoint });\n            }\n        });\n        this.gesture = gesture;\n        this.moveSheetToBreakpoint = moveSheetToBreakpoint;\n        this.gesture.enable(true);\n    }\n    sheetOnDismiss() {\n        /**\n         * While the gesture animation is finishing\n         * it is possible for a user to tap the backdrop.\n         * This would result in the dismiss animation\n         * being played again. Typically this is avoided\n         * by setting `presented = false` on the overlay\n         * component; however, we cannot do that here as\n         * that would prevent the element from being\n         * removed from the DOM.\n         */\n        this.gestureAnimationDismissing = true;\n        this.animation.onFinish(async () => {\n            this.currentBreakpoint = 0;\n            this.ionBreakpointDidChange.emit({ breakpoint: this.currentBreakpoint });\n            await this.dismiss(undefined, GESTURE);\n            this.gestureAnimationDismissing = false;\n        });\n    }\n    /**\n     * Dismiss the modal overlay after it has been presented.\n     * This is a no-op if the overlay has not been presented yet. If you want\n     * to remove an overlay from the DOM that was never presented, use the\n     * [remove](https://developer.mozilla.org/en-US/docs/Web/API/Element/remove) method.\n     *\n     * @param data Any data to emit in the dismiss events.\n     * @param role The role of the element that is dismissing the modal.\n     * For example, `cancel` or `backdrop`.\n     */\n    async dismiss(data, role) {\n        var _a;\n        if (this.gestureAnimationDismissing && role !== GESTURE) {\n            return false;\n        }\n        /**\n         * Because the canDismiss check below is async,\n         * we need to claim a lock before the check happens,\n         * in case the dismiss transition does run.\n         */\n        const unlock = await this.lockController.lock();\n        /**\n         * Dismiss all child modals. This is especially important in\n         * Angular and React because it's possible to lose control of a child\n         * modal when the parent modal is dismissed.\n         */\n        await this.dismissNestedModals();\n        /**\n         * If a canDismiss handler is responsible\n         * for calling the dismiss method, we should\n         * not run the canDismiss check again.\n         */\n        if (role !== 'handler' && !(await this.checkCanDismiss(data, role))) {\n            unlock();\n            return false;\n        }\n        const { presentingElement } = this;\n        /**\n         * We need to start the status bar change\n         * before the animation so that the change\n         * finishes when the dismiss animation does.\n         */\n        const hasCardModal = presentingElement !== undefined;\n        if (hasCardModal && getIonMode(this) === 'ios') {\n            setCardStatusBarDefault(this.statusBarStyle);\n        }\n        /* tslint:disable-next-line */\n        if (typeof window !== 'undefined' && this.keyboardOpenCallback) {\n            window.removeEventListener(KEYBOARD_DID_OPEN, this.keyboardOpenCallback);\n            this.keyboardOpenCallback = undefined;\n        }\n        const dismissed = await dismiss(this, data, role, 'modalLeave', iosLeaveAnimation, mdLeaveAnimation, {\n            presentingEl: presentingElement,\n            currentBreakpoint: (_a = this.currentBreakpoint) !== null && _a !== void 0 ? _a : this.initialBreakpoint,\n            backdropBreakpoint: this.backdropBreakpoint,\n            expandToScroll: this.expandToScroll,\n        });\n        if (dismissed) {\n            const { delegate } = this.getDelegate();\n            await detachComponent(delegate, this.usersElement);\n            writeTask(() => this.el.classList.remove('show-modal'));\n            if (this.animation) {\n                this.animation.destroy();\n            }\n            if (this.gesture) {\n                this.gesture.destroy();\n            }\n            this.cleanupViewTransitionListener();\n            this.cleanupParentRemovalObserver();\n        }\n        this.currentBreakpoint = undefined;\n        this.animation = undefined;\n        unlock();\n        return dismissed;\n    }\n    /**\n     * Returns a promise that resolves when the modal did dismiss.\n     */\n    onDidDismiss() {\n        return eventMethod(this.el, 'ionModalDidDismiss');\n    }\n    /**\n     * Returns a promise that resolves when the modal will dismiss.\n     */\n    onWillDismiss() {\n        return eventMethod(this.el, 'ionModalWillDismiss');\n    }\n    /**\n     * Move a sheet style modal to a specific breakpoint.\n     *\n     * @param breakpoint The breakpoint value to move the sheet modal to.\n     * Must be a value defined in your `breakpoints` array.\n     */\n    async setCurrentBreakpoint(breakpoint) {\n        if (!this.isSheetModal) {\n            printIonWarning('[ion-modal] - setCurrentBreakpoint is only supported on sheet modals.');\n            return;\n        }\n        if (!this.breakpoints.includes(breakpoint)) {\n            printIonWarning(`[ion-modal] - Attempted to set invalid breakpoint value ${breakpoint}. Please double check that the breakpoint value is part of your defined breakpoints.`);\n            return;\n        }\n        const { currentBreakpoint, moveSheetToBreakpoint, canDismiss, breakpoints, animated } = this;\n        if (currentBreakpoint === breakpoint) {\n            return;\n        }\n        if (moveSheetToBreakpoint) {\n            this.sheetTransition = moveSheetToBreakpoint({\n                breakpoint,\n                breakpointOffset: 1 - currentBreakpoint,\n                canDismiss: canDismiss !== undefined && canDismiss !== true && breakpoints[0] === 0,\n                animated,\n            });\n            await this.sheetTransition;\n            this.sheetTransition = undefined;\n        }\n    }\n    /**\n     * Returns the current breakpoint of a sheet style modal\n     */\n    async getCurrentBreakpoint() {\n        return this.currentBreakpoint;\n    }\n    async moveToNextBreakpoint() {\n        const { breakpoints, currentBreakpoint } = this;\n        if (!breakpoints || currentBreakpoint == null) {\n            /**\n             * If the modal does not have breakpoints and/or the current\n             * breakpoint is not set, we can't move to the next breakpoint.\n             */\n            return false;\n        }\n        const allowedBreakpoints = breakpoints.filter((b) => b !== 0);\n        const currentBreakpointIndex = allowedBreakpoints.indexOf(currentBreakpoint);\n        const nextBreakpointIndex = (currentBreakpointIndex + 1) % allowedBreakpoints.length;\n        const nextBreakpoint = allowedBreakpoints[nextBreakpointIndex];\n        /**\n         * Sets the current breakpoint to the next available breakpoint.\n         * If the current breakpoint is the last breakpoint, we set the current\n         * breakpoint to the first non-zero breakpoint to avoid dismissing the sheet.\n         */\n        await this.setCurrentBreakpoint(nextBreakpoint);\n        return true;\n    }\n    initViewTransitionListener() {\n        // Only enable for iOS card modals when no custom animations are provided\n        if (getIonMode(this) !== 'ios' || !this.presentingElement || this.enterAnimation || this.leaveAnimation) {\n            return;\n        }\n        // Set initial view state\n        this.currentViewIsPortrait = window.innerWidth < 768;\n    }\n    handleViewTransition() {\n        const isPortrait = window.innerWidth < 768;\n        // Only transition if view state actually changed\n        if (this.currentViewIsPortrait === isPortrait) {\n            return;\n        }\n        // Cancel any ongoing transition animation\n        if (this.viewTransitionAnimation) {\n            this.viewTransitionAnimation.destroy();\n            this.viewTransitionAnimation = undefined;\n        }\n        const { presentingElement } = this;\n        if (!presentingElement) {\n            return;\n        }\n        // Create transition animation\n        let transitionAnimation;\n        if (this.currentViewIsPortrait && !isPortrait) {\n            // Portrait to landscape transition\n            transitionAnimation = portraitToLandscapeTransition(this.el, {\n                presentingEl: presentingElement});\n        }\n        else {\n            // Landscape to portrait transition\n            transitionAnimation = landscapeToPortraitTransition(this.el, {\n                presentingEl: presentingElement});\n        }\n        // Update state and play animation\n        this.currentViewIsPortrait = isPortrait;\n        this.viewTransitionAnimation = transitionAnimation;\n        transitionAnimation.play().then(() => {\n            this.viewTransitionAnimation = undefined;\n            // After orientation transition, recreate the swipe-to-close gesture\n            // with updated animation that reflects the new presenting element state\n            this.reinitSwipeToClose();\n        });\n    }\n    cleanupViewTransitionListener() {\n        // Clear any pending resize timeout\n        if (this.resizeTimeout) {\n            clearTimeout(this.resizeTimeout);\n            this.resizeTimeout = undefined;\n        }\n        if (this.viewTransitionAnimation) {\n            this.viewTransitionAnimation.destroy();\n            this.viewTransitionAnimation = undefined;\n        }\n    }\n    reinitSwipeToClose() {\n        // Only reinitialize if we have a presenting element and are on iOS\n        if (getIonMode(this) !== 'ios' || !this.presentingElement) {\n            return;\n        }\n        // Clean up existing gesture and animation\n        if (this.gesture) {\n            this.gesture.destroy();\n            this.gesture = undefined;\n        }\n        if (this.animation) {\n            // Properly end the progress-based animation at initial state before destroying\n            // to avoid leaving modal in intermediate swipe position\n            this.animation.progressEnd(0, 0, 0);\n            this.animation.destroy();\n            this.animation = undefined;\n        }\n        // Force the modal back to the correct position or it could end up\n        // in a weird state after destroying the animation\n        raf(() => {\n            this.ensureCorrectModalPosition();\n            this.initSwipeToClose();\n        });\n    }\n    ensureCorrectModalPosition() {\n        const { el, presentingElement } = this;\n        const root = getElementRoot(el);\n        const wrapperEl = root.querySelector('.modal-wrapper');\n        if (wrapperEl) {\n            wrapperEl.style.transform = 'translateY(0vh)';\n            wrapperEl.style.opacity = '1';\n        }\n        if ((presentingElement === null || presentingElement === void 0 ? void 0 : presentingElement.tagName) === 'ION-MODAL') {\n            const isPortrait = window.innerWidth < 768;\n            if (isPortrait) {\n                const transformOffset = !CSS.supports('width', 'max(0px, 1px)')\n                    ? '30px'\n                    : 'max(30px, var(--ion-safe-area-top))';\n                const scale = SwipeToCloseDefaults.MIN_PRESENTING_SCALE;\n                presentingElement.style.transform = `translateY(${transformOffset}) scale(${scale})`;\n            }\n            else {\n                presentingElement.style.transform = 'translateY(0px) scale(1)';\n            }\n        }\n    }\n    async dismissNestedModals() {\n        const nestedModals = document.querySelectorAll(`ion-modal[data-parent-ion-modal=\"${this.el.id}\"]`);\n        nestedModals === null || nestedModals === void 0 ? void 0 : nestedModals.forEach(async (modal) => {\n            await modal.dismiss(undefined, 'parent-dismissed');\n        });\n    }\n    initParentRemovalObserver() {\n        if (typeof MutationObserver === 'undefined') {\n            return;\n        }\n        // Only observe if we have a cached parent and are in browser environment\n        if (typeof window === 'undefined' || !this.cachedOriginalParent) {\n            return;\n        }\n        // Don't observe document or fragment nodes as they can't be \"removed\"\n        if (this.cachedOriginalParent.nodeType === Node.DOCUMENT_NODE ||\n            this.cachedOriginalParent.nodeType === Node.DOCUMENT_FRAGMENT_NODE) {\n            return;\n        }\n        this.parentRemovalObserver = new MutationObserver((mutations) => {\n            mutations.forEach((mutation) => {\n                if (mutation.type === 'childList' && mutation.removedNodes.length > 0) {\n                    // Check if our cached original parent was removed\n                    const cachedParentWasRemoved = Array.from(mutation.removedNodes).some((node) => {\n                        var _a, _b;\n                        const isDirectMatch = node === this.cachedOriginalParent;\n                        const isContainedMatch = this.cachedOriginalParent\n                            ? (_b = (_a = node).contains) === null || _b === void 0 ? void 0 : _b.call(_a, this.cachedOriginalParent)\n                            : false;\n                        return isDirectMatch || isContainedMatch;\n                    });\n                    // Also check if parent is no longer connected to DOM\n                    const cachedParentDisconnected = this.cachedOriginalParent && !this.cachedOriginalParent.isConnected;\n                    if (cachedParentWasRemoved || cachedParentDisconnected) {\n                        this.dismiss(undefined, 'parent-removed');\n                        // Release the reference to the cached original parent\n                        // so we don't have a memory leak\n                        this.cachedOriginalParent = undefined;\n                    }\n                }\n            });\n        });\n        // Observe document body with subtree to catch removals at any level\n        this.parentRemovalObserver.observe(document.body, {\n            childList: true,\n            subtree: true,\n        });\n    }\n    cleanupParentRemovalObserver() {\n        var _a;\n        (_a = this.parentRemovalObserver) === null || _a === void 0 ? void 0 : _a.disconnect();\n        this.parentRemovalObserver = undefined;\n    }\n    render() {\n        const { handle, isSheetModal, presentingElement, htmlAttributes, handleBehavior, inheritedAttributes, focusTrap, expandToScroll, } = this;\n        const showHandle = handle !== false && isSheetModal;\n        const mode = getIonMode(this);\n        const isCardModal = presentingElement !== undefined && mode === 'ios';\n        const isHandleCycle = handleBehavior === 'cycle';\n        const isSheetModalWithHandle = isSheetModal && showHandle;\n        return (h(Host, Object.assign({ key: '9e9a7bd591eb17a225a00b4fa2e379e94601d17f', \"no-router\": true,\n            // Allow the modal to be navigable when the handle is focusable\n            tabIndex: isHandleCycle && isSheetModalWithHandle ? 0 : -1 }, htmlAttributes, { style: {\n                zIndex: `${20000 + this.overlayIndex}`,\n            }, class: Object.assign({ [mode]: true, ['modal-default']: !isCardModal && !isSheetModal, [`modal-card`]: isCardModal, [`modal-sheet`]: isSheetModal, [`modal-no-expand-scroll`]: isSheetModal && !expandToScroll, 'overlay-hidden': true, [FOCUS_TRAP_DISABLE_CLASS]: focusTrap === false }, getClassMap(this.cssClass)), onIonBackdropTap: this.onBackdropTap, onIonModalDidPresent: this.onLifecycle, onIonModalWillPresent: this.onLifecycle, onIonModalWillDismiss: this.onLifecycle, onIonModalDidDismiss: this.onLifecycle, onFocus: this.onModalFocus }), h(\"ion-backdrop\", { key: 'e5eae2c14f830f75e308fcd7f4c10c86fac5b962', ref: (el) => (this.backdropEl = el), visible: this.showBackdrop, tappable: this.backdropDismiss, part: \"backdrop\" }), mode === 'ios' && h(\"div\", { key: 'e268f9cd310c3cf4e051b5b92524ce4fb70d005e', class: \"modal-shadow\" }), h(\"div\", Object.assign({ key: '9c380f36c18144c153077b15744d1c3346bce63e',\n            /*\n              role and aria-modal must be used on the\n              same element. They must also be set inside the\n              shadow DOM otherwise ion-button will not be highlighted\n              when using VoiceOver: https://bugs.webkit.org/show_bug.cgi?id=247134\n            */\n            role: \"dialog\" }, inheritedAttributes, { \"aria-modal\": \"true\", class: \"modal-wrapper ion-overlay-wrapper\", part: \"content\", ref: (el) => (this.wrapperEl = el) }), showHandle && (h(\"button\", { key: '2d5ee6d5959d97309c306e8ce72eb0f2c19be144', class: \"modal-handle\",\n            // Prevents the handle from receiving keyboard focus when it does not cycle\n            tabIndex: !isHandleCycle ? -1 : 0, \"aria-label\": \"Activate to adjust the size of the dialog overlaying the screen\", onClick: isHandleCycle ? this.onHandleClick : undefined, part: \"handle\", ref: (el) => (this.dragHandleEl = el) })), h(\"slot\", { key: '5590434c35ea04c42fc006498bc189038e15a298', onSlotchange: this.onSlotChange }))));\n    }\n    get el() { return getElement(this); }\n    static get watchers() { return {\n        \"isOpen\": [\"onIsOpenChange\"],\n        \"trigger\": [\"triggerChanged\"]\n    }; }\n};\nconst LIFECYCLE_MAP = {\n    ionModalDidPresent: 'ionViewDidEnter',\n    ionModalWillPresent: 'ionViewWillEnter',\n    ionModalWillDismiss: 'ionViewWillLeave',\n    ionModalDidDismiss: 'ionViewDidLeave',\n};\nModal.style = {\n    ios: modalIosCss,\n    md: modalMdCss\n};\n\nexport { Modal as ion_modal };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAqBA,IAAI;AAAA,CACH,SAAUA,QAAO;AACd,EAAAA,OAAM,MAAM,IAAI;AAChB,EAAAA,OAAM,OAAO,IAAI;AACjB,EAAAA,OAAM,SAAS,IAAI;AACvB,GAAG,UAAU,QAAQ,CAAC,EAAE;AACxB,IAAM,YAAY;AAAA,EACd,YAAY;AACR,UAAM,YAAY,aAAa;AAC/B,QAAI,cAAc,QAAQ,cAAc,SAAS,SAAS,UAAU,kBAAkB,WAAW,GAAG;AAChG,aAAO,UAAU,QAAQ;AAAA,IAC7B;AACA,WAAO;AAAA,EACX;AAAA,EACA,SAAS,SAAS;AACd,UAAM,SAAS,KAAK,UAAU;AAC9B,QAAI,CAAC,QAAQ;AACT;AAAA,IACJ;AACA,WAAO,SAAS,OAAO;AAAA,EAC3B;AAAA,EACA,UAAU,WAAkB;AAAA;AACxB,YAAM,SAAS,KAAK,UAAU;AAC9B,UAAI,CAAC,QAAQ;AACT,eAAO,MAAM;AAAA,MACjB;AACA,YAAM,EAAE,MAAM,IAAI,MAAM,OAAO,QAAQ;AACvC,aAAO;AAAA,IACX;AAAA;AACJ;AAUA,IAAM,2BAA2B,CAAC,GAAG,uBAAuB;AAsBxD,MAAI,uBAAuB,GAAG;AAC1B,WAAO;AAAA,EACX;AACA,QAAM,QAAQ,KAAK,IAAI;AAcvB,QAAM,IAAI,EAAE,qBAAqB;AAMjC,SAAO,IAAI,QAAQ;AACvB;AASA,IAAM,uBAAuB,MAAM;AAC/B,MAAI,CAAC,OAAO,IAAI,cAAc,KAAK;AAC/B;AAAA,EACJ;AACA,YAAU,SAAS,EAAE,OAAO,MAAM,KAAK,CAAC;AAC5C;AACA,IAAM,0BAA0B,CAAC,eAAe,MAAM,YAAY;AAC9D,MAAI,CAAC,OAAO,IAAI,cAAc,KAAK;AAC/B;AAAA,EACJ;AACA,YAAU,SAAS,EAAE,OAAO,aAAa,CAAC;AAC9C;AAEA,IAAM,mBAAmB,CAAO,IAAI,cAAc;AAS9C,MAAI,OAAO,GAAG,eAAe,YAAY;AACrC;AAAA,EACJ;AAMA,QAAM,gBAAgB,MAAM,GAAG,WAAW,QAAW,OAAO;AAC5D,MAAI,CAAC,eAAe;AAChB;AAAA,EACJ;AAWA,MAAI,UAAU,UAAU,GAAG;AACvB,cAAU,SAAS,MAAM;AACrB,SAAG,QAAQ,QAAW,SAAS;AAAA,IACnC,GAAG,EAAE,iBAAiB,KAAK,CAAC;AAAA,EAChC,OACK;AACD,OAAG,QAAQ,QAAW,SAAS;AAAA,EACnC;AACJ;AAyEA,IAAM,sBAAsB,CAAC,MAAM;AAC/B,SAAO,YAAa,YAAY,WAAW,KAAK,UAAU,YAAY,aAAa,KAAK;AAC5F;AAGA,IAAM,uBAAuB;AAAA,EACzB,sBAAsB;AAC1B;AACA,IAAM,4BAA4B,CAAC,IAAI,WAAW,gBAAgB,cAAc;AAK5E,QAAM,oBAAoB;AAC1B,QAAM,SAAS,GAAG;AAClB,MAAI,SAAS;AACb,MAAI,0BAA0B;AAC9B,MAAI,YAAY;AAChB,MAAI,WAAW;AACf,QAAM,oBAAoB;AAC1B,MAAI,iBAAiB;AACrB,MAAI,WAAW;AACf,QAAM,aAAa,MAAM;AACrB,QAAI,aAAa,aAAa,SAAS,GAAG;AACtC,aAAO,UAAU;AAAA,IAMrB,OACK;AACD,aAAO;AAAA,IACX;AAAA,EACJ;AACA,QAAM,WAAW,CAAC,WAAW;AACzB,UAAM,SAAS,OAAO,MAAM;AAC5B,QAAI,WAAW,QAAQ,CAAC,OAAO,SAAS;AACpC,aAAO;AAAA,IACX;AAgBA,gBAAY,sBAAsB,MAAM;AACxC,QAAI,WAAW;AAeX,UAAI,aAAa,SAAS,GAAG;AACzB,cAAM,OAAO,eAAe,SAAS;AACrC,mBAAW,KAAK,cAAc,eAAe;AAAA,MACjD,OACK;AACD,mBAAW;AAAA,MACf;AACA,YAAM,wBAAwB,CAAC,CAAC,UAAU,cAAc,eAAe;AACvE,aAAO,CAAC,yBAAyB,SAAS,cAAc;AAAA,IAC5D;AAKA,UAAM,SAAS,OAAO,QAAQ,YAAY;AAC1C,QAAI,WAAW,MAAM;AACjB,aAAO;AAAA,IACX;AACA,WAAO;AAAA,EACX;AACA,QAAM,UAAU,CAAC,WAAW;AACxB,UAAM,EAAE,OAAO,IAAI;AAMnB,qBAAiB,WAAW;AAS5B,8BAA0B,GAAG,eAAe,UAAa,GAAG,eAAe;AAO3E,QAAI,SAAS,KAAK,WAAW;AACzB,4BAAsB,SAAS;AAAA,IACnC;AACA,cAAU,cAAc,MAAM,SAAS,IAAI,CAAC;AAAA,EAChD;AACA,QAAM,SAAS,CAAC,WAAW;AACvB,UAAM,EAAE,OAAO,IAAI;AAOnB,QAAI,SAAS,KAAK,WAAW;AACzB,4BAAsB,SAAS;AAAA,IACnC;AAWA,UAAM,OAAO,OAAO,SAAS;AAO7B,UAAM,oCAAoC,QAAQ,KAAK;AAMvD,UAAM,UAAU,oCAAoC,oBAAoB;AASxE,UAAM,gBAAgB,oCAAoC,oBAAoB,OAAO,OAAO,IAAI;AAChG,UAAM,cAAc,MAAM,MAAQ,eAAe,OAAO;AACxD,cAAU,aAAa,WAAW;AASlC,QAAI,eAAe,qBAAqB,WAAW,mBAAmB;AAClE,8BAAwB,cAAc;AAAA,IAM1C,WACS,cAAc,qBAAqB,YAAY,mBAAmB;AACvE,2BAAqB;AAAA,IACzB;AACA,eAAW;AAAA,EACf;AACA,QAAM,QAAQ,CAAC,WAAW;AACtB,UAAM,WAAW,OAAO;AACxB,UAAM,OAAO,OAAO,SAAS;AAC7B,UAAM,oCAAoC,QAAQ,KAAK;AACvD,UAAM,UAAU,oCAAoC,oBAAoB;AACxE,UAAM,gBAAgB,oCAAoC,oBAAoB,OAAO,OAAO,IAAI;AAChG,UAAM,cAAc,MAAM,MAAQ,eAAe,OAAO;AACxD,UAAM,aAAa,OAAO,SAAS,WAAW,OAAQ;AAOtD,UAAM,iBAAiB,CAAC,qCAAqC,aAAa;AAC1E,QAAI,eAAe,iBAAiB,QAAQ;AAC5C,QAAI,CAAC,gBAAgB;AACjB,gBAAU,OAAO,gCAAgC;AACjD,sBAAgB,wBAAwB,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,MAAM,IAAI,GAAG,CAAC,GAAG,CAAC,GAAG,WAAW,EAAE,CAAC;AAAA,IAChG,OACK;AACD,gBAAU,OAAO,gCAAgC;AACjD,sBAAgB,wBAAwB,CAAC,GAAG,CAAC,GAAG,CAAC,MAAM,IAAI,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,WAAW,EAAE,CAAC;AAAA,IAChG;AACA,UAAM,WAAW,iBACX,gBAAgB,OAAO,QAAQ,QAAQ,IACvC,iBAAiB,IAAI,eAAe,QAAQ,QAAQ;AAC1D,aAAS;AACT,YAAQ,OAAO,KAAK;AACpB,QAAI,WAAW;AACX,0BAAoB,WAAW,cAAc;AAAA,IACjD;AACA,cACK,SAAS,MAAM;AAChB,UAAI,CAAC,gBAAgB;AACjB,gBAAQ,OAAO,IAAI;AAAA,MACvB;AAAA,IACJ,CAAC,EACI,YAAY,iBAAiB,IAAI,GAAG,cAAc,QAAQ;AAa/D,QAAI,qCAAqC,cAAc,UAAU,GAAG;AAChE,uBAAiB,IAAI,SAAS;AAAA,IAClC,WACS,gBAAgB;AACrB,gBAAU;AAAA,IACd;AAAA,EACJ;AACA,QAAM,UAAU,cAAc;AAAA,IAC1B;AAAA,IACA,aAAa;AAAA,IACb,iBAAiB;AAAA,IACjB,WAAW;AAAA,IACX,WAAW;AAAA,IACX;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ,CAAC;AACD,SAAO;AACX;AACA,IAAM,kBAAkB,CAAC,WAAW,aAAa;AAC7C,SAAO,MAAM,KAAK,YAAY,KAAK,IAAI,WAAW,GAAG,GAAG,GAAG;AAC/D;AAEA,IAAM,4BAA4B,CAAC,SAAS;AACxC,QAAM,EAAE,mBAAmB,oBAAoB,eAAe,IAAI;AAMlE,QAAM,qBAAqB,uBAAuB,UAAa,qBAAqB;AACpF,QAAM,kBAAkB,qBAAqB,kCAAkC,iBAAiB,MAAM;AACtG,QAAM,oBAAoB,gBAAgB,mBAAmB,EAAE,OAAO,WAAW,GAAG,eAAe;AACnG,MAAI,oBAAoB;AACpB,sBACK,aAAa;AAAA,MACd,kBAAkB;AAAA,IACtB,CAAC,EACI,iBAAiB,CAAC,gBAAgB,CAAC;AAAA,EAC5C;AACA,QAAM,mBAAmB,gBAAgB,kBAAkB,EAAE,UAAU;AAAA,IACnE,EAAE,QAAQ,GAAG,SAAS,GAAG,WAAW,mBAAmB;AAAA,IACvD,EAAE,QAAQ,GAAG,SAAS,GAAG,WAAW,cAAc,MAAM,oBAAoB,GAAG,KAAK;AAAA,EACxF,CAAC;AAID,QAAM,mBAAmB,CAAC,iBACpB,gBAAgB,kBAAkB,EAAE,UAAU;AAAA,IAC5C,EAAE,QAAQ,GAAG,SAAS,GAAG,WAAW,IAAI,IAAI,qBAAqB,GAAG,IAAI;AAAA,IACxE,EAAE,QAAQ,GAAG,SAAS,GAAG,WAAW,GAAG,oBAAoB,GAAG,IAAI;AAAA,EACtE,CAAC,IACC;AACN,SAAO,EAAE,kBAAkB,mBAAmB,iBAAiB;AACnE;AACA,IAAM,4BAA4B,CAAC,SAAS;AACxC,QAAM,EAAE,mBAAmB,mBAAmB,IAAI;AAMlD,QAAM,gBAAgB,kCAAkC,yBAAyB,mBAAmB,kBAAkB,CAAC;AACvH,QAAM,kBAAkB;AAAA,IACpB,EAAE,QAAQ,GAAG,SAAS,cAAc;AAAA,IACpC,EAAE,QAAQ,GAAG,SAAS,EAAE;AAAA,EAC5B;AACA,QAAM,iBAAiB;AAAA,IACnB,EAAE,QAAQ,GAAG,SAAS,cAAc;AAAA,IACpC,EAAE,QAAQ,oBAAoB,SAAS,EAAE;AAAA,IACzC,EAAE,QAAQ,GAAG,SAAS,EAAE;AAAA,EAC5B;AACA,QAAM,oBAAoB,gBAAgB,mBAAmB,EAAE,UAAU,uBAAuB,IAAI,iBAAiB,eAAe;AACpI,QAAM,mBAAmB,gBAAgB,kBAAkB,EAAE,UAAU;AAAA,IACnE,EAAE,QAAQ,GAAG,SAAS,GAAG,WAAW,cAAc,MAAM,oBAAoB,GAAG,KAAK;AAAA,IACpF,EAAE,QAAQ,GAAG,SAAS,GAAG,WAAW,mBAAmB;AAAA,EAC3D,CAAC;AACD,SAAO,EAAE,kBAAkB,kBAAkB;AACjD;AAEA,IAAM,yBAAyB,MAAM;AACjC,QAAM,oBAAoB,gBAAgB,EACrC,OAAO,WAAW,MAAM,yBAAyB,EACjD,aAAa;AAAA,IACd,kBAAkB;AAAA,EACtB,CAAC,EACI,iBAAiB,CAAC,gBAAgB,CAAC;AACxC,QAAM,mBAAmB,gBAAgB,EAAE,OAAO,aAAa,qBAAqB,iBAAiB;AACrG,SAAO,EAAE,mBAAmB,kBAAkB,kBAAkB,OAAU;AAC9E;AAIA,IAAM,oBAAoB,CAAC,QAAQ,SAAS;AACxC,QAAM,EAAE,cAAc,mBAAmB,eAAe,IAAI;AAC5D,QAAM,OAAO,eAAe,MAAM;AAClC,QAAM,EAAE,kBAAkB,mBAAmB,iBAAiB,IAAI,sBAAsB,SAAY,0BAA0B,IAAI,IAAI,uBAAuB;AAC7J,oBAAkB,WAAW,KAAK,cAAc,cAAc,CAAC;AAC/D,mBAAiB,WAAW,KAAK,iBAAiB,+BAA+B,CAAC,EAAE,aAAa,EAAE,SAAS,EAAE,CAAC;AAG/G,GAAC,mBAAmB,qBAAqB,QAAQ,qBAAqB,SAAS,SAAS,iBAAiB,WAAW,OAAO,cAAc,WAAW,CAAC;AACrJ,QAAM,gBAAgB,gBAAgB,eAAe,EAChD,WAAW,MAAM,EACjB,OAAO,6BAA6B,EACpC,SAAS,GAAG,EACZ,aAAa,CAAC,gBAAgB,CAAC;AACpC,MAAI,kBAAkB;AAClB,kBAAc,aAAa,gBAAgB;AAAA,EAC/C;AACA,MAAI,cAAc;AACd,UAAM,aAAa,OAAO,aAAa;AACvC,UAAM,eAAe,aAAa,YAAY,eAAe,aAAa,sBAAsB;AAChG,UAAM,mBAAmB,eAAe,YAAY;AACpD,UAAM,sBAAsB,gBAAgB,EAAE,aAAa;AAAA,MACvD,WAAW;AAAA,MACX,oBAAoB;AAAA,MACpB,UAAU;AAAA,IACd,CAAC;AACD,UAAM,SAAS,SAAS;AACxB,QAAI,YAAY;AAMZ,YAAM,kBAAkB,CAAC,IAAI,SAAS,SAAS,eAAe,IAAI,SAAS;AAC3E,YAAM,iBAAiB,eAAe,UAAU;AAChD,YAAM,oBAAoB,qBAAqB;AAC/C,YAAM,iBAAiB,cAAc,cAAc,WAAW,iBAAiB;AAC/E,0BACK,YAAY;AAAA,QACb,WAAW;AAAA,MACf,CAAC,EACI,eAAe,MAAM,OAAO,MAAM,YAAY,oBAAoB,OAAO,CAAC,EAC1E,WAAW,YAAY,EACvB,UAAU;AAAA,QACX,EAAE,QAAQ,GAAG,QAAQ,eAAe,WAAW,4BAA4B,cAAc,MAAM;AAAA,QAC/F,EAAE,QAAQ,GAAG,QAAQ,kBAAkB,WAAW,gBAAgB,cAAc,gBAAgB;AAAA,MACpG,CAAC;AACD,oBAAc,aAAa,mBAAmB;AAAA,IAClD,OACK;AACD,oBAAc,aAAa,iBAAiB;AAC5C,UAAI,CAAC,cAAc;AACf,yBAAiB,OAAO,WAAW,KAAK,GAAG;AAAA,MAC/C,OACK;AACD,cAAM,oBAAoB,eAAe,qBAAqB,uBAAuB;AACrF,cAAM,iBAAiB,2BAA2B,iBAAiB;AACnE,4BACK,YAAY;AAAA,UACb,WAAW;AAAA,QACf,CAAC,EACI,WAAW,iBAAiB,cAAc,gBAAgB,CAAC,EAC3D,UAAU;AAAA,UACX,EAAE,QAAQ,GAAG,QAAQ,eAAe,WAAW,yBAAyB;AAAA,UACxE,EAAE,QAAQ,GAAG,QAAQ,kBAAkB,WAAW,eAAe;AAAA,QACrE,CAAC;AACD,cAAM,kBAAkB,gBAAgB,EACnC,YAAY;AAAA,UACb,WAAW;AAAA,QACf,CAAC,EACI,WAAW,iBAAiB,cAAc,eAAe,CAAC,EAC1D,UAAU;AAAA,UACX,EAAE,QAAQ,GAAG,SAAS,KAAK,WAAW,yBAAyB;AAAA,UAC/D,EAAE,QAAQ,GAAG,SAAS,KAAK,WAAW,eAAe;AAAA,QACzD,CAAC;AACD,sBAAc,aAAa,CAAC,qBAAqB,eAAe,CAAC;AAAA,MACrE;AAAA,IACJ;AAAA,EACJ,OACK;AACD,kBAAc,aAAa,iBAAiB;AAAA,EAChD;AACA,SAAO;AACX;AAEA,IAAM,yBAAyB,MAAM;AACjC,QAAM,oBAAoB,gBAAgB,EAAE,OAAO,WAAW,2BAA2B,CAAC;AAC1F,QAAM,mBAAmB,gBAAgB,EAAE,OAAO,aAAa,mBAAmB,mBAAmB;AACrG,SAAO,EAAE,mBAAmB,iBAAiB;AACjD;AAIA,IAAM,oBAAoB,CAAC,QAAQ,MAAM,WAAW,QAAQ;AACxD,QAAM,EAAE,cAAc,kBAAkB,IAAI;AAC5C,QAAM,OAAO,eAAe,MAAM;AAClC,QAAM,EAAE,kBAAkB,kBAAkB,IAAI,sBAAsB,SAAY,0BAA0B,IAAI,IAAI,uBAAuB;AAC3I,oBAAkB,WAAW,KAAK,cAAc,cAAc,CAAC;AAC/D,mBAAiB,WAAW,KAAK,iBAAiB,+BAA+B,CAAC,EAAE,aAAa,EAAE,SAAS,EAAE,CAAC;AAC/G,QAAM,gBAAgB,gBAAgB,cAAc,EAC/C,WAAW,MAAM,EACjB,OAAO,6BAA6B,EACpC,SAAS,QAAQ,EACjB,aAAa,gBAAgB;AAClC,MAAI,cAAc;AACd,UAAM,aAAa,OAAO,aAAa;AACvC,UAAM,eAAe,aAAa,YAAY,eAAe,aAAa,sBAAsB;AAChG,UAAM,mBAAmB,eAAe,YAAY;AACpD,UAAM,sBAAsB,gBAAgB,EACvC,kBAAkB,CAAC,WAAW,CAAC,EAC/B,iBAAiB,CAAC,WAAW,CAAC,EAC9B,SAAS,CAAC,gBAAgB;AAE3B,UAAI,gBAAgB,GAAG;AACnB;AAAA,MACJ;AACA,mBAAa,MAAM,YAAY,YAAY,EAAE;AAC7C,YAAM,YAAY,MAAM,KAAK,OAAO,iBAAiB,gCAAgC,CAAC,EAAE,OAAO,CAAC,MAAM,EAAE,sBAAsB,MAAS,EAAE;AACzI,UAAI,aAAa,GAAG;AAChB,eAAO,MAAM,YAAY,oBAAoB,EAAE;AAAA,MACnD;AAAA,IACJ,CAAC;AACD,UAAM,SAAS,SAAS;AACxB,QAAI,YAAY;AACZ,YAAM,kBAAkB,CAAC,IAAI,SAAS,SAAS,eAAe,IAAI,SAAS;AAC3E,YAAM,iBAAiB,eAAe,UAAU;AAChD,YAAM,oBAAoB,qBAAqB;AAC/C,YAAM,iBAAiB,cAAc,cAAc,WAAW,iBAAiB;AAC/E,0BAAoB,WAAW,YAAY,EAAE,UAAU;AAAA,QACnD,EAAE,QAAQ,GAAG,QAAQ,kBAAkB,WAAW,gBAAgB,cAAc,gBAAgB;AAAA,QAChG,EAAE,QAAQ,GAAG,QAAQ,eAAe,WAAW,4BAA4B,cAAc,MAAM;AAAA,MACnG,CAAC;AACD,oBAAc,aAAa,mBAAmB;AAAA,IAClD,OACK;AACD,oBAAc,aAAa,iBAAiB;AAC5C,UAAI,CAAC,cAAc;AACf,yBAAiB,OAAO,WAAW,KAAK,GAAG;AAAA,MAC/C,OACK;AACD,cAAM,oBAAoB,eAAe,qBAAqB,uBAAuB;AACrF,cAAM,iBAAiB,2BAA2B,iBAAiB;AACnE,4BACK,WAAW,iBAAiB,cAAc,gBAAgB,CAAC,EAC3D,YAAY;AAAA,UACb,WAAW;AAAA,QACf,CAAC,EACI,UAAU;AAAA,UACX,EAAE,QAAQ,GAAG,QAAQ,kBAAkB,WAAW,eAAe;AAAA,UACjE,EAAE,QAAQ,GAAG,QAAQ,eAAe,WAAW,yBAAyB;AAAA,QAC5E,CAAC;AACD,cAAM,kBAAkB,gBAAgB,EACnC,WAAW,iBAAiB,cAAc,eAAe,CAAC,EAC1D,YAAY;AAAA,UACb,WAAW;AAAA,QACf,CAAC,EACI,UAAU;AAAA,UACX,EAAE,QAAQ,GAAG,SAAS,KAAK,WAAW,eAAe;AAAA,UACrD,EAAE,QAAQ,GAAG,SAAS,KAAK,WAAW,yBAAyB;AAAA,QACnE,CAAC;AACD,sBAAc,aAAa,CAAC,qBAAqB,eAAe,CAAC;AAAA,MACrE;AAAA,IACJ;AAAA,EACJ,OACK;AACD,kBAAc,aAAa,iBAAiB;AAAA,EAChD;AACA,SAAO;AACX;AAOA,IAAM,gCAAgC,CAAC,QAAQ,MAAM,WAAW,QAAQ;AACpE,QAAM,EAAE,aAAa,IAAI;AACzB,MAAI,CAAC,cAAc;AAEf,WAAO,gBAAgB,kCAAkC;AAAA,EAC7D;AACA,QAAM,0BAA0B,aAAa,YAAY,eAAe,aAAa,sBAAsB;AAC3G,QAAM,mBAAmB,eAAe,YAAY;AACpD,QAAM,SAAS,SAAS;AACxB,QAAM,gBAAgB,gBAAgB,kCAAkC,EACnE,WAAW,MAAM,EACjB,OAAO,6BAA6B,EACpC,SAAS,QAAQ;AACtB,QAAM,sBAAsB,gBAAgB,EAAE,aAAa;AAAA,IACvD,WAAW;AAAA,IACX,oBAAoB;AAAA,IACpB,UAAU;AAAA,EACd,CAAC;AACD,MAAI,CAAC,yBAAyB;AAG1B,UAAM,OAAO,eAAe,MAAM;AAClC,UAAM,mBAAmB,gBAAgB,EACpC,WAAW,KAAK,iBAAiB,+BAA+B,CAAC,EACjE,OAAO,WAAW,KAAK,GAAG;AAC/B,UAAM,oBAAoB,gBAAgB,EACrC,WAAW,KAAK,cAAc,cAAc,CAAC,EAC7C,OAAO,WAAW,2BAA2B,yBAAyB;AAE3E,UAAM,kBAAkB,CAAC,IAAI,SAAS,SAAS,eAAe,IAAI,SAAS;AAC3E,UAAM,oBAAoB,qBAAqB;AAC/C,UAAM,gBAAgB,cAAc,eAAe,WAAW,iBAAiB;AAC/E,wBACK,WAAW,YAAY,EACvB,YAAY;AAAA,MACb,WAAW;AAAA,MACX,iBAAiB;AAAA,IACrB,CAAC,EACI,eAAe,MAAM,OAAO,MAAM,YAAY,oBAAoB,EAAE,CAAC,EACrE,OAAO,aAAa,eAAe,0BAA0B,EAC7D,OAAO,UAAU,kBAAkB,aAAa,EAChD,OAAO,iBAAiB,iBAAiB,KAAK;AACnD,kBAAc,aAAa,CAAC,qBAAqB,kBAAkB,iBAAiB,CAAC;AAAA,EACzF,OACK;AAGD,UAAM,oBAAoB,qBAAqB;AAC/C,UAAM,gBAAgB,2BAA2B,iBAAiB;AAClE,UAAM,cAAc;AACpB,wBACK,WAAW,YAAY,EACvB,YAAY;AAAA,MACb,WAAW;AAAA,IACf,CAAC,EACI,OAAO,aAAa,eAAe,WAAW,EAC9C,OAAO,UAAU,kBAAkB,aAAa;AACrD,UAAM,kBAAkB,gBAAgB,EACnC,WAAW,iBAAiB,cAAc,eAAe,CAAC,EAC1D,YAAY;AAAA,MACb,WAAW;AAAA,MACX,SAAS;AAAA,IACb,CAAC,EACI,OAAO,aAAa,eAAe,WAAW;AACnD,kBAAc,aAAa,CAAC,qBAAqB,eAAe,CAAC;AAAA,EACrE;AACA,SAAO;AACX;AAMA,IAAM,gCAAgC,CAAC,QAAQ,MAAM,WAAW,QAAQ;AACpE,QAAM,EAAE,aAAa,IAAI;AACzB,MAAI,CAAC,cAAc;AAEf,WAAO,gBAAgB,kCAAkC;AAAA,EAC7D;AACA,QAAM,0BAA0B,aAAa,YAAY,eAAe,aAAa,sBAAsB;AAC3G,QAAM,mBAAmB,eAAe,YAAY;AACpD,QAAM,SAAS,SAAS;AACxB,QAAM,gBAAgB,gBAAgB,kCAAkC,EACnE,WAAW,MAAM,EACjB,OAAO,6BAA6B,EACpC,SAAS,QAAQ;AACtB,QAAM,sBAAsB,gBAAgB,EAAE,aAAa;AAAA,IACvD,WAAW;AAAA,IACX,oBAAoB;AAAA,IACpB,UAAU;AAAA,EACd,CAAC;AACD,MAAI,CAAC,yBAAyB;AAG1B,UAAM,OAAO,eAAe,MAAM;AAClC,UAAM,mBAAmB,gBAAgB,EACpC,WAAW,KAAK,iBAAiB,+BAA+B,CAAC,EACjE,OAAO,WAAW,KAAK,GAAG;AAC/B,UAAM,oBAAoB,gBAAgB,EACrC,WAAW,KAAK,cAAc,cAAc,CAAC,EAC7C,OAAO,WAAW,2BAA2B,yBAAyB;AAE3E,UAAM,kBAAkB,CAAC,IAAI,SAAS,SAAS,eAAe,IAAI,SAAS;AAC3E,UAAM,oBAAoB,qBAAqB;AAC/C,UAAM,cAAc,cAAc,eAAe,WAAW,iBAAiB;AAC7E,wBACK,WAAW,YAAY,EACvB,YAAY;AAAA,MACb,WAAW;AAAA,IACf,CAAC,EACI,eAAe,MAAM,OAAO,MAAM,YAAY,oBAAoB,OAAO,CAAC,EAC1E,UAAU;AAAA,MACX,EAAE,QAAQ,GAAG,WAAW,4BAA4B,QAAQ,eAAe,cAAc,MAAM;AAAA,MAC/F,EAAE,QAAQ,KAAK,WAAW,4BAA4B,QAAQ,eAAe,cAAc,gBAAgB;AAAA,MAC3G,EAAE,QAAQ,GAAG,WAAW,aAAa,QAAQ,kBAAkB,cAAc,gBAAgB;AAAA,IACjG,CAAC;AACD,kBAAc,aAAa,CAAC,qBAAqB,kBAAkB,iBAAiB,CAAC;AAAA,EACzF,OACK;AAGD,UAAM,oBAAoB,qBAAqB;AAC/C,UAAM,gBAAgB,2BAA2B,iBAAiB;AAClE,UAAM,cAAc;AACpB,wBACK,WAAW,YAAY,EACvB,YAAY;AAAA,MACb,WAAW;AAAA,IACf,CAAC,EACI,OAAO,aAAa,eAAe,WAAW;AACnD,UAAM,kBAAkB,gBAAgB,EACnC,WAAW,iBAAiB,cAAc,eAAe,CAAC,EAC1D,YAAY;AAAA,MACb,WAAW;AAAA,MACX,SAAS;AAAA,IACb,CAAC,EACI,OAAO,aAAa,eAAe,WAAW;AACnD,kBAAc,aAAa,CAAC,qBAAqB,eAAe,CAAC;AAAA,EACrE;AACA,SAAO;AACX;AAEA,IAAM,uBAAuB,MAAM;AAC/B,QAAM,oBAAoB,gBAAgB,EACrC,OAAO,WAAW,MAAM,yBAAyB,EACjD,aAAa;AAAA,IACd,kBAAkB;AAAA,EACtB,CAAC,EACI,iBAAiB,CAAC,gBAAgB,CAAC;AACxC,QAAM,mBAAmB,gBAAgB,EAAE,UAAU;AAAA,IACjD,EAAE,QAAQ,GAAG,SAAS,MAAM,WAAW,mBAAmB;AAAA,IAC1D,EAAE,QAAQ,GAAG,SAAS,GAAG,WAAW,kBAAkB;AAAA,EAC1D,CAAC;AACD,SAAO,EAAE,mBAAmB,kBAAkB,kBAAkB,OAAU;AAC9E;AAIA,IAAM,mBAAmB,CAAC,QAAQ,SAAS;AACvC,QAAM,EAAE,mBAAmB,eAAe,IAAI;AAC9C,QAAM,OAAO,eAAe,MAAM;AAClC,QAAM,EAAE,kBAAkB,mBAAmB,iBAAiB,IAAI,sBAAsB,SAAY,0BAA0B,IAAI,IAAI,qBAAqB;AAC3J,oBAAkB,WAAW,KAAK,cAAc,cAAc,CAAC;AAC/D,mBAAiB,WAAW,KAAK,cAAc,gBAAgB,CAAC;AAGhE,GAAC,mBAAmB,qBAAqB,QAAQ,qBAAqB,SAAS,SAAS,iBAAiB,WAAW,OAAO,cAAc,WAAW,CAAC;AACrJ,QAAM,gBAAgB,gBAAgB,EACjC,WAAW,MAAM,EACjB,OAAO,gCAAgC,EACvC,SAAS,GAAG,EACZ,aAAa,CAAC,mBAAmB,gBAAgB,CAAC;AACvD,MAAI,kBAAkB;AAClB,kBAAc,aAAa,gBAAgB;AAAA,EAC/C;AACA,SAAO;AACX;AAEA,IAAM,uBAAuB,MAAM;AAC/B,QAAM,oBAAoB,gBAAgB,EAAE,OAAO,WAAW,2BAA2B,CAAC;AAC1F,QAAM,mBAAmB,gBAAgB,EAAE,UAAU;AAAA,IACjD,EAAE,QAAQ,GAAG,SAAS,MAAM,WAAW,kBAAkB;AAAA,IACzD,EAAE,QAAQ,GAAG,SAAS,GAAG,WAAW,mBAAmB;AAAA,EAC3D,CAAC;AACD,SAAO,EAAE,mBAAmB,iBAAiB;AACjD;AAIA,IAAM,mBAAmB,CAAC,QAAQ,SAAS;AACvC,QAAM,EAAE,kBAAkB,IAAI;AAC9B,QAAM,OAAO,eAAe,MAAM;AAClC,QAAM,EAAE,kBAAkB,kBAAkB,IAAI,sBAAsB,SAAY,0BAA0B,IAAI,IAAI,qBAAqB;AACzI,oBAAkB,WAAW,KAAK,cAAc,cAAc,CAAC;AAC/D,mBAAiB,WAAW,KAAK,cAAc,gBAAgB,CAAC;AAChE,QAAM,gBAAgB,gBAAgB,EACjC,OAAO,kCAAkC,EACzC,SAAS,GAAG,EACZ,aAAa,CAAC,mBAAmB,gBAAgB,CAAC;AACvD,SAAO;AACX;AAEA,IAAM,qBAAqB,CAAC,QAAQ,YAAY,WAAW,mBAAmB,oBAAoB,WAAW,cAAc,CAAC,GAAG,gBAAgB,sBAAsB,WAAW,uBAAuB;AAEnM,QAAM,kBAAkB;AAAA,IACpB,EAAE,QAAQ,GAAG,SAAS,0BAA0B;AAAA,IAChD,EAAE,QAAQ,GAAG,SAAS,KAAK;AAAA,EAC/B;AACA,QAAM,iBAAiB;AAAA,IACnB,EAAE,QAAQ,GAAG,SAAS,0BAA0B;AAAA,IAChD,EAAE,QAAQ,IAAI,oBAAoB,SAAS,EAAE;AAAA,IAC7C,EAAE,QAAQ,GAAG,SAAS,EAAE;AAAA,EAC5B;AACA,QAAM,gBAAgB;AAAA,IAClB,mBAAmB;AAAA,MACf,EAAE,QAAQ,GAAG,WAAW,iBAAiB;AAAA,MACzC,EAAE,QAAQ,GAAG,WAAW,mBAAmB;AAAA,IAC/C;AAAA,IACA,oBAAoB,uBAAuB,IAAI,iBAAiB;AAAA,IAChE,mBAAmB;AAAA,MACf,EAAE,QAAQ,GAAG,WAAW,OAAO;AAAA,MAC/B,EAAE,QAAQ,GAAG,WAAW,KAAK;AAAA,IACjC;AAAA,EACJ;AACA,QAAM,YAAY,OAAO,cAAc,aAAa;AACpD,QAAM,SAAS,UAAU;AACzB,MAAI,oBAAoB;AACxB,MAAI,SAAS;AACb,MAAI,0BAA0B;AAC9B,MAAI,iBAAiB;AACrB,MAAI,kBAAkB;AACtB,MAAI,wBAAwB;AAC5B,MAAI,qBAAqB;AACzB,QAAM,oBAAoB;AAC1B,QAAM,gBAAgB,YAAY,YAAY,SAAS,CAAC;AACxD,QAAM,gBAAgB,YAAY,CAAC;AACnC,QAAM,mBAAmB,UAAU,gBAAgB,KAAK,CAAC,QAAQ,IAAI,OAAO,kBAAkB;AAC9F,QAAM,oBAAoB,UAAU,gBAAgB,KAAK,CAAC,QAAQ,IAAI,OAAO,mBAAmB;AAChG,QAAM,mBAAmB,UAAU,gBAAgB,KAAK,CAAC,QAAQ,IAAI,OAAO,kBAAkB;AAC9F,QAAM,iBAAiB,MAAM;AACzB,WAAO,MAAM,YAAY,kBAAkB,MAAM;AACjD,eAAW,MAAM,YAAY,kBAAkB,MAAM;AAMrD,WAAO,UAAU,OAAO,wBAAwB;AAAA,EACpD;AACA,QAAM,kBAAkB,MAAM;AAC1B,WAAO,MAAM,YAAY,kBAAkB,MAAM;AACjD,eAAW,MAAM,YAAY,kBAAkB,MAAM;AAQrD,WAAO,UAAU,IAAI,wBAAwB;AAAA,EACjD;AAMA,QAAM,qBAAqB,CAAC,gBAAgB;AACxC,QAAI,CAAC,iBAAiB;AAClB,wBAAkB,MAAM,KAAK,OAAO,iBAAiB,YAAY,CAAC;AAClE,UAAI,CAAC,gBAAgB,QAAQ;AACzB;AAAA,MACJ;AAAA,IACJ;AACA,UAAM,OAAO,OAAO,cAAc,WAAW;AAC7C,yBAAqB;AACrB,QAAI,gBAAgB,cAAc;AAC9B,sBAAgB,QAAQ,CAAC,mBAAmB;AAExC,uBAAe,UAAU,OAAO,qBAAqB;AACrD,uBAAe,MAAM,eAAe,UAAU;AAC9C,uBAAe,MAAM,eAAe,OAAO;AAC3C,uBAAe,MAAM,eAAe,QAAQ;AAC5C,uBAAe,MAAM,eAAe,KAAK;AACzC,uBAAe,MAAM,eAAe,MAAM;AAC1C,iBAAS,QAAQ,SAAS,SAAS,SAAS,KAAK,MAAM,eAAe,gBAAgB;AAEtF,iBAAS,QAAQ,SAAS,SAAS,SAAS,KAAK,YAAY,cAAc;AAAA,MAC/E,CAAC;AAAA,IACL,OACK;AACD,UAAI,gBAAgB;AACpB,sBAAgB,QAAQ,CAAC,gBAAgB,UAAU;AAE/C,cAAM,qBAAqB,eAAe,sBAAsB;AAChE,cAAM,WAAW,SAAS,KAAK,sBAAsB;AAGrD,yBAAiB,eAAe;AAGhC,cAAM,cAAc,mBAAmB,MAAM,SAAS;AACtD,cAAM,eAAe,mBAAmB,OAAO,SAAS;AAGxD,uBAAe,MAAM,YAAY,kBAAkB,GAAG,eAAe,WAAW,IAAI;AACpF,uBAAe,MAAM,YAAY,mBAAmB,GAAG,eAAe,YAAY,IAAI;AACtF,uBAAe,MAAM,YAAY,gBAAgB,GAAG,WAAW,IAAI;AACnE,uBAAe,MAAM,YAAY,iBAAiB,GAAG,YAAY,IAAI;AAIrE,YAAI,UAAU,GAAG;AACb,kCAAwB;AAIxB,gBAAM,SAAS,OAAO,cAAc,YAAY;AAChD,cAAI,QAAQ;AACR,qCAAyB,OAAO;AAAA,UACpC;AAAA,QACJ;AAAA,MACJ,CAAC;AAID,sBAAgB,QAAQ,CAAC,mBAAmB;AAKxC,iBAAS,QAAQ,SAAS,SAAS,SAAS,KAAK,MAAM,YAAY,kBAAkB,GAAG,aAAa,IAAI;AAEzG,uBAAe,UAAU,IAAI,qBAAqB;AAElD,uBAAe,MAAM,YAAY,YAAY,UAAU;AACvD,uBAAe,MAAM,YAAY,SAAS,qBAAqB;AAC/D,uBAAe,MAAM,YAAY,UAAU,sBAAsB;AACjE,uBAAe,MAAM,YAAY,OAAO,mBAAmB;AAC3D,uBAAe,MAAM,YAAY,QAAQ,oBAAoB;AAE7D,iBAAS,KAAK,YAAY,cAAc;AAAA,MAC5C,CAAC;AAAA,IACL;AAAA,EACJ;AASA,MAAI,oBAAoB,mBAAmB;AACvC,qBAAiB,UAAU,CAAC,GAAG,cAAc,iBAAiB,CAAC;AAC/D,sBAAkB,UAAU,CAAC,GAAG,cAAc,kBAAkB,CAAC;AACjE,yBAAqB,QAAQ,qBAAqB,SAAS,SAAS,iBAAiB,UAAU,CAAC,GAAG,cAAc,iBAAiB,CAAC;AACnI,cAAU,cAAc,MAAM,IAAI,iBAAiB;AAQnD,UAAM,uBAAuB,oBAAoB;AACjD,QAAI,sBAAsB;AACtB,qBAAe;AAAA,IACnB,OACK;AACD,sBAAgB;AAAA,IACpB;AAAA,EACJ;AACA,MAAI,aAAa,sBAAsB,iBAAiB,gBAAgB;AACpE,cAAU,UAAU;AAAA,EACxB;AACA,QAAM,WAAW,CAAC,WAAW;AASzB,UAAMC,aAAY,sBAAsB,OAAO,MAAM,MAAM;AAC3D,wBAAoB,qBAAqB;AAKzC,QAAI,CAAC,kBAAkBA,YAAW;AAC9B,YAAM,WAAW,aAAaA,UAAS,IAAI,eAAeA,UAAS,EAAE,cAAc,eAAe,IAAIA;AACtG,aAAO,SAAS,cAAc;AAAA,IAClC;AACA,QAAI,sBAAsB,KAAKA,YAAW;AAUtC,YAAM,WAAW,aAAaA,UAAS,IAAI,eAAeA,UAAS,EAAE,cAAc,eAAe,IAAIA;AACtG,YAAM,wBAAwB,CAAC,CAACA,WAAU,cAAc,eAAe;AACvE,aAAO,CAAC,yBAAyB,SAAS,cAAc;AAAA,IAC5D;AACA,WAAO;AAAA,EACX;AACA,QAAM,UAAU,CAAC,WAAW;AAaxB,8BAA0B,OAAO,eAAe,UAAa,OAAO,eAAe,QAAQ,kBAAkB;AAM7G,QAAI,CAAC,gBAAgB;AACjB,YAAM,WAAW,sBAAsB,OAAO,MAAM,MAAM;AAC1D,uBACI,YAAY,aAAa,QAAQ,IAAI,eAAe,QAAQ,EAAE,cAAc,eAAe,IAAI;AAAA,IACvG;AAMA,QAAI,CAAC,gBAAgB;AACjB,yBAAmB,QAAQ;AAAA,IAC/B;AAKA,QAAI,OAAO,SAAS,KAAK,WAAW;AAChC,gBAAU,UAAU;AAAA,IACxB;AACA,QAAI,MAAM;AAKN,aAAO,MAAM;AAAA,IACjB,CAAC;AACD,cAAU,cAAc,MAAM,IAAI,iBAAiB;AAAA,EACvD;AACA,QAAM,SAAS,CAAC,WAAW;AAOvB,QAAI,CAAC,kBAAkB,0BAA0B,QAAQ,uBAAuB,MAAM;AAElF,UAAI,OAAO,YAAY,yBAAyB,uBAAuB,UAAU;AAC7E,2BAAmB,YAAY;AAAA,MACnC,WACS,OAAO,WAAW,yBAAyB,uBAAuB,cAAc;AACrF,2BAAmB,QAAQ;AAAA,MAC/B;AAAA,IACJ;AAKA,QAAI,CAAC,kBAAkB,OAAO,UAAU,KAAK,gBAAgB;AACzD;AAAA,IACJ;AAOA,QAAI,OAAO,SAAS,KAAK,WAAW;AAChC,gBAAU,UAAU;AAAA,IACxB;AAMA,UAAM,cAAc,IAAI;AACxB,UAAM,yBAAyB,YAAY,SAAS,IAAI,IAAI,YAAY,CAAC,IAAI;AAC7E,UAAM,OAAO,cAAc,OAAO,SAAS;AAC3C,UAAM,oCAAoC,2BAA2B,UAAa,QAAQ,0BAA0B;AAMpH,UAAM,UAAU,oCAAoC,oBAAoB;AAexE,UAAM,gBAAgB,qCAAqC,2BAA2B,SAChF,yBACE,qBAAqB,OAAO,2BAA2B,UAAU,uBAAuB,IAC1F;AACN,aAAS,MAAM,MAAQ,eAAe,OAAO;AAC7C,cAAU,aAAa,MAAM;AAAA,EACjC;AACA,QAAM,QAAQ,CAAC,WAAW;AAMtB,QAAI,CAAC,kBAAkB,OAAO,UAAU,KAAK,kBAAkB,eAAe,YAAY,GAAG;AAQzF,yBAAmB,YAAY;AAC/B;AAAA,IACJ;AAKA,UAAM,WAAW,OAAO;AACxB,UAAM,aAAa,OAAO,SAAS,WAAW,OAAO;AACrD,UAAM,OAAO,oBAAoB;AACjC,UAAM,UAAU,YAAY,OAAO,CAAC,GAAG,MAAM;AACzC,aAAO,KAAK,IAAI,IAAI,IAAI,IAAI,KAAK,IAAI,IAAI,IAAI,IAAI,IAAI;AAAA,IACzD,CAAC;AACD,0BAAsB;AAAA,MAClB,YAAY;AAAA,MACZ,kBAAkB;AAAA,MAClB,YAAY;AAAA;AAAA;AAAA;AAAA;AAAA,MAKZ,UAAU;AAAA,IACd,CAAC;AAAA,EACL;AACA,QAAM,wBAAwB,CAAC,YAAY;AACvC,UAAM,EAAE,YAAY,YAAY,kBAAkB,SAAS,IAAI;AAO/D,UAAM,uBAAuB,cAAc,eAAe;AAC1D,UAAM,mBAAmB,uBAAuB,oBAAoB;AACpE,UAAM,mBAAmB,qBAAqB;AAC9C,wBAAoB;AAKpB,QAAI,oBAAoB,mBAAmB;AACvC,uBAAiB,UAAU;AAAA,QACvB,EAAE,QAAQ,GAAG,WAAW,cAAc,mBAAmB,GAAG,KAAK;AAAA,QACjE,EAAE,QAAQ,GAAG,WAAW,eAAe,IAAI,oBAAoB,GAAG,KAAK;AAAA,MAC3E,CAAC;AACD,wBAAkB,UAAU;AAAA,QACxB;AAAA,UACI,QAAQ;AAAA,UACR,SAAS,kCAAkC,yBAAyB,IAAI,kBAAkB,kBAAkB,CAAC;AAAA,QACjH;AAAA,QACA;AAAA,UACI,QAAQ;AAAA,UACR,SAAS,kCAAkC,yBAAyB,kBAAkB,kBAAkB,CAAC;AAAA,QAC7G;AAAA,MACJ,CAAC;AACD,UAAI,kBAAkB;AAQlB,yBAAiB,UAAU;AAAA,UACvB,EAAE,QAAQ,GAAG,WAAW,IAAI,IAAI,oBAAoB,GAAG,IAAI;AAAA,UAC3D,EAAE,QAAQ,GAAG,WAAW,GAAG,mBAAmB,GAAG,IAAI;AAAA,QACzD,CAAC;AAAA,MACL;AACA,gBAAU,aAAa,CAAC;AAAA,IAC5B;AAKA,YAAQ,OAAO,KAAK;AACpB,QAAI,sBAAsB;AACtB,uBAAiB,QAAQ,SAAS;AAAA,IACtC,WACS,CAAC,kBAAkB;AACxB,gBAAU;AAAA,IACd;AAQA,QAAI,cAAc,qBAAqB,YAAY,YAAY,SAAS,CAAC,KAAK,CAAC,iBAAiB;AAC5F,gBAAU,UAAU;AAAA,IACxB;AAQA,QAAI,CAAC,kBAAkB,qBAAqB,GAAG;AAC3C,yBAAmB,YAAY;AAAA,IACnC;AACA,WAAO,IAAI,QAAQ,CAAC,YAAY;AAC5B,gBACK,SAAS,MAAM;AAChB,YAAI,kBAAkB;AAMlB,cAAI,CAAC,gBAAgB;AACjB,+BAAmB,YAAY;AAAA,UACnC;AAQA,cAAI,oBAAoB,mBAAmB;AACvC,gBAAI,MAAM;AACN,+BAAiB,UAAU,CAAC,GAAG,cAAc,iBAAiB,CAAC;AAC/D,gCAAkB,UAAU,CAAC,GAAG,cAAc,kBAAkB,CAAC;AACjE,mCAAqB,QAAQ,qBAAqB,SAAS,SAAS,iBAAiB,UAAU,CAAC,GAAG,cAAc,iBAAiB,CAAC;AACnI,wBAAU,cAAc,MAAM,IAAI,gBAAgB;AAClD,kCAAoB;AACpB,iCAAmB,iBAAiB;AAKpC,oBAAM,uBAAuB,oBAAoB;AACjD,kBAAI,sBAAsB;AACtB,+BAAe;AAAA,cACnB,OACK;AACD,gCAAgB;AAAA,cACpB;AACA,sBAAQ,OAAO,IAAI;AACnB,sBAAQ;AAAA,YACZ,CAAC;AAAA,UACL,OACK;AACD,oBAAQ,OAAO,IAAI;AACnB,oBAAQ;AAAA,UACZ;AAAA,QACJ,OACK;AACD,kBAAQ;AAAA,QACZ;AAAA,MAMJ,GAAG,EAAE,iBAAiB,KAAK,CAAC,EACvB,YAAY,GAAG,GAAG,WAAW,MAAM,CAAC;AAAA,IAC7C,CAAC;AAAA,EACL;AACA,QAAM,UAAU,cAAc;AAAA,IAC1B,IAAI;AAAA,IACJ,aAAa;AAAA,IACb,iBAAiB;AAAA,IACjB,WAAW;AAAA,IACX,WAAW;AAAA,IACX;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ,CAAC;AACD,SAAO;AAAA,IACH;AAAA,IACA;AAAA,EACJ;AACJ;AAEA,IAAM,cAAc;AAEpB,IAAM,aAAa;AAEnB,IAAM,QAAQ,MAAM;AAAA,EAChB,YAAY,SAAS;AACjB,qBAAiB,MAAM,OAAO;AAC9B,SAAK,aAAa,YAAY,MAAM,sBAAsB,CAAC;AAC3D,SAAK,cAAc,YAAY,MAAM,uBAAuB,CAAC;AAC7D,SAAK,cAAc,YAAY,MAAM,uBAAuB,CAAC;AAC7D,SAAK,aAAa,YAAY,MAAM,sBAAsB,CAAC;AAC3D,SAAK,yBAAyB,YAAY,MAAM,0BAA0B,CAAC;AAC3E,SAAK,sBAAsB,YAAY,MAAM,cAAc,CAAC;AAC5D,SAAK,uBAAuB,YAAY,MAAM,eAAe,CAAC;AAC9D,SAAK,uBAAuB,YAAY,MAAM,eAAe,CAAC;AAC9D,SAAK,sBAAsB,YAAY,MAAM,cAAc,CAAC;AAC5D,SAAK,WAAW,YAAY,MAAM,YAAY,CAAC;AAC/C,SAAK,iBAAiB,qBAAqB;AAC3C,SAAK,oBAAoB,wBAAwB;AACjD,SAAK,eAAe,aAAa;AACjC,SAAK,eAAe;AACpB,SAAK,sBAAsB,CAAC;AAC5B,SAAK,SAAS;AAEd,SAAK,6BAA6B;AAClC,SAAK,YAAY;AAEjB,SAAK,gBAAgB;AAIrB,SAAK,gBAAgB;AAcrB,SAAK,iBAAiB;AAUtB,SAAK,qBAAqB;AAU1B,SAAK,iBAAiB;AAItB,SAAK,kBAAkB;AAQvB,SAAK,eAAe;AAIpB,SAAK,WAAW;AAQhB,SAAK,SAAS;AAYd,SAAK,sBAAsB;AAkB3B,SAAK,YAAY;AAWjB,SAAK,aAAa;AAClB,SAAK,gBAAgB,MAAM;AACvB,YAAM,EAAE,iBAAiB,eAAe,IAAI;AAC5C,UAAI,mBAAmB,WAAW,oBAAoB,QAAW;AAM7D;AAAA,MACJ;AACA,WAAK,qBAAqB;AAAA,IAC9B;AACA,SAAK,gBAAgB,MAAM;AACvB,YAAM,EAAE,gBAAgB,IAAI;AAC5B,UAAI,oBAAoB,QAAW;AAO/B;AAAA,MACJ;AACA,WAAK,QAAQ,QAAW,QAAQ;AAAA,IACpC;AACA,SAAK,cAAc,CAAC,eAAe;AAC/B,YAAM,KAAK,KAAK;AAChB,YAAM,OAAO,cAAc,WAAW,IAAI;AAC1C,UAAI,MAAM,MAAM;AACZ,cAAM,KAAK,IAAI,YAAY,MAAM;AAAA,UAC7B,SAAS;AAAA,UACT,YAAY;AAAA,UACZ,QAAQ,WAAW;AAAA,QACvB,CAAC;AACD,WAAG,cAAc,EAAE;AAAA,MACvB;AAAA,IACJ;AAKA,SAAK,eAAe,CAAC,OAAO;AACxB,YAAM,EAAE,cAAc,GAAG,IAAI;AAE7B,UAAI,GAAG,WAAW,MAAM,gBAAgB,aAAa,aAAa,IAAI;AAClE,qBAAa,MAAM;AAAA,MACvB;AAAA,IACJ;AAQA,SAAK,eAAe,CAAC,EAAE,OAAO,MAAM;AAChC,YAAM,OAAO;AACb,WAAK,iBAAiB,EAAE,QAAQ,CAAC,OAAO;AACpC,WAAG,iBAAiB,WAAW,EAAE,QAAQ,CAAC,eAAe;AAIrD,cAAI,WAAW,aAAa,uBAAuB,MAAM,MAAM;AAC3D,uBAAW,aAAa,yBAAyB,KAAK,GAAG,EAAE;AAAA,UAC/D;AAAA,QACJ,CAAC;AAAA,MACL,CAAC;AAAA,IACL;AAAA,EACJ;AAAA,EACA,eAAe,UAAU,UAAU;AAC/B,QAAI,aAAa,QAAQ,aAAa,OAAO;AACzC,WAAK,QAAQ;AAAA,IACjB,WACS,aAAa,SAAS,aAAa,MAAM;AAC9C,WAAK,QAAQ;AAAA,IACjB;AAAA,EACJ;AAAA,EACA,iBAAiB;AACb,UAAM,EAAE,SAAS,IAAI,kBAAkB,IAAI;AAC3C,QAAI,SAAS;AACT,wBAAkB,iBAAiB,IAAI,OAAO;AAAA,IAClD;AAAA,EACJ;AAAA,EACA,iBAAiB;AAEb,QAAI,WAAW,IAAI,MAAM,SAAS,CAAC,KAAK,qBAAqB,KAAK,kBAAkB,KAAK,gBAAgB;AACrG;AAAA,IACJ;AACA,iBAAa,KAAK,aAAa;AAC/B,SAAK,gBAAgB,WAAW,MAAM;AAClC,WAAK,qBAAqB;AAAA,IAC9B,GAAG,EAAE;AAAA,EACT;AAAA,EACA,mBAAmB,aAAa;AAC5B,QAAI,gBAAgB,QAAW;AAC3B,WAAK,oBAAoB,YAAY,KAAK,CAAC,GAAG,MAAM,IAAI,CAAC;AAAA,IAC7D;AAAA,EACJ;AAAA,EACA,oBAAoB;AAChB,UAAM,EAAE,GAAG,IAAI;AACf,mBAAe,EAAE;AACjB,SAAK,eAAe;AAAA,EACxB;AAAA,EACA,uBAAuB;AACnB,SAAK,kBAAkB,oBAAoB;AAC3C,SAAK,8BAA8B;AACnC,SAAK,6BAA6B;AAAA,EACtC;AAAA,EACA,oBAAoB;AAChB,QAAI;AACJ,UAAM,EAAE,aAAa,mBAAmB,IAAI,eAAe,IAAI;AAC/D,UAAM,eAAgB,KAAK,eAAe,gBAAgB,UAAa,sBAAsB;AAC7F,UAAM,sBAAsB,CAAC,cAAc,MAAM;AACjD,SAAK,sBAAsB,kBAAkB,IAAI,mBAAmB;AAEpE,QAAI,GAAG,YAAY;AACf,WAAK,uBAAuB,GAAG;AAAA,IACnC;AAWA,QAAI,mBAAmB,QAAW;AAC9B,0BAAoB,QAAQ,CAAC,cAAc;AACvC,cAAM,iBAAiB,eAAe,SAAS;AAC/C,YAAI,gBAAgB;AAahB,eAAK,sBAAsB,OAAO,OAAO,OAAO,OAAO,CAAC,GAAG,KAAK,mBAAmB,GAAG,EAAE,CAAC,SAAS,GAAG,eAAe,SAAS,EAAE,CAAC;AAChI,iBAAO,eAAe,SAAS;AAAA,QACnC;AAAA,MACJ,CAAC;AAAA,IACL;AACA,QAAI,cAAc;AACd,WAAK,oBAAoB,KAAK;AAAA,IAClC;AACA,QAAI,gBAAgB,UAAa,sBAAsB,UAAa,CAAC,YAAY,SAAS,iBAAiB,GAAG;AAC1G,sBAAgB,gFAAgF;AAAA,IACpG;AACA,QAAI,GAAG,KAAK,KAAK,oBAAoB,QAAQ,OAAO,SAAS,SAAS,GAAG,KAAK;AAC1E,mBAAa,KAAK,EAAE;AAAA,IACxB;AAAA,EACJ;AAAA,EACA,mBAAmB;AAKf,QAAI,KAAK,WAAW,MAAM;AACtB,UAAI,MAAM,KAAK,QAAQ,CAAC;AAAA,IAC5B;AACA,SAAK,mBAAmB,KAAK,WAAW;AAUxC,SAAK,eAAe;AAAA,EACxB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAUA,YAAY,QAAQ,OAAO;AACvB,QAAI,KAAK,mBAAmB,CAAC,OAAO;AAChC,aAAO;AAAA,QACH,UAAU,KAAK;AAAA,QACf,QAAQ,KAAK;AAAA,MACjB;AAAA,IACJ;AAUA,UAAM,WAAW,KAAK,GAAG;AACzB,UAAM,SAAU,KAAK,SAAS,aAAa,QAAQ,CAAC,KAAK;AACzD,UAAM,WAAY,KAAK,kBAAkB,SAAS,KAAK,YAAY,KAAK,eAAe,KAAK;AAC5F,WAAO,EAAE,QAAQ,SAAS;AAAA,EAC9B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMM,gBAAgB,MAAM,MAAM;AAAA;AAC9B,YAAM,EAAE,WAAW,IAAI;AACvB,UAAI,OAAO,eAAe,YAAY;AAClC,eAAO,WAAW,MAAM,IAAI;AAAA,MAChC;AACA,aAAO;AAAA,IACX;AAAA;AAAA;AAAA;AAAA;AAAA,EAIM,UAAU;AAAA;AACZ,YAAM,SAAS,MAAM,KAAK,eAAe,KAAK;AAC9C,UAAI,KAAK,WAAW;AAChB,eAAO;AACP;AAAA,MACJ;AACA,YAAM,EAAE,mBAAmB,GAAG,IAAI;AAKlC,WAAK,oBAAoB,KAAK;AAC9B,YAAM,EAAE,QAAQ,SAAS,IAAI,KAAK,YAAY,IAAI;AAMlD,WAAK,SAAS,KAAK;AACnB,WAAK,eAAe,MAAM,gBAAgB,UAAU,IAAI,KAAK,WAAW,CAAC,UAAU,GAAG,KAAK,gBAAgB,MAAM;AASjH,UAAI,aAAa,EAAE,GAAG;AAClB,cAAM,UAAU,KAAK,YAAY;AAAA,MASrC,WACS,CAAC,KAAK,qBAAqB;AAChC,cAAM,aAAa;AAAA,MACvB;AACA,gBAAU,MAAM,KAAK,GAAG,UAAU,IAAI,YAAY,CAAC;AACnD,YAAM,eAAe,sBAAsB;AAM3C,UAAI,gBAAgB,WAAW,IAAI,MAAM,OAAO;AAE5C,aAAK,iBAAiB,MAAM,UAAU,SAAS;AAC/C,6BAAqB;AAAA,MACzB;AACA,YAAM,QAAQ,MAAM,cAAc,mBAAmB,kBAAkB;AAAA,QACnE,cAAc;AAAA,QACd,mBAAmB,KAAK;AAAA,QACxB,oBAAoB,KAAK;AAAA,QACzB,gBAAgB,KAAK;AAAA,MACzB,CAAC;AAED,UAAI,OAAO,WAAW,aAAa;AAS/B,aAAK,uBAAuB,MAAM;AAC9B,cAAI,KAAK,SAAS;AAUd,iBAAK,QAAQ,OAAO,KAAK;AACzB,gBAAI,MAAM;AACN,kBAAI,KAAK,SAAS;AACd,qBAAK,QAAQ,OAAO,IAAI;AAAA,cAC5B;AAAA,YACJ,CAAC;AAAA,UACL;AAAA,QACJ;AACA,eAAO,iBAAiB,mBAAmB,KAAK,oBAAoB;AAAA,MACxE;AACA,UAAI,KAAK,cAAc;AACnB,aAAK,iBAAiB;AAAA,MAC1B,WACS,cAAc;AACnB,aAAK,iBAAiB;AAAA,MAC1B;AAEA,WAAK,2BAA2B;AAEhC,WAAK,0BAA0B;AAC/B,aAAO;AAAA,IACX;AAAA;AAAA,EACA,mBAAmB;AACf,QAAI;AACJ,QAAI,WAAW,IAAI,MAAM,OAAO;AAC5B;AAAA,IACJ;AACA,UAAM,EAAE,GAAG,IAAI;AAIf,UAAM,mBAAmB,KAAK,kBAAkB,OAAO,IAAI,cAAc,iBAAiB;AAC1F,UAAM,MAAO,KAAK,YAAY,iBAAiB,IAAI;AAAA,MAC/C,cAAc,KAAK;AAAA,MACnB,gBAAgB,KAAK;AAAA,IACzB,CAAC;AACD,UAAM,YAAY,eAAe,EAAE;AACnC,QAAI,CAAC,WAAW;AACZ,8BAAwB,EAAE;AAC1B;AAAA,IACJ;AACA,UAAM,kBAAkB,KAAK,KAAK,oBAAoB,QAAQ,OAAO,SAAS,KAAK,MAAM;AACzF,SAAK,UAAU,0BAA0B,IAAI,KAAK,gBAAgB,MAAM;AAWpE,WAAK,6BAA6B;AASlC,8BAAwB,KAAK,cAAc;AAC3C,WAAK,UAAU,SAAS,MAAY;AAChC,cAAM,KAAK,QAAQ,QAAW,OAAO;AACrC,aAAK,6BAA6B;AAAA,MACtC,EAAC;AAAA,IACL,CAAC;AACD,SAAK,QAAQ,OAAO,IAAI;AAAA,EAC5B;AAAA,EACA,mBAAmB;AACf,UAAM,EAAE,WAAW,mBAAmB,mBAAmB,IAAI;AAC7D,QAAI,CAAC,aAAa,sBAAsB,QAAW;AAC/C;AAAA,IACJ;AACA,UAAM,mBAAmB,KAAK,kBAAkB,OAAO,IAAI,cAAc,iBAAiB;AAC1F,UAAM,MAAO,KAAK,YAAY,iBAAiB,KAAK,IAAI;AAAA,MACpD,cAAc,KAAK;AAAA,MACnB,mBAAmB;AAAA,MACnB;AAAA,MACA,gBAAgB,KAAK;AAAA,IACzB,CAAC;AACD,QAAI,cAAc,MAAM,CAAC;AACzB,UAAM,EAAE,SAAS,sBAAsB,IAAI,mBAAmB,KAAK,IAAI,KAAK,YAAY,WAAW,mBAAmB,oBAAoB,KAAK,KAAK,mBAAmB,KAAK,gBAAgB,MAAM;AAAE,UAAI;AAAI,cAAQ,KAAK,KAAK,uBAAuB,QAAQ,OAAO,SAAS,KAAK;AAAA,IAAG,GAAG,MAAM,KAAK,eAAe,GAAG,CAAC,eAAe;AACjU,UAAI,KAAK,sBAAsB,YAAY;AACvC,aAAK,oBAAoB;AACzB,aAAK,uBAAuB,KAAK,EAAE,WAAW,CAAC;AAAA,MACnD;AAAA,IACJ,CAAC;AACD,SAAK,UAAU;AACf,SAAK,wBAAwB;AAC7B,SAAK,QAAQ,OAAO,IAAI;AAAA,EAC5B;AAAA,EACA,iBAAiB;AAWb,SAAK,6BAA6B;AAClC,SAAK,UAAU,SAAS,MAAY;AAChC,WAAK,oBAAoB;AACzB,WAAK,uBAAuB,KAAK,EAAE,YAAY,KAAK,kBAAkB,CAAC;AACvE,YAAM,KAAK,QAAQ,QAAW,OAAO;AACrC,WAAK,6BAA6B;AAAA,IACtC,EAAC;AAAA,EACL;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAWM,QAAQ,MAAM,MAAM;AAAA;AACtB,UAAI;AACJ,UAAI,KAAK,8BAA8B,SAAS,SAAS;AACrD,eAAO;AAAA,MACX;AAMA,YAAM,SAAS,MAAM,KAAK,eAAe,KAAK;AAM9C,YAAM,KAAK,oBAAoB;AAM/B,UAAI,SAAS,aAAa,EAAE,MAAM,KAAK,gBAAgB,MAAM,IAAI,IAAI;AACjE,eAAO;AACP,eAAO;AAAA,MACX;AACA,YAAM,EAAE,kBAAkB,IAAI;AAM9B,YAAM,eAAe,sBAAsB;AAC3C,UAAI,gBAAgB,WAAW,IAAI,MAAM,OAAO;AAC5C,gCAAwB,KAAK,cAAc;AAAA,MAC/C;AAEA,UAAI,OAAO,WAAW,eAAe,KAAK,sBAAsB;AAC5D,eAAO,oBAAoB,mBAAmB,KAAK,oBAAoB;AACvE,aAAK,uBAAuB;AAAA,MAChC;AACA,YAAM,YAAY,MAAM,QAAQ,MAAM,MAAM,MAAM,cAAc,mBAAmB,kBAAkB;AAAA,QACjG,cAAc;AAAA,QACd,oBAAoB,KAAK,KAAK,uBAAuB,QAAQ,OAAO,SAAS,KAAK,KAAK;AAAA,QACvF,oBAAoB,KAAK;AAAA,QACzB,gBAAgB,KAAK;AAAA,MACzB,CAAC;AACD,UAAI,WAAW;AACX,cAAM,EAAE,SAAS,IAAI,KAAK,YAAY;AACtC,cAAM,gBAAgB,UAAU,KAAK,YAAY;AACjD,kBAAU,MAAM,KAAK,GAAG,UAAU,OAAO,YAAY,CAAC;AACtD,YAAI,KAAK,WAAW;AAChB,eAAK,UAAU,QAAQ;AAAA,QAC3B;AACA,YAAI,KAAK,SAAS;AACd,eAAK,QAAQ,QAAQ;AAAA,QACzB;AACA,aAAK,8BAA8B;AACnC,aAAK,6BAA6B;AAAA,MACtC;AACA,WAAK,oBAAoB;AACzB,WAAK,YAAY;AACjB,aAAO;AACP,aAAO;AAAA,IACX;AAAA;AAAA;AAAA;AAAA;AAAA,EAIA,eAAe;AACX,WAAO,YAAY,KAAK,IAAI,oBAAoB;AAAA,EACpD;AAAA;AAAA;AAAA;AAAA,EAIA,gBAAgB;AACZ,WAAO,YAAY,KAAK,IAAI,qBAAqB;AAAA,EACrD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOM,qBAAqB,YAAY;AAAA;AACnC,UAAI,CAAC,KAAK,cAAc;AACpB,wBAAgB,uEAAuE;AACvF;AAAA,MACJ;AACA,UAAI,CAAC,KAAK,YAAY,SAAS,UAAU,GAAG;AACxC,wBAAgB,2DAA2D,UAAU,sFAAsF;AAC3K;AAAA,MACJ;AACA,YAAM,EAAE,mBAAmB,uBAAuB,YAAY,aAAa,SAAS,IAAI;AACxF,UAAI,sBAAsB,YAAY;AAClC;AAAA,MACJ;AACA,UAAI,uBAAuB;AACvB,aAAK,kBAAkB,sBAAsB;AAAA,UACzC;AAAA,UACA,kBAAkB,IAAI;AAAA,UACtB,YAAY,eAAe,UAAa,eAAe,QAAQ,YAAY,CAAC,MAAM;AAAA,UAClF;AAAA,QACJ,CAAC;AACD,cAAM,KAAK;AACX,aAAK,kBAAkB;AAAA,MAC3B;AAAA,IACJ;AAAA;AAAA;AAAA;AAAA;AAAA,EAIM,uBAAuB;AAAA;AACzB,aAAO,KAAK;AAAA,IAChB;AAAA;AAAA,EACM,uBAAuB;AAAA;AACzB,YAAM,EAAE,aAAa,kBAAkB,IAAI;AAC3C,UAAI,CAAC,eAAe,qBAAqB,MAAM;AAK3C,eAAO;AAAA,MACX;AACA,YAAM,qBAAqB,YAAY,OAAO,CAAC,MAAM,MAAM,CAAC;AAC5D,YAAM,yBAAyB,mBAAmB,QAAQ,iBAAiB;AAC3E,YAAM,uBAAuB,yBAAyB,KAAK,mBAAmB;AAC9E,YAAM,iBAAiB,mBAAmB,mBAAmB;AAM7D,YAAM,KAAK,qBAAqB,cAAc;AAC9C,aAAO;AAAA,IACX;AAAA;AAAA,EACA,6BAA6B;AAEzB,QAAI,WAAW,IAAI,MAAM,SAAS,CAAC,KAAK,qBAAqB,KAAK,kBAAkB,KAAK,gBAAgB;AACrG;AAAA,IACJ;AAEA,SAAK,wBAAwB,OAAO,aAAa;AAAA,EACrD;AAAA,EACA,uBAAuB;AACnB,UAAM,aAAa,OAAO,aAAa;AAEvC,QAAI,KAAK,0BAA0B,YAAY;AAC3C;AAAA,IACJ;AAEA,QAAI,KAAK,yBAAyB;AAC9B,WAAK,wBAAwB,QAAQ;AACrC,WAAK,0BAA0B;AAAA,IACnC;AACA,UAAM,EAAE,kBAAkB,IAAI;AAC9B,QAAI,CAAC,mBAAmB;AACpB;AAAA,IACJ;AAEA,QAAI;AACJ,QAAI,KAAK,yBAAyB,CAAC,YAAY;AAE3C,4BAAsB,8BAA8B,KAAK,IAAI;AAAA,QACzD,cAAc;AAAA,MAAiB,CAAC;AAAA,IACxC,OACK;AAED,4BAAsB,8BAA8B,KAAK,IAAI;AAAA,QACzD,cAAc;AAAA,MAAiB,CAAC;AAAA,IACxC;AAEA,SAAK,wBAAwB;AAC7B,SAAK,0BAA0B;AAC/B,wBAAoB,KAAK,EAAE,KAAK,MAAM;AAClC,WAAK,0BAA0B;AAG/B,WAAK,mBAAmB;AAAA,IAC5B,CAAC;AAAA,EACL;AAAA,EACA,gCAAgC;AAE5B,QAAI,KAAK,eAAe;AACpB,mBAAa,KAAK,aAAa;AAC/B,WAAK,gBAAgB;AAAA,IACzB;AACA,QAAI,KAAK,yBAAyB;AAC9B,WAAK,wBAAwB,QAAQ;AACrC,WAAK,0BAA0B;AAAA,IACnC;AAAA,EACJ;AAAA,EACA,qBAAqB;AAEjB,QAAI,WAAW,IAAI,MAAM,SAAS,CAAC,KAAK,mBAAmB;AACvD;AAAA,IACJ;AAEA,QAAI,KAAK,SAAS;AACd,WAAK,QAAQ,QAAQ;AACrB,WAAK,UAAU;AAAA,IACnB;AACA,QAAI,KAAK,WAAW;AAGhB,WAAK,UAAU,YAAY,GAAG,GAAG,CAAC;AAClC,WAAK,UAAU,QAAQ;AACvB,WAAK,YAAY;AAAA,IACrB;AAGA,QAAI,MAAM;AACN,WAAK,2BAA2B;AAChC,WAAK,iBAAiB;AAAA,IAC1B,CAAC;AAAA,EACL;AAAA,EACA,6BAA6B;AACzB,UAAM,EAAE,IAAI,kBAAkB,IAAI;AAClC,UAAM,OAAO,eAAe,EAAE;AAC9B,UAAM,YAAY,KAAK,cAAc,gBAAgB;AACrD,QAAI,WAAW;AACX,gBAAU,MAAM,YAAY;AAC5B,gBAAU,MAAM,UAAU;AAAA,IAC9B;AACA,SAAK,sBAAsB,QAAQ,sBAAsB,SAAS,SAAS,kBAAkB,aAAa,aAAa;AACnH,YAAM,aAAa,OAAO,aAAa;AACvC,UAAI,YAAY;AACZ,cAAM,kBAAkB,CAAC,IAAI,SAAS,SAAS,eAAe,IACxD,SACA;AACN,cAAM,QAAQ,qBAAqB;AACnC,0BAAkB,MAAM,YAAY,cAAc,eAAe,WAAW,KAAK;AAAA,MACrF,OACK;AACD,0BAAkB,MAAM,YAAY;AAAA,MACxC;AAAA,IACJ;AAAA,EACJ;AAAA,EACM,sBAAsB;AAAA;AACxB,YAAM,eAAe,SAAS,iBAAiB,oCAAoC,KAAK,GAAG,EAAE,IAAI;AACjG,uBAAiB,QAAQ,iBAAiB,SAAS,SAAS,aAAa,QAAQ,CAAO,UAAU;AAC9F,cAAM,MAAM,QAAQ,QAAW,kBAAkB;AAAA,MACrD,EAAC;AAAA,IACL;AAAA;AAAA,EACA,4BAA4B;AACxB,QAAI,OAAO,qBAAqB,aAAa;AACzC;AAAA,IACJ;AAEA,QAAI,OAAO,WAAW,eAAe,CAAC,KAAK,sBAAsB;AAC7D;AAAA,IACJ;AAEA,QAAI,KAAK,qBAAqB,aAAa,KAAK,iBAC5C,KAAK,qBAAqB,aAAa,KAAK,wBAAwB;AACpE;AAAA,IACJ;AACA,SAAK,wBAAwB,IAAI,iBAAiB,CAAC,cAAc;AAC7D,gBAAU,QAAQ,CAAC,aAAa;AAC5B,YAAI,SAAS,SAAS,eAAe,SAAS,aAAa,SAAS,GAAG;AAEnE,gBAAM,yBAAyB,MAAM,KAAK,SAAS,YAAY,EAAE,KAAK,CAAC,SAAS;AAC5E,gBAAI,IAAI;AACR,kBAAM,gBAAgB,SAAS,KAAK;AACpC,kBAAM,mBAAmB,KAAK,wBACvB,MAAM,KAAK,MAAM,cAAc,QAAQ,OAAO,SAAS,SAAS,GAAG,KAAK,IAAI,KAAK,oBAAoB,IACtG;AACN,mBAAO,iBAAiB;AAAA,UAC5B,CAAC;AAED,gBAAM,2BAA2B,KAAK,wBAAwB,CAAC,KAAK,qBAAqB;AACzF,cAAI,0BAA0B,0BAA0B;AACpD,iBAAK,QAAQ,QAAW,gBAAgB;AAGxC,iBAAK,uBAAuB;AAAA,UAChC;AAAA,QACJ;AAAA,MACJ,CAAC;AAAA,IACL,CAAC;AAED,SAAK,sBAAsB,QAAQ,SAAS,MAAM;AAAA,MAC9C,WAAW;AAAA,MACX,SAAS;AAAA,IACb,CAAC;AAAA,EACL;AAAA,EACA,+BAA+B;AAC3B,QAAI;AACJ,KAAC,KAAK,KAAK,2BAA2B,QAAQ,OAAO,SAAS,SAAS,GAAG,WAAW;AACrF,SAAK,wBAAwB;AAAA,EACjC;AAAA,EACA,SAAS;AACL,UAAM,EAAE,QAAQ,cAAc,mBAAmB,gBAAgB,gBAAgB,qBAAqB,WAAW,eAAgB,IAAI;AACrI,UAAM,aAAa,WAAW,SAAS;AACvC,UAAM,OAAO,WAAW,IAAI;AAC5B,UAAM,cAAc,sBAAsB,UAAa,SAAS;AAChE,UAAM,gBAAgB,mBAAmB;AACzC,UAAM,yBAAyB,gBAAgB;AAC/C,WAAQ,EAAE,MAAM,OAAO,OAAO;AAAA,MAAE,KAAK;AAAA,MAA4C,aAAa;AAAA;AAAA,MAE1F,UAAU,iBAAiB,yBAAyB,IAAI;AAAA,IAAG,GAAG,gBAAgB,EAAE,OAAO;AAAA,MACnF,QAAQ,GAAG,MAAQ,KAAK,YAAY;AAAA,IACxC,GAAG,OAAO,OAAO,OAAO,EAAE,CAAC,IAAI,GAAG,MAAM,CAAC,eAAe,GAAG,CAAC,eAAe,CAAC,cAAc,CAAC,YAAY,GAAG,aAAa,CAAC,aAAa,GAAG,cAAc,CAAC,wBAAwB,GAAG,gBAAgB,CAAC,gBAAgB,kBAAkB,MAAM,CAAC,wBAAwB,GAAG,cAAc,MAAM,GAAG,YAAY,KAAK,QAAQ,CAAC,GAAG,kBAAkB,KAAK,eAAe,sBAAsB,KAAK,aAAa,uBAAuB,KAAK,aAAa,uBAAuB,KAAK,aAAa,sBAAsB,KAAK,aAAa,SAAS,KAAK,aAAa,CAAC,GAAG,EAAE,gBAAgB,EAAE,KAAK,4CAA4C,KAAK,CAAC,OAAQ,KAAK,aAAa,IAAK,SAAS,KAAK,cAAc,UAAU,KAAK,iBAAiB,MAAM,WAAW,CAAC,GAAG,SAAS,SAAS,EAAE,OAAO,EAAE,KAAK,4CAA4C,OAAO,eAAe,CAAC,GAAG,EAAE,OAAO,OAAO,OAAO;AAAA,MAAE,KAAK;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAOn2B,MAAM;AAAA,IAAS,GAAG,qBAAqB,EAAE,cAAc,QAAQ,OAAO,qCAAqC,MAAM,WAAW,KAAK,CAAC,OAAQ,KAAK,YAAY,GAAI,CAAC,GAAG,cAAe,EAAE,UAAU;AAAA,MAAE,KAAK;AAAA,MAA4C,OAAO;AAAA;AAAA,MAExP,UAAU,CAAC,gBAAgB,KAAK;AAAA,MAAG,cAAc;AAAA,MAAmE,SAAS,gBAAgB,KAAK,gBAAgB;AAAA,MAAW,MAAM;AAAA,MAAU,KAAK,CAAC,OAAQ,KAAK,eAAe;AAAA,IAAI,CAAC,GAAI,EAAE,QAAQ,EAAE,KAAK,4CAA4C,cAAc,KAAK,aAAa,CAAC,CAAC,CAAC;AAAA,EAChV;AAAA,EACA,IAAI,KAAK;AAAE,WAAO,WAAW,IAAI;AAAA,EAAG;AAAA,EACpC,WAAW,WAAW;AAAE,WAAO;AAAA,MAC3B,UAAU,CAAC,gBAAgB;AAAA,MAC3B,WAAW,CAAC,gBAAgB;AAAA,IAChC;AAAA,EAAG;AACP;AACA,IAAM,gBAAgB;AAAA,EAClB,oBAAoB;AAAA,EACpB,qBAAqB;AAAA,EACrB,qBAAqB;AAAA,EACrB,oBAAoB;AACxB;AACA,MAAM,QAAQ;AAAA,EACV,KAAK;AAAA,EACL,IAAI;AACR;", "names": ["Style", "contentEl"]}