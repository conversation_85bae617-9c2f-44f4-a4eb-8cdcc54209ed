{"version": 3, "sources": ["../../../../../../node_modules/@ionic/core/dist/esm/ion-item-option_3.entry.js"], "sourcesContent": ["/*!\n * (C) Ionic http://ionicframework.com - MIT License\n */\nimport { r as registerInstance, e as getIonMode, h, j as Host, k as getElement, d as createEvent, m as printIonWarning } from './index-B_U9CtaY.js';\nimport { c as createColorClasses } from './theme-DiVJyqlX.js';\nimport { o as isEndSide } from './helpers-1O4D2b7y.js';\nimport { f as findClosestIonContent, d as disableContentScrollY, r as resetContentScrollY } from './index-BlJTBdxG.js';\nimport { w as watchForOptions } from './watch-options-Dtdm8lKC.js';\n\nconst itemOptionIosCss = \":host{--background:var(--ion-color-primary, #0054e9);--color:var(--ion-color-primary-contrast, #fff);background:var(--background);color:var(--color);font-family:var(--ion-font-family, inherit)}:host(.ion-color){background:var(--ion-color-base);color:var(--ion-color-contrast)}.button-native{font-family:inherit;font-size:inherit;font-style:inherit;font-weight:inherit;letter-spacing:inherit;text-decoration:inherit;text-indent:inherit;text-overflow:inherit;text-transform:inherit;text-align:inherit;white-space:inherit;color:inherit;-webkit-padding-start:0.7em;padding-inline-start:0.7em;-webkit-padding-end:0.7em;padding-inline-end:0.7em;padding-top:0;padding-bottom:0;display:inline-block;position:relative;width:100%;height:100%;border:0;outline:none;background:transparent;cursor:pointer;-webkit-appearance:none;-moz-appearance:none;appearance:none;-webkit-box-sizing:border-box;box-sizing:border-box}.button-inner{display:-ms-flexbox;display:flex;-ms-flex-flow:column nowrap;flex-flow:column nowrap;-ms-flex-negative:0;flex-shrink:0;-ms-flex-align:center;align-items:center;-ms-flex-pack:center;justify-content:center;width:100%;height:100%}.horizontal-wrapper{display:-ms-flexbox;display:flex;-ms-flex-flow:row nowrap;flex-flow:row nowrap;-ms-flex-negative:0;flex-shrink:0;-ms-flex-align:center;align-items:center;-ms-flex-pack:center;justify-content:center;width:100%}::slotted(*){-ms-flex-negative:0;flex-shrink:0}::slotted([slot=start]){-webkit-margin-start:0;margin-inline-start:0;-webkit-margin-end:5px;margin-inline-end:5px;margin-top:0;margin-bottom:0}::slotted([slot=end]){-webkit-margin-start:5px;margin-inline-start:5px;-webkit-margin-end:0;margin-inline-end:0;margin-top:0;margin-bottom:0}::slotted([slot=icon-only]){padding-left:0;padding-right:0;padding-top:0;padding-bottom:0;-webkit-margin-start:10px;margin-inline-start:10px;-webkit-margin-end:10px;margin-inline-end:10px;margin-top:0;margin-bottom:0;min-width:0.9em;font-size:1.8em}:host(.item-option-expandable){-ms-flex-negative:0;flex-shrink:0;-webkit-transition-duration:0;transition-duration:0;-webkit-transition-property:none;transition-property:none;-webkit-transition-timing-function:cubic-bezier(0.65, 0.05, 0.36, 1);transition-timing-function:cubic-bezier(0.65, 0.05, 0.36, 1)}:host(.item-option-disabled){pointer-events:none}:host(.item-option-disabled) .button-native{cursor:default;opacity:0.5;pointer-events:none}:host{font-size:clamp(16px, 1rem, 35.2px)}:host(.ion-activated){background:var(--ion-color-primary-shade, #004acd)}:host(.ion-color.ion-activated){background:var(--ion-color-shade)}\";\n\nconst itemOptionMdCss = \":host{--background:var(--ion-color-primary, #0054e9);--color:var(--ion-color-primary-contrast, #fff);background:var(--background);color:var(--color);font-family:var(--ion-font-family, inherit)}:host(.ion-color){background:var(--ion-color-base);color:var(--ion-color-contrast)}.button-native{font-family:inherit;font-size:inherit;font-style:inherit;font-weight:inherit;letter-spacing:inherit;text-decoration:inherit;text-indent:inherit;text-overflow:inherit;text-transform:inherit;text-align:inherit;white-space:inherit;color:inherit;-webkit-padding-start:0.7em;padding-inline-start:0.7em;-webkit-padding-end:0.7em;padding-inline-end:0.7em;padding-top:0;padding-bottom:0;display:inline-block;position:relative;width:100%;height:100%;border:0;outline:none;background:transparent;cursor:pointer;-webkit-appearance:none;-moz-appearance:none;appearance:none;-webkit-box-sizing:border-box;box-sizing:border-box}.button-inner{display:-ms-flexbox;display:flex;-ms-flex-flow:column nowrap;flex-flow:column nowrap;-ms-flex-negative:0;flex-shrink:0;-ms-flex-align:center;align-items:center;-ms-flex-pack:center;justify-content:center;width:100%;height:100%}.horizontal-wrapper{display:-ms-flexbox;display:flex;-ms-flex-flow:row nowrap;flex-flow:row nowrap;-ms-flex-negative:0;flex-shrink:0;-ms-flex-align:center;align-items:center;-ms-flex-pack:center;justify-content:center;width:100%}::slotted(*){-ms-flex-negative:0;flex-shrink:0}::slotted([slot=start]){-webkit-margin-start:0;margin-inline-start:0;-webkit-margin-end:5px;margin-inline-end:5px;margin-top:0;margin-bottom:0}::slotted([slot=end]){-webkit-margin-start:5px;margin-inline-start:5px;-webkit-margin-end:0;margin-inline-end:0;margin-top:0;margin-bottom:0}::slotted([slot=icon-only]){padding-left:0;padding-right:0;padding-top:0;padding-bottom:0;-webkit-margin-start:10px;margin-inline-start:10px;-webkit-margin-end:10px;margin-inline-end:10px;margin-top:0;margin-bottom:0;min-width:0.9em;font-size:1.8em}:host(.item-option-expandable){-ms-flex-negative:0;flex-shrink:0;-webkit-transition-duration:0;transition-duration:0;-webkit-transition-property:none;transition-property:none;-webkit-transition-timing-function:cubic-bezier(0.65, 0.05, 0.36, 1);transition-timing-function:cubic-bezier(0.65, 0.05, 0.36, 1)}:host(.item-option-disabled){pointer-events:none}:host(.item-option-disabled) .button-native{cursor:default;opacity:0.5;pointer-events:none}:host{font-size:0.875rem;font-weight:500;text-transform:uppercase}\";\n\nconst ItemOption = class {\n    constructor(hostRef) {\n        registerInstance(this, hostRef);\n        /**\n         * If `true`, the user cannot interact with the item option.\n         */\n        this.disabled = false;\n        /**\n         * If `true`, the option will expand to take up the available width and cover any other options.\n         */\n        this.expandable = false;\n        /**\n         * The type of the button.\n         */\n        this.type = 'button';\n        this.onClick = (ev) => {\n            const el = ev.target.closest('ion-item-option');\n            if (el) {\n                ev.preventDefault();\n            }\n        };\n    }\n    render() {\n        const { disabled, expandable, href } = this;\n        const TagType = href === undefined ? 'button' : 'a';\n        const mode = getIonMode(this);\n        const attrs = TagType === 'button'\n            ? { type: this.type }\n            : {\n                download: this.download,\n                href: this.href,\n                target: this.target,\n            };\n        return (h(Host, { key: '189a0040b97163b2336bf216baa71d584c5923a8', onClick: this.onClick, class: createColorClasses(this.color, {\n                [mode]: true,\n                'item-option-disabled': disabled,\n                'item-option-expandable': expandable,\n                'ion-activatable': true,\n            }) }, h(TagType, Object.assign({ key: '5a7140eb99da5ec82fe2ea3ea134513130763399' }, attrs, { class: \"button-native\", part: \"native\", disabled: disabled }), h(\"span\", { key: '9b8577e612706b43e575c9a20f2f9d35c0d1bcb1', class: \"button-inner\" }, h(\"slot\", { key: '9acb82f04e4822bfaa363cc2c4d29d5c0fec0ad6', name: \"top\" }), h(\"div\", { key: '66f5fb4fdd0c39f205574c602c793dcf109c7a17', class: \"horizontal-wrapper\" }, h(\"slot\", { key: '3761a32bca7c6c41b7eb394045497cfde181a62a', name: \"start\" }), h(\"slot\", { key: 'a96a568955cf6962883dc6771726d3d07462da00', name: \"icon-only\" }), h(\"slot\", { key: 'af5dfe5eb41456b9359bafe3615b576617ed7b57' }), h(\"slot\", { key: '00426958066ab7b949ff966fabad5cf8a0b54079', name: \"end\" })), h(\"slot\", { key: 'ae66c8bd536a9f27865f49240980d7b4b831b229', name: \"bottom\" })), mode === 'md' && h(\"ion-ripple-effect\", { key: '30df6c935ef8a3f28a6bc1f3bb162ca4f80aaf26' }))));\n    }\n    get el() { return getElement(this); }\n};\nItemOption.style = {\n    ios: itemOptionIosCss,\n    md: itemOptionMdCss\n};\n\nconst itemOptionsIosCss = \"ion-item-options{top:0;right:0;-ms-flex-pack:end;justify-content:flex-end;display:none;position:absolute;height:100%;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none;z-index:1}:host-context([dir=rtl]) ion-item-options{-ms-flex-pack:start;justify-content:flex-start}:host-context([dir=rtl]) ion-item-options:not(.item-options-end){right:auto;left:0;-ms-flex-pack:end;justify-content:flex-end}[dir=rtl] ion-item-options{-ms-flex-pack:start;justify-content:flex-start}[dir=rtl] ion-item-options:not(.item-options-end){right:auto;left:0;-ms-flex-pack:end;justify-content:flex-end}@supports selector(:dir(rtl)){ion-item-options:dir(rtl){-ms-flex-pack:start;justify-content:flex-start}ion-item-options:dir(rtl):not(.item-options-end){right:auto;left:0;-ms-flex-pack:end;justify-content:flex-end}}.item-options-start{right:auto;left:0;-ms-flex-pack:start;justify-content:flex-start}:host-context([dir=rtl]) .item-options-start{-ms-flex-pack:end;justify-content:flex-end}[dir=rtl] .item-options-start{-ms-flex-pack:end;justify-content:flex-end}@supports selector(:dir(rtl)){.item-options-start:dir(rtl){-ms-flex-pack:end;justify-content:flex-end}}[dir=ltr] .item-options-start ion-item-option:first-child,[dir=rtl] .item-options-start ion-item-option:last-child{padding-left:var(--ion-safe-area-left)}[dir=ltr] .item-options-end ion-item-option:last-child,[dir=rtl] .item-options-end ion-item-option:first-child{padding-right:var(--ion-safe-area-right)}:host-context([dir=rtl]) .item-sliding-active-slide.item-sliding-active-options-start ion-item-options:not(.item-options-end){width:100%;visibility:visible}[dir=rtl] .item-sliding-active-slide.item-sliding-active-options-start ion-item-options:not(.item-options-end){width:100%;visibility:visible}@supports selector(:dir(rtl)){.item-sliding-active-slide:dir(rtl).item-sliding-active-options-start ion-item-options:not(.item-options-end){width:100%;visibility:visible}}.item-sliding-active-slide ion-item-options{display:-ms-flexbox;display:flex;visibility:hidden}.item-sliding-active-slide.item-sliding-active-options-start .item-options-start,.item-sliding-active-slide.item-sliding-active-options-end ion-item-options:not(.item-options-start){width:100%;visibility:visible}.item-options-ios{border-bottom-width:0;border-bottom-style:solid;border-bottom-color:var(--ion-item-border-color, var(--ion-border-color, var(--ion-color-step-250, var(--ion-background-color-step-250, #c8c7cc))))}.item-options-ios.item-options-end{border-bottom-width:0.55px}.list-ios-lines-none .item-options-ios{border-bottom-width:0}.list-ios-lines-full .item-options-ios,.list-ios-lines-inset .item-options-ios.item-options-end{border-bottom-width:0.55px}\";\n\nconst itemOptionsMdCss = \"ion-item-options{top:0;right:0;-ms-flex-pack:end;justify-content:flex-end;display:none;position:absolute;height:100%;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none;z-index:1}:host-context([dir=rtl]) ion-item-options{-ms-flex-pack:start;justify-content:flex-start}:host-context([dir=rtl]) ion-item-options:not(.item-options-end){right:auto;left:0;-ms-flex-pack:end;justify-content:flex-end}[dir=rtl] ion-item-options{-ms-flex-pack:start;justify-content:flex-start}[dir=rtl] ion-item-options:not(.item-options-end){right:auto;left:0;-ms-flex-pack:end;justify-content:flex-end}@supports selector(:dir(rtl)){ion-item-options:dir(rtl){-ms-flex-pack:start;justify-content:flex-start}ion-item-options:dir(rtl):not(.item-options-end){right:auto;left:0;-ms-flex-pack:end;justify-content:flex-end}}.item-options-start{right:auto;left:0;-ms-flex-pack:start;justify-content:flex-start}:host-context([dir=rtl]) .item-options-start{-ms-flex-pack:end;justify-content:flex-end}[dir=rtl] .item-options-start{-ms-flex-pack:end;justify-content:flex-end}@supports selector(:dir(rtl)){.item-options-start:dir(rtl){-ms-flex-pack:end;justify-content:flex-end}}[dir=ltr] .item-options-start ion-item-option:first-child,[dir=rtl] .item-options-start ion-item-option:last-child{padding-left:var(--ion-safe-area-left)}[dir=ltr] .item-options-end ion-item-option:last-child,[dir=rtl] .item-options-end ion-item-option:first-child{padding-right:var(--ion-safe-area-right)}:host-context([dir=rtl]) .item-sliding-active-slide.item-sliding-active-options-start ion-item-options:not(.item-options-end){width:100%;visibility:visible}[dir=rtl] .item-sliding-active-slide.item-sliding-active-options-start ion-item-options:not(.item-options-end){width:100%;visibility:visible}@supports selector(:dir(rtl)){.item-sliding-active-slide:dir(rtl).item-sliding-active-options-start ion-item-options:not(.item-options-end){width:100%;visibility:visible}}.item-sliding-active-slide ion-item-options{display:-ms-flexbox;display:flex;visibility:hidden}.item-sliding-active-slide.item-sliding-active-options-start .item-options-start,.item-sliding-active-slide.item-sliding-active-options-end ion-item-options:not(.item-options-start){width:100%;visibility:visible}.item-options-md{border-bottom-width:0;border-bottom-style:solid;border-bottom-color:var(--ion-item-border-color, var(--ion-border-color, var(--ion-color-step-150, var(--ion-background-color-step-150, rgba(0, 0, 0, 0.13)))))}.list-md-lines-none .item-options-md{border-bottom-width:0}.list-md-lines-full .item-options-md,.list-md-lines-inset .item-options-md.item-options-end{border-bottom-width:1px}\";\n\nconst ItemOptions = class {\n    constructor(hostRef) {\n        registerInstance(this, hostRef);\n        this.ionSwipe = createEvent(this, \"ionSwipe\", 7);\n        /**\n         * The side the option button should be on. Possible values: `\"start\"` and `\"end\"`. If you have multiple `ion-item-options`, a side must be provided for each.\n         *\n         */\n        this.side = 'end';\n    }\n    /** @internal */\n    async fireSwipeEvent() {\n        this.ionSwipe.emit({\n            side: this.side,\n        });\n    }\n    render() {\n        const mode = getIonMode(this);\n        const isEnd = isEndSide(this.side);\n        return (h(Host, { key: '05a22a505e043c2715e3805e5e26ab4668940af0', class: {\n                [mode]: true,\n                // Used internally for styling\n                [`item-options-${mode}`]: true,\n                /**\n                 * Note: The \"start\" and \"end\" terms refer to the\n                 * direction ion-item-option instances within ion-item-options flow.\n                 * They do not refer to how ion-item-options flows within ion-item-sliding.\n                 * As a result, \"item-options-start\" means the ion-item-options container\n                 * always appears on the left, and \"item-options-end\" means the ion-item-options\n                 * container always appears on the right.\n                 */\n                'item-options-start': !isEnd,\n                'item-options-end': isEnd,\n            } }));\n    }\n    get el() { return getElement(this); }\n};\nItemOptions.style = {\n    ios: itemOptionsIosCss,\n    md: itemOptionsMdCss\n};\n\nconst itemSlidingCss = \"ion-item-sliding{display:block;position:relative;width:100%;overflow:hidden;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none}ion-item-sliding .item{-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none}.item-sliding-active-slide .item{position:relative;-webkit-transition:-webkit-transform 500ms cubic-bezier(0.36, 0.66, 0.04, 1);transition:-webkit-transform 500ms cubic-bezier(0.36, 0.66, 0.04, 1);transition:transform 500ms cubic-bezier(0.36, 0.66, 0.04, 1);transition:transform 500ms cubic-bezier(0.36, 0.66, 0.04, 1), -webkit-transform 500ms cubic-bezier(0.36, 0.66, 0.04, 1);opacity:1;z-index:2;pointer-events:none;will-change:transform}.item-sliding-closing ion-item-options{pointer-events:none}.item-sliding-active-swipe-end .item-options-end .item-option-expandable{padding-left:100%;-ms-flex-order:1;order:1;-webkit-transition-duration:0.6s;transition-duration:0.6s;-webkit-transition-property:padding-left;transition-property:padding-left}:host-context([dir=rtl]) .item-sliding-active-swipe-end .item-options-end .item-option-expandable{-ms-flex-order:-1;order:-1}[dir=rtl] .item-sliding-active-swipe-end .item-options-end .item-option-expandable{-ms-flex-order:-1;order:-1}@supports selector(:dir(rtl)){.item-sliding-active-swipe-end .item-options-end .item-option-expandable:dir(rtl){-ms-flex-order:-1;order:-1}}.item-sliding-active-swipe-start .item-options-start .item-option-expandable{padding-right:100%;-ms-flex-order:-1;order:-1;-webkit-transition-duration:0.6s;transition-duration:0.6s;-webkit-transition-property:padding-right;transition-property:padding-right}:host-context([dir=rtl]) .item-sliding-active-swipe-start .item-options-start .item-option-expandable{-ms-flex-order:1;order:1}[dir=rtl] .item-sliding-active-swipe-start .item-options-start .item-option-expandable{-ms-flex-order:1;order:1}@supports selector(:dir(rtl)){.item-sliding-active-swipe-start .item-options-start .item-option-expandable:dir(rtl){-ms-flex-order:1;order:1}}\";\n\nconst SWIPE_MARGIN = 30;\nconst ELASTIC_FACTOR = 0.55;\nlet openSlidingItem;\nconst ItemSliding = class {\n    constructor(hostRef) {\n        registerInstance(this, hostRef);\n        this.ionDrag = createEvent(this, \"ionDrag\", 7);\n        this.item = null;\n        this.openAmount = 0;\n        this.initialOpenAmount = 0;\n        this.optsWidthRightSide = 0;\n        this.optsWidthLeftSide = 0;\n        this.sides = 0 /* ItemSide.None */;\n        this.optsDirty = true;\n        this.contentEl = null;\n        this.initialContentScrollY = true;\n        this.state = 2 /* SlidingState.Disabled */;\n        /**\n         * If `true`, the user cannot interact with the sliding item.\n         */\n        this.disabled = false;\n    }\n    disabledChanged() {\n        if (this.gesture) {\n            this.gesture.enable(!this.disabled);\n        }\n    }\n    async connectedCallback() {\n        const { el } = this;\n        this.item = el.querySelector('ion-item');\n        this.contentEl = findClosestIonContent(el);\n        /**\n         * The MutationObserver needs to be added before we\n         * call updateOptions below otherwise we may miss\n         * ion-item-option elements that are added to the DOM\n         * while updateOptions is running and before the MutationObserver\n         * has been initialized.\n         */\n        this.mutationObserver = watchForOptions(el, 'ion-item-option', async () => {\n            await this.updateOptions();\n        });\n        await this.updateOptions();\n        this.gesture = (await import('./index-CfgBF1SE.js')).createGesture({\n            el,\n            gestureName: 'item-swipe',\n            gesturePriority: 100,\n            threshold: 5,\n            canStart: (ev) => this.canStart(ev),\n            onStart: () => this.onStart(),\n            onMove: (ev) => this.onMove(ev),\n            onEnd: (ev) => this.onEnd(ev),\n        });\n        this.disabledChanged();\n    }\n    disconnectedCallback() {\n        if (this.gesture) {\n            this.gesture.destroy();\n            this.gesture = undefined;\n        }\n        this.item = null;\n        this.leftOptions = this.rightOptions = undefined;\n        if (openSlidingItem === this.el) {\n            openSlidingItem = undefined;\n        }\n        if (this.mutationObserver) {\n            this.mutationObserver.disconnect();\n            this.mutationObserver = undefined;\n        }\n    }\n    /**\n     * Get the amount the item is open in pixels.\n     */\n    getOpenAmount() {\n        return Promise.resolve(this.openAmount);\n    }\n    /**\n     * Get the ratio of the open amount of the item compared to the width of the options.\n     * If the number returned is positive, then the options on the right side are open.\n     * If the number returned is negative, then the options on the left side are open.\n     * If the absolute value of the number is greater than 1, the item is open more than\n     * the width of the options.\n     */\n    getSlidingRatio() {\n        return Promise.resolve(this.getSlidingRatioSync());\n    }\n    /**\n     * Open the sliding item.\n     *\n     * @param side The side of the options to open. If a side is not provided, it will open the first set of options it finds within the item.\n     */\n    async open(side) {\n        var _a;\n        /**\n         * It is possible for the item to be added to the DOM\n         * after the item-sliding component was created. As a result,\n         * if this.item is null, then we should attempt to\n         * query for the ion-item again.\n         * However, if the item is already defined then\n         * we do not query for it again.\n         */\n        const item = (this.item = (_a = this.item) !== null && _a !== void 0 ? _a : this.el.querySelector('ion-item'));\n        if (item === null) {\n            return;\n        }\n        const optionsToOpen = this.getOptions(side);\n        if (!optionsToOpen) {\n            return;\n        }\n        /**\n         * If side is not set, we need to infer the side\n         * so we know which direction to move the options\n         */\n        if (side === undefined) {\n            side = optionsToOpen === this.leftOptions ? 'start' : 'end';\n        }\n        // In RTL we want to switch the sides\n        side = isEndSide(side) ? 'end' : 'start';\n        const isStartOpen = this.openAmount < 0;\n        const isEndOpen = this.openAmount > 0;\n        /**\n         * If a side is open and a user tries to\n         * re-open the same side, we should not do anything\n         */\n        if (isStartOpen && optionsToOpen === this.leftOptions) {\n            return;\n        }\n        if (isEndOpen && optionsToOpen === this.rightOptions) {\n            return;\n        }\n        this.closeOpened();\n        this.state = 4 /* SlidingState.Enabled */;\n        requestAnimationFrame(() => {\n            this.calculateOptsWidth();\n            const width = side === 'end' ? this.optsWidthRightSide : -this.optsWidthLeftSide;\n            openSlidingItem = this.el;\n            this.setOpenAmount(width, false);\n            this.state = side === 'end' ? 8 /* SlidingState.End */ : 16 /* SlidingState.Start */;\n        });\n    }\n    /**\n     * Close the sliding item. Items can also be closed from the [List](./list).\n     */\n    async close() {\n        this.setOpenAmount(0, true);\n    }\n    /**\n     * Close all of the sliding items in the list. Items can also be closed from the [List](./list).\n     */\n    async closeOpened() {\n        if (openSlidingItem !== undefined) {\n            openSlidingItem.close();\n            openSlidingItem = undefined;\n            return true;\n        }\n        return false;\n    }\n    /**\n     * Given an optional side, return the ion-item-options element.\n     *\n     * @param side This side of the options to get. If a side is not provided it will\n     * return the first one available.\n     */\n    getOptions(side) {\n        if (side === undefined) {\n            return this.leftOptions || this.rightOptions;\n        }\n        else if (side === 'start') {\n            return this.leftOptions;\n        }\n        else {\n            return this.rightOptions;\n        }\n    }\n    async updateOptions() {\n        var _a;\n        const options = this.el.querySelectorAll('ion-item-options');\n        let sides = 0;\n        // Reset left and right options in case they were removed\n        this.leftOptions = this.rightOptions = undefined;\n        for (let i = 0; i < options.length; i++) {\n            const item = options.item(i);\n            /**\n             * We cannot use the componentOnReady helper\n             * util here since we need to wait for all of these items\n             * to be ready before we set `this.sides` and `this.optsDirty`.\n             */\n            // eslint-disable-next-line custom-rules/no-component-on-ready-method\n            const option = item.componentOnReady !== undefined ? await item.componentOnReady() : item;\n            const side = isEndSide((_a = option.side) !== null && _a !== void 0 ? _a : option.getAttribute('side')) ? 'end' : 'start';\n            if (side === 'start') {\n                this.leftOptions = option;\n                sides |= 1 /* ItemSide.Start */;\n            }\n            else {\n                this.rightOptions = option;\n                sides |= 2 /* ItemSide.End */;\n            }\n        }\n        this.optsDirty = true;\n        this.sides = sides;\n    }\n    canStart(gesture) {\n        /**\n         * If very close to start of the screen\n         * do not open left side so swipe to go\n         * back will still work.\n         */\n        const rtl = document.dir === 'rtl';\n        const atEdge = rtl ? window.innerWidth - gesture.startX < 15 : gesture.startX < 15;\n        if (atEdge) {\n            return false;\n        }\n        const selected = openSlidingItem;\n        if (selected && selected !== this.el) {\n            this.closeOpened();\n        }\n        return !!(this.rightOptions || this.leftOptions);\n    }\n    onStart() {\n        /**\n         * We need to query for the ion-item\n         * every time the gesture starts. Developers\n         * may toggle ion-item elements via *ngIf.\n         */\n        this.item = this.el.querySelector('ion-item');\n        const { contentEl } = this;\n        if (contentEl) {\n            this.initialContentScrollY = disableContentScrollY(contentEl);\n        }\n        openSlidingItem = this.el;\n        if (this.tmr !== undefined) {\n            clearTimeout(this.tmr);\n            this.tmr = undefined;\n        }\n        if (this.openAmount === 0) {\n            this.optsDirty = true;\n            this.state = 4 /* SlidingState.Enabled */;\n        }\n        this.initialOpenAmount = this.openAmount;\n        if (this.item) {\n            this.item.style.transition = 'none';\n        }\n    }\n    onMove(gesture) {\n        if (this.optsDirty) {\n            this.calculateOptsWidth();\n        }\n        let openAmount = this.initialOpenAmount - gesture.deltaX;\n        switch (this.sides) {\n            case 2 /* ItemSide.End */:\n                openAmount = Math.max(0, openAmount);\n                break;\n            case 1 /* ItemSide.Start */:\n                openAmount = Math.min(0, openAmount);\n                break;\n            case 3 /* ItemSide.Both */:\n                break;\n            case 0 /* ItemSide.None */:\n                return;\n            default:\n                printIonWarning('[ion-item-sliding] - invalid ItemSideFlags value', this.sides);\n                break;\n        }\n        let optsWidth;\n        if (openAmount > this.optsWidthRightSide) {\n            optsWidth = this.optsWidthRightSide;\n            openAmount = optsWidth + (openAmount - optsWidth) * ELASTIC_FACTOR;\n        }\n        else if (openAmount < -this.optsWidthLeftSide) {\n            optsWidth = -this.optsWidthLeftSide;\n            openAmount = optsWidth + (openAmount - optsWidth) * ELASTIC_FACTOR;\n        }\n        this.setOpenAmount(openAmount, false);\n    }\n    onEnd(gesture) {\n        const { contentEl, initialContentScrollY } = this;\n        if (contentEl) {\n            resetContentScrollY(contentEl, initialContentScrollY);\n        }\n        const velocity = gesture.velocityX;\n        let restingPoint = this.openAmount > 0 ? this.optsWidthRightSide : -this.optsWidthLeftSide;\n        // Check if the drag didn't clear the buttons mid-point\n        // and we aren't moving fast enough to swipe open\n        const isResetDirection = this.openAmount > 0 === !(velocity < 0);\n        const isMovingFast = Math.abs(velocity) > 0.3;\n        const isOnCloseZone = Math.abs(this.openAmount) < Math.abs(restingPoint / 2);\n        if (swipeShouldReset(isResetDirection, isMovingFast, isOnCloseZone)) {\n            restingPoint = 0;\n        }\n        const state = this.state;\n        this.setOpenAmount(restingPoint, true);\n        if ((state & 32 /* SlidingState.SwipeEnd */) !== 0 && this.rightOptions) {\n            this.rightOptions.fireSwipeEvent();\n        }\n        else if ((state & 64 /* SlidingState.SwipeStart */) !== 0 && this.leftOptions) {\n            this.leftOptions.fireSwipeEvent();\n        }\n    }\n    calculateOptsWidth() {\n        this.optsWidthRightSide = 0;\n        if (this.rightOptions) {\n            this.rightOptions.style.display = 'flex';\n            this.optsWidthRightSide = this.rightOptions.offsetWidth;\n            this.rightOptions.style.display = '';\n        }\n        this.optsWidthLeftSide = 0;\n        if (this.leftOptions) {\n            this.leftOptions.style.display = 'flex';\n            this.optsWidthLeftSide = this.leftOptions.offsetWidth;\n            this.leftOptions.style.display = '';\n        }\n        this.optsDirty = false;\n    }\n    setOpenAmount(openAmount, isFinal) {\n        if (this.tmr !== undefined) {\n            clearTimeout(this.tmr);\n            this.tmr = undefined;\n        }\n        if (!this.item) {\n            return;\n        }\n        const { el } = this;\n        const style = this.item.style;\n        this.openAmount = openAmount;\n        if (isFinal) {\n            style.transition = '';\n        }\n        if (openAmount > 0) {\n            this.state =\n                openAmount >= this.optsWidthRightSide + SWIPE_MARGIN\n                    ? 8 /* SlidingState.End */ | 32 /* SlidingState.SwipeEnd */\n                    : 8 /* SlidingState.End */;\n        }\n        else if (openAmount < 0) {\n            this.state =\n                openAmount <= -this.optsWidthLeftSide - SWIPE_MARGIN\n                    ? 16 /* SlidingState.Start */ | 64 /* SlidingState.SwipeStart */\n                    : 16 /* SlidingState.Start */;\n        }\n        else {\n            /**\n             * The sliding options should not be\n             * clickable while the item is closing.\n             */\n            el.classList.add('item-sliding-closing');\n            /**\n             * Item sliding cannot be interrupted\n             * while closing the item. If it did,\n             * it would allow the item to get into an\n             * inconsistent state where multiple\n             * items are then open at the same time.\n             */\n            if (this.gesture) {\n                this.gesture.enable(false);\n            }\n            this.tmr = setTimeout(() => {\n                this.state = 2 /* SlidingState.Disabled */;\n                this.tmr = undefined;\n                if (this.gesture) {\n                    this.gesture.enable(!this.disabled);\n                }\n                el.classList.remove('item-sliding-closing');\n            }, 600);\n            openSlidingItem = undefined;\n            style.transform = '';\n            return;\n        }\n        style.transform = `translate3d(${-openAmount}px,0,0)`;\n        this.ionDrag.emit({\n            amount: openAmount,\n            ratio: this.getSlidingRatioSync(),\n        });\n    }\n    getSlidingRatioSync() {\n        if (this.openAmount > 0) {\n            return this.openAmount / this.optsWidthRightSide;\n        }\n        else if (this.openAmount < 0) {\n            return this.openAmount / this.optsWidthLeftSide;\n        }\n        else {\n            return 0;\n        }\n    }\n    render() {\n        const mode = getIonMode(this);\n        return (h(Host, { key: 'd812322c9fb5da4ee16e99dc38bfb24cb4590d03', class: {\n                [mode]: true,\n                'item-sliding-active-slide': this.state !== 2 /* SlidingState.Disabled */,\n                'item-sliding-active-options-end': (this.state & 8 /* SlidingState.End */) !== 0,\n                'item-sliding-active-options-start': (this.state & 16 /* SlidingState.Start */) !== 0,\n                'item-sliding-active-swipe-end': (this.state & 32 /* SlidingState.SwipeEnd */) !== 0,\n                'item-sliding-active-swipe-start': (this.state & 64 /* SlidingState.SwipeStart */) !== 0,\n            } }));\n    }\n    get el() { return getElement(this); }\n    static get watchers() { return {\n        \"disabled\": [\"disabledChanged\"]\n    }; }\n};\nconst swipeShouldReset = (isResetDirection, isMovingFast, isOnResetZone) => {\n    // The logic required to know when the sliding item should close (openAmount=0)\n    // depends on three booleans (isResetDirection, isMovingFast, isOnResetZone)\n    // and it ended up being too complicated to be written manually without errors\n    // so the truth table is attached below: (0=false, 1=true)\n    // isResetDirection | isMovingFast | isOnResetZone || shouldClose\n    //         0        |       0      |       0       ||    0\n    //         0        |       0      |       1       ||    1\n    //         0        |       1      |       0       ||    0\n    //         0        |       1      |       1       ||    0\n    //         1        |       0      |       0       ||    0\n    //         1        |       0      |       1       ||    1\n    //         1        |       1      |       0       ||    1\n    //         1        |       1      |       1       ||    1\n    // The resulting expression was generated by resolving the K-map (Karnaugh map):\n    return (!isMovingFast && isOnResetZone) || (isResetDirection && isMovingFast);\n};\nItemSliding.style = itemSlidingCss;\n\nexport { ItemOption as ion_item_option, ItemOptions as ion_item_options, ItemSliding as ion_item_sliding };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;AASA,IAAM,mBAAmB;AAEzB,IAAM,kBAAkB;AAExB,IAAM,aAAa,MAAM;AAAA,EACrB,YAAY,SAAS;AACjB,qBAAiB,MAAM,OAAO;AAI9B,SAAK,WAAW;AAIhB,SAAK,aAAa;AAIlB,SAAK,OAAO;AACZ,SAAK,UAAU,CAAC,OAAO;AACnB,YAAM,KAAK,GAAG,OAAO,QAAQ,iBAAiB;AAC9C,UAAI,IAAI;AACJ,WAAG,eAAe;AAAA,MACtB;AAAA,IACJ;AAAA,EACJ;AAAA,EACA,SAAS;AACL,UAAM,EAAE,UAAU,YAAY,KAAK,IAAI;AACvC,UAAM,UAAU,SAAS,SAAY,WAAW;AAChD,UAAM,OAAO,WAAW,IAAI;AAC5B,UAAM,QAAQ,YAAY,WACpB,EAAE,MAAM,KAAK,KAAK,IAClB;AAAA,MACE,UAAU,KAAK;AAAA,MACf,MAAM,KAAK;AAAA,MACX,QAAQ,KAAK;AAAA,IACjB;AACJ,WAAQ,EAAE,MAAM,EAAE,KAAK,4CAA4C,SAAS,KAAK,SAAS,OAAO,mBAAmB,KAAK,OAAO;AAAA,MACxH,CAAC,IAAI,GAAG;AAAA,MACR,wBAAwB;AAAA,MACxB,0BAA0B;AAAA,MAC1B,mBAAmB;AAAA,IACvB,CAAC,EAAE,GAAG,EAAE,SAAS,OAAO,OAAO,EAAE,KAAK,2CAA2C,GAAG,OAAO,EAAE,OAAO,iBAAiB,MAAM,UAAU,SAAmB,CAAC,GAAG,EAAE,QAAQ,EAAE,KAAK,4CAA4C,OAAO,eAAe,GAAG,EAAE,QAAQ,EAAE,KAAK,4CAA4C,MAAM,MAAM,CAAC,GAAG,EAAE,OAAO,EAAE,KAAK,4CAA4C,OAAO,qBAAqB,GAAG,EAAE,QAAQ,EAAE,KAAK,4CAA4C,MAAM,QAAQ,CAAC,GAAG,EAAE,QAAQ,EAAE,KAAK,4CAA4C,MAAM,YAAY,CAAC,GAAG,EAAE,QAAQ,EAAE,KAAK,2CAA2C,CAAC,GAAG,EAAE,QAAQ,EAAE,KAAK,4CAA4C,MAAM,MAAM,CAAC,CAAC,GAAG,EAAE,QAAQ,EAAE,KAAK,4CAA4C,MAAM,SAAS,CAAC,CAAC,GAAG,SAAS,QAAQ,EAAE,qBAAqB,EAAE,KAAK,2CAA2C,CAAC,CAAC,CAAC;AAAA,EACh4B;AAAA,EACA,IAAI,KAAK;AAAE,WAAO,WAAW,IAAI;AAAA,EAAG;AACxC;AACA,WAAW,QAAQ;AAAA,EACf,KAAK;AAAA,EACL,IAAI;AACR;AAEA,IAAM,oBAAoB;AAE1B,IAAM,mBAAmB;AAEzB,IAAM,cAAc,MAAM;AAAA,EACtB,YAAY,SAAS;AACjB,qBAAiB,MAAM,OAAO;AAC9B,SAAK,WAAW,YAAY,MAAM,YAAY,CAAC;AAK/C,SAAK,OAAO;AAAA,EAChB;AAAA;AAAA,EAEM,iBAAiB;AAAA;AACnB,WAAK,SAAS,KAAK;AAAA,QACf,MAAM,KAAK;AAAA,MACf,CAAC;AAAA,IACL;AAAA;AAAA,EACA,SAAS;AACL,UAAM,OAAO,WAAW,IAAI;AAC5B,UAAM,QAAQ,UAAU,KAAK,IAAI;AACjC,WAAQ,EAAE,MAAM,EAAE,KAAK,4CAA4C,OAAO;AAAA,MAClE,CAAC,IAAI,GAAG;AAAA;AAAA,MAER,CAAC,gBAAgB,IAAI,EAAE,GAAG;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAS1B,sBAAsB,CAAC;AAAA,MACvB,oBAAoB;AAAA,IACxB,EAAE,CAAC;AAAA,EACX;AAAA,EACA,IAAI,KAAK;AAAE,WAAO,WAAW,IAAI;AAAA,EAAG;AACxC;AACA,YAAY,QAAQ;AAAA,EAChB,KAAK;AAAA,EACL,IAAI;AACR;AAEA,IAAM,iBAAiB;AAEvB,IAAM,eAAe;AACrB,IAAM,iBAAiB;AACvB,IAAI;AACJ,IAAM,cAAc,MAAM;AAAA,EACtB,YAAY,SAAS;AACjB,qBAAiB,MAAM,OAAO;AAC9B,SAAK,UAAU,YAAY,MAAM,WAAW,CAAC;AAC7C,SAAK,OAAO;AACZ,SAAK,aAAa;AAClB,SAAK,oBAAoB;AACzB,SAAK,qBAAqB;AAC1B,SAAK,oBAAoB;AACzB,SAAK,QAAQ;AACb,SAAK,YAAY;AACjB,SAAK,YAAY;AACjB,SAAK,wBAAwB;AAC7B,SAAK,QAAQ;AAIb,SAAK,WAAW;AAAA,EACpB;AAAA,EACA,kBAAkB;AACd,QAAI,KAAK,SAAS;AACd,WAAK,QAAQ,OAAO,CAAC,KAAK,QAAQ;AAAA,IACtC;AAAA,EACJ;AAAA,EACM,oBAAoB;AAAA;AACtB,YAAM,EAAE,GAAG,IAAI;AACf,WAAK,OAAO,GAAG,cAAc,UAAU;AACvC,WAAK,YAAY,sBAAsB,EAAE;AAQzC,WAAK,mBAAmB,gBAAgB,IAAI,mBAAmB,MAAY;AACvE,cAAM,KAAK,cAAc;AAAA,MAC7B,EAAC;AACD,YAAM,KAAK,cAAc;AACzB,WAAK,WAAW,MAAM,OAAO,8BAAqB,GAAG,cAAc;AAAA,QAC/D;AAAA,QACA,aAAa;AAAA,QACb,iBAAiB;AAAA,QACjB,WAAW;AAAA,QACX,UAAU,CAAC,OAAO,KAAK,SAAS,EAAE;AAAA,QAClC,SAAS,MAAM,KAAK,QAAQ;AAAA,QAC5B,QAAQ,CAAC,OAAO,KAAK,OAAO,EAAE;AAAA,QAC9B,OAAO,CAAC,OAAO,KAAK,MAAM,EAAE;AAAA,MAChC,CAAC;AACD,WAAK,gBAAgB;AAAA,IACzB;AAAA;AAAA,EACA,uBAAuB;AACnB,QAAI,KAAK,SAAS;AACd,WAAK,QAAQ,QAAQ;AACrB,WAAK,UAAU;AAAA,IACnB;AACA,SAAK,OAAO;AACZ,SAAK,cAAc,KAAK,eAAe;AACvC,QAAI,oBAAoB,KAAK,IAAI;AAC7B,wBAAkB;AAAA,IACtB;AACA,QAAI,KAAK,kBAAkB;AACvB,WAAK,iBAAiB,WAAW;AACjC,WAAK,mBAAmB;AAAA,IAC5B;AAAA,EACJ;AAAA;AAAA;AAAA;AAAA,EAIA,gBAAgB;AACZ,WAAO,QAAQ,QAAQ,KAAK,UAAU;AAAA,EAC1C;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,kBAAkB;AACd,WAAO,QAAQ,QAAQ,KAAK,oBAAoB,CAAC;AAAA,EACrD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMM,KAAK,MAAM;AAAA;AACb,UAAI;AASJ,YAAM,OAAQ,KAAK,QAAQ,KAAK,KAAK,UAAU,QAAQ,OAAO,SAAS,KAAK,KAAK,GAAG,cAAc,UAAU;AAC5G,UAAI,SAAS,MAAM;AACf;AAAA,MACJ;AACA,YAAM,gBAAgB,KAAK,WAAW,IAAI;AAC1C,UAAI,CAAC,eAAe;AAChB;AAAA,MACJ;AAKA,UAAI,SAAS,QAAW;AACpB,eAAO,kBAAkB,KAAK,cAAc,UAAU;AAAA,MAC1D;AAEA,aAAO,UAAU,IAAI,IAAI,QAAQ;AACjC,YAAM,cAAc,KAAK,aAAa;AACtC,YAAM,YAAY,KAAK,aAAa;AAKpC,UAAI,eAAe,kBAAkB,KAAK,aAAa;AACnD;AAAA,MACJ;AACA,UAAI,aAAa,kBAAkB,KAAK,cAAc;AAClD;AAAA,MACJ;AACA,WAAK,YAAY;AACjB,WAAK,QAAQ;AACb,4BAAsB,MAAM;AACxB,aAAK,mBAAmB;AACxB,cAAM,QAAQ,SAAS,QAAQ,KAAK,qBAAqB,CAAC,KAAK;AAC/D,0BAAkB,KAAK;AACvB,aAAK,cAAc,OAAO,KAAK;AAC/B,aAAK,QAAQ,SAAS,QAAQ,IAA2B;AAAA,MAC7D,CAAC;AAAA,IACL;AAAA;AAAA;AAAA;AAAA;AAAA,EAIM,QAAQ;AAAA;AACV,WAAK,cAAc,GAAG,IAAI;AAAA,IAC9B;AAAA;AAAA;AAAA;AAAA;AAAA,EAIM,cAAc;AAAA;AAChB,UAAI,oBAAoB,QAAW;AAC/B,wBAAgB,MAAM;AACtB,0BAAkB;AAClB,eAAO;AAAA,MACX;AACA,aAAO;AAAA,IACX;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,WAAW,MAAM;AACb,QAAI,SAAS,QAAW;AACpB,aAAO,KAAK,eAAe,KAAK;AAAA,IACpC,WACS,SAAS,SAAS;AACvB,aAAO,KAAK;AAAA,IAChB,OACK;AACD,aAAO,KAAK;AAAA,IAChB;AAAA,EACJ;AAAA,EACM,gBAAgB;AAAA;AAClB,UAAI;AACJ,YAAM,UAAU,KAAK,GAAG,iBAAiB,kBAAkB;AAC3D,UAAI,QAAQ;AAEZ,WAAK,cAAc,KAAK,eAAe;AACvC,eAAS,IAAI,GAAG,IAAI,QAAQ,QAAQ,KAAK;AACrC,cAAM,OAAO,QAAQ,KAAK,CAAC;AAO3B,cAAM,SAAS,KAAK,qBAAqB,SAAY,MAAM,KAAK,iBAAiB,IAAI;AACrF,cAAM,OAAO,WAAW,KAAK,OAAO,UAAU,QAAQ,OAAO,SAAS,KAAK,OAAO,aAAa,MAAM,CAAC,IAAI,QAAQ;AAClH,YAAI,SAAS,SAAS;AAClB,eAAK,cAAc;AACnB,mBAAS;AAAA,QACb,OACK;AACD,eAAK,eAAe;AACpB,mBAAS;AAAA,QACb;AAAA,MACJ;AACA,WAAK,YAAY;AACjB,WAAK,QAAQ;AAAA,IACjB;AAAA;AAAA,EACA,SAAS,SAAS;AAMd,UAAM,MAAM,SAAS,QAAQ;AAC7B,UAAM,SAAS,MAAM,OAAO,aAAa,QAAQ,SAAS,KAAK,QAAQ,SAAS;AAChF,QAAI,QAAQ;AACR,aAAO;AAAA,IACX;AACA,UAAM,WAAW;AACjB,QAAI,YAAY,aAAa,KAAK,IAAI;AAClC,WAAK,YAAY;AAAA,IACrB;AACA,WAAO,CAAC,EAAE,KAAK,gBAAgB,KAAK;AAAA,EACxC;AAAA,EACA,UAAU;AAMN,SAAK,OAAO,KAAK,GAAG,cAAc,UAAU;AAC5C,UAAM,EAAE,UAAU,IAAI;AACtB,QAAI,WAAW;AACX,WAAK,wBAAwB,sBAAsB,SAAS;AAAA,IAChE;AACA,sBAAkB,KAAK;AACvB,QAAI,KAAK,QAAQ,QAAW;AACxB,mBAAa,KAAK,GAAG;AACrB,WAAK,MAAM;AAAA,IACf;AACA,QAAI,KAAK,eAAe,GAAG;AACvB,WAAK,YAAY;AACjB,WAAK,QAAQ;AAAA,IACjB;AACA,SAAK,oBAAoB,KAAK;AAC9B,QAAI,KAAK,MAAM;AACX,WAAK,KAAK,MAAM,aAAa;AAAA,IACjC;AAAA,EACJ;AAAA,EACA,OAAO,SAAS;AACZ,QAAI,KAAK,WAAW;AAChB,WAAK,mBAAmB;AAAA,IAC5B;AACA,QAAI,aAAa,KAAK,oBAAoB,QAAQ;AAClD,YAAQ,KAAK,OAAO;AAAA,MAChB,KAAK;AACD,qBAAa,KAAK,IAAI,GAAG,UAAU;AACnC;AAAA,MACJ,KAAK;AACD,qBAAa,KAAK,IAAI,GAAG,UAAU;AACnC;AAAA,MACJ,KAAK;AACD;AAAA,MACJ,KAAK;AACD;AAAA,MACJ;AACI,wBAAgB,oDAAoD,KAAK,KAAK;AAC9E;AAAA,IACR;AACA,QAAI;AACJ,QAAI,aAAa,KAAK,oBAAoB;AACtC,kBAAY,KAAK;AACjB,mBAAa,aAAa,aAAa,aAAa;AAAA,IACxD,WACS,aAAa,CAAC,KAAK,mBAAmB;AAC3C,kBAAY,CAAC,KAAK;AAClB,mBAAa,aAAa,aAAa,aAAa;AAAA,IACxD;AACA,SAAK,cAAc,YAAY,KAAK;AAAA,EACxC;AAAA,EACA,MAAM,SAAS;AACX,UAAM,EAAE,WAAW,sBAAsB,IAAI;AAC7C,QAAI,WAAW;AACX,0BAAoB,WAAW,qBAAqB;AAAA,IACxD;AACA,UAAM,WAAW,QAAQ;AACzB,QAAI,eAAe,KAAK,aAAa,IAAI,KAAK,qBAAqB,CAAC,KAAK;AAGzE,UAAM,mBAAmB,KAAK,aAAa,MAAM,EAAE,WAAW;AAC9D,UAAM,eAAe,KAAK,IAAI,QAAQ,IAAI;AAC1C,UAAM,gBAAgB,KAAK,IAAI,KAAK,UAAU,IAAI,KAAK,IAAI,eAAe,CAAC;AAC3E,QAAI,iBAAiB,kBAAkB,cAAc,aAAa,GAAG;AACjE,qBAAe;AAAA,IACnB;AACA,UAAM,QAAQ,KAAK;AACnB,SAAK,cAAc,cAAc,IAAI;AACrC,SAAK,QAAQ,QAAoC,KAAK,KAAK,cAAc;AACrE,WAAK,aAAa,eAAe;AAAA,IACrC,YACU,QAAQ,QAAsC,KAAK,KAAK,aAAa;AAC3E,WAAK,YAAY,eAAe;AAAA,IACpC;AAAA,EACJ;AAAA,EACA,qBAAqB;AACjB,SAAK,qBAAqB;AAC1B,QAAI,KAAK,cAAc;AACnB,WAAK,aAAa,MAAM,UAAU;AAClC,WAAK,qBAAqB,KAAK,aAAa;AAC5C,WAAK,aAAa,MAAM,UAAU;AAAA,IACtC;AACA,SAAK,oBAAoB;AACzB,QAAI,KAAK,aAAa;AAClB,WAAK,YAAY,MAAM,UAAU;AACjC,WAAK,oBAAoB,KAAK,YAAY;AAC1C,WAAK,YAAY,MAAM,UAAU;AAAA,IACrC;AACA,SAAK,YAAY;AAAA,EACrB;AAAA,EACA,cAAc,YAAY,SAAS;AAC/B,QAAI,KAAK,QAAQ,QAAW;AACxB,mBAAa,KAAK,GAAG;AACrB,WAAK,MAAM;AAAA,IACf;AACA,QAAI,CAAC,KAAK,MAAM;AACZ;AAAA,IACJ;AACA,UAAM,EAAE,GAAG,IAAI;AACf,UAAM,QAAQ,KAAK,KAAK;AACxB,SAAK,aAAa;AAClB,QAAI,SAAS;AACT,YAAM,aAAa;AAAA,IACvB;AACA,QAAI,aAAa,GAAG;AAChB,WAAK,QACD,cAAc,KAAK,qBAAqB,eAClC,IAA2B,KAC3B;AAAA,IACd,WACS,aAAa,GAAG;AACrB,WAAK,QACD,cAAc,CAAC,KAAK,oBAAoB,eAClC,KAA8B,KAC9B;AAAA,IACd,OACK;AAKD,SAAG,UAAU,IAAI,sBAAsB;AAQvC,UAAI,KAAK,SAAS;AACd,aAAK,QAAQ,OAAO,KAAK;AAAA,MAC7B;AACA,WAAK,MAAM,WAAW,MAAM;AACxB,aAAK,QAAQ;AACb,aAAK,MAAM;AACX,YAAI,KAAK,SAAS;AACd,eAAK,QAAQ,OAAO,CAAC,KAAK,QAAQ;AAAA,QACtC;AACA,WAAG,UAAU,OAAO,sBAAsB;AAAA,MAC9C,GAAG,GAAG;AACN,wBAAkB;AAClB,YAAM,YAAY;AAClB;AAAA,IACJ;AACA,UAAM,YAAY,eAAe,CAAC,UAAU;AAC5C,SAAK,QAAQ,KAAK;AAAA,MACd,QAAQ;AAAA,MACR,OAAO,KAAK,oBAAoB;AAAA,IACpC,CAAC;AAAA,EACL;AAAA,EACA,sBAAsB;AAClB,QAAI,KAAK,aAAa,GAAG;AACrB,aAAO,KAAK,aAAa,KAAK;AAAA,IAClC,WACS,KAAK,aAAa,GAAG;AAC1B,aAAO,KAAK,aAAa,KAAK;AAAA,IAClC,OACK;AACD,aAAO;AAAA,IACX;AAAA,EACJ;AAAA,EACA,SAAS;AACL,UAAM,OAAO,WAAW,IAAI;AAC5B,WAAQ,EAAE,MAAM,EAAE,KAAK,4CAA4C,OAAO;AAAA,MAClE,CAAC,IAAI,GAAG;AAAA,MACR,6BAA6B,KAAK,UAAU;AAAA,MAC5C,oCAAoC,KAAK,QAAQ,OAA8B;AAAA,MAC/E,sCAAsC,KAAK,QAAQ,QAAiC;AAAA,MACpF,kCAAkC,KAAK,QAAQ,QAAoC;AAAA,MACnF,oCAAoC,KAAK,QAAQ,QAAsC;AAAA,IAC3F,EAAE,CAAC;AAAA,EACX;AAAA,EACA,IAAI,KAAK;AAAE,WAAO,WAAW,IAAI;AAAA,EAAG;AAAA,EACpC,WAAW,WAAW;AAAE,WAAO;AAAA,MAC3B,YAAY,CAAC,iBAAiB;AAAA,IAClC;AAAA,EAAG;AACP;AACA,IAAM,mBAAmB,CAAC,kBAAkB,cAAc,kBAAkB;AAexE,SAAQ,CAAC,gBAAgB,iBAAmB,oBAAoB;AACpE;AACA,YAAY,QAAQ;", "names": []}