{"version": 3, "sources": ["../../../../../../node_modules/@ionic/core/dist/esm/ion-tab_2.entry.js"], "sourcesContent": ["/*!\n * (C) Ionic http://ionicframework.com - MIT License\n */\nimport { r as registerInstance, o as printIonError, h, j as Host, k as getElement, d as createEvent } from './index-B_U9CtaY.js';\nimport { a as attachComponent } from './framework-delegate-DxcnWic_.js';\nimport './helpers-1O4D2b7y.js';\n\nconst tabCss = \":host(.tab-hidden){display:none !important}\";\n\nconst Tab = class {\n    constructor(hostRef) {\n        registerInstance(this, hostRef);\n        this.loaded = false;\n        /** @internal */\n        this.active = false;\n    }\n    async componentWillLoad() {\n        if (this.active) {\n            await this.setActive();\n        }\n    }\n    /** Set the active component for the tab */\n    async setActive() {\n        await this.prepareLazyLoaded();\n        this.active = true;\n    }\n    changeActive(isActive) {\n        if (isActive) {\n            this.prepareLazyLoaded();\n        }\n    }\n    prepareLazyLoaded() {\n        if (!this.loaded && this.component != null) {\n            this.loaded = true;\n            try {\n                return attachComponent(this.delegate, this.el, this.component, ['ion-page']);\n            }\n            catch (e) {\n                printIonError('[ion-tab] - Exception in prepareLazyLoaded:', e);\n            }\n        }\n        return Promise.resolve(undefined);\n    }\n    render() {\n        const { tab, active, component } = this;\n        return (h(Host, { key: 'dbad8fe9f1566277d14647626308eaf1601ab01f', role: \"tabpanel\", \"aria-hidden\": !active ? 'true' : null, \"aria-labelledby\": `tab-button-${tab}`, class: {\n                'ion-page': component === undefined,\n                'tab-hidden': !active,\n            } }, h(\"slot\", { key: '3be64f4e7161f6769aaf8e4dcb5293fcaa09af45' })));\n    }\n    get el() { return getElement(this); }\n    static get watchers() { return {\n        \"active\": [\"changeActive\"]\n    }; }\n};\nTab.style = tabCss;\n\nconst tabsCss = \":host{left:0;right:0;top:0;bottom:0;display:-ms-flexbox;display:flex;position:absolute;-ms-flex-direction:column;flex-direction:column;width:100%;height:100%;contain:layout size style;z-index:0}.tabs-inner{position:relative;-ms-flex:1;flex:1;contain:layout size style}\";\n\nconst Tabs = class {\n    constructor(hostRef) {\n        registerInstance(this, hostRef);\n        this.ionNavWillLoad = createEvent(this, \"ionNavWillLoad\", 7);\n        this.ionTabsWillChange = createEvent(this, \"ionTabsWillChange\", 3);\n        this.ionTabsDidChange = createEvent(this, \"ionTabsDidChange\", 3);\n        this.transitioning = false;\n        /** @internal */\n        this.useRouter = false;\n        this.onTabClicked = (ev) => {\n            const { href, tab } = ev.detail;\n            if (this.useRouter && href !== undefined) {\n                const router = document.querySelector('ion-router');\n                if (router) {\n                    router.push(href);\n                }\n            }\n            else {\n                this.select(tab);\n            }\n        };\n    }\n    async componentWillLoad() {\n        if (!this.useRouter) {\n            /**\n             * JavaScript and StencilJS use `ion-router`, while\n             * the other frameworks use `ion-router-outlet`.\n             *\n             * If either component is present then tabs will not use\n             * a basic tab-based navigation. It will use the history\n             * stack or URL updates associated with the router.\n             */\n            this.useRouter =\n                (!!this.el.querySelector('ion-router-outlet') || !!document.querySelector('ion-router')) &&\n                    !this.el.closest('[no-router]');\n        }\n        if (!this.useRouter) {\n            const tabs = this.tabs;\n            if (tabs.length > 0) {\n                await this.select(tabs[0]);\n            }\n        }\n        this.ionNavWillLoad.emit();\n    }\n    componentWillRender() {\n        const tabBar = this.el.querySelector('ion-tab-bar');\n        if (tabBar) {\n            const tab = this.selectedTab ? this.selectedTab.tab : undefined;\n            tabBar.selectedTab = tab;\n        }\n    }\n    /**\n     * Select a tab by the value of its `tab` property or an element reference. This method is only available for vanilla JavaScript projects. The Angular, React, and Vue implementations of tabs are coupled to each framework's router.\n     *\n     * @param tab The tab instance to select. If passed a string, it should be the value of the tab's `tab` property.\n     */\n    async select(tab) {\n        const selectedTab = getTab(this.tabs, tab);\n        if (!this.shouldSwitch(selectedTab)) {\n            return false;\n        }\n        await this.setActive(selectedTab);\n        await this.notifyRouter();\n        this.tabSwitch();\n        return true;\n    }\n    /**\n     * Get a specific tab by the value of its `tab` property or an element reference. This method is only available for vanilla JavaScript projects. The Angular, React, and Vue implementations of tabs are coupled to each framework's router.\n     *\n     * @param tab The tab instance to select. If passed a string, it should be the value of the tab's `tab` property.\n     */\n    async getTab(tab) {\n        return getTab(this.tabs, tab);\n    }\n    /**\n     * Get the currently selected tab. This method is only available for vanilla JavaScript projects. The Angular, React, and Vue implementations of tabs are coupled to each framework's router.\n     */\n    getSelected() {\n        return Promise.resolve(this.selectedTab ? this.selectedTab.tab : undefined);\n    }\n    /** @internal */\n    async setRouteId(id) {\n        const selectedTab = getTab(this.tabs, id);\n        if (!this.shouldSwitch(selectedTab)) {\n            return { changed: false, element: this.selectedTab };\n        }\n        await this.setActive(selectedTab);\n        return {\n            changed: true,\n            element: this.selectedTab,\n            markVisible: () => this.tabSwitch(),\n        };\n    }\n    /** @internal */\n    async getRouteId() {\n        var _a;\n        const tabId = (_a = this.selectedTab) === null || _a === void 0 ? void 0 : _a.tab;\n        return tabId !== undefined ? { id: tabId, element: this.selectedTab } : undefined;\n    }\n    setActive(selectedTab) {\n        if (this.transitioning) {\n            return Promise.reject('transitioning already happening');\n        }\n        this.transitioning = true;\n        this.leavingTab = this.selectedTab;\n        this.selectedTab = selectedTab;\n        this.ionTabsWillChange.emit({ tab: selectedTab.tab });\n        selectedTab.active = true;\n        return Promise.resolve();\n    }\n    tabSwitch() {\n        const selectedTab = this.selectedTab;\n        const leavingTab = this.leavingTab;\n        this.leavingTab = undefined;\n        this.transitioning = false;\n        if (!selectedTab) {\n            return;\n        }\n        if (leavingTab !== selectedTab) {\n            if (leavingTab) {\n                leavingTab.active = false;\n            }\n            this.ionTabsDidChange.emit({ tab: selectedTab.tab });\n        }\n    }\n    notifyRouter() {\n        if (this.useRouter) {\n            const router = document.querySelector('ion-router');\n            if (router) {\n                return router.navChanged('forward');\n            }\n        }\n        return Promise.resolve(false);\n    }\n    shouldSwitch(selectedTab) {\n        const leavingTab = this.selectedTab;\n        return selectedTab !== undefined && selectedTab !== leavingTab && !this.transitioning;\n    }\n    get tabs() {\n        return Array.from(this.el.querySelectorAll('ion-tab'));\n    }\n    render() {\n        return (h(Host, { key: '73ecd3294ca6c78ce6d8b6a7e5b6ccb11d84ada4', onIonTabButtonClick: this.onTabClicked }, h(\"slot\", { key: '09661b26f07a3069a58e76ea4dceb9a6acbf365d', name: \"top\" }), h(\"div\", { key: 'db50d59fad8f9b11873b695fc548f3cfe4aceb6a', class: \"tabs-inner\" }, h(\"slot\", { key: '02694dde2d8381f48fc06dd9e79798c4bd540ccd' })), h(\"slot\", { key: '92c4661a5f3fa1c08c964fab7c422c1a2a03d3d8', name: \"bottom\" })));\n    }\n    get el() { return getElement(this); }\n};\nconst getTab = (tabs, tab) => {\n    const tabEl = typeof tab === 'string' ? tabs.find((t) => t.tab === tab) : tab;\n    if (!tabEl) {\n        printIonError(`[ion-tabs] - Tab with id: \"${tabEl}\" does not exist`);\n    }\n    return tabEl;\n};\nTabs.style = tabsCss;\n\nexport { Tab as ion_tab, Tabs as ion_tabs };\n"], "mappings": ";;;;;;;;;;;;;;;;;AAOA,IAAM,SAAS;AAEf,IAAM,MAAM,MAAM;AAAA,EACd,YAAY,SAAS;AACjB,qBAAiB,MAAM,OAAO;AAC9B,SAAK,SAAS;AAEd,SAAK,SAAS;AAAA,EAClB;AAAA,EACM,oBAAoB;AAAA;AACtB,UAAI,KAAK,QAAQ;AACb,cAAM,KAAK,UAAU;AAAA,MACzB;AAAA,IACJ;AAAA;AAAA;AAAA,EAEM,YAAY;AAAA;AACd,YAAM,KAAK,kBAAkB;AAC7B,WAAK,SAAS;AAAA,IAClB;AAAA;AAAA,EACA,aAAa,UAAU;AACnB,QAAI,UAAU;AACV,WAAK,kBAAkB;AAAA,IAC3B;AAAA,EACJ;AAAA,EACA,oBAAoB;AAChB,QAAI,CAAC,KAAK,UAAU,KAAK,aAAa,MAAM;AACxC,WAAK,SAAS;AACd,UAAI;AACA,eAAO,gBAAgB,KAAK,UAAU,KAAK,IAAI,KAAK,WAAW,CAAC,UAAU,CAAC;AAAA,MAC/E,SACO,GAAG;AACN,sBAAc,+CAA+C,CAAC;AAAA,MAClE;AAAA,IACJ;AACA,WAAO,QAAQ,QAAQ,MAAS;AAAA,EACpC;AAAA,EACA,SAAS;AACL,UAAM,EAAE,KAAK,QAAQ,UAAU,IAAI;AACnC,WAAQ,EAAE,MAAM,EAAE,KAAK,4CAA4C,MAAM,YAAY,eAAe,CAAC,SAAS,SAAS,MAAM,mBAAmB,cAAc,GAAG,IAAI,OAAO;AAAA,MACpK,YAAY,cAAc;AAAA,MAC1B,cAAc,CAAC;AAAA,IACnB,EAAE,GAAG,EAAE,QAAQ,EAAE,KAAK,2CAA2C,CAAC,CAAC;AAAA,EAC3E;AAAA,EACA,IAAI,KAAK;AAAE,WAAO,WAAW,IAAI;AAAA,EAAG;AAAA,EACpC,WAAW,WAAW;AAAE,WAAO;AAAA,MAC3B,UAAU,CAAC,cAAc;AAAA,IAC7B;AAAA,EAAG;AACP;AACA,IAAI,QAAQ;AAEZ,IAAM,UAAU;AAEhB,IAAM,OAAO,MAAM;AAAA,EACf,YAAY,SAAS;AACjB,qBAAiB,MAAM,OAAO;AAC9B,SAAK,iBAAiB,YAAY,MAAM,kBAAkB,CAAC;AAC3D,SAAK,oBAAoB,YAAY,MAAM,qBAAqB,CAAC;AACjE,SAAK,mBAAmB,YAAY,MAAM,oBAAoB,CAAC;AAC/D,SAAK,gBAAgB;AAErB,SAAK,YAAY;AACjB,SAAK,eAAe,CAAC,OAAO;AACxB,YAAM,EAAE,MAAM,IAAI,IAAI,GAAG;AACzB,UAAI,KAAK,aAAa,SAAS,QAAW;AACtC,cAAM,SAAS,SAAS,cAAc,YAAY;AAClD,YAAI,QAAQ;AACR,iBAAO,KAAK,IAAI;AAAA,QACpB;AAAA,MACJ,OACK;AACD,aAAK,OAAO,GAAG;AAAA,MACnB;AAAA,IACJ;AAAA,EACJ;AAAA,EACM,oBAAoB;AAAA;AACtB,UAAI,CAAC,KAAK,WAAW;AASjB,aAAK,aACA,CAAC,CAAC,KAAK,GAAG,cAAc,mBAAmB,KAAK,CAAC,CAAC,SAAS,cAAc,YAAY,MAClF,CAAC,KAAK,GAAG,QAAQ,aAAa;AAAA,MAC1C;AACA,UAAI,CAAC,KAAK,WAAW;AACjB,cAAM,OAAO,KAAK;AAClB,YAAI,KAAK,SAAS,GAAG;AACjB,gBAAM,KAAK,OAAO,KAAK,CAAC,CAAC;AAAA,QAC7B;AAAA,MACJ;AACA,WAAK,eAAe,KAAK;AAAA,IAC7B;AAAA;AAAA,EACA,sBAAsB;AAClB,UAAM,SAAS,KAAK,GAAG,cAAc,aAAa;AAClD,QAAI,QAAQ;AACR,YAAM,MAAM,KAAK,cAAc,KAAK,YAAY,MAAM;AACtD,aAAO,cAAc;AAAA,IACzB;AAAA,EACJ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMM,OAAO,KAAK;AAAA;AACd,YAAM,cAAc,OAAO,KAAK,MAAM,GAAG;AACzC,UAAI,CAAC,KAAK,aAAa,WAAW,GAAG;AACjC,eAAO;AAAA,MACX;AACA,YAAM,KAAK,UAAU,WAAW;AAChC,YAAM,KAAK,aAAa;AACxB,WAAK,UAAU;AACf,aAAO;AAAA,IACX;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMM,OAAO,KAAK;AAAA;AACd,aAAO,OAAO,KAAK,MAAM,GAAG;AAAA,IAChC;AAAA;AAAA;AAAA;AAAA;AAAA,EAIA,cAAc;AACV,WAAO,QAAQ,QAAQ,KAAK,cAAc,KAAK,YAAY,MAAM,MAAS;AAAA,EAC9E;AAAA;AAAA,EAEM,WAAW,IAAI;AAAA;AACjB,YAAM,cAAc,OAAO,KAAK,MAAM,EAAE;AACxC,UAAI,CAAC,KAAK,aAAa,WAAW,GAAG;AACjC,eAAO,EAAE,SAAS,OAAO,SAAS,KAAK,YAAY;AAAA,MACvD;AACA,YAAM,KAAK,UAAU,WAAW;AAChC,aAAO;AAAA,QACH,SAAS;AAAA,QACT,SAAS,KAAK;AAAA,QACd,aAAa,MAAM,KAAK,UAAU;AAAA,MACtC;AAAA,IACJ;AAAA;AAAA;AAAA,EAEM,aAAa;AAAA;AACf,UAAI;AACJ,YAAM,SAAS,KAAK,KAAK,iBAAiB,QAAQ,OAAO,SAAS,SAAS,GAAG;AAC9E,aAAO,UAAU,SAAY,EAAE,IAAI,OAAO,SAAS,KAAK,YAAY,IAAI;AAAA,IAC5E;AAAA;AAAA,EACA,UAAU,aAAa;AACnB,QAAI,KAAK,eAAe;AACpB,aAAO,QAAQ,OAAO,iCAAiC;AAAA,IAC3D;AACA,SAAK,gBAAgB;AACrB,SAAK,aAAa,KAAK;AACvB,SAAK,cAAc;AACnB,SAAK,kBAAkB,KAAK,EAAE,KAAK,YAAY,IAAI,CAAC;AACpD,gBAAY,SAAS;AACrB,WAAO,QAAQ,QAAQ;AAAA,EAC3B;AAAA,EACA,YAAY;AACR,UAAM,cAAc,KAAK;AACzB,UAAM,aAAa,KAAK;AACxB,SAAK,aAAa;AAClB,SAAK,gBAAgB;AACrB,QAAI,CAAC,aAAa;AACd;AAAA,IACJ;AACA,QAAI,eAAe,aAAa;AAC5B,UAAI,YAAY;AACZ,mBAAW,SAAS;AAAA,MACxB;AACA,WAAK,iBAAiB,KAAK,EAAE,KAAK,YAAY,IAAI,CAAC;AAAA,IACvD;AAAA,EACJ;AAAA,EACA,eAAe;AACX,QAAI,KAAK,WAAW;AAChB,YAAM,SAAS,SAAS,cAAc,YAAY;AAClD,UAAI,QAAQ;AACR,eAAO,OAAO,WAAW,SAAS;AAAA,MACtC;AAAA,IACJ;AACA,WAAO,QAAQ,QAAQ,KAAK;AAAA,EAChC;AAAA,EACA,aAAa,aAAa;AACtB,UAAM,aAAa,KAAK;AACxB,WAAO,gBAAgB,UAAa,gBAAgB,cAAc,CAAC,KAAK;AAAA,EAC5E;AAAA,EACA,IAAI,OAAO;AACP,WAAO,MAAM,KAAK,KAAK,GAAG,iBAAiB,SAAS,CAAC;AAAA,EACzD;AAAA,EACA,SAAS;AACL,WAAQ,EAAE,MAAM,EAAE,KAAK,4CAA4C,qBAAqB,KAAK,aAAa,GAAG,EAAE,QAAQ,EAAE,KAAK,4CAA4C,MAAM,MAAM,CAAC,GAAG,EAAE,OAAO,EAAE,KAAK,4CAA4C,OAAO,aAAa,GAAG,EAAE,QAAQ,EAAE,KAAK,2CAA2C,CAAC,CAAC,GAAG,EAAE,QAAQ,EAAE,KAAK,4CAA4C,MAAM,SAAS,CAAC,CAAC;AAAA,EACha;AAAA,EACA,IAAI,KAAK;AAAE,WAAO,WAAW,IAAI;AAAA,EAAG;AACxC;AACA,IAAM,SAAS,CAAC,MAAM,QAAQ;AAC1B,QAAM,QAAQ,OAAO,QAAQ,WAAW,KAAK,KAAK,CAAC,MAAM,EAAE,QAAQ,GAAG,IAAI;AAC1E,MAAI,CAAC,OAAO;AACR,kBAAc,8BAA8B,KAAK,kBAAkB;AAAA,EACvE;AACA,SAAO;AACX;AACA,KAAK,QAAQ;", "names": []}