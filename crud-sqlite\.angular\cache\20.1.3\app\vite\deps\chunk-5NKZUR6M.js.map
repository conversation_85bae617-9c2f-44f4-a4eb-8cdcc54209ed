{"version": 3, "sources": ["../../../../../../node_modules/@ionic/core/dist/esm/dir-C53feagD.js"], "sourcesContent": ["/*!\n * (C) Ionic http://ionicframework.com - MIT License\n */\n/**\n * Returns `true` if the document or host element\n * has a `dir` set to `rtl`. The host value will always\n * take priority over the root document value.\n */\nconst isRTL = (hostEl) => {\n    if (hostEl) {\n        if (hostEl.dir !== '') {\n            return hostEl.dir.toLowerCase() === 'rtl';\n        }\n    }\n    return (document === null || document === void 0 ? void 0 : document.dir.toLowerCase()) === 'rtl';\n};\n\nexport { isRTL as i };\n"], "mappings": ";AAQA,IAAM,QAAQ,CAAC,WAAW;AACtB,MAAI,QAAQ;AACR,QAAI,OAAO,QAAQ,IAAI;AACnB,aAAO,OAAO,IAAI,YAAY,MAAM;AAAA,IACxC;AAAA,EACJ;AACA,UAAQ,aAAa,QAAQ,aAAa,SAAS,SAAS,SAAS,IAAI,YAAY,OAAO;AAChG;", "names": []}