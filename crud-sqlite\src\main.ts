import { bootstrapApplication } from '@angular/platform-browser';
import { RouteReuseStrategy, provideRouter, withPreloading, PreloadAllModules } from '@angular/router';
import { IonicRouteStrategy, provideIonicAngular } from '@ionic/angular/standalone';

import { routes } from './app/app.routes';
import { AppComponent } from './app/app.component';

// Initialize jeep-sqlite for web
async function initializeApp() {
  // Check if we're in a web environment
  if (typeof window !== 'undefined' && window.customElements) {
    try {
      // Import and define jeep-sqlite for web
      const { defineCustomElements } = await import('jeep-sqlite/loader');
      await defineCustomElements(window);
      console.log('jeep-sqlite web components loaded');
    } catch (error) {
      console.log('jeep-sqlite not available or already loaded:', error);
    }
  }

  bootstrapApplication(AppComponent, {
    providers: [
      { provide: RouteReuseStrategy, useClass: IonicRouteStrategy },
      provideIonicAngular(),
      provideRouter(routes, withPreloading(PreloadAllModules)),
    ],
  });
}

initializeApp();
