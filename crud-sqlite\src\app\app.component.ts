import { Component, OnInit, CUSTOM_ELEMENTS_SCHEMA } from '@angular/core';
import { IonApp, IonRouterOutlet } from '@ionic/angular/standalone';
import { SqliteService } from './services/sqlite-crudop.service';

@Component({
  selector: 'app-root',
  templateUrl: 'app.component.html',
  imports: [IonApp, IonRouterOutlet],
  schemas: [CUSTOM_ELEMENTS_SCHEMA]
})
export class AppComponent implements OnInit {
  constructor(private sqlite: SqliteService) {}
  async ngOnInit() {
    // Wait for jeep-sqlite element to be defined before initializing DB
    if (typeof customElements !== 'undefined') {
      await customElements.whenDefined('jeep-sqlite');
      const jeepSqlite = document.querySelector('jeep-sqlite');
      if (jeepSqlite) {
        await (jeepSqlite as any).componentOnReady();
      }
    }
    await this.sqlite.initDB(); //  Initialize DB
  }
}
