import { Component, OnInit, CUSTOM_ELEMENTS_SCHEMA } from '@angular/core';
import { IonApp, IonRouterOutlet } from '@ionic/angular/standalone';
import { Platform } from '@ionic/angular';
import { SqliteCrudopService } from './services/sqlite-crudop.service';

@Component({
  selector: 'app-root',
  templateUrl: 'app.component.html',
  imports: [IonApp, IonRouterOutlet],
  schemas: [CUSTOM_ELEMENTS_SCHEMA]
})
export class AppComponent implements OnInit {
  constructor(private platform: Platform, private sqlite: SqliteCrudopService) {}

  async ngOnInit() {
    try {
      await this.platform.ready();
      if (window.customElements.get('jeep-sqlite') === undefined) {
        const jeepSqlite = document.createElement('jeep-sqlite');
        document.body.appendChild(jeepSqlite);
        await customElements.whenDefined('jeep-sqlite');
        console.log('jeep-sqlite component defined');
      } else {
        console.log('jeep-sqlite component already defined');
      }
      await this.sqlite.initDB(); //  Initialize DB
      console.log('Database initialized in app.component');
    } catch (error) {
      console.error('Error during app initialization:', error);
    }
  }
}
