{"logs": [{"outputFile": "io.ionic.starter.app-mergeDebugResources-31:/values-b+sr+Latn/values-b+sr+Latn.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\e2542f07e03e83966d61d2ce34c14b7c\\transformed\\appcompat-1.7.0\\res\\values-b+sr+Latn\\values-b+sr+Latn.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,212,313,419,505,609,731,816,898,989,1082,1177,1271,1371,1464,1559,1664,1755,1846,1932,2037,2143,2246,2353,2462,2569,2739,2836", "endColumns": "106,100,105,85,103,121,84,81,90,92,94,93,99,92,94,104,90,90,85,104,105,102,106,108,106,169,96,86", "endOffsets": "207,308,414,500,604,726,811,893,984,1077,1172,1266,1366,1459,1554,1659,1750,1841,1927,2032,2138,2241,2348,2457,2564,2734,2831,2918"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,48", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,212,313,419,505,609,731,816,898,989,1082,1177,1271,1371,1464,1559,1664,1755,1846,1932,2037,2143,2246,2353,2462,2569,2739,5043", "endColumns": "106,100,105,85,103,121,84,81,90,92,94,93,99,92,94,104,90,90,85,104,105,102,106,108,106,169,96,86", "endOffsets": "207,308,414,500,604,726,811,893,984,1077,1172,1266,1366,1459,1554,1659,1750,1841,1927,2032,2138,2241,2348,2457,2564,2734,2831,5125"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\ea82f0e4740fea90ca38a885280cdee8\\transformed\\core-1.15.0\\res\\values-b+sr+Latn\\values-b+sr+Latn.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,255,352,456,560,665,781", "endColumns": "97,101,96,103,103,104,115,100", "endOffsets": "148,250,347,451,555,660,776,877"}, "to": {"startLines": "29,30,31,32,33,34,35,49", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "2836,2934,3036,3133,3237,3341,3446,5130", "endColumns": "97,101,96,103,103,104,115,100", "endOffsets": "2929,3031,3128,3232,3336,3441,3557,5226"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\eaaca94d50affd3c3a86a8212751e60b\\transformed\\biometric-1.1.0\\res\\values-b+sr+Latn\\values-b+sr+Latn.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,166,259,380,514,645,772,903,1038,1138,1277,1410", "endColumns": "110,92,120,133,130,126,130,134,99,138,132,125", "endOffsets": "161,254,375,509,640,767,898,1033,1133,1272,1405,1531"}, "to": {"startLines": "36,37,38,39,40,41,42,43,44,45,46,47", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3562,3673,3766,3887,4021,4152,4279,4410,4545,4645,4784,4917", "endColumns": "110,92,120,133,130,126,130,134,99,138,132,125", "endOffsets": "3668,3761,3882,4016,4147,4274,4405,4540,4640,4779,4912,5038"}}]}]}