{"version": 3, "sources": ["../../../../../../node_modules/@capacitor/core/dist/index.js"], "sourcesContent": ["/*! Capacitor: https://capacitorjs.com/ - MIT License */\nvar ExceptionCode;\n(function (ExceptionCode) {\n    /**\n     * API is not implemented.\n     *\n     * This usually means the API can't be used because it is not implemented for\n     * the current platform.\n     */\n    ExceptionCode[\"Unimplemented\"] = \"UNIMPLEMENTED\";\n    /**\n     * API is not available.\n     *\n     * This means the API can't be used right now because:\n     *   - it is currently missing a prerequisite, such as network connectivity\n     *   - it requires a particular platform or browser version\n     */\n    ExceptionCode[\"Unavailable\"] = \"UNAVAILABLE\";\n})(ExceptionCode || (ExceptionCode = {}));\nclass CapacitorException extends Error {\n    constructor(message, code, data) {\n        super(message);\n        this.message = message;\n        this.code = code;\n        this.data = data;\n    }\n}\nconst getPlatformId = (win) => {\n    var _a, _b;\n    if (win === null || win === void 0 ? void 0 : win.androidBridge) {\n        return 'android';\n    }\n    else if ((_b = (_a = win === null || win === void 0 ? void 0 : win.webkit) === null || _a === void 0 ? void 0 : _a.messageHandlers) === null || _b === void 0 ? void 0 : _b.bridge) {\n        return 'ios';\n    }\n    else {\n        return 'web';\n    }\n};\n\nconst createCapacitor = (win) => {\n    const capCustomPlatform = win.CapacitorCustomPlatform || null;\n    const cap = win.Capacitor || {};\n    const Plugins = (cap.Plugins = cap.Plugins || {});\n    const getPlatform = () => {\n        return capCustomPlatform !== null ? capCustomPlatform.name : getPlatformId(win);\n    };\n    const isNativePlatform = () => getPlatform() !== 'web';\n    const isPluginAvailable = (pluginName) => {\n        const plugin = registeredPlugins.get(pluginName);\n        if (plugin === null || plugin === void 0 ? void 0 : plugin.platforms.has(getPlatform())) {\n            // JS implementation available for the current platform.\n            return true;\n        }\n        if (getPluginHeader(pluginName)) {\n            // Native implementation available.\n            return true;\n        }\n        return false;\n    };\n    const getPluginHeader = (pluginName) => { var _a; return (_a = cap.PluginHeaders) === null || _a === void 0 ? void 0 : _a.find((h) => h.name === pluginName); };\n    const handleError = (err) => win.console.error(err);\n    const registeredPlugins = new Map();\n    const registerPlugin = (pluginName, jsImplementations = {}) => {\n        const registeredPlugin = registeredPlugins.get(pluginName);\n        if (registeredPlugin) {\n            console.warn(`Capacitor plugin \"${pluginName}\" already registered. Cannot register plugins twice.`);\n            return registeredPlugin.proxy;\n        }\n        const platform = getPlatform();\n        const pluginHeader = getPluginHeader(pluginName);\n        let jsImplementation;\n        const loadPluginImplementation = async () => {\n            if (!jsImplementation && platform in jsImplementations) {\n                jsImplementation =\n                    typeof jsImplementations[platform] === 'function'\n                        ? (jsImplementation = await jsImplementations[platform]())\n                        : (jsImplementation = jsImplementations[platform]);\n            }\n            else if (capCustomPlatform !== null && !jsImplementation && 'web' in jsImplementations) {\n                jsImplementation =\n                    typeof jsImplementations['web'] === 'function'\n                        ? (jsImplementation = await jsImplementations['web']())\n                        : (jsImplementation = jsImplementations['web']);\n            }\n            return jsImplementation;\n        };\n        const createPluginMethod = (impl, prop) => {\n            var _a, _b;\n            if (pluginHeader) {\n                const methodHeader = pluginHeader === null || pluginHeader === void 0 ? void 0 : pluginHeader.methods.find((m) => prop === m.name);\n                if (methodHeader) {\n                    if (methodHeader.rtype === 'promise') {\n                        return (options) => cap.nativePromise(pluginName, prop.toString(), options);\n                    }\n                    else {\n                        return (options, callback) => cap.nativeCallback(pluginName, prop.toString(), options, callback);\n                    }\n                }\n                else if (impl) {\n                    return (_a = impl[prop]) === null || _a === void 0 ? void 0 : _a.bind(impl);\n                }\n            }\n            else if (impl) {\n                return (_b = impl[prop]) === null || _b === void 0 ? void 0 : _b.bind(impl);\n            }\n            else {\n                throw new CapacitorException(`\"${pluginName}\" plugin is not implemented on ${platform}`, ExceptionCode.Unimplemented);\n            }\n        };\n        const createPluginMethodWrapper = (prop) => {\n            let remove;\n            const wrapper = (...args) => {\n                const p = loadPluginImplementation().then((impl) => {\n                    const fn = createPluginMethod(impl, prop);\n                    if (fn) {\n                        const p = fn(...args);\n                        remove = p === null || p === void 0 ? void 0 : p.remove;\n                        return p;\n                    }\n                    else {\n                        throw new CapacitorException(`\"${pluginName}.${prop}()\" is not implemented on ${platform}`, ExceptionCode.Unimplemented);\n                    }\n                });\n                if (prop === 'addListener') {\n                    p.remove = async () => remove();\n                }\n                return p;\n            };\n            // Some flair ✨\n            wrapper.toString = () => `${prop.toString()}() { [capacitor code] }`;\n            Object.defineProperty(wrapper, 'name', {\n                value: prop,\n                writable: false,\n                configurable: false,\n            });\n            return wrapper;\n        };\n        const addListener = createPluginMethodWrapper('addListener');\n        const removeListener = createPluginMethodWrapper('removeListener');\n        const addListenerNative = (eventName, callback) => {\n            const call = addListener({ eventName }, callback);\n            const remove = async () => {\n                const callbackId = await call;\n                removeListener({\n                    eventName,\n                    callbackId,\n                }, callback);\n            };\n            const p = new Promise((resolve) => call.then(() => resolve({ remove })));\n            p.remove = async () => {\n                console.warn(`Using addListener() without 'await' is deprecated.`);\n                await remove();\n            };\n            return p;\n        };\n        const proxy = new Proxy({}, {\n            get(_, prop) {\n                switch (prop) {\n                    // https://github.com/facebook/react/issues/20030\n                    case '$$typeof':\n                        return undefined;\n                    case 'toJSON':\n                        return () => ({});\n                    case 'addListener':\n                        return pluginHeader ? addListenerNative : addListener;\n                    case 'removeListener':\n                        return removeListener;\n                    default:\n                        return createPluginMethodWrapper(prop);\n                }\n            },\n        });\n        Plugins[pluginName] = proxy;\n        registeredPlugins.set(pluginName, {\n            name: pluginName,\n            proxy,\n            platforms: new Set([...Object.keys(jsImplementations), ...(pluginHeader ? [platform] : [])]),\n        });\n        return proxy;\n    };\n    // Add in convertFileSrc for web, it will already be available in native context\n    if (!cap.convertFileSrc) {\n        cap.convertFileSrc = (filePath) => filePath;\n    }\n    cap.getPlatform = getPlatform;\n    cap.handleError = handleError;\n    cap.isNativePlatform = isNativePlatform;\n    cap.isPluginAvailable = isPluginAvailable;\n    cap.registerPlugin = registerPlugin;\n    cap.Exception = CapacitorException;\n    cap.DEBUG = !!cap.DEBUG;\n    cap.isLoggingEnabled = !!cap.isLoggingEnabled;\n    return cap;\n};\nconst initCapacitorGlobal = (win) => (win.Capacitor = createCapacitor(win));\n\nconst Capacitor = /*#__PURE__*/ initCapacitorGlobal(typeof globalThis !== 'undefined'\n    ? globalThis\n    : typeof self !== 'undefined'\n        ? self\n        : typeof window !== 'undefined'\n            ? window\n            : typeof global !== 'undefined'\n                ? global\n                : {});\nconst registerPlugin = Capacitor.registerPlugin;\n\n/**\n * Base class web plugins should extend.\n */\nclass WebPlugin {\n    constructor() {\n        this.listeners = {};\n        this.retainedEventArguments = {};\n        this.windowListeners = {};\n    }\n    addListener(eventName, listenerFunc) {\n        let firstListener = false;\n        const listeners = this.listeners[eventName];\n        if (!listeners) {\n            this.listeners[eventName] = [];\n            firstListener = true;\n        }\n        this.listeners[eventName].push(listenerFunc);\n        // If we haven't added a window listener for this event and it requires one,\n        // go ahead and add it\n        const windowListener = this.windowListeners[eventName];\n        if (windowListener && !windowListener.registered) {\n            this.addWindowListener(windowListener);\n        }\n        if (firstListener) {\n            this.sendRetainedArgumentsForEvent(eventName);\n        }\n        const remove = async () => this.removeListener(eventName, listenerFunc);\n        const p = Promise.resolve({ remove });\n        return p;\n    }\n    async removeAllListeners() {\n        this.listeners = {};\n        for (const listener in this.windowListeners) {\n            this.removeWindowListener(this.windowListeners[listener]);\n        }\n        this.windowListeners = {};\n    }\n    notifyListeners(eventName, data, retainUntilConsumed) {\n        const listeners = this.listeners[eventName];\n        if (!listeners) {\n            if (retainUntilConsumed) {\n                let args = this.retainedEventArguments[eventName];\n                if (!args) {\n                    args = [];\n                }\n                args.push(data);\n                this.retainedEventArguments[eventName] = args;\n            }\n            return;\n        }\n        listeners.forEach((listener) => listener(data));\n    }\n    hasListeners(eventName) {\n        var _a;\n        return !!((_a = this.listeners[eventName]) === null || _a === void 0 ? void 0 : _a.length);\n    }\n    registerWindowListener(windowEventName, pluginEventName) {\n        this.windowListeners[pluginEventName] = {\n            registered: false,\n            windowEventName,\n            pluginEventName,\n            handler: (event) => {\n                this.notifyListeners(pluginEventName, event);\n            },\n        };\n    }\n    unimplemented(msg = 'not implemented') {\n        return new Capacitor.Exception(msg, ExceptionCode.Unimplemented);\n    }\n    unavailable(msg = 'not available') {\n        return new Capacitor.Exception(msg, ExceptionCode.Unavailable);\n    }\n    async removeListener(eventName, listenerFunc) {\n        const listeners = this.listeners[eventName];\n        if (!listeners) {\n            return;\n        }\n        const index = listeners.indexOf(listenerFunc);\n        this.listeners[eventName].splice(index, 1);\n        // If there are no more listeners for this type of event,\n        // remove the window listener\n        if (!this.listeners[eventName].length) {\n            this.removeWindowListener(this.windowListeners[eventName]);\n        }\n    }\n    addWindowListener(handle) {\n        window.addEventListener(handle.windowEventName, handle.handler);\n        handle.registered = true;\n    }\n    removeWindowListener(handle) {\n        if (!handle) {\n            return;\n        }\n        window.removeEventListener(handle.windowEventName, handle.handler);\n        handle.registered = false;\n    }\n    sendRetainedArgumentsForEvent(eventName) {\n        const args = this.retainedEventArguments[eventName];\n        if (!args) {\n            return;\n        }\n        delete this.retainedEventArguments[eventName];\n        args.forEach((arg) => {\n            this.notifyListeners(eventName, arg);\n        });\n    }\n}\n\nconst WebView = /*#__PURE__*/ registerPlugin('WebView');\n/******** END WEB VIEW PLUGIN ********/\n/******** COOKIES PLUGIN ********/\n/**\n * Safely web encode a string value (inspired by js-cookie)\n * @param str The string value to encode\n */\nconst encode = (str) => encodeURIComponent(str)\n    .replace(/%(2[346B]|5E|60|7C)/g, decodeURIComponent)\n    .replace(/[()]/g, escape);\n/**\n * Safely web decode a string value (inspired by js-cookie)\n * @param str The string value to decode\n */\nconst decode = (str) => str.replace(/(%[\\dA-F]{2})+/gi, decodeURIComponent);\nclass CapacitorCookiesPluginWeb extends WebPlugin {\n    async getCookies() {\n        const cookies = document.cookie;\n        const cookieMap = {};\n        cookies.split(';').forEach((cookie) => {\n            if (cookie.length <= 0)\n                return;\n            // Replace first \"=\" with CAP_COOKIE to prevent splitting on additional \"=\"\n            let [key, value] = cookie.replace(/=/, 'CAP_COOKIE').split('CAP_COOKIE');\n            key = decode(key).trim();\n            value = decode(value).trim();\n            cookieMap[key] = value;\n        });\n        return cookieMap;\n    }\n    async setCookie(options) {\n        try {\n            // Safely Encoded Key/Value\n            const encodedKey = encode(options.key);\n            const encodedValue = encode(options.value);\n            // Clean & sanitize options\n            const expires = `; expires=${(options.expires || '').replace('expires=', '')}`; // Default is \"; expires=\"\n            const path = (options.path || '/').replace('path=', ''); // Default is \"path=/\"\n            const domain = options.url != null && options.url.length > 0 ? `domain=${options.url}` : '';\n            document.cookie = `${encodedKey}=${encodedValue || ''}${expires}; path=${path}; ${domain};`;\n        }\n        catch (error) {\n            return Promise.reject(error);\n        }\n    }\n    async deleteCookie(options) {\n        try {\n            document.cookie = `${options.key}=; Max-Age=0`;\n        }\n        catch (error) {\n            return Promise.reject(error);\n        }\n    }\n    async clearCookies() {\n        try {\n            const cookies = document.cookie.split(';') || [];\n            for (const cookie of cookies) {\n                document.cookie = cookie.replace(/^ +/, '').replace(/=.*/, `=;expires=${new Date().toUTCString()};path=/`);\n            }\n        }\n        catch (error) {\n            return Promise.reject(error);\n        }\n    }\n    async clearAllCookies() {\n        try {\n            await this.clearCookies();\n        }\n        catch (error) {\n            return Promise.reject(error);\n        }\n    }\n}\nconst CapacitorCookies = registerPlugin('CapacitorCookies', {\n    web: () => new CapacitorCookiesPluginWeb(),\n});\n// UTILITY FUNCTIONS\n/**\n * Read in a Blob value and return it as a base64 string\n * @param blob The blob value to convert to a base64 string\n */\nconst readBlobAsBase64 = async (blob) => new Promise((resolve, reject) => {\n    const reader = new FileReader();\n    reader.onload = () => {\n        const base64String = reader.result;\n        // remove prefix \"data:application/pdf;base64,\"\n        resolve(base64String.indexOf(',') >= 0 ? base64String.split(',')[1] : base64String);\n    };\n    reader.onerror = (error) => reject(error);\n    reader.readAsDataURL(blob);\n});\n/**\n * Normalize an HttpHeaders map by lowercasing all of the values\n * @param headers The HttpHeaders object to normalize\n */\nconst normalizeHttpHeaders = (headers = {}) => {\n    const originalKeys = Object.keys(headers);\n    const loweredKeys = Object.keys(headers).map((k) => k.toLocaleLowerCase());\n    const normalized = loweredKeys.reduce((acc, key, index) => {\n        acc[key] = headers[originalKeys[index]];\n        return acc;\n    }, {});\n    return normalized;\n};\n/**\n * Builds a string of url parameters that\n * @param params A map of url parameters\n * @param shouldEncode true if you should encodeURIComponent() the values (true by default)\n */\nconst buildUrlParams = (params, shouldEncode = true) => {\n    if (!params)\n        return null;\n    const output = Object.entries(params).reduce((accumulator, entry) => {\n        const [key, value] = entry;\n        let encodedValue;\n        let item;\n        if (Array.isArray(value)) {\n            item = '';\n            value.forEach((str) => {\n                encodedValue = shouldEncode ? encodeURIComponent(str) : str;\n                item += `${key}=${encodedValue}&`;\n            });\n            // last character will always be \"&\" so slice it off\n            item.slice(0, -1);\n        }\n        else {\n            encodedValue = shouldEncode ? encodeURIComponent(value) : value;\n            item = `${key}=${encodedValue}`;\n        }\n        return `${accumulator}&${item}`;\n    }, '');\n    // Remove initial \"&\" from the reduce\n    return output.substr(1);\n};\n/**\n * Build the RequestInit object based on the options passed into the initial request\n * @param options The Http plugin options\n * @param extra Any extra RequestInit values\n */\nconst buildRequestInit = (options, extra = {}) => {\n    const output = Object.assign({ method: options.method || 'GET', headers: options.headers }, extra);\n    // Get the content-type\n    const headers = normalizeHttpHeaders(options.headers);\n    const type = headers['content-type'] || '';\n    // If body is already a string, then pass it through as-is.\n    if (typeof options.data === 'string') {\n        output.body = options.data;\n    }\n    // Build request initializers based off of content-type\n    else if (type.includes('application/x-www-form-urlencoded')) {\n        const params = new URLSearchParams();\n        for (const [key, value] of Object.entries(options.data || {})) {\n            params.set(key, value);\n        }\n        output.body = params.toString();\n    }\n    else if (type.includes('multipart/form-data') || options.data instanceof FormData) {\n        const form = new FormData();\n        if (options.data instanceof FormData) {\n            options.data.forEach((value, key) => {\n                form.append(key, value);\n            });\n        }\n        else {\n            for (const key of Object.keys(options.data)) {\n                form.append(key, options.data[key]);\n            }\n        }\n        output.body = form;\n        const headers = new Headers(output.headers);\n        headers.delete('content-type'); // content-type will be set by `window.fetch` to includy boundary\n        output.headers = headers;\n    }\n    else if (type.includes('application/json') || typeof options.data === 'object') {\n        output.body = JSON.stringify(options.data);\n    }\n    return output;\n};\n// WEB IMPLEMENTATION\nclass CapacitorHttpPluginWeb extends WebPlugin {\n    /**\n     * Perform an Http request given a set of options\n     * @param options Options to build the HTTP request\n     */\n    async request(options) {\n        const requestInit = buildRequestInit(options, options.webFetchExtra);\n        const urlParams = buildUrlParams(options.params, options.shouldEncodeUrlParams);\n        const url = urlParams ? `${options.url}?${urlParams}` : options.url;\n        const response = await fetch(url, requestInit);\n        const contentType = response.headers.get('content-type') || '';\n        // Default to 'text' responseType so no parsing happens\n        let { responseType = 'text' } = response.ok ? options : {};\n        // If the response content-type is json, force the response to be json\n        if (contentType.includes('application/json')) {\n            responseType = 'json';\n        }\n        let data;\n        let blob;\n        switch (responseType) {\n            case 'arraybuffer':\n            case 'blob':\n                blob = await response.blob();\n                data = await readBlobAsBase64(blob);\n                break;\n            case 'json':\n                data = await response.json();\n                break;\n            case 'document':\n            case 'text':\n            default:\n                data = await response.text();\n        }\n        // Convert fetch headers to Capacitor HttpHeaders\n        const headers = {};\n        response.headers.forEach((value, key) => {\n            headers[key] = value;\n        });\n        return {\n            data,\n            headers,\n            status: response.status,\n            url: response.url,\n        };\n    }\n    /**\n     * Perform an Http GET request given a set of options\n     * @param options Options to build the HTTP request\n     */\n    async get(options) {\n        return this.request(Object.assign(Object.assign({}, options), { method: 'GET' }));\n    }\n    /**\n     * Perform an Http POST request given a set of options\n     * @param options Options to build the HTTP request\n     */\n    async post(options) {\n        return this.request(Object.assign(Object.assign({}, options), { method: 'POST' }));\n    }\n    /**\n     * Perform an Http PUT request given a set of options\n     * @param options Options to build the HTTP request\n     */\n    async put(options) {\n        return this.request(Object.assign(Object.assign({}, options), { method: 'PUT' }));\n    }\n    /**\n     * Perform an Http PATCH request given a set of options\n     * @param options Options to build the HTTP request\n     */\n    async patch(options) {\n        return this.request(Object.assign(Object.assign({}, options), { method: 'PATCH' }));\n    }\n    /**\n     * Perform an Http DELETE request given a set of options\n     * @param options Options to build the HTTP request\n     */\n    async delete(options) {\n        return this.request(Object.assign(Object.assign({}, options), { method: 'DELETE' }));\n    }\n}\nconst CapacitorHttp = registerPlugin('CapacitorHttp', {\n    web: () => new CapacitorHttpPluginWeb(),\n});\n/******** END HTTP PLUGIN ********/\n\nexport { Capacitor, CapacitorCookies, CapacitorException, CapacitorHttp, ExceptionCode, WebPlugin, WebView, buildRequestInit, registerPlugin };\n\n"], "mappings": ";;;;;AACA,IAAI;AAAA,CACH,SAAUA,gBAAe;AAOtB,EAAAA,eAAc,eAAe,IAAI;AAQjC,EAAAA,eAAc,aAAa,IAAI;AACnC,GAAG,kBAAkB,gBAAgB,CAAC,EAAE;AACxC,IAAM,qBAAN,cAAiC,MAAM;AAAA,EACnC,YAAY,SAAS,MAAM,MAAM;AAC7B,UAAM,OAAO;AACb,SAAK,UAAU;AACf,SAAK,OAAO;AACZ,SAAK,OAAO;AAAA,EAChB;AACJ;AACA,IAAM,gBAAgB,CAAC,QAAQ;AAC3B,MAAI,IAAI;AACR,MAAI,QAAQ,QAAQ,QAAQ,SAAS,SAAS,IAAI,eAAe;AAC7D,WAAO;AAAA,EACX,YACU,MAAM,KAAK,QAAQ,QAAQ,QAAQ,SAAS,SAAS,IAAI,YAAY,QAAQ,OAAO,SAAS,SAAS,GAAG,qBAAqB,QAAQ,OAAO,SAAS,SAAS,GAAG,QAAQ;AAChL,WAAO;AAAA,EACX,OACK;AACD,WAAO;AAAA,EACX;AACJ;AAEA,IAAM,kBAAkB,CAAC,QAAQ;AAC7B,QAAM,oBAAoB,IAAI,2BAA2B;AACzD,QAAM,MAAM,IAAI,aAAa,CAAC;AAC9B,QAAM,UAAW,IAAI,UAAU,IAAI,WAAW,CAAC;AAC/C,QAAM,cAAc,MAAM;AACtB,WAAO,sBAAsB,OAAO,kBAAkB,OAAO,cAAc,GAAG;AAAA,EAClF;AACA,QAAM,mBAAmB,MAAM,YAAY,MAAM;AACjD,QAAM,oBAAoB,CAAC,eAAe;AACtC,UAAM,SAAS,kBAAkB,IAAI,UAAU;AAC/C,QAAI,WAAW,QAAQ,WAAW,SAAS,SAAS,OAAO,UAAU,IAAI,YAAY,CAAC,GAAG;AAErF,aAAO;AAAA,IACX;AACA,QAAI,gBAAgB,UAAU,GAAG;AAE7B,aAAO;AAAA,IACX;AACA,WAAO;AAAA,EACX;AACA,QAAM,kBAAkB,CAAC,eAAe;AAAE,QAAI;AAAI,YAAQ,KAAK,IAAI,mBAAmB,QAAQ,OAAO,SAAS,SAAS,GAAG,KAAK,CAAC,MAAM,EAAE,SAAS,UAAU;AAAA,EAAG;AAC9J,QAAM,cAAc,CAAC,QAAQ,IAAI,QAAQ,MAAM,GAAG;AAClD,QAAM,oBAAoB,oBAAI,IAAI;AAClC,QAAMC,kBAAiB,CAAC,YAAY,oBAAoB,CAAC,MAAM;AAC3D,UAAM,mBAAmB,kBAAkB,IAAI,UAAU;AACzD,QAAI,kBAAkB;AAClB,cAAQ,KAAK,qBAAqB,UAAU,sDAAsD;AAClG,aAAO,iBAAiB;AAAA,IAC5B;AACA,UAAM,WAAW,YAAY;AAC7B,UAAM,eAAe,gBAAgB,UAAU;AAC/C,QAAI;AACJ,UAAM,2BAA2B,MAAY;AACzC,UAAI,CAAC,oBAAoB,YAAY,mBAAmB;AACpD,2BACI,OAAO,kBAAkB,QAAQ,MAAM,aAChC,mBAAmB,MAAM,kBAAkB,QAAQ,EAAE,IACrD,mBAAmB,kBAAkB,QAAQ;AAAA,MAC5D,WACS,sBAAsB,QAAQ,CAAC,oBAAoB,SAAS,mBAAmB;AACpF,2BACI,OAAO,kBAAkB,KAAK,MAAM,aAC7B,mBAAmB,MAAM,kBAAkB,KAAK,EAAE,IAClD,mBAAmB,kBAAkB,KAAK;AAAA,MACzD;AACA,aAAO;AAAA,IACX;AACA,UAAM,qBAAqB,CAAC,MAAM,SAAS;AACvC,UAAI,IAAI;AACR,UAAI,cAAc;AACd,cAAM,eAAe,iBAAiB,QAAQ,iBAAiB,SAAS,SAAS,aAAa,QAAQ,KAAK,CAAC,MAAM,SAAS,EAAE,IAAI;AACjI,YAAI,cAAc;AACd,cAAI,aAAa,UAAU,WAAW;AAClC,mBAAO,CAAC,YAAY,IAAI,cAAc,YAAY,KAAK,SAAS,GAAG,OAAO;AAAA,UAC9E,OACK;AACD,mBAAO,CAAC,SAAS,aAAa,IAAI,eAAe,YAAY,KAAK,SAAS,GAAG,SAAS,QAAQ;AAAA,UACnG;AAAA,QACJ,WACS,MAAM;AACX,kBAAQ,KAAK,KAAK,IAAI,OAAO,QAAQ,OAAO,SAAS,SAAS,GAAG,KAAK,IAAI;AAAA,QAC9E;AAAA,MACJ,WACS,MAAM;AACX,gBAAQ,KAAK,KAAK,IAAI,OAAO,QAAQ,OAAO,SAAS,SAAS,GAAG,KAAK,IAAI;AAAA,MAC9E,OACK;AACD,cAAM,IAAI,mBAAmB,IAAI,UAAU,kCAAkC,QAAQ,IAAI,cAAc,aAAa;AAAA,MACxH;AAAA,IACJ;AACA,UAAM,4BAA4B,CAAC,SAAS;AACxC,UAAI;AACJ,YAAM,UAAU,IAAI,SAAS;AACzB,cAAM,IAAI,yBAAyB,EAAE,KAAK,CAAC,SAAS;AAChD,gBAAM,KAAK,mBAAmB,MAAM,IAAI;AACxC,cAAI,IAAI;AACJ,kBAAMC,KAAI,GAAG,GAAG,IAAI;AACpB,qBAASA,OAAM,QAAQA,OAAM,SAAS,SAASA,GAAE;AACjD,mBAAOA;AAAA,UACX,OACK;AACD,kBAAM,IAAI,mBAAmB,IAAI,UAAU,IAAI,IAAI,6BAA6B,QAAQ,IAAI,cAAc,aAAa;AAAA,UAC3H;AAAA,QACJ,CAAC;AACD,YAAI,SAAS,eAAe;AACxB,YAAE,SAAS,MAAS;AAAG,0BAAO;AAAA;AAAA,QAClC;AACA,eAAO;AAAA,MACX;AAEA,cAAQ,WAAW,MAAM,GAAG,KAAK,SAAS,CAAC;AAC3C,aAAO,eAAe,SAAS,QAAQ;AAAA,QACnC,OAAO;AAAA,QACP,UAAU;AAAA,QACV,cAAc;AAAA,MAClB,CAAC;AACD,aAAO;AAAA,IACX;AACA,UAAM,cAAc,0BAA0B,aAAa;AAC3D,UAAM,iBAAiB,0BAA0B,gBAAgB;AACjE,UAAM,oBAAoB,CAAC,WAAW,aAAa;AAC/C,YAAM,OAAO,YAAY,EAAE,UAAU,GAAG,QAAQ;AAChD,YAAM,SAAS,MAAY;AACvB,cAAM,aAAa,MAAM;AACzB,uBAAe;AAAA,UACX;AAAA,UACA;AAAA,QACJ,GAAG,QAAQ;AAAA,MACf;AACA,YAAM,IAAI,IAAI,QAAQ,CAAC,YAAY,KAAK,KAAK,MAAM,QAAQ,EAAE,OAAO,CAAC,CAAC,CAAC;AACvE,QAAE,SAAS,MAAY;AACnB,gBAAQ,KAAK,oDAAoD;AACjE,cAAM,OAAO;AAAA,MACjB;AACA,aAAO;AAAA,IACX;AACA,UAAM,QAAQ,IAAI,MAAM,CAAC,GAAG;AAAA,MACxB,IAAI,GAAG,MAAM;AACT,gBAAQ,MAAM;AAAA;AAAA,UAEV,KAAK;AACD,mBAAO;AAAA,UACX,KAAK;AACD,mBAAO,OAAO,CAAC;AAAA,UACnB,KAAK;AACD,mBAAO,eAAe,oBAAoB;AAAA,UAC9C,KAAK;AACD,mBAAO;AAAA,UACX;AACI,mBAAO,0BAA0B,IAAI;AAAA,QAC7C;AAAA,MACJ;AAAA,IACJ,CAAC;AACD,YAAQ,UAAU,IAAI;AACtB,sBAAkB,IAAI,YAAY;AAAA,MAC9B,MAAM;AAAA,MACN;AAAA,MACA,WAAW,oBAAI,IAAI,CAAC,GAAG,OAAO,KAAK,iBAAiB,GAAG,GAAI,eAAe,CAAC,QAAQ,IAAI,CAAC,CAAE,CAAC;AAAA,IAC/F,CAAC;AACD,WAAO;AAAA,EACX;AAEA,MAAI,CAAC,IAAI,gBAAgB;AACrB,QAAI,iBAAiB,CAAC,aAAa;AAAA,EACvC;AACA,MAAI,cAAc;AAClB,MAAI,cAAc;AAClB,MAAI,mBAAmB;AACvB,MAAI,oBAAoB;AACxB,MAAI,iBAAiBD;AACrB,MAAI,YAAY;AAChB,MAAI,QAAQ,CAAC,CAAC,IAAI;AAClB,MAAI,mBAAmB,CAAC,CAAC,IAAI;AAC7B,SAAO;AACX;AACA,IAAM,sBAAsB,CAAC,QAAS,IAAI,YAAY,gBAAgB,GAAG;AAEzE,IAAM,YAA0B,oBAAoB,OAAO,eAAe,cACpE,aACA,OAAO,SAAS,cACZ,OACA,OAAO,WAAW,cACd,SACA,OAAO,WAAW,cACd,SACA,CAAC,CAAC;AACpB,IAAM,iBAAiB,UAAU;AAKjC,IAAM,YAAN,MAAgB;AAAA,EACZ,cAAc;AACV,SAAK,YAAY,CAAC;AAClB,SAAK,yBAAyB,CAAC;AAC/B,SAAK,kBAAkB,CAAC;AAAA,EAC5B;AAAA,EACA,YAAY,WAAW,cAAc;AACjC,QAAI,gBAAgB;AACpB,UAAM,YAAY,KAAK,UAAU,SAAS;AAC1C,QAAI,CAAC,WAAW;AACZ,WAAK,UAAU,SAAS,IAAI,CAAC;AAC7B,sBAAgB;AAAA,IACpB;AACA,SAAK,UAAU,SAAS,EAAE,KAAK,YAAY;AAG3C,UAAM,iBAAiB,KAAK,gBAAgB,SAAS;AACrD,QAAI,kBAAkB,CAAC,eAAe,YAAY;AAC9C,WAAK,kBAAkB,cAAc;AAAA,IACzC;AACA,QAAI,eAAe;AACf,WAAK,8BAA8B,SAAS;AAAA,IAChD;AACA,UAAM,SAAS,MAAS;AAAG,kBAAK,eAAe,WAAW,YAAY;AAAA;AACtE,UAAM,IAAI,QAAQ,QAAQ,EAAE,OAAO,CAAC;AACpC,WAAO;AAAA,EACX;AAAA,EACM,qBAAqB;AAAA;AACvB,WAAK,YAAY,CAAC;AAClB,iBAAW,YAAY,KAAK,iBAAiB;AACzC,aAAK,qBAAqB,KAAK,gBAAgB,QAAQ,CAAC;AAAA,MAC5D;AACA,WAAK,kBAAkB,CAAC;AAAA,IAC5B;AAAA;AAAA,EACA,gBAAgB,WAAW,MAAM,qBAAqB;AAClD,UAAM,YAAY,KAAK,UAAU,SAAS;AAC1C,QAAI,CAAC,WAAW;AACZ,UAAI,qBAAqB;AACrB,YAAI,OAAO,KAAK,uBAAuB,SAAS;AAChD,YAAI,CAAC,MAAM;AACP,iBAAO,CAAC;AAAA,QACZ;AACA,aAAK,KAAK,IAAI;AACd,aAAK,uBAAuB,SAAS,IAAI;AAAA,MAC7C;AACA;AAAA,IACJ;AACA,cAAU,QAAQ,CAAC,aAAa,SAAS,IAAI,CAAC;AAAA,EAClD;AAAA,EACA,aAAa,WAAW;AACpB,QAAI;AACJ,WAAO,CAAC,GAAG,KAAK,KAAK,UAAU,SAAS,OAAO,QAAQ,OAAO,SAAS,SAAS,GAAG;AAAA,EACvF;AAAA,EACA,uBAAuB,iBAAiB,iBAAiB;AACrD,SAAK,gBAAgB,eAAe,IAAI;AAAA,MACpC,YAAY;AAAA,MACZ;AAAA,MACA;AAAA,MACA,SAAS,CAAC,UAAU;AAChB,aAAK,gBAAgB,iBAAiB,KAAK;AAAA,MAC/C;AAAA,IACJ;AAAA,EACJ;AAAA,EACA,cAAc,MAAM,mBAAmB;AACnC,WAAO,IAAI,UAAU,UAAU,KAAK,cAAc,aAAa;AAAA,EACnE;AAAA,EACA,YAAY,MAAM,iBAAiB;AAC/B,WAAO,IAAI,UAAU,UAAU,KAAK,cAAc,WAAW;AAAA,EACjE;AAAA,EACM,eAAe,WAAW,cAAc;AAAA;AAC1C,YAAM,YAAY,KAAK,UAAU,SAAS;AAC1C,UAAI,CAAC,WAAW;AACZ;AAAA,MACJ;AACA,YAAM,QAAQ,UAAU,QAAQ,YAAY;AAC5C,WAAK,UAAU,SAAS,EAAE,OAAO,OAAO,CAAC;AAGzC,UAAI,CAAC,KAAK,UAAU,SAAS,EAAE,QAAQ;AACnC,aAAK,qBAAqB,KAAK,gBAAgB,SAAS,CAAC;AAAA,MAC7D;AAAA,IACJ;AAAA;AAAA,EACA,kBAAkB,QAAQ;AACtB,WAAO,iBAAiB,OAAO,iBAAiB,OAAO,OAAO;AAC9D,WAAO,aAAa;AAAA,EACxB;AAAA,EACA,qBAAqB,QAAQ;AACzB,QAAI,CAAC,QAAQ;AACT;AAAA,IACJ;AACA,WAAO,oBAAoB,OAAO,iBAAiB,OAAO,OAAO;AACjE,WAAO,aAAa;AAAA,EACxB;AAAA,EACA,8BAA8B,WAAW;AACrC,UAAM,OAAO,KAAK,uBAAuB,SAAS;AAClD,QAAI,CAAC,MAAM;AACP;AAAA,IACJ;AACA,WAAO,KAAK,uBAAuB,SAAS;AAC5C,SAAK,QAAQ,CAAC,QAAQ;AAClB,WAAK,gBAAgB,WAAW,GAAG;AAAA,IACvC,CAAC;AAAA,EACL;AACJ;AAEA,IAAM,UAAwB,eAAe,SAAS;AAOtD,IAAM,SAAS,CAAC,QAAQ,mBAAmB,GAAG,EACzC,QAAQ,wBAAwB,kBAAkB,EAClD,QAAQ,SAAS,MAAM;AAK5B,IAAM,SAAS,CAAC,QAAQ,IAAI,QAAQ,oBAAoB,kBAAkB;AAC1E,IAAM,4BAAN,cAAwC,UAAU;AAAA,EACxC,aAAa;AAAA;AACf,YAAM,UAAU,SAAS;AACzB,YAAM,YAAY,CAAC;AACnB,cAAQ,MAAM,GAAG,EAAE,QAAQ,CAAC,WAAW;AACnC,YAAI,OAAO,UAAU;AACjB;AAEJ,YAAI,CAAC,KAAK,KAAK,IAAI,OAAO,QAAQ,KAAK,YAAY,EAAE,MAAM,YAAY;AACvE,cAAM,OAAO,GAAG,EAAE,KAAK;AACvB,gBAAQ,OAAO,KAAK,EAAE,KAAK;AAC3B,kBAAU,GAAG,IAAI;AAAA,MACrB,CAAC;AACD,aAAO;AAAA,IACX;AAAA;AAAA,EACM,UAAU,SAAS;AAAA;AACrB,UAAI;AAEA,cAAM,aAAa,OAAO,QAAQ,GAAG;AACrC,cAAM,eAAe,OAAO,QAAQ,KAAK;AAEzC,cAAM,UAAU,cAAc,QAAQ,WAAW,IAAI,QAAQ,YAAY,EAAE,CAAC;AAC5E,cAAM,QAAQ,QAAQ,QAAQ,KAAK,QAAQ,SAAS,EAAE;AACtD,cAAM,SAAS,QAAQ,OAAO,QAAQ,QAAQ,IAAI,SAAS,IAAI,UAAU,QAAQ,GAAG,KAAK;AACzF,iBAAS,SAAS,GAAG,UAAU,IAAI,gBAAgB,EAAE,GAAG,OAAO,UAAU,IAAI,KAAK,MAAM;AAAA,MAC5F,SACO,OAAO;AACV,eAAO,QAAQ,OAAO,KAAK;AAAA,MAC/B;AAAA,IACJ;AAAA;AAAA,EACM,aAAa,SAAS;AAAA;AACxB,UAAI;AACA,iBAAS,SAAS,GAAG,QAAQ,GAAG;AAAA,MACpC,SACO,OAAO;AACV,eAAO,QAAQ,OAAO,KAAK;AAAA,MAC/B;AAAA,IACJ;AAAA;AAAA,EACM,eAAe;AAAA;AACjB,UAAI;AACA,cAAM,UAAU,SAAS,OAAO,MAAM,GAAG,KAAK,CAAC;AAC/C,mBAAW,UAAU,SAAS;AAC1B,mBAAS,SAAS,OAAO,QAAQ,OAAO,EAAE,EAAE,QAAQ,OAAO,cAAa,oBAAI,KAAK,GAAE,YAAY,CAAC,SAAS;AAAA,QAC7G;AAAA,MACJ,SACO,OAAO;AACV,eAAO,QAAQ,OAAO,KAAK;AAAA,MAC/B;AAAA,IACJ;AAAA;AAAA,EACM,kBAAkB;AAAA;AACpB,UAAI;AACA,cAAM,KAAK,aAAa;AAAA,MAC5B,SACO,OAAO;AACV,eAAO,QAAQ,OAAO,KAAK;AAAA,MAC/B;AAAA,IACJ;AAAA;AACJ;AACA,IAAM,mBAAmB,eAAe,oBAAoB;AAAA,EACxD,KAAK,MAAM,IAAI,0BAA0B;AAC7C,CAAC;AAMD,IAAM,mBAAmB,CAAO,SAAM;AAAG,aAAI,QAAQ,CAAC,SAAS,WAAW;AACtE,UAAM,SAAS,IAAI,WAAW;AAC9B,WAAO,SAAS,MAAM;AAClB,YAAM,eAAe,OAAO;AAE5B,cAAQ,aAAa,QAAQ,GAAG,KAAK,IAAI,aAAa,MAAM,GAAG,EAAE,CAAC,IAAI,YAAY;AAAA,IACtF;AACA,WAAO,UAAU,CAAC,UAAU,OAAO,KAAK;AACxC,WAAO,cAAc,IAAI;AAAA,EAC7B,CAAC;AAAA;AAKD,IAAM,uBAAuB,CAAC,UAAU,CAAC,MAAM;AAC3C,QAAM,eAAe,OAAO,KAAK,OAAO;AACxC,QAAM,cAAc,OAAO,KAAK,OAAO,EAAE,IAAI,CAAC,MAAM,EAAE,kBAAkB,CAAC;AACzE,QAAM,aAAa,YAAY,OAAO,CAAC,KAAK,KAAK,UAAU;AACvD,QAAI,GAAG,IAAI,QAAQ,aAAa,KAAK,CAAC;AACtC,WAAO;AAAA,EACX,GAAG,CAAC,CAAC;AACL,SAAO;AACX;AAMA,IAAM,iBAAiB,CAAC,QAAQ,eAAe,SAAS;AACpD,MAAI,CAAC;AACD,WAAO;AACX,QAAM,SAAS,OAAO,QAAQ,MAAM,EAAE,OAAO,CAAC,aAAa,UAAU;AACjE,UAAM,CAAC,KAAK,KAAK,IAAI;AACrB,QAAI;AACJ,QAAI;AACJ,QAAI,MAAM,QAAQ,KAAK,GAAG;AACtB,aAAO;AACP,YAAM,QAAQ,CAAC,QAAQ;AACnB,uBAAe,eAAe,mBAAmB,GAAG,IAAI;AACxD,gBAAQ,GAAG,GAAG,IAAI,YAAY;AAAA,MAClC,CAAC;AAED,WAAK,MAAM,GAAG,EAAE;AAAA,IACpB,OACK;AACD,qBAAe,eAAe,mBAAmB,KAAK,IAAI;AAC1D,aAAO,GAAG,GAAG,IAAI,YAAY;AAAA,IACjC;AACA,WAAO,GAAG,WAAW,IAAI,IAAI;AAAA,EACjC,GAAG,EAAE;AAEL,SAAO,OAAO,OAAO,CAAC;AAC1B;AAMA,IAAM,mBAAmB,CAAC,SAAS,QAAQ,CAAC,MAAM;AAC9C,QAAM,SAAS,OAAO,OAAO,EAAE,QAAQ,QAAQ,UAAU,OAAO,SAAS,QAAQ,QAAQ,GAAG,KAAK;AAEjG,QAAM,UAAU,qBAAqB,QAAQ,OAAO;AACpD,QAAM,OAAO,QAAQ,cAAc,KAAK;AAExC,MAAI,OAAO,QAAQ,SAAS,UAAU;AAClC,WAAO,OAAO,QAAQ;AAAA,EAC1B,WAES,KAAK,SAAS,mCAAmC,GAAG;AACzD,UAAM,SAAS,IAAI,gBAAgB;AACnC,eAAW,CAAC,KAAK,KAAK,KAAK,OAAO,QAAQ,QAAQ,QAAQ,CAAC,CAAC,GAAG;AAC3D,aAAO,IAAI,KAAK,KAAK;AAAA,IACzB;AACA,WAAO,OAAO,OAAO,SAAS;AAAA,EAClC,WACS,KAAK,SAAS,qBAAqB,KAAK,QAAQ,gBAAgB,UAAU;AAC/E,UAAM,OAAO,IAAI,SAAS;AAC1B,QAAI,QAAQ,gBAAgB,UAAU;AAClC,cAAQ,KAAK,QAAQ,CAAC,OAAO,QAAQ;AACjC,aAAK,OAAO,KAAK,KAAK;AAAA,MAC1B,CAAC;AAAA,IACL,OACK;AACD,iBAAW,OAAO,OAAO,KAAK,QAAQ,IAAI,GAAG;AACzC,aAAK,OAAO,KAAK,QAAQ,KAAK,GAAG,CAAC;AAAA,MACtC;AAAA,IACJ;AACA,WAAO,OAAO;AACd,UAAME,WAAU,IAAI,QAAQ,OAAO,OAAO;AAC1C,IAAAA,SAAQ,OAAO,cAAc;AAC7B,WAAO,UAAUA;AAAA,EACrB,WACS,KAAK,SAAS,kBAAkB,KAAK,OAAO,QAAQ,SAAS,UAAU;AAC5E,WAAO,OAAO,KAAK,UAAU,QAAQ,IAAI;AAAA,EAC7C;AACA,SAAO;AACX;AAEA,IAAM,yBAAN,cAAqC,UAAU;AAAA;AAAA;AAAA;AAAA;AAAA,EAKrC,QAAQ,SAAS;AAAA;AACnB,YAAM,cAAc,iBAAiB,SAAS,QAAQ,aAAa;AACnE,YAAM,YAAY,eAAe,QAAQ,QAAQ,QAAQ,qBAAqB;AAC9E,YAAM,MAAM,YAAY,GAAG,QAAQ,GAAG,IAAI,SAAS,KAAK,QAAQ;AAChE,YAAM,WAAW,MAAM,MAAM,KAAK,WAAW;AAC7C,YAAM,cAAc,SAAS,QAAQ,IAAI,cAAc,KAAK;AAE5D,UAAI,EAAE,eAAe,OAAO,IAAI,SAAS,KAAK,UAAU,CAAC;AAEzD,UAAI,YAAY,SAAS,kBAAkB,GAAG;AAC1C,uBAAe;AAAA,MACnB;AACA,UAAI;AACJ,UAAI;AACJ,cAAQ,cAAc;AAAA,QAClB,KAAK;AAAA,QACL,KAAK;AACD,iBAAO,MAAM,SAAS,KAAK;AAC3B,iBAAO,MAAM,iBAAiB,IAAI;AAClC;AAAA,QACJ,KAAK;AACD,iBAAO,MAAM,SAAS,KAAK;AAC3B;AAAA,QACJ,KAAK;AAAA,QACL,KAAK;AAAA,QACL;AACI,iBAAO,MAAM,SAAS,KAAK;AAAA,MACnC;AAEA,YAAM,UAAU,CAAC;AACjB,eAAS,QAAQ,QAAQ,CAAC,OAAO,QAAQ;AACrC,gBAAQ,GAAG,IAAI;AAAA,MACnB,CAAC;AACD,aAAO;AAAA,QACH;AAAA,QACA;AAAA,QACA,QAAQ,SAAS;AAAA,QACjB,KAAK,SAAS;AAAA,MAClB;AAAA,IACJ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAKM,IAAI,SAAS;AAAA;AACf,aAAO,KAAK,QAAQ,OAAO,OAAO,OAAO,OAAO,CAAC,GAAG,OAAO,GAAG,EAAE,QAAQ,MAAM,CAAC,CAAC;AAAA,IACpF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAKM,KAAK,SAAS;AAAA;AAChB,aAAO,KAAK,QAAQ,OAAO,OAAO,OAAO,OAAO,CAAC,GAAG,OAAO,GAAG,EAAE,QAAQ,OAAO,CAAC,CAAC;AAAA,IACrF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAKM,IAAI,SAAS;AAAA;AACf,aAAO,KAAK,QAAQ,OAAO,OAAO,OAAO,OAAO,CAAC,GAAG,OAAO,GAAG,EAAE,QAAQ,MAAM,CAAC,CAAC;AAAA,IACpF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAKM,MAAM,SAAS;AAAA;AACjB,aAAO,KAAK,QAAQ,OAAO,OAAO,OAAO,OAAO,CAAC,GAAG,OAAO,GAAG,EAAE,QAAQ,QAAQ,CAAC,CAAC;AAAA,IACtF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAKM,OAAO,SAAS;AAAA;AAClB,aAAO,KAAK,QAAQ,OAAO,OAAO,OAAO,OAAO,CAAC,GAAG,OAAO,GAAG,EAAE,QAAQ,SAAS,CAAC,CAAC;AAAA,IACvF;AAAA;AACJ;AACA,IAAM,gBAAgB,eAAe,iBAAiB;AAAA,EAClD,KAAK,MAAM,IAAI,uBAAuB;AAC1C,CAAC;", "names": ["ExceptionCode", "registerPlugin", "p", "headers"]}