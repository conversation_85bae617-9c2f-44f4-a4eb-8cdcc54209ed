import{a as Ce,d as we}from"./chunk-YSN7XVTD.js";import{h as K}from"./chunk-REYR55MP.js";import{a as q,e as Fe,f as Le}from"./chunk-C5RQ2IC2.js";import{a as re}from"./chunk-42C7ZIID.js";import{e as v}from"./chunk-JHI3MBHO.js";var oe,wn=n=>{if(oe===void 0){let t=n.style.animationName!==void 0,r=n.style.webkitAnimationName!==void 0;oe=!t&&r?"-webkit-":""}return oe},se=(n,t,r)=>{let a=t.startsWith("animation")?wn(n):"";n.style.setProperty(a+t,r)},N=(n=[],t)=>{if(t!==void 0){let r=Array.isArray(t)?t:[t];return[...n,...r]}return n},Kn=n=>{let t,r,a,d,b,A,f=[],C=[],$=[],w=!1,l,G={},Z=[],J=[],Q={},L=0,x=!1,M=!1,k,I,P,S=!0,W=!1,T=!0,o,O=!1,ce=n,X=[],V=[],U=[],p=[],m=[],fe=[],le=[],ue=[],de=[],me=[],E=[],Ve=typeof AnimationEffect=="function"||re!==void 0&&typeof re.AnimationEffect=="function",h=typeof Element=="function"&&typeof Element.prototype.animate=="function"&&Ve,ge=()=>E,Re=e=>(m.forEach(i=>{i.destroy(e)}),De(e),p.length=0,m.length=0,f.length=0,Ue(),w=!1,T=!0,o),De=e=>{Ee(),e&&Be()},xe=()=>{x=!1,M=!1,T=!0,k=void 0,I=void 0,P=void 0,L=0,W=!1,S=!0,O=!1},Me=()=>L!==0&&!O,he=(e,i)=>{let s=i.findIndex(c=>c.c===e);s>-1&&i.splice(s,1)},Oe=(e,i)=>(U.push({c:e,o:i}),o),j=(e,i)=>((i?.oneTimeCallback?V:X).push({c:e,o:i}),o),Ue=()=>(X.length=0,V.length=0,o),Ee=()=>{h&&(E.forEach(e=>{e.cancel()}),E.length=0)},Be=()=>{fe.forEach(e=>{e?.parentNode&&e.parentNode.removeChild(e)}),fe.length=0},ze=e=>(le.push(e),o),qe=e=>(ue.push(e),o),Ke=e=>(de.push(e),o),Ne=e=>(me.push(e),o),Ye=e=>(C=N(C,e),o),He=e=>($=N($,e),o),$e=(e={})=>(G=e,o),Ge=(e=[])=>{for(let i of e)G[i]="";return o},Ze=e=>(Z=N(Z,e),o),Je=e=>(J=N(J,e),o),Qe=(e={})=>(Q=e,o),Xe=(e=[])=>{for(let i of e)Q[i]="";return o},ee=()=>b!==void 0?b:l?l.getFill():"both",B=()=>k!==void 0?k:A!==void 0?A:l?l.getDirection():"normal",ne=()=>x?"linear":a!==void 0?a:l?l.getEasing():"linear",_=()=>M?0:I!==void 0?I:r!==void 0?r:l?l.getDuration():0,te=()=>d!==void 0?d:l?l.getIterations():1,ie=()=>P!==void 0?P:t!==void 0?t:l?l.getDelay():0,je=()=>f,en=e=>(A=e,g(!0),o),nn=e=>(b=e,g(!0),o),tn=e=>(t=e,g(!0),o),rn=e=>(a=e,g(!0),o),on=e=>(!h&&e===0&&(e=1),r=e,g(!0),o),sn=e=>(d=e,g(!0),o),an=e=>(l=e,o),cn=e=>{if(e!=null)if(e.nodeType===1)p.push(e);else if(e.length>=0)for(let i=0;i<e.length;i++)p.push(e[i]);else Le("createAnimation - Invalid addElement value.");return o},fn=e=>{if(e!=null)if(Array.isArray(e))for(let i of e)i.parent(o),m.push(i);else e.parent(o),m.push(e);return o},ln=e=>{let i=f!==e;return f=e,i&&un(f),o},un=e=>{h&&ge().forEach(i=>{let s=i.effect;if(s.setKeyframes)s.setKeyframes(e);else{let c=new KeyframeEffect(s.target,e,s.getTiming());i.effect=c}})},dn=()=>{le.forEach(c=>c()),ue.forEach(c=>c());let e=C,i=$,s=G;p.forEach(c=>{let u=c.classList;e.forEach(y=>u.add(y)),i.forEach(y=>u.remove(y));for(let y in s)s.hasOwnProperty(y)&&se(c,y,s[y])})},mn=()=>{de.forEach(u=>u()),me.forEach(u=>u());let e=S?1:0,i=Z,s=J,c=Q;p.forEach(u=>{let y=u.classList;i.forEach(F=>y.add(F)),s.forEach(F=>y.remove(F));for(let F in c)c.hasOwnProperty(F)&&se(u,F,c[F])}),I=void 0,k=void 0,P=void 0,X.forEach(u=>u.c(e,o)),V.forEach(u=>u.c(e,o)),V.length=0,T=!0,S&&(W=!0),S=!0},z=()=>{L!==0&&(L--,L===0&&(mn(),l&&l.animationFinish()))},gn=()=>{p.forEach(e=>{let i=e.animate(f,{id:ce,delay:ie(),duration:_(),easing:ne(),iterations:te(),fill:ee(),direction:B()});i.pause(),E.push(i)}),E.length>0&&(E[0].onfinish=()=>{z()})},ye=()=>{dn(),f.length>0&&h&&gn(),w=!0},R=e=>{e=Math.min(Math.max(e,0),.9999),h&&E.forEach(i=>{i.currentTime=i.effect.getComputedTiming().delay+_()*e,i.pause()})},pe=e=>{E.forEach(i=>{i.effect.updateTiming({delay:ie(),duration:_(),easing:ne(),iterations:te(),fill:ee(),direction:B()})}),e!==void 0&&R(e)},g=(e=!1,i=!0,s)=>(e&&m.forEach(c=>{c.update(e,i,s)}),h&&pe(s),o),hn=(e=!1,i)=>(m.forEach(s=>{s.progressStart(e,i)}),be(),x=e,w||ye(),g(!1,!0,i),o),En=e=>(m.forEach(i=>{i.progressStep(e)}),R(e),o),yn=(e,i,s)=>(x=!1,m.forEach(c=>{c.progressEnd(e,i,s)}),s!==void 0&&(I=s),W=!1,S=!0,e===0?(k=B()==="reverse"?"normal":"reverse",k==="reverse"&&(S=!1),h?(g(),R(1-i)):(P=(1-i)*_()*-1,g(!1,!1))):e===1&&(h?(g(),R(i)):(P=i*_()*-1,g(!1,!1))),e!==void 0&&!l&&ve(),o),be=()=>{w&&(h?E.forEach(e=>{e.pause()}):p.forEach(e=>{se(e,"animation-play-state","paused")}),O=!0)},pn=()=>(m.forEach(e=>{e.pause()}),be(),o),bn=()=>{z()},vn=()=>{E.forEach(e=>{e.play()}),(f.length===0||p.length===0)&&z()},An=()=>{h&&(R(0),pe())},ve=e=>new Promise(i=>{e?.sync&&(M=!0,j(()=>M=!1,{oneTimeCallback:!0})),w||ye(),W&&(An(),W=!1),T&&(L=m.length+1,T=!1);let s=()=>{he(c,V),i()},c=()=>{he(s,U),i()};j(c,{oneTimeCallback:!0}),Oe(s,{oneTimeCallback:!0}),m.forEach(u=>{u.play()}),h?vn():bn(),O=!1}),Cn=()=>{m.forEach(e=>{e.stop()}),w&&(Ee(),w=!1),xe(),U.forEach(e=>e.c(0,o)),U.length=0},Ae=(e,i)=>{let s=f[0];return s!==void 0&&(s.offset===void 0||s.offset===0)?s[e]=i:f=[{offset:0,[e]:i},...f],o};return o={parentAnimation:l,elements:p,childAnimations:m,id:ce,animationFinish:z,from:Ae,to:(e,i)=>{let s=f[f.length-1];return s!==void 0&&(s.offset===void 0||s.offset===1)?s[e]=i:f=[...f,{offset:1,[e]:i}],o},fromTo:(e,i,s)=>Ae(e,i).to(e,s),parent:an,play:ve,pause:pn,stop:Cn,destroy:Re,keyframes:ln,addAnimation:fn,addElement:cn,update:g,fill:nn,direction:en,iterations:sn,duration:on,easing:rn,delay:tn,getWebAnimations:ge,getKeyframes:je,getFill:ee,getDirection:B,getDelay:ie,getIterations:te,getEasing:ne,getDuration:_,afterAddRead:Ke,afterAddWrite:Ne,afterClearStyles:Xe,afterStyles:Qe,afterRemoveClass:Je,afterAddClass:Ze,beforeAddRead:ze,beforeAddWrite:qe,beforeClearStyles:Ge,beforeStyles:$e,beforeRemoveClass:He,beforeAddClass:Ye,onFinish:j,isRunning:Me,progressStart:hn,progressStep:En,progressEnd:yn}};var Fn="ionViewWillEnter",Ln="ionViewDidEnter",kn="ionViewWillLeave",Pn="ionViewDidLeave",Gn="ionViewWillUnload",D=n=>{n.tabIndex=-1,n.focus()},Y=n=>n.offsetParent!==null,Sn=()=>({saveViewFocus:r=>{if(q.get("focusManagerPriority",!1)){let d=document.activeElement;d!==null&&r?.contains(d)&&d.setAttribute(ke,"true")}},setViewFocus:r=>{let a=q.get("focusManagerPriority",!1);if(Array.isArray(a)&&!r.contains(document.activeElement)){let d=r.querySelector(`[${ke}]`);if(d&&Y(d)){D(d);return}for(let b of a)switch(b){case"content":let A=r.querySelector('main, [role="main"]');if(A&&Y(A)){D(A);return}break;case"heading":let f=r.querySelector('h1, [role="heading"][aria-level="1"]');if(f&&Y(f)){D(f);return}break;case"banner":let C=r.querySelector('header, [role="banner"]');if(C&&Y(C)){D(C);return}break;default:Fe(`Unrecognized focus manager priority value ${b}`);break}D(r)}}}),ke="ion-last-focus",_n=()=>import("./chunk-ZCM4LAKF.js"),In=()=>import("./chunk-42K7QRLD.js"),_e=Sn(),Zn=n=>new Promise((t,r)=>{we(()=>{Wn(n),Tn(n).then(a=>{a.animation&&a.animation.destroy(),Pe(n),t(a)},a=>{Pe(n),r(a)})})}),Wn=n=>{let t=n.enteringEl,r=n.leavingEl;_e.saveViewFocus(r),On(t,r,n.direction),n.showGoBack?t.classList.add("can-go-back"):t.classList.remove("can-go-back"),Se(t,!1),t.style.setProperty("pointer-events","none"),r&&(Se(r,!1),r.style.setProperty("pointer-events","none"))},Tn=n=>v(null,null,function*(){let t=yield Vn(n);return t&&Ce.isBrowser?Rn(t,n):Dn(n)}),Pe=n=>{let t=n.enteringEl,r=n.leavingEl;t.classList.remove("ion-page-invisible"),t.style.removeProperty("pointer-events"),r!==void 0&&(r.classList.remove("ion-page-invisible"),r.style.removeProperty("pointer-events")),_e.setViewFocus(t)},Vn=n=>v(null,null,function*(){return!n.leavingEl||!n.animated||n.duration===0?void 0:n.animationBuilder?n.animationBuilder:n.mode==="ios"?(yield _n()).iosTransitionAnimation:(yield In()).mdTransitionAnimation}),Rn=(n,t)=>v(null,null,function*(){yield Ie(t,!0);let r=n(t.baseEl,t);We(t.enteringEl,t.leavingEl);let a=yield Mn(r,t);return t.progressCallback&&t.progressCallback(void 0),a&&Te(t.enteringEl,t.leavingEl),{hasCompleted:a,animation:r}}),Dn=n=>v(null,null,function*(){let t=n.enteringEl,r=n.leavingEl,a=q.get("focusManagerPriority",!1);return yield Ie(n,a),We(t,r),Te(t,r),{hasCompleted:!0}}),Ie=(n,t)=>v(null,null,function*(){(n.deepWait!==void 0?n.deepWait:t)&&(yield Promise.all([ae(n.enteringEl),ae(n.leavingEl)])),yield xn(n.viewIsReady,n.enteringEl)}),xn=(n,t)=>v(null,null,function*(){n&&(yield n(t))}),Mn=(n,t)=>{let r=t.progressCallback,a=new Promise(d=>{n.onFinish(b=>d(b===1))});return r?(n.progressStart(!0),r(n)):n.play(),a},We=(n,t)=>{H(t,kn),H(n,Fn)},Te=(n,t)=>{H(n,Ln),H(t,Pn)},H=(n,t)=>{if(n){let r=new CustomEvent(t,{bubbles:!1,cancelable:!1});n.dispatchEvent(r)}},Jn=()=>new Promise(n=>K(()=>K(()=>n()))),ae=n=>v(null,null,function*(){let t=n;if(t){if(t.componentOnReady!=null){if((yield t.componentOnReady())!=null)return}else if(t.__registerHost!=null){yield new Promise(a=>K(a));return}yield Promise.all(Array.from(t.children).map(ae))}}),Se=(n,t)=>{t?(n.setAttribute("aria-hidden","true"),n.classList.add("ion-page-hidden")):(n.hidden=!1,n.removeAttribute("aria-hidden"),n.classList.remove("ion-page-hidden"))},On=(n,t,r)=>{n!==void 0&&(n.style.zIndex=r==="back"?"99":"101"),t!==void 0&&(t.style.zIndex="100")},Qn=n=>{if(n.classList.contains("ion-page"))return n;let t=n.querySelector(":scope > .ion-page, :scope > ion-nav, :scope > ion-tabs");return t||n};export{Kn as a,Fn as b,Ln as c,kn as d,Pn as e,Gn as f,Zn as g,Jn as h,ae as i,Qn as j};
