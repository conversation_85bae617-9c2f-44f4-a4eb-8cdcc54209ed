{"version": 3, "sources": ["../../../../../../node_modules/@ionic/core/dist/esm/ion-nav_2.entry.js"], "sourcesContent": ["/*!\n * (C) Ionic http://ionicframework.com - MIT License\n */\nimport { r as registerInstance, d as createEvent, e as getIonMode, l as config, m as printIonWarning, h, k as getElement, j as Host } from './index-B_U9CtaY.js';\nimport { g as getTimeGivenProgression } from './cubic-bezier-hHmYLOfE.js';\nimport { s as shallowEqualStringMap, l as assert } from './helpers-1O4D2b7y.js';\nimport { l as lifecycle, t as transition, s as set<PERSON>ageHidden, d as LIFECYCLE_WILL_UNLOAD, b as LIFECYCLE_WILL_LEAVE, c as LIFECYCLE_DID_LEAVE } from './index-DfBA5ztX.js';\nimport { a as attachComponent } from './framework-delegate-DxcnWic_.js';\n\nconst VIEW_STATE_NEW = 1;\nconst VIEW_STATE_ATTACHED = 2;\nconst VIEW_STATE_DESTROYED = 3;\n// TODO(FW-2832): types\nclass ViewController {\n    constructor(component, params) {\n        this.component = component;\n        this.params = params;\n        this.state = VIEW_STATE_NEW;\n    }\n    async init(container) {\n        this.state = VIEW_STATE_ATTACHED;\n        if (!this.element) {\n            const component = this.component;\n            this.element = await attachComponent(this.delegate, container, component, ['ion-page', 'ion-page-invisible'], this.params);\n        }\n    }\n    /**\n     * DOM WRITE\n     */\n    _destroy() {\n        assert(this.state !== VIEW_STATE_DESTROYED, 'view state must be ATTACHED');\n        const element = this.element;\n        if (element) {\n            if (this.delegate) {\n                this.delegate.removeViewFromDom(element.parentElement, element);\n            }\n            else {\n                element.remove();\n            }\n        }\n        this.nav = undefined;\n        this.state = VIEW_STATE_DESTROYED;\n    }\n}\nconst matches = (view, id, params) => {\n    if (!view) {\n        return false;\n    }\n    if (view.component !== id) {\n        return false;\n    }\n    return shallowEqualStringMap(view.params, params);\n};\nconst convertToView = (page, params) => {\n    if (!page) {\n        return null;\n    }\n    if (page instanceof ViewController) {\n        return page;\n    }\n    return new ViewController(page, params);\n};\nconst convertToViews = (pages) => {\n    return pages\n        .map((page) => {\n        if (page instanceof ViewController) {\n            return page;\n        }\n        if ('component' in page) {\n            return convertToView(page.component, page.componentProps === null ? undefined : page.componentProps);\n        }\n        return convertToView(page, undefined);\n    })\n        .filter((v) => v !== null);\n};\n\nconst navCss = \":host{left:0;right:0;top:0;bottom:0;position:absolute;contain:layout size style;z-index:0}\";\n\nconst Nav = class {\n    constructor(hostRef) {\n        registerInstance(this, hostRef);\n        this.ionNavWillLoad = createEvent(this, \"ionNavWillLoad\", 7);\n        this.ionNavWillChange = createEvent(this, \"ionNavWillChange\", 3);\n        this.ionNavDidChange = createEvent(this, \"ionNavDidChange\", 3);\n        this.transInstr = [];\n        this.gestureOrAnimationInProgress = false;\n        this.useRouter = false;\n        this.isTransitioning = false;\n        this.destroyed = false;\n        this.views = [];\n        this.didLoad = false;\n        /**\n         * If `true`, the nav should animate the transition of components.\n         */\n        this.animated = true;\n    }\n    swipeGestureChanged() {\n        if (this.gesture) {\n            this.gesture.enable(this.swipeGesture === true);\n        }\n    }\n    rootChanged() {\n        if (this.root === undefined) {\n            return;\n        }\n        if (this.didLoad === false) {\n            /**\n             * If the component has not loaded yet, we can skip setting up the root component.\n             * It will be called when `componentDidLoad` fires.\n             */\n            return;\n        }\n        if (!this.useRouter) {\n            if (this.root !== undefined) {\n                this.setRoot(this.root, this.rootParams);\n            }\n        }\n    }\n    componentWillLoad() {\n        this.useRouter = document.querySelector('ion-router') !== null && this.el.closest('[no-router]') === null;\n        if (this.swipeGesture === undefined) {\n            const mode = getIonMode(this);\n            this.swipeGesture = config.getBoolean('swipeBackEnabled', mode === 'ios');\n        }\n        this.ionNavWillLoad.emit();\n    }\n    async componentDidLoad() {\n        // We want to set this flag before any watch callbacks are manually called\n        this.didLoad = true;\n        this.rootChanged();\n        this.gesture = (await import('./swipe-back-VdaUzLWy.js')).createSwipeBackGesture(this.el, this.canStart.bind(this), this.onStart.bind(this), this.onMove.bind(this), this.onEnd.bind(this));\n        this.swipeGestureChanged();\n    }\n    connectedCallback() {\n        this.destroyed = false;\n    }\n    disconnectedCallback() {\n        for (const view of this.views) {\n            lifecycle(view.element, LIFECYCLE_WILL_UNLOAD);\n            view._destroy();\n        }\n        // Release swipe back gesture and transition.\n        if (this.gesture) {\n            this.gesture.destroy();\n            this.gesture = undefined;\n        }\n        this.transInstr.length = 0;\n        this.views.length = 0;\n        this.destroyed = true;\n    }\n    /**\n     * Push a new component onto the current navigation stack. Pass any additional\n     * information along as an object. This additional information is accessible\n     * through NavParams.\n     *\n     * @param component The component to push onto the navigation stack.\n     * @param componentProps Any properties of the component.\n     * @param opts The navigation options.\n     * @param done The transition complete function.\n     */\n    push(component, componentProps, opts, done) {\n        return this.insert(-1, component, componentProps, opts, done);\n    }\n    /**\n     * Inserts a component into the navigation stack at the specified index.\n     * This is useful to add a component at any point in the navigation stack.\n     *\n     * @param insertIndex The index to insert the component at in the stack.\n     * @param component The component to insert into the navigation stack.\n     * @param componentProps Any properties of the component.\n     * @param opts The navigation options.\n     * @param done The transition complete function.\n     */\n    insert(insertIndex, component, componentProps, opts, done) {\n        return this.insertPages(insertIndex, [{ component, componentProps }], opts, done);\n    }\n    /**\n     * Inserts an array of components into the navigation stack at the specified index.\n     * The last component in the array will become instantiated as a view, and animate\n     * in to become the active view.\n     *\n     * @param insertIndex The index to insert the components at in the stack.\n     * @param insertComponents The components to insert into the navigation stack.\n     * @param opts The navigation options.\n     * @param done The transition complete function.\n     */\n    insertPages(insertIndex, insertComponents, opts, done) {\n        return this.queueTrns({\n            insertStart: insertIndex,\n            insertViews: insertComponents,\n            opts,\n        }, done);\n    }\n    /**\n     * Pop a component off of the navigation stack. Navigates back from the current\n     * component.\n     *\n     * @param opts The navigation options.\n     * @param done The transition complete function.\n     */\n    pop(opts, done) {\n        return this.removeIndex(-1, 1, opts, done);\n    }\n    /**\n     * Pop to a specific index in the navigation stack.\n     *\n     * @param indexOrViewCtrl The index or view controller to pop to.\n     * @param opts The navigation options.\n     * @param done The transition complete function.\n     */\n    popTo(indexOrViewCtrl, opts, done) {\n        const ti = {\n            removeStart: -1,\n            removeCount: -1,\n            opts,\n        };\n        if (typeof indexOrViewCtrl === 'object' && indexOrViewCtrl.component) {\n            ti.removeView = indexOrViewCtrl;\n            ti.removeStart = 1;\n        }\n        else if (typeof indexOrViewCtrl === 'number') {\n            ti.removeStart = indexOrViewCtrl + 1;\n        }\n        return this.queueTrns(ti, done);\n    }\n    /**\n     * Navigate back to the root of the stack, no matter how far back that is.\n     *\n     * @param opts The navigation options.\n     * @param done The transition complete function.\n     */\n    popToRoot(opts, done) {\n        return this.removeIndex(1, -1, opts, done);\n    }\n    /**\n     * Removes a component from the navigation stack at the specified index.\n     *\n     * @param startIndex The number to begin removal at.\n     * @param removeCount The number of components to remove.\n     * @param opts The navigation options.\n     * @param done The transition complete function.\n     */\n    removeIndex(startIndex, removeCount = 1, opts, done) {\n        return this.queueTrns({\n            removeStart: startIndex,\n            removeCount,\n            opts,\n        }, done);\n    }\n    /**\n     * Set the root for the current navigation stack to a component.\n     *\n     * @param component The component to set as the root of the navigation stack.\n     * @param componentProps Any properties of the component.\n     * @param opts The navigation options.\n     * @param done The transition complete function.\n     */\n    setRoot(component, componentProps, opts, done) {\n        return this.setPages([{ component, componentProps }], opts, done);\n    }\n    /**\n     * Set the views of the current navigation stack and navigate to the last view.\n     * By default animations are disabled, but they can be enabled by passing options\n     * to the navigation controller. Navigation parameters can also be passed to the\n     * individual pages in the array.\n     *\n     * @param views The list of views to set as the navigation stack.\n     * @param opts The navigation options.\n     * @param done The transition complete function.\n     */\n    setPages(views, opts, done) {\n        opts !== null && opts !== void 0 ? opts : (opts = {});\n        // if animation wasn't set to true then default it to NOT animate\n        if (opts.animated !== true) {\n            opts.animated = false;\n        }\n        return this.queueTrns({\n            insertStart: 0,\n            insertViews: views,\n            removeStart: 0,\n            removeCount: -1,\n            opts,\n        }, done);\n    }\n    /**\n     * Called by the router to update the view.\n     *\n     * @param id The component tag.\n     * @param params The component params.\n     * @param direction A direction hint.\n     * @param animation an AnimationBuilder.\n     *\n     * @return the status.\n     * @internal\n     */\n    setRouteId(id, params, direction, animation) {\n        const active = this.getActiveSync();\n        if (matches(active, id, params)) {\n            return Promise.resolve({\n                changed: false,\n                element: active.element,\n            });\n        }\n        let resolve;\n        const promise = new Promise((r) => (resolve = r));\n        let finish;\n        const commonOpts = {\n            updateURL: false,\n            viewIsReady: (enteringEl) => {\n                let mark;\n                const p = new Promise((r) => (mark = r));\n                resolve({\n                    changed: true,\n                    element: enteringEl,\n                    markVisible: async () => {\n                        mark();\n                        await finish;\n                    },\n                });\n                return p;\n            },\n        };\n        if (direction === 'root') {\n            finish = this.setRoot(id, params, commonOpts);\n        }\n        else {\n            // Look for a view matching the target in the view stack.\n            const viewController = this.views.find((v) => matches(v, id, params));\n            if (viewController) {\n                finish = this.popTo(viewController, Object.assign(Object.assign({}, commonOpts), { direction: 'back', animationBuilder: animation }));\n            }\n            else if (direction === 'forward') {\n                finish = this.push(id, params, Object.assign(Object.assign({}, commonOpts), { animationBuilder: animation }));\n            }\n            else if (direction === 'back') {\n                finish = this.setRoot(id, params, Object.assign(Object.assign({}, commonOpts), { direction: 'back', animated: true, animationBuilder: animation }));\n            }\n        }\n        return promise;\n    }\n    /**\n     * Called by <ion-router> to retrieve the current component.\n     *\n     * @internal\n     */\n    async getRouteId() {\n        const active = this.getActiveSync();\n        if (active) {\n            return {\n                id: active.element.tagName,\n                params: active.params,\n                element: active.element,\n            };\n        }\n        return undefined;\n    }\n    /**\n     * Get the active view.\n     */\n    async getActive() {\n        return this.getActiveSync();\n    }\n    /**\n     * Get the view at the specified index.\n     *\n     * @param index The index of the view.\n     */\n    async getByIndex(index) {\n        return this.views[index];\n    }\n    /**\n     * Returns `true` if the current view can go back.\n     *\n     * @param view The view to check.\n     */\n    async canGoBack(view) {\n        return this.canGoBackSync(view);\n    }\n    /**\n     * Get the previous view.\n     *\n     * @param view The view to get.\n     */\n    async getPrevious(view) {\n        return this.getPreviousSync(view);\n    }\n    /**\n     * Returns the number of views in the stack.\n     */\n    async getLength() {\n        return Promise.resolve(this.views.length);\n    }\n    getActiveSync() {\n        return this.views[this.views.length - 1];\n    }\n    canGoBackSync(view = this.getActiveSync()) {\n        return !!(view && this.getPreviousSync(view));\n    }\n    getPreviousSync(view = this.getActiveSync()) {\n        if (!view) {\n            return undefined;\n        }\n        const views = this.views;\n        const index = views.indexOf(view);\n        return index > 0 ? views[index - 1] : undefined;\n    }\n    /**\n     * Adds a navigation stack change to the queue and schedules it to run.\n     *\n     * @returns Whether the transition succeeds.\n     */\n    async queueTrns(ti, done) {\n        var _a, _b;\n        if (this.isTransitioning && ((_a = ti.opts) === null || _a === void 0 ? void 0 : _a.skipIfBusy)) {\n            return false;\n        }\n        const promise = new Promise((resolve, reject) => {\n            ti.resolve = resolve;\n            ti.reject = reject;\n        });\n        ti.done = done;\n        /**\n         * If using router, check to see if navigation hooks\n         * will allow us to perform this transition. This\n         * is required in order for hooks to work with\n         * the ion-back-button or swipe to go back.\n         */\n        if (ti.opts && ti.opts.updateURL !== false && this.useRouter) {\n            const router = document.querySelector('ion-router');\n            if (router) {\n                const canTransition = await router.canTransition();\n                if (canTransition === false) {\n                    return false;\n                }\n                if (typeof canTransition === 'string') {\n                    router.push(canTransition, ti.opts.direction || 'back');\n                    return false;\n                }\n            }\n        }\n        // Normalize empty\n        if (((_b = ti.insertViews) === null || _b === void 0 ? void 0 : _b.length) === 0) {\n            ti.insertViews = undefined;\n        }\n        // Enqueue transition instruction\n        this.transInstr.push(ti);\n        // if there isn't a transition already happening\n        // then this will kick off this transition\n        this.nextTrns();\n        return promise;\n    }\n    success(result, ti) {\n        if (this.destroyed) {\n            this.fireError('nav controller was destroyed', ti);\n            return;\n        }\n        if (ti.done) {\n            ti.done(result.hasCompleted, result.requiresTransition, result.enteringView, result.leavingView, result.direction);\n        }\n        ti.resolve(result.hasCompleted);\n        if (ti.opts.updateURL !== false && this.useRouter) {\n            const router = document.querySelector('ion-router');\n            if (router) {\n                const direction = result.direction === 'back' ? 'back' : 'forward';\n                router.navChanged(direction);\n            }\n        }\n    }\n    failed(rejectReason, ti) {\n        if (this.destroyed) {\n            this.fireError('nav controller was destroyed', ti);\n            return;\n        }\n        this.transInstr.length = 0;\n        this.fireError(rejectReason, ti);\n    }\n    fireError(rejectReason, ti) {\n        if (ti.done) {\n            ti.done(false, false, rejectReason);\n        }\n        if (ti.reject && !this.destroyed) {\n            ti.reject(rejectReason);\n        }\n        else {\n            ti.resolve(false);\n        }\n    }\n    /**\n     * Consumes the next transition in the queue.\n     *\n     * @returns whether the transition is executed.\n     */\n    nextTrns() {\n        // this is the framework's bread 'n butta function\n        // only one transition is allowed at any given time\n        if (this.isTransitioning) {\n            return false;\n        }\n        // there is no transition happening right now, executes the next instructions.\n        const ti = this.transInstr.shift();\n        if (!ti) {\n            return false;\n        }\n        this.runTransition(ti);\n        return true;\n    }\n    /** Executes all the transition instruction from the queue. */\n    async runTransition(ti) {\n        try {\n            // set that this nav is actively transitioning\n            this.ionNavWillChange.emit();\n            this.isTransitioning = true;\n            this.prepareTI(ti);\n            const leavingView = this.getActiveSync();\n            const enteringView = this.getEnteringView(ti, leavingView);\n            if (!leavingView && !enteringView) {\n                throw new Error('no views in the stack to be removed');\n            }\n            if (enteringView && enteringView.state === VIEW_STATE_NEW) {\n                await enteringView.init(this.el);\n            }\n            this.postViewInit(enteringView, leavingView, ti);\n            // Needs transition?\n            const requiresTransition = (ti.enteringRequiresTransition || ti.leavingRequiresTransition) && enteringView !== leavingView;\n            if (requiresTransition && ti.opts && leavingView) {\n                const isBackDirection = ti.opts.direction === 'back';\n                /**\n                 * If heading back, use the entering page's animation\n                 * unless otherwise specified by the developer.\n                 */\n                if (isBackDirection) {\n                    ti.opts.animationBuilder = ti.opts.animationBuilder || (enteringView === null || enteringView === void 0 ? void 0 : enteringView.animationBuilder);\n                }\n                leavingView.animationBuilder = ti.opts.animationBuilder;\n            }\n            let result;\n            if (requiresTransition) {\n                result = await this.transition(enteringView, leavingView, ti);\n            }\n            else {\n                // transition is not required, so we are already done!\n                // they're inserting/removing the views somewhere in the middle or\n                // beginning, so visually nothing needs to animate/transition\n                // resolve immediately because there's no animation that's happening\n                result = {\n                    hasCompleted: true,\n                    requiresTransition: false,\n                };\n            }\n            this.success(result, ti);\n            this.ionNavDidChange.emit();\n        }\n        catch (rejectReason) {\n            this.failed(rejectReason, ti);\n        }\n        this.isTransitioning = false;\n        this.nextTrns();\n    }\n    prepareTI(ti) {\n        var _a, _b;\n        var _c;\n        const viewsLength = this.views.length;\n        (_a = ti.opts) !== null && _a !== void 0 ? _a : (ti.opts = {});\n        (_b = (_c = ti.opts).delegate) !== null && _b !== void 0 ? _b : (_c.delegate = this.delegate);\n        if (ti.removeView !== undefined) {\n            assert(ti.removeStart !== undefined, 'removeView needs removeStart');\n            assert(ti.removeCount !== undefined, 'removeView needs removeCount');\n            const index = this.views.indexOf(ti.removeView);\n            if (index < 0) {\n                throw new Error('removeView was not found');\n            }\n            ti.removeStart += index;\n        }\n        if (ti.removeStart !== undefined) {\n            if (ti.removeStart < 0) {\n                ti.removeStart = viewsLength - 1;\n            }\n            if (ti.removeCount < 0) {\n                ti.removeCount = viewsLength - ti.removeStart;\n            }\n            ti.leavingRequiresTransition = ti.removeCount > 0 && ti.removeStart + ti.removeCount === viewsLength;\n        }\n        if (ti.insertViews) {\n            // allow -1 to be passed in to auto push it on the end\n            // and clean up the index if it's larger then the size of the stack\n            if (ti.insertStart < 0 || ti.insertStart > viewsLength) {\n                ti.insertStart = viewsLength;\n            }\n            ti.enteringRequiresTransition = ti.insertStart === viewsLength;\n        }\n        const insertViews = ti.insertViews;\n        if (!insertViews) {\n            return;\n        }\n        assert(insertViews.length > 0, 'length can not be zero');\n        const viewControllers = convertToViews(insertViews);\n        if (viewControllers.length === 0) {\n            throw new Error('invalid views to insert');\n        }\n        // Check all the inserted view are correct\n        for (const view of viewControllers) {\n            view.delegate = ti.opts.delegate;\n            const nav = view.nav;\n            if (nav && nav !== this) {\n                throw new Error('inserted view was already inserted');\n            }\n            if (view.state === VIEW_STATE_DESTROYED) {\n                throw new Error('inserted view was already destroyed');\n            }\n        }\n        ti.insertViews = viewControllers;\n    }\n    /**\n     * Returns the view that will be entered considering the transition instructions.\n     *\n     * @param ti The instructions.\n     * @param leavingView The view being left or undefined if none.\n     *\n     * @returns The view that will be entered, undefined if none.\n     */\n    getEnteringView(ti, leavingView) {\n        // The last inserted view will be entered when view are inserted.\n        const insertViews = ti.insertViews;\n        if (insertViews !== undefined) {\n            return insertViews[insertViews.length - 1];\n        }\n        // When views are deleted, we will enter the last view that is not removed and not the view being left.\n        const removeStart = ti.removeStart;\n        if (removeStart !== undefined) {\n            const views = this.views;\n            const removeEnd = removeStart + ti.removeCount;\n            for (let i = views.length - 1; i >= 0; i--) {\n                const view = views[i];\n                if ((i < removeStart || i >= removeEnd) && view !== leavingView) {\n                    return view;\n                }\n            }\n        }\n        return undefined;\n    }\n    /**\n     * Adds and Removes the views from the navigation stack.\n     *\n     * @param enteringView The view being entered.\n     * @param leavingView The view being left.\n     * @param ti The instructions.\n     */\n    postViewInit(enteringView, leavingView, ti) {\n        var _a, _b, _c;\n        assert(leavingView || enteringView, 'Both leavingView and enteringView are null');\n        assert(ti.resolve, 'resolve must be valid');\n        assert(ti.reject, 'reject must be valid');\n        // Compute the views to remove.\n        const opts = ti.opts;\n        const { insertViews, removeStart, removeCount } = ti;\n        /** Records the view to destroy */\n        let destroyQueue;\n        // there are views to remove\n        if (removeStart !== undefined && removeCount !== undefined) {\n            assert(removeStart >= 0, 'removeStart can not be negative');\n            assert(removeCount >= 0, 'removeCount can not be negative');\n            destroyQueue = [];\n            for (let i = removeStart; i < removeStart + removeCount; i++) {\n                const view = this.views[i];\n                if (view !== undefined && view !== enteringView && view !== leavingView) {\n                    destroyQueue.push(view);\n                }\n            }\n            // default the direction to \"back\"\n            (_a = opts.direction) !== null && _a !== void 0 ? _a : (opts.direction = 'back');\n        }\n        const finalNumViews = this.views.length + ((_b = insertViews === null || insertViews === void 0 ? void 0 : insertViews.length) !== null && _b !== void 0 ? _b : 0) - (removeCount !== null && removeCount !== void 0 ? removeCount : 0);\n        assert(finalNumViews >= 0, 'final balance can not be negative');\n        if (finalNumViews === 0) {\n            printIonWarning(`[ion-nav] - You can't remove all the pages in the navigation stack. nav.pop() is probably called too many times.`, this, this.el);\n            throw new Error('navigation stack needs at least one root page');\n        }\n        // At this point the transition can not be rejected, any throw should be an error\n        // Insert the new views in the stack.\n        if (insertViews) {\n            // add the views to the\n            let insertIndex = ti.insertStart;\n            for (const view of insertViews) {\n                this.insertViewAt(view, insertIndex);\n                insertIndex++;\n            }\n            if (ti.enteringRequiresTransition) {\n                // default to forward if not already set\n                (_c = opts.direction) !== null && _c !== void 0 ? _c : (opts.direction = 'forward');\n            }\n        }\n        // if the views to be removed are in the beginning or middle\n        // and there is not a view that needs to visually transition out\n        // then just destroy them and don't transition anything\n        // batch all of lifecycles together\n        // let's make sure, callbacks are zoned\n        if (destroyQueue && destroyQueue.length > 0) {\n            for (const view of destroyQueue) {\n                lifecycle(view.element, LIFECYCLE_WILL_LEAVE);\n                lifecycle(view.element, LIFECYCLE_DID_LEAVE);\n                lifecycle(view.element, LIFECYCLE_WILL_UNLOAD);\n            }\n            // once all lifecycle events has been delivered, we can safely detroy the views\n            for (const view of destroyQueue) {\n                this.destroyView(view);\n            }\n        }\n    }\n    async transition(enteringView, leavingView, ti) {\n        // we should animate (duration > 0) if the pushed page is not the first one (startup)\n        // or if it is a portal (modal, actionsheet, etc.)\n        const opts = ti.opts;\n        const progressCallback = opts.progressAnimation\n            ? (ani) => {\n                /**\n                 * Because this progress callback is called asynchronously\n                 * it is possible for the gesture to start and end before\n                 * the animation is ever set. In that scenario, we should\n                 * immediately call progressEnd so that the transition promise\n                 * resolves and the gesture does not get locked up.\n                 */\n                if (ani !== undefined && !this.gestureOrAnimationInProgress) {\n                    this.gestureOrAnimationInProgress = true;\n                    ani.onFinish(() => {\n                        this.gestureOrAnimationInProgress = false;\n                    }, { oneTimeCallback: true });\n                    /**\n                     * Playing animation to beginning\n                     * with a duration of 0 prevents\n                     * any flickering when the animation\n                     * is later cleaned up.\n                     */\n                    ani.progressEnd(0, 0, 0);\n                }\n                else {\n                    this.sbAni = ani;\n                }\n            }\n            : undefined;\n        const mode = getIonMode(this);\n        const enteringEl = enteringView.element;\n        // eslint-disable-next-line @typescript-eslint/prefer-optional-chain\n        const leavingEl = leavingView && leavingView.element;\n        const animationOpts = Object.assign(Object.assign({ mode, showGoBack: this.canGoBackSync(enteringView), baseEl: this.el, progressCallback, animated: this.animated && config.getBoolean('animated', true), enteringEl,\n            leavingEl }, opts), { animationBuilder: opts.animationBuilder || this.animation || config.get('navAnimation') });\n        const { hasCompleted } = await transition(animationOpts);\n        return this.transitionFinish(hasCompleted, enteringView, leavingView, opts);\n    }\n    transitionFinish(hasCompleted, enteringView, leavingView, opts) {\n        /**\n         * If the transition did not complete, the leavingView will still be the active\n         * view on the stack. Otherwise unmount all the views after the enteringView.\n         */\n        const activeView = hasCompleted ? enteringView : leavingView;\n        if (activeView) {\n            this.unmountInactiveViews(activeView);\n        }\n        return {\n            hasCompleted,\n            requiresTransition: true,\n            enteringView,\n            leavingView,\n            direction: opts.direction,\n        };\n    }\n    /**\n     * Inserts a view at the specified index.\n     *\n     * When the view already is in the stack it will be moved to the new position.\n     *\n     * @param view The view to insert.\n     * @param index The index where to insert the view.\n     */\n    insertViewAt(view, index) {\n        const views = this.views;\n        const existingIndex = views.indexOf(view);\n        if (existingIndex > -1) {\n            assert(view.nav === this, 'view is not part of the nav');\n            // The view already in the stack, removes it.\n            views.splice(existingIndex, 1);\n            // and add it back at the requested index.\n            views.splice(index, 0, view);\n        }\n        else {\n            assert(!view.nav, 'nav is used');\n            // this is a new view to add to the stack\n            // create the new entering view\n            view.nav = this;\n            views.splice(index, 0, view);\n        }\n    }\n    /**\n     * Removes a view from the stack.\n     *\n     * @param view The view to remove.\n     */\n    removeView(view) {\n        assert(view.state === VIEW_STATE_ATTACHED || view.state === VIEW_STATE_DESTROYED, 'view state should be loaded or destroyed');\n        const views = this.views;\n        const index = views.indexOf(view);\n        assert(index > -1, 'view must be part of the stack');\n        if (index >= 0) {\n            views.splice(index, 1);\n        }\n    }\n    destroyView(view) {\n        view._destroy();\n        this.removeView(view);\n    }\n    /**\n     * Unmounts all inactive views after the specified active view.\n     *\n     * DOM WRITE\n     *\n     * @param activeView The view that is actively visible in the stack. Used to calculate which views to unmount.\n     */\n    unmountInactiveViews(activeView) {\n        // ok, cleanup time!! Destroy all of the views that are\n        // INACTIVE and come after the active view\n        // only do this if the views exist, though\n        if (this.destroyed) {\n            return;\n        }\n        const views = this.views;\n        const activeViewIndex = views.indexOf(activeView);\n        for (let i = views.length - 1; i >= 0; i--) {\n            const view = views[i];\n            /**\n             * When inserting multiple views via insertPages\n             * the last page will be transitioned to, but the\n             * others will not be. As a result, a DOM element\n             * will only be created for the last page inserted.\n             * As a result, it is possible to have views in the\n             * stack that do not have `view.element` yet.\n             */\n            const element = view.element;\n            if (element) {\n                if (i > activeViewIndex) {\n                    // this view comes after the active view\n                    // let's unload it\n                    lifecycle(element, LIFECYCLE_WILL_UNLOAD);\n                    this.destroyView(view);\n                }\n                else if (i < activeViewIndex) {\n                    // this view comes before the active view\n                    // and it is not a portal then ensure it is hidden\n                    setPageHidden(element, true);\n                }\n            }\n        }\n    }\n    canStart() {\n        return (!this.gestureOrAnimationInProgress &&\n            !!this.swipeGesture &&\n            !this.isTransitioning &&\n            this.transInstr.length === 0 &&\n            this.canGoBackSync());\n    }\n    onStart() {\n        this.gestureOrAnimationInProgress = true;\n        this.pop({ direction: 'back', progressAnimation: true });\n    }\n    onMove(stepValue) {\n        if (this.sbAni) {\n            this.sbAni.progressStep(stepValue);\n        }\n    }\n    onEnd(shouldComplete, stepValue, dur) {\n        if (this.sbAni) {\n            this.sbAni.onFinish(() => {\n                this.gestureOrAnimationInProgress = false;\n            }, { oneTimeCallback: true });\n            // Account for rounding errors in JS\n            let newStepValue = shouldComplete ? -1e-3 : 0.001;\n            /**\n             * Animation will be reversed here, so need to\n             * reverse the easing curve as well\n             *\n             * Additionally, we need to account for the time relative\n             * to the new easing curve, as `stepValue` is going to be given\n             * in terms of a linear curve.\n             */\n            if (!shouldComplete) {\n                this.sbAni.easing('cubic-bezier(1, 0, 0.68, 0.28)');\n                newStepValue += getTimeGivenProgression([0, 0], [1, 0], [0.68, 0.28], [1, 1], stepValue)[0];\n            }\n            else {\n                newStepValue += getTimeGivenProgression([0, 0], [0.32, 0.72], [0, 1], [1, 1], stepValue)[0];\n            }\n            this.sbAni.progressEnd(shouldComplete ? 1 : 0, newStepValue, dur);\n        }\n        else {\n            this.gestureOrAnimationInProgress = false;\n        }\n    }\n    render() {\n        return h(\"slot\", { key: '8067c9835d255daec61f33dba200fd3a6ff839a0' });\n    }\n    get el() { return getElement(this); }\n    static get watchers() { return {\n        \"swipeGesture\": [\"swipeGestureChanged\"],\n        \"root\": [\"rootChanged\"]\n    }; }\n};\nNav.style = navCss;\n\nconst navLink = (el, routerDirection, component, componentProps, routerAnimation) => {\n    const nav = el.closest('ion-nav');\n    if (nav) {\n        if (routerDirection === 'forward') {\n            if (component !== undefined) {\n                return nav.push(component, componentProps, { skipIfBusy: true, animationBuilder: routerAnimation });\n            }\n        }\n        else if (routerDirection === 'root') {\n            if (component !== undefined) {\n                return nav.setRoot(component, componentProps, { skipIfBusy: true, animationBuilder: routerAnimation });\n            }\n        }\n        else if (routerDirection === 'back') {\n            return nav.pop({ skipIfBusy: true, animationBuilder: routerAnimation });\n        }\n    }\n    return Promise.resolve(false);\n};\n\nconst NavLink = class {\n    constructor(hostRef) {\n        registerInstance(this, hostRef);\n        /**\n         * The transition direction when navigating to another page.\n         */\n        this.routerDirection = 'forward';\n        this.onClick = () => {\n            return navLink(this.el, this.routerDirection, this.component, this.componentProps, this.routerAnimation);\n        };\n    }\n    render() {\n        return h(Host, { key: '6dbb1ad4f351e9215375aac11ab9b53762e07a08', onClick: this.onClick });\n    }\n    get el() { return getElement(this); }\n};\n\nexport { Nav as ion_nav, NavLink as ion_nav_link };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASA,IAAM,iBAAiB;AACvB,IAAM,sBAAsB;AAC5B,IAAM,uBAAuB;AAE7B,IAAM,iBAAN,MAAqB;AAAA,EACjB,YAAY,WAAW,QAAQ;AAC3B,SAAK,YAAY;AACjB,SAAK,SAAS;AACd,SAAK,QAAQ;AAAA,EACjB;AAAA,EACM,KAAK,WAAW;AAAA;AAClB,WAAK,QAAQ;AACb,UAAI,CAAC,KAAK,SAAS;AACf,cAAM,YAAY,KAAK;AACvB,aAAK,UAAU,MAAM,gBAAgB,KAAK,UAAU,WAAW,WAAW,CAAC,YAAY,oBAAoB,GAAG,KAAK,MAAM;AAAA,MAC7H;AAAA,IACJ;AAAA;AAAA;AAAA;AAAA;AAAA,EAIA,WAAW;AACP,WAAO,KAAK,UAAU,sBAAsB,6BAA6B;AACzE,UAAM,UAAU,KAAK;AACrB,QAAI,SAAS;AACT,UAAI,KAAK,UAAU;AACf,aAAK,SAAS,kBAAkB,QAAQ,eAAe,OAAO;AAAA,MAClE,OACK;AACD,gBAAQ,OAAO;AAAA,MACnB;AAAA,IACJ;AACA,SAAK,MAAM;AACX,SAAK,QAAQ;AAAA,EACjB;AACJ;AACA,IAAM,UAAU,CAAC,MAAM,IAAI,WAAW;AAClC,MAAI,CAAC,MAAM;AACP,WAAO;AAAA,EACX;AACA,MAAI,KAAK,cAAc,IAAI;AACvB,WAAO;AAAA,EACX;AACA,SAAO,sBAAsB,KAAK,QAAQ,MAAM;AACpD;AACA,IAAM,gBAAgB,CAAC,MAAM,WAAW;AACpC,MAAI,CAAC,MAAM;AACP,WAAO;AAAA,EACX;AACA,MAAI,gBAAgB,gBAAgB;AAChC,WAAO;AAAA,EACX;AACA,SAAO,IAAI,eAAe,MAAM,MAAM;AAC1C;AACA,IAAM,iBAAiB,CAAC,UAAU;AAC9B,SAAO,MACF,IAAI,CAAC,SAAS;AACf,QAAI,gBAAgB,gBAAgB;AAChC,aAAO;AAAA,IACX;AACA,QAAI,eAAe,MAAM;AACrB,aAAO,cAAc,KAAK,WAAW,KAAK,mBAAmB,OAAO,SAAY,KAAK,cAAc;AAAA,IACvG;AACA,WAAO,cAAc,MAAM,MAAS;AAAA,EACxC,CAAC,EACI,OAAO,CAAC,MAAM,MAAM,IAAI;AACjC;AAEA,IAAM,SAAS;AAEf,IAAM,MAAM,MAAM;AAAA,EACd,YAAY,SAAS;AACjB,qBAAiB,MAAM,OAAO;AAC9B,SAAK,iBAAiB,YAAY,MAAM,kBAAkB,CAAC;AAC3D,SAAK,mBAAmB,YAAY,MAAM,oBAAoB,CAAC;AAC/D,SAAK,kBAAkB,YAAY,MAAM,mBAAmB,CAAC;AAC7D,SAAK,aAAa,CAAC;AACnB,SAAK,+BAA+B;AACpC,SAAK,YAAY;AACjB,SAAK,kBAAkB;AACvB,SAAK,YAAY;AACjB,SAAK,QAAQ,CAAC;AACd,SAAK,UAAU;AAIf,SAAK,WAAW;AAAA,EACpB;AAAA,EACA,sBAAsB;AAClB,QAAI,KAAK,SAAS;AACd,WAAK,QAAQ,OAAO,KAAK,iBAAiB,IAAI;AAAA,IAClD;AAAA,EACJ;AAAA,EACA,cAAc;AACV,QAAI,KAAK,SAAS,QAAW;AACzB;AAAA,IACJ;AACA,QAAI,KAAK,YAAY,OAAO;AAKxB;AAAA,IACJ;AACA,QAAI,CAAC,KAAK,WAAW;AACjB,UAAI,KAAK,SAAS,QAAW;AACzB,aAAK,QAAQ,KAAK,MAAM,KAAK,UAAU;AAAA,MAC3C;AAAA,IACJ;AAAA,EACJ;AAAA,EACA,oBAAoB;AAChB,SAAK,YAAY,SAAS,cAAc,YAAY,MAAM,QAAQ,KAAK,GAAG,QAAQ,aAAa,MAAM;AACrG,QAAI,KAAK,iBAAiB,QAAW;AACjC,YAAM,OAAO,WAAW,IAAI;AAC5B,WAAK,eAAe,OAAO,WAAW,oBAAoB,SAAS,KAAK;AAAA,IAC5E;AACA,SAAK,eAAe,KAAK;AAAA,EAC7B;AAAA,EACM,mBAAmB;AAAA;AAErB,WAAK,UAAU;AACf,WAAK,YAAY;AACjB,WAAK,WAAW,MAAM,OAAO,mCAA0B,GAAG,uBAAuB,KAAK,IAAI,KAAK,SAAS,KAAK,IAAI,GAAG,KAAK,QAAQ,KAAK,IAAI,GAAG,KAAK,OAAO,KAAK,IAAI,GAAG,KAAK,MAAM,KAAK,IAAI,CAAC;AAC1L,WAAK,oBAAoB;AAAA,IAC7B;AAAA;AAAA,EACA,oBAAoB;AAChB,SAAK,YAAY;AAAA,EACrB;AAAA,EACA,uBAAuB;AACnB,eAAW,QAAQ,KAAK,OAAO;AAC3B,gBAAU,KAAK,SAAS,qBAAqB;AAC7C,WAAK,SAAS;AAAA,IAClB;AAEA,QAAI,KAAK,SAAS;AACd,WAAK,QAAQ,QAAQ;AACrB,WAAK,UAAU;AAAA,IACnB;AACA,SAAK,WAAW,SAAS;AACzB,SAAK,MAAM,SAAS;AACpB,SAAK,YAAY;AAAA,EACrB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAWA,KAAK,WAAW,gBAAgB,MAAM,MAAM;AACxC,WAAO,KAAK,OAAO,IAAI,WAAW,gBAAgB,MAAM,IAAI;AAAA,EAChE;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAWA,OAAO,aAAa,WAAW,gBAAgB,MAAM,MAAM;AACvD,WAAO,KAAK,YAAY,aAAa,CAAC,EAAE,WAAW,eAAe,CAAC,GAAG,MAAM,IAAI;AAAA,EACpF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAWA,YAAY,aAAa,kBAAkB,MAAM,MAAM;AACnD,WAAO,KAAK,UAAU;AAAA,MAClB,aAAa;AAAA,MACb,aAAa;AAAA,MACb;AAAA,IACJ,GAAG,IAAI;AAAA,EACX;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,IAAI,MAAM,MAAM;AACZ,WAAO,KAAK,YAAY,IAAI,GAAG,MAAM,IAAI;AAAA,EAC7C;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,MAAM,iBAAiB,MAAM,MAAM;AAC/B,UAAM,KAAK;AAAA,MACP,aAAa;AAAA,MACb,aAAa;AAAA,MACb;AAAA,IACJ;AACA,QAAI,OAAO,oBAAoB,YAAY,gBAAgB,WAAW;AAClE,SAAG,aAAa;AAChB,SAAG,cAAc;AAAA,IACrB,WACS,OAAO,oBAAoB,UAAU;AAC1C,SAAG,cAAc,kBAAkB;AAAA,IACvC;AACA,WAAO,KAAK,UAAU,IAAI,IAAI;AAAA,EAClC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,UAAU,MAAM,MAAM;AAClB,WAAO,KAAK,YAAY,GAAG,IAAI,MAAM,IAAI;AAAA,EAC7C;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASA,YAAY,YAAY,cAAc,GAAG,MAAM,MAAM;AACjD,WAAO,KAAK,UAAU;AAAA,MAClB,aAAa;AAAA,MACb;AAAA,MACA;AAAA,IACJ,GAAG,IAAI;AAAA,EACX;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASA,QAAQ,WAAW,gBAAgB,MAAM,MAAM;AAC3C,WAAO,KAAK,SAAS,CAAC,EAAE,WAAW,eAAe,CAAC,GAAG,MAAM,IAAI;AAAA,EACpE;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAWA,SAAS,OAAO,MAAM,MAAM;AACxB,aAAS,QAAQ,SAAS,SAAS,OAAQ,OAAO,CAAC;AAEnD,QAAI,KAAK,aAAa,MAAM;AACxB,WAAK,WAAW;AAAA,IACpB;AACA,WAAO,KAAK,UAAU;AAAA,MAClB,aAAa;AAAA,MACb,aAAa;AAAA,MACb,aAAa;AAAA,MACb,aAAa;AAAA,MACb;AAAA,IACJ,GAAG,IAAI;AAAA,EACX;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAYA,WAAW,IAAI,QAAQ,WAAW,WAAW;AACzC,UAAM,SAAS,KAAK,cAAc;AAClC,QAAI,QAAQ,QAAQ,IAAI,MAAM,GAAG;AAC7B,aAAO,QAAQ,QAAQ;AAAA,QACnB,SAAS;AAAA,QACT,SAAS,OAAO;AAAA,MACpB,CAAC;AAAA,IACL;AACA,QAAI;AACJ,UAAM,UAAU,IAAI,QAAQ,CAAC,MAAO,UAAU,CAAE;AAChD,QAAI;AACJ,UAAM,aAAa;AAAA,MACf,WAAW;AAAA,MACX,aAAa,CAAC,eAAe;AACzB,YAAI;AACJ,cAAM,IAAI,IAAI,QAAQ,CAAC,MAAO,OAAO,CAAE;AACvC,gBAAQ;AAAA,UACJ,SAAS;AAAA,UACT,SAAS;AAAA,UACT,aAAa,MAAY;AACrB,iBAAK;AACL,kBAAM;AAAA,UACV;AAAA,QACJ,CAAC;AACD,eAAO;AAAA,MACX;AAAA,IACJ;AACA,QAAI,cAAc,QAAQ;AACtB,eAAS,KAAK,QAAQ,IAAI,QAAQ,UAAU;AAAA,IAChD,OACK;AAED,YAAM,iBAAiB,KAAK,MAAM,KAAK,CAAC,MAAM,QAAQ,GAAG,IAAI,MAAM,CAAC;AACpE,UAAI,gBAAgB;AAChB,iBAAS,KAAK,MAAM,gBAAgB,OAAO,OAAO,OAAO,OAAO,CAAC,GAAG,UAAU,GAAG,EAAE,WAAW,QAAQ,kBAAkB,UAAU,CAAC,CAAC;AAAA,MACxI,WACS,cAAc,WAAW;AAC9B,iBAAS,KAAK,KAAK,IAAI,QAAQ,OAAO,OAAO,OAAO,OAAO,CAAC,GAAG,UAAU,GAAG,EAAE,kBAAkB,UAAU,CAAC,CAAC;AAAA,MAChH,WACS,cAAc,QAAQ;AAC3B,iBAAS,KAAK,QAAQ,IAAI,QAAQ,OAAO,OAAO,OAAO,OAAO,CAAC,GAAG,UAAU,GAAG,EAAE,WAAW,QAAQ,UAAU,MAAM,kBAAkB,UAAU,CAAC,CAAC;AAAA,MACtJ;AAAA,IACJ;AACA,WAAO;AAAA,EACX;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMM,aAAa;AAAA;AACf,YAAM,SAAS,KAAK,cAAc;AAClC,UAAI,QAAQ;AACR,eAAO;AAAA,UACH,IAAI,OAAO,QAAQ;AAAA,UACnB,QAAQ,OAAO;AAAA,UACf,SAAS,OAAO;AAAA,QACpB;AAAA,MACJ;AACA,aAAO;AAAA,IACX;AAAA;AAAA;AAAA;AAAA;AAAA,EAIM,YAAY;AAAA;AACd,aAAO,KAAK,cAAc;AAAA,IAC9B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMM,WAAW,OAAO;AAAA;AACpB,aAAO,KAAK,MAAM,KAAK;AAAA,IAC3B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMM,UAAU,MAAM;AAAA;AAClB,aAAO,KAAK,cAAc,IAAI;AAAA,IAClC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMM,YAAY,MAAM;AAAA;AACpB,aAAO,KAAK,gBAAgB,IAAI;AAAA,IACpC;AAAA;AAAA;AAAA;AAAA;AAAA,EAIM,YAAY;AAAA;AACd,aAAO,QAAQ,QAAQ,KAAK,MAAM,MAAM;AAAA,IAC5C;AAAA;AAAA,EACA,gBAAgB;AACZ,WAAO,KAAK,MAAM,KAAK,MAAM,SAAS,CAAC;AAAA,EAC3C;AAAA,EACA,cAAc,OAAO,KAAK,cAAc,GAAG;AACvC,WAAO,CAAC,EAAE,QAAQ,KAAK,gBAAgB,IAAI;AAAA,EAC/C;AAAA,EACA,gBAAgB,OAAO,KAAK,cAAc,GAAG;AACzC,QAAI,CAAC,MAAM;AACP,aAAO;AAAA,IACX;AACA,UAAM,QAAQ,KAAK;AACnB,UAAM,QAAQ,MAAM,QAAQ,IAAI;AAChC,WAAO,QAAQ,IAAI,MAAM,QAAQ,CAAC,IAAI;AAAA,EAC1C;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMM,UAAU,IAAI,MAAM;AAAA;AACtB,UAAI,IAAI;AACR,UAAI,KAAK,qBAAqB,KAAK,GAAG,UAAU,QAAQ,OAAO,SAAS,SAAS,GAAG,aAAa;AAC7F,eAAO;AAAA,MACX;AACA,YAAM,UAAU,IAAI,QAAQ,CAAC,SAAS,WAAW;AAC7C,WAAG,UAAU;AACb,WAAG,SAAS;AAAA,MAChB,CAAC;AACD,SAAG,OAAO;AAOV,UAAI,GAAG,QAAQ,GAAG,KAAK,cAAc,SAAS,KAAK,WAAW;AAC1D,cAAM,SAAS,SAAS,cAAc,YAAY;AAClD,YAAI,QAAQ;AACR,gBAAM,gBAAgB,MAAM,OAAO,cAAc;AACjD,cAAI,kBAAkB,OAAO;AACzB,mBAAO;AAAA,UACX;AACA,cAAI,OAAO,kBAAkB,UAAU;AACnC,mBAAO,KAAK,eAAe,GAAG,KAAK,aAAa,MAAM;AACtD,mBAAO;AAAA,UACX;AAAA,QACJ;AAAA,MACJ;AAEA,YAAM,KAAK,GAAG,iBAAiB,QAAQ,OAAO,SAAS,SAAS,GAAG,YAAY,GAAG;AAC9E,WAAG,cAAc;AAAA,MACrB;AAEA,WAAK,WAAW,KAAK,EAAE;AAGvB,WAAK,SAAS;AACd,aAAO;AAAA,IACX;AAAA;AAAA,EACA,QAAQ,QAAQ,IAAI;AAChB,QAAI,KAAK,WAAW;AAChB,WAAK,UAAU,gCAAgC,EAAE;AACjD;AAAA,IACJ;AACA,QAAI,GAAG,MAAM;AACT,SAAG,KAAK,OAAO,cAAc,OAAO,oBAAoB,OAAO,cAAc,OAAO,aAAa,OAAO,SAAS;AAAA,IACrH;AACA,OAAG,QAAQ,OAAO,YAAY;AAC9B,QAAI,GAAG,KAAK,cAAc,SAAS,KAAK,WAAW;AAC/C,YAAM,SAAS,SAAS,cAAc,YAAY;AAClD,UAAI,QAAQ;AACR,cAAM,YAAY,OAAO,cAAc,SAAS,SAAS;AACzD,eAAO,WAAW,SAAS;AAAA,MAC/B;AAAA,IACJ;AAAA,EACJ;AAAA,EACA,OAAO,cAAc,IAAI;AACrB,QAAI,KAAK,WAAW;AAChB,WAAK,UAAU,gCAAgC,EAAE;AACjD;AAAA,IACJ;AACA,SAAK,WAAW,SAAS;AACzB,SAAK,UAAU,cAAc,EAAE;AAAA,EACnC;AAAA,EACA,UAAU,cAAc,IAAI;AACxB,QAAI,GAAG,MAAM;AACT,SAAG,KAAK,OAAO,OAAO,YAAY;AAAA,IACtC;AACA,QAAI,GAAG,UAAU,CAAC,KAAK,WAAW;AAC9B,SAAG,OAAO,YAAY;AAAA,IAC1B,OACK;AACD,SAAG,QAAQ,KAAK;AAAA,IACpB;AAAA,EACJ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,WAAW;AAGP,QAAI,KAAK,iBAAiB;AACtB,aAAO;AAAA,IACX;AAEA,UAAM,KAAK,KAAK,WAAW,MAAM;AACjC,QAAI,CAAC,IAAI;AACL,aAAO;AAAA,IACX;AACA,SAAK,cAAc,EAAE;AACrB,WAAO;AAAA,EACX;AAAA;AAAA,EAEM,cAAc,IAAI;AAAA;AACpB,UAAI;AAEA,aAAK,iBAAiB,KAAK;AAC3B,aAAK,kBAAkB;AACvB,aAAK,UAAU,EAAE;AACjB,cAAM,cAAc,KAAK,cAAc;AACvC,cAAM,eAAe,KAAK,gBAAgB,IAAI,WAAW;AACzD,YAAI,CAAC,eAAe,CAAC,cAAc;AAC/B,gBAAM,IAAI,MAAM,qCAAqC;AAAA,QACzD;AACA,YAAI,gBAAgB,aAAa,UAAU,gBAAgB;AACvD,gBAAM,aAAa,KAAK,KAAK,EAAE;AAAA,QACnC;AACA,aAAK,aAAa,cAAc,aAAa,EAAE;AAE/C,cAAM,sBAAsB,GAAG,8BAA8B,GAAG,8BAA8B,iBAAiB;AAC/G,YAAI,sBAAsB,GAAG,QAAQ,aAAa;AAC9C,gBAAM,kBAAkB,GAAG,KAAK,cAAc;AAK9C,cAAI,iBAAiB;AACjB,eAAG,KAAK,mBAAmB,GAAG,KAAK,qBAAqB,iBAAiB,QAAQ,iBAAiB,SAAS,SAAS,aAAa;AAAA,UACrI;AACA,sBAAY,mBAAmB,GAAG,KAAK;AAAA,QAC3C;AACA,YAAI;AACJ,YAAI,oBAAoB;AACpB,mBAAS,MAAM,KAAK,WAAW,cAAc,aAAa,EAAE;AAAA,QAChE,OACK;AAKD,mBAAS;AAAA,YACL,cAAc;AAAA,YACd,oBAAoB;AAAA,UACxB;AAAA,QACJ;AACA,aAAK,QAAQ,QAAQ,EAAE;AACvB,aAAK,gBAAgB,KAAK;AAAA,MAC9B,SACO,cAAc;AACjB,aAAK,OAAO,cAAc,EAAE;AAAA,MAChC;AACA,WAAK,kBAAkB;AACvB,WAAK,SAAS;AAAA,IAClB;AAAA;AAAA,EACA,UAAU,IAAI;AACV,QAAI,IAAI;AACR,QAAI;AACJ,UAAM,cAAc,KAAK,MAAM;AAC/B,KAAC,KAAK,GAAG,UAAU,QAAQ,OAAO,SAAS,KAAM,GAAG,OAAO,CAAC;AAC5D,KAAC,MAAM,KAAK,GAAG,MAAM,cAAc,QAAQ,OAAO,SAAS,KAAM,GAAG,WAAW,KAAK;AACpF,QAAI,GAAG,eAAe,QAAW;AAC7B,aAAO,GAAG,gBAAgB,QAAW,8BAA8B;AACnE,aAAO,GAAG,gBAAgB,QAAW,8BAA8B;AACnE,YAAM,QAAQ,KAAK,MAAM,QAAQ,GAAG,UAAU;AAC9C,UAAI,QAAQ,GAAG;AACX,cAAM,IAAI,MAAM,0BAA0B;AAAA,MAC9C;AACA,SAAG,eAAe;AAAA,IACtB;AACA,QAAI,GAAG,gBAAgB,QAAW;AAC9B,UAAI,GAAG,cAAc,GAAG;AACpB,WAAG,cAAc,cAAc;AAAA,MACnC;AACA,UAAI,GAAG,cAAc,GAAG;AACpB,WAAG,cAAc,cAAc,GAAG;AAAA,MACtC;AACA,SAAG,4BAA4B,GAAG,cAAc,KAAK,GAAG,cAAc,GAAG,gBAAgB;AAAA,IAC7F;AACA,QAAI,GAAG,aAAa;AAGhB,UAAI,GAAG,cAAc,KAAK,GAAG,cAAc,aAAa;AACpD,WAAG,cAAc;AAAA,MACrB;AACA,SAAG,6BAA6B,GAAG,gBAAgB;AAAA,IACvD;AACA,UAAM,cAAc,GAAG;AACvB,QAAI,CAAC,aAAa;AACd;AAAA,IACJ;AACA,WAAO,YAAY,SAAS,GAAG,wBAAwB;AACvD,UAAM,kBAAkB,eAAe,WAAW;AAClD,QAAI,gBAAgB,WAAW,GAAG;AAC9B,YAAM,IAAI,MAAM,yBAAyB;AAAA,IAC7C;AAEA,eAAW,QAAQ,iBAAiB;AAChC,WAAK,WAAW,GAAG,KAAK;AACxB,YAAM,MAAM,KAAK;AACjB,UAAI,OAAO,QAAQ,MAAM;AACrB,cAAM,IAAI,MAAM,oCAAoC;AAAA,MACxD;AACA,UAAI,KAAK,UAAU,sBAAsB;AACrC,cAAM,IAAI,MAAM,qCAAqC;AAAA,MACzD;AAAA,IACJ;AACA,OAAG,cAAc;AAAA,EACrB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASA,gBAAgB,IAAI,aAAa;AAE7B,UAAM,cAAc,GAAG;AACvB,QAAI,gBAAgB,QAAW;AAC3B,aAAO,YAAY,YAAY,SAAS,CAAC;AAAA,IAC7C;AAEA,UAAM,cAAc,GAAG;AACvB,QAAI,gBAAgB,QAAW;AAC3B,YAAM,QAAQ,KAAK;AACnB,YAAM,YAAY,cAAc,GAAG;AACnC,eAAS,IAAI,MAAM,SAAS,GAAG,KAAK,GAAG,KAAK;AACxC,cAAM,OAAO,MAAM,CAAC;AACpB,aAAK,IAAI,eAAe,KAAK,cAAc,SAAS,aAAa;AAC7D,iBAAO;AAAA,QACX;AAAA,MACJ;AAAA,IACJ;AACA,WAAO;AAAA,EACX;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,aAAa,cAAc,aAAa,IAAI;AACxC,QAAI,IAAI,IAAI;AACZ,WAAO,eAAe,cAAc,4CAA4C;AAChF,WAAO,GAAG,SAAS,uBAAuB;AAC1C,WAAO,GAAG,QAAQ,sBAAsB;AAExC,UAAM,OAAO,GAAG;AAChB,UAAM,EAAE,aAAa,aAAa,YAAY,IAAI;AAElD,QAAI;AAEJ,QAAI,gBAAgB,UAAa,gBAAgB,QAAW;AACxD,aAAO,eAAe,GAAG,iCAAiC;AAC1D,aAAO,eAAe,GAAG,iCAAiC;AAC1D,qBAAe,CAAC;AAChB,eAAS,IAAI,aAAa,IAAI,cAAc,aAAa,KAAK;AAC1D,cAAM,OAAO,KAAK,MAAM,CAAC;AACzB,YAAI,SAAS,UAAa,SAAS,gBAAgB,SAAS,aAAa;AACrE,uBAAa,KAAK,IAAI;AAAA,QAC1B;AAAA,MACJ;AAEA,OAAC,KAAK,KAAK,eAAe,QAAQ,OAAO,SAAS,KAAM,KAAK,YAAY;AAAA,IAC7E;AACA,UAAM,gBAAgB,KAAK,MAAM,WAAW,KAAK,gBAAgB,QAAQ,gBAAgB,SAAS,SAAS,YAAY,YAAY,QAAQ,OAAO,SAAS,KAAK,MAAM,gBAAgB,QAAQ,gBAAgB,SAAS,cAAc;AACrO,WAAO,iBAAiB,GAAG,mCAAmC;AAC9D,QAAI,kBAAkB,GAAG;AACrB,sBAAgB,oHAAoH,MAAM,KAAK,EAAE;AACjJ,YAAM,IAAI,MAAM,+CAA+C;AAAA,IACnE;AAGA,QAAI,aAAa;AAEb,UAAI,cAAc,GAAG;AACrB,iBAAW,QAAQ,aAAa;AAC5B,aAAK,aAAa,MAAM,WAAW;AACnC;AAAA,MACJ;AACA,UAAI,GAAG,4BAA4B;AAE/B,SAAC,KAAK,KAAK,eAAe,QAAQ,OAAO,SAAS,KAAM,KAAK,YAAY;AAAA,MAC7E;AAAA,IACJ;AAMA,QAAI,gBAAgB,aAAa,SAAS,GAAG;AACzC,iBAAW,QAAQ,cAAc;AAC7B,kBAAU,KAAK,SAAS,oBAAoB;AAC5C,kBAAU,KAAK,SAAS,mBAAmB;AAC3C,kBAAU,KAAK,SAAS,qBAAqB;AAAA,MACjD;AAEA,iBAAW,QAAQ,cAAc;AAC7B,aAAK,YAAY,IAAI;AAAA,MACzB;AAAA,IACJ;AAAA,EACJ;AAAA,EACM,WAAW,cAAc,aAAa,IAAI;AAAA;AAG5C,YAAM,OAAO,GAAG;AAChB,YAAM,mBAAmB,KAAK,oBACxB,CAAC,QAAQ;AAQP,YAAI,QAAQ,UAAa,CAAC,KAAK,8BAA8B;AACzD,eAAK,+BAA+B;AACpC,cAAI,SAAS,MAAM;AACf,iBAAK,+BAA+B;AAAA,UACxC,GAAG,EAAE,iBAAiB,KAAK,CAAC;AAO5B,cAAI,YAAY,GAAG,GAAG,CAAC;AAAA,QAC3B,OACK;AACD,eAAK,QAAQ;AAAA,QACjB;AAAA,MACJ,IACE;AACN,YAAM,OAAO,WAAW,IAAI;AAC5B,YAAM,aAAa,aAAa;AAEhC,YAAM,YAAY,eAAe,YAAY;AAC7C,YAAM,gBAAgB,OAAO,OAAO,OAAO,OAAO;AAAA,QAAE;AAAA,QAAM,YAAY,KAAK,cAAc,YAAY;AAAA,QAAG,QAAQ,KAAK;AAAA,QAAI;AAAA,QAAkB,UAAU,KAAK,YAAY,OAAO,WAAW,YAAY,IAAI;AAAA,QAAG;AAAA,QACvM;AAAA,MAAU,GAAG,IAAI,GAAG,EAAE,kBAAkB,KAAK,oBAAoB,KAAK,aAAa,OAAO,IAAI,cAAc,EAAE,CAAC;AACnH,YAAM,EAAE,aAAa,IAAI,MAAM,WAAW,aAAa;AACvD,aAAO,KAAK,iBAAiB,cAAc,cAAc,aAAa,IAAI;AAAA,IAC9E;AAAA;AAAA,EACA,iBAAiB,cAAc,cAAc,aAAa,MAAM;AAK5D,UAAM,aAAa,eAAe,eAAe;AACjD,QAAI,YAAY;AACZ,WAAK,qBAAqB,UAAU;AAAA,IACxC;AACA,WAAO;AAAA,MACH;AAAA,MACA,oBAAoB;AAAA,MACpB;AAAA,MACA;AAAA,MACA,WAAW,KAAK;AAAA,IACpB;AAAA,EACJ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASA,aAAa,MAAM,OAAO;AACtB,UAAM,QAAQ,KAAK;AACnB,UAAM,gBAAgB,MAAM,QAAQ,IAAI;AACxC,QAAI,gBAAgB,IAAI;AACpB,aAAO,KAAK,QAAQ,MAAM,6BAA6B;AAEvD,YAAM,OAAO,eAAe,CAAC;AAE7B,YAAM,OAAO,OAAO,GAAG,IAAI;AAAA,IAC/B,OACK;AACD,aAAO,CAAC,KAAK,KAAK,aAAa;AAG/B,WAAK,MAAM;AACX,YAAM,OAAO,OAAO,GAAG,IAAI;AAAA,IAC/B;AAAA,EACJ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,WAAW,MAAM;AACb,WAAO,KAAK,UAAU,uBAAuB,KAAK,UAAU,sBAAsB,0CAA0C;AAC5H,UAAM,QAAQ,KAAK;AACnB,UAAM,QAAQ,MAAM,QAAQ,IAAI;AAChC,WAAO,QAAQ,IAAI,gCAAgC;AACnD,QAAI,SAAS,GAAG;AACZ,YAAM,OAAO,OAAO,CAAC;AAAA,IACzB;AAAA,EACJ;AAAA,EACA,YAAY,MAAM;AACd,SAAK,SAAS;AACd,SAAK,WAAW,IAAI;AAAA,EACxB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,qBAAqB,YAAY;AAI7B,QAAI,KAAK,WAAW;AAChB;AAAA,IACJ;AACA,UAAM,QAAQ,KAAK;AACnB,UAAM,kBAAkB,MAAM,QAAQ,UAAU;AAChD,aAAS,IAAI,MAAM,SAAS,GAAG,KAAK,GAAG,KAAK;AACxC,YAAM,OAAO,MAAM,CAAC;AASpB,YAAM,UAAU,KAAK;AACrB,UAAI,SAAS;AACT,YAAI,IAAI,iBAAiB;AAGrB,oBAAU,SAAS,qBAAqB;AACxC,eAAK,YAAY,IAAI;AAAA,QACzB,WACS,IAAI,iBAAiB;AAG1B,wBAAc,SAAS,IAAI;AAAA,QAC/B;AAAA,MACJ;AAAA,IACJ;AAAA,EACJ;AAAA,EACA,WAAW;AACP,WAAQ,CAAC,KAAK,gCACV,CAAC,CAAC,KAAK,gBACP,CAAC,KAAK,mBACN,KAAK,WAAW,WAAW,KAC3B,KAAK,cAAc;AAAA,EAC3B;AAAA,EACA,UAAU;AACN,SAAK,+BAA+B;AACpC,SAAK,IAAI,EAAE,WAAW,QAAQ,mBAAmB,KAAK,CAAC;AAAA,EAC3D;AAAA,EACA,OAAO,WAAW;AACd,QAAI,KAAK,OAAO;AACZ,WAAK,MAAM,aAAa,SAAS;AAAA,IACrC;AAAA,EACJ;AAAA,EACA,MAAM,gBAAgB,WAAW,KAAK;AAClC,QAAI,KAAK,OAAO;AACZ,WAAK,MAAM,SAAS,MAAM;AACtB,aAAK,+BAA+B;AAAA,MACxC,GAAG,EAAE,iBAAiB,KAAK,CAAC;AAE5B,UAAI,eAAe,iBAAiB,QAAQ;AAS5C,UAAI,CAAC,gBAAgB;AACjB,aAAK,MAAM,OAAO,gCAAgC;AAClD,wBAAgB,wBAAwB,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,MAAM,IAAI,GAAG,CAAC,GAAG,CAAC,GAAG,SAAS,EAAE,CAAC;AAAA,MAC9F,OACK;AACD,wBAAgB,wBAAwB,CAAC,GAAG,CAAC,GAAG,CAAC,MAAM,IAAI,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,SAAS,EAAE,CAAC;AAAA,MAC9F;AACA,WAAK,MAAM,YAAY,iBAAiB,IAAI,GAAG,cAAc,GAAG;AAAA,IACpE,OACK;AACD,WAAK,+BAA+B;AAAA,IACxC;AAAA,EACJ;AAAA,EACA,SAAS;AACL,WAAO,EAAE,QAAQ,EAAE,KAAK,2CAA2C,CAAC;AAAA,EACxE;AAAA,EACA,IAAI,KAAK;AAAE,WAAO,WAAW,IAAI;AAAA,EAAG;AAAA,EACpC,WAAW,WAAW;AAAE,WAAO;AAAA,MAC3B,gBAAgB,CAAC,qBAAqB;AAAA,MACtC,QAAQ,CAAC,aAAa;AAAA,IAC1B;AAAA,EAAG;AACP;AACA,IAAI,QAAQ;AAEZ,IAAM,UAAU,CAAC,IAAI,iBAAiB,WAAW,gBAAgB,oBAAoB;AACjF,QAAM,MAAM,GAAG,QAAQ,SAAS;AAChC,MAAI,KAAK;AACL,QAAI,oBAAoB,WAAW;AAC/B,UAAI,cAAc,QAAW;AACzB,eAAO,IAAI,KAAK,WAAW,gBAAgB,EAAE,YAAY,MAAM,kBAAkB,gBAAgB,CAAC;AAAA,MACtG;AAAA,IACJ,WACS,oBAAoB,QAAQ;AACjC,UAAI,cAAc,QAAW;AACzB,eAAO,IAAI,QAAQ,WAAW,gBAAgB,EAAE,YAAY,MAAM,kBAAkB,gBAAgB,CAAC;AAAA,MACzG;AAAA,IACJ,WACS,oBAAoB,QAAQ;AACjC,aAAO,IAAI,IAAI,EAAE,YAAY,MAAM,kBAAkB,gBAAgB,CAAC;AAAA,IAC1E;AAAA,EACJ;AACA,SAAO,QAAQ,QAAQ,KAAK;AAChC;AAEA,IAAM,UAAU,MAAM;AAAA,EAClB,YAAY,SAAS;AACjB,qBAAiB,MAAM,OAAO;AAI9B,SAAK,kBAAkB;AACvB,SAAK,UAAU,MAAM;AACjB,aAAO,QAAQ,KAAK,IAAI,KAAK,iBAAiB,KAAK,WAAW,KAAK,gBAAgB,KAAK,eAAe;AAAA,IAC3G;AAAA,EACJ;AAAA,EACA,SAAS;AACL,WAAO,EAAE,MAAM,EAAE,KAAK,4CAA4C,SAAS,KAAK,QAAQ,CAAC;AAAA,EAC7F;AAAA,EACA,IAAI,KAAK;AAAE,WAAO,WAAW,IAAI;AAAA,EAAG;AACxC;", "names": []}