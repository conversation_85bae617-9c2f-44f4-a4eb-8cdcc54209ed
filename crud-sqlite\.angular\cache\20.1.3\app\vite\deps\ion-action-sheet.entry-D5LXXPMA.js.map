{"version": 3, "sources": ["../../../../../../node_modules/@ionic/core/dist/esm/ion-action-sheet.entry.js"], "sourcesContent": ["/*!\n * (C) Ionic http://ionicframework.com - MIT License\n */\nimport { r as registerInstance, d as createEvent, e as getIonMode, f as readTask, h, j as Host, k as getElement } from './index-B_U9CtaY.js';\nimport { c as createButtonActiveGesture } from './button-active-Bxcnevju.js';\nimport { r as raf } from './helpers-1O4D2b7y.js';\nimport { c as createLockController } from './lock-controller-B-hirT0v.js';\nimport { d as createDelegateController, e as createTriggerController, B as BACKDROP, i as isCancel, f as present, g as dismiss, h as eventMethod, s as safeCall, j as prepareOverlay, k as setOverlayId } from './overlays-8Y2rA-ps.js';\nimport { g as getClassMap } from './theme-DiVJyqlX.js';\nimport { c as createAnimation } from './animation-BWcUKtbn.js';\nimport './haptic-DzAMWJuk.js';\nimport './capacitor-CFERIeaU.js';\nimport './index-ZjP4CjeZ.js';\nimport './index-CfgBF1SE.js';\nimport './gesture-controller-BTEOs1at.js';\nimport './hardware-back-button-DcH0BbDp.js';\nimport './framework-delegate-DxcnWic_.js';\n\n/**\n * iOS Action Sheet Enter Animation\n */\nconst iosEnterAnimation = (baseEl) => {\n    const baseAnimation = createAnimation();\n    const backdropAnimation = createAnimation();\n    const wrapperAnimation = createAnimation();\n    backdropAnimation\n        .addElement(baseEl.querySelector('ion-backdrop'))\n        .fromTo('opacity', 0.01, 'var(--backdrop-opacity)')\n        .beforeStyles({\n        'pointer-events': 'none',\n    })\n        .afterClearStyles(['pointer-events']);\n    wrapperAnimation\n        .addElement(baseEl.querySelector('.action-sheet-wrapper'))\n        .fromTo('transform', 'translateY(100%)', 'translateY(0%)');\n    return baseAnimation\n        .addElement(baseEl)\n        .easing('cubic-bezier(.36,.66,.04,1)')\n        .duration(400)\n        .addAnimation([backdropAnimation, wrapperAnimation]);\n};\n\n/**\n * iOS Action Sheet Leave Animation\n */\nconst iosLeaveAnimation = (baseEl) => {\n    const baseAnimation = createAnimation();\n    const backdropAnimation = createAnimation();\n    const wrapperAnimation = createAnimation();\n    backdropAnimation.addElement(baseEl.querySelector('ion-backdrop')).fromTo('opacity', 'var(--backdrop-opacity)', 0);\n    wrapperAnimation\n        .addElement(baseEl.querySelector('.action-sheet-wrapper'))\n        .fromTo('transform', 'translateY(0%)', 'translateY(100%)');\n    return baseAnimation\n        .addElement(baseEl)\n        .easing('cubic-bezier(.36,.66,.04,1)')\n        .duration(450)\n        .addAnimation([backdropAnimation, wrapperAnimation]);\n};\n\n/**\n * MD Action Sheet Enter Animation\n */\nconst mdEnterAnimation = (baseEl) => {\n    const baseAnimation = createAnimation();\n    const backdropAnimation = createAnimation();\n    const wrapperAnimation = createAnimation();\n    backdropAnimation\n        .addElement(baseEl.querySelector('ion-backdrop'))\n        .fromTo('opacity', 0.01, 'var(--backdrop-opacity)')\n        .beforeStyles({\n        'pointer-events': 'none',\n    })\n        .afterClearStyles(['pointer-events']);\n    wrapperAnimation\n        .addElement(baseEl.querySelector('.action-sheet-wrapper'))\n        .fromTo('transform', 'translateY(100%)', 'translateY(0%)');\n    return baseAnimation\n        .addElement(baseEl)\n        .easing('cubic-bezier(.36,.66,.04,1)')\n        .duration(400)\n        .addAnimation([backdropAnimation, wrapperAnimation]);\n};\n\n/**\n * MD Action Sheet Leave Animation\n */\nconst mdLeaveAnimation = (baseEl) => {\n    const baseAnimation = createAnimation();\n    const backdropAnimation = createAnimation();\n    const wrapperAnimation = createAnimation();\n    backdropAnimation.addElement(baseEl.querySelector('ion-backdrop')).fromTo('opacity', 'var(--backdrop-opacity)', 0);\n    wrapperAnimation\n        .addElement(baseEl.querySelector('.action-sheet-wrapper'))\n        .fromTo('transform', 'translateY(0%)', 'translateY(100%)');\n    return baseAnimation\n        .addElement(baseEl)\n        .easing('cubic-bezier(.36,.66,.04,1)')\n        .duration(450)\n        .addAnimation([backdropAnimation, wrapperAnimation]);\n};\n\nconst actionSheetIosCss = \".sc-ion-action-sheet-ios-h{--color:initial;--button-color-activated:var(--button-color);--button-color-focused:var(--button-color);--button-color-hover:var(--button-color);--button-color-selected:var(--button-color);--min-width:auto;--width:100%;--max-width:500px;--min-height:auto;--height:auto;--max-height:calc(100% - (var(--ion-safe-area-top) + var(--ion-safe-area-bottom)));-moz-osx-font-smoothing:grayscale;-webkit-font-smoothing:antialiased;left:0;right:0;top:0;bottom:0;display:block;position:fixed;outline:none;font-family:var(--ion-font-family, inherit);-ms-touch-action:none;touch-action:none;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none;z-index:1001}.overlay-hidden.sc-ion-action-sheet-ios-h{display:none}.action-sheet-wrapper.sc-ion-action-sheet-ios{left:0;right:0;bottom:0;-webkit-transform:translate3d(0,  100%,  0);transform:translate3d(0,  100%,  0);display:block;position:absolute;width:var(--width);min-width:var(--min-width);max-width:var(--max-width);height:var(--height);min-height:var(--min-height);max-height:var(--max-height);z-index:10;pointer-events:none}.action-sheet-button.sc-ion-action-sheet-ios{display:block;position:relative;width:100%;border:0;outline:none;background:var(--button-background);color:var(--button-color);font-family:inherit;overflow:hidden}.action-sheet-button.sc-ion-action-sheet-ios:disabled{color:var(--button-color-disabled);opacity:0.4}.action-sheet-button-inner.sc-ion-action-sheet-ios{display:-ms-flexbox;display:flex;position:relative;-ms-flex-flow:row nowrap;flex-flow:row nowrap;-ms-flex-negative:0;flex-shrink:0;-ms-flex-align:center;align-items:center;-ms-flex-pack:center;justify-content:center;pointer-events:none;width:100%;height:100%;z-index:1}.action-sheet-container.sc-ion-action-sheet-ios{display:-ms-flexbox;display:flex;-ms-flex-flow:column;flex-flow:column;-ms-flex-pack:end;justify-content:flex-end;height:100%;max-height:calc(100vh - (var(--ion-safe-area-top, 0) + var(--ion-safe-area-bottom, 0)));max-height:calc(100dvh - (var(--ion-safe-area-top, 0) + var(--ion-safe-area-bottom, 0)))}.action-sheet-group.sc-ion-action-sheet-ios{-ms-flex-negative:2;flex-shrink:2;overscroll-behavior-y:contain;overflow-y:auto;-webkit-overflow-scrolling:touch;pointer-events:all;background:var(--background)}@media (any-pointer: coarse){.action-sheet-group.sc-ion-action-sheet-ios::-webkit-scrollbar{display:none}}.action-sheet-group-cancel.sc-ion-action-sheet-ios{-ms-flex-negative:0;flex-shrink:0;overflow:hidden}.action-sheet-button.sc-ion-action-sheet-ios::after{left:0;right:0;top:0;bottom:0;position:absolute;content:\\\"\\\";opacity:0}.action-sheet-selected.sc-ion-action-sheet-ios{color:var(--button-color-selected)}.action-sheet-selected.sc-ion-action-sheet-ios::after{background:var(--button-background-selected);opacity:var(--button-background-selected-opacity)}.action-sheet-button.ion-activated.sc-ion-action-sheet-ios{color:var(--button-color-activated)}.action-sheet-button.ion-activated.sc-ion-action-sheet-ios::after{background:var(--button-background-activated);opacity:var(--button-background-activated-opacity)}.action-sheet-button.ion-focused.sc-ion-action-sheet-ios{color:var(--button-color-focused)}.action-sheet-button.ion-focused.sc-ion-action-sheet-ios::after{background:var(--button-background-focused);opacity:var(--button-background-focused-opacity)}@media (any-hover: hover){.action-sheet-button.sc-ion-action-sheet-ios:not(:disabled):hover{color:var(--button-color-hover)}.action-sheet-button.sc-ion-action-sheet-ios:not(:disabled):hover::after{background:var(--button-background-hover);opacity:var(--button-background-hover-opacity)}}.sc-ion-action-sheet-ios-h{--background:var(--ion-overlay-background-color, var(--ion-color-step-100, var(--ion-background-color-step-100, #f9f9f9)));--backdrop-opacity:var(--ion-backdrop-opacity, 0.4);--button-background:linear-gradient(0deg, rgba(var(--ion-text-color-rgb, 0, 0, 0), 0.08), rgba(var(--ion-text-color-rgb, 0, 0, 0), 0.08) 50%, transparent 50%) bottom/100% 1px no-repeat transparent;--button-background-activated:var(--ion-text-color, #000);--button-background-activated-opacity:.08;--button-background-hover:currentColor;--button-background-hover-opacity:.04;--button-background-focused:currentColor;--button-background-focused-opacity:.12;--button-background-selected:var(--ion-color-step-150, var(--ion-background-color-step-150, var(--ion-background-color, #fff)));--button-background-selected-opacity:1;--button-color:var(--ion-color-primary, #0054e9);--button-color-disabled:var(--ion-color-step-850, var(--ion-text-color-step-150, #262626));--color:var(--ion-color-step-400, var(--ion-text-color-step-600, #999999));text-align:center}.action-sheet-wrapper.sc-ion-action-sheet-ios{-webkit-margin-start:auto;margin-inline-start:auto;-webkit-margin-end:auto;margin-inline-end:auto;margin-top:var(--ion-safe-area-top, 0);padding-bottom:var(--ion-safe-area-bottom, 0);-webkit-box-sizing:content-box;box-sizing:content-box}.action-sheet-container.sc-ion-action-sheet-ios{-webkit-padding-start:8px;padding-inline-start:8px;-webkit-padding-end:8px;padding-inline-end:8px;padding-top:0;padding-bottom:0}.action-sheet-group.sc-ion-action-sheet-ios{border-radius:13px;margin-bottom:8px}.action-sheet-group.sc-ion-action-sheet-ios:first-child{margin-top:10px}.action-sheet-group.sc-ion-action-sheet-ios:last-child{margin-bottom:10px}@supports ((-webkit-backdrop-filter: blur(0)) or (backdrop-filter: blur(0))){.action-sheet-translucent.sc-ion-action-sheet-ios-h .action-sheet-group.sc-ion-action-sheet-ios{background-color:transparent;-webkit-backdrop-filter:saturate(280%) blur(20px);backdrop-filter:saturate(280%) blur(20px)}.action-sheet-translucent.sc-ion-action-sheet-ios-h .action-sheet-title.sc-ion-action-sheet-ios,.action-sheet-translucent.sc-ion-action-sheet-ios-h .action-sheet-button.sc-ion-action-sheet-ios{background-color:transparent;background-image:-webkit-gradient(linear, left bottom, left top, from(rgba(var(--ion-background-color-rgb, 255, 255, 255), 0.8)), to(rgba(var(--ion-background-color-rgb, 255, 255, 255), 0.8))), -webkit-gradient(linear, left bottom, left top, from(rgba(var(--ion-background-color-rgb, 255, 255, 255), 0.4)), color-stop(50%, rgba(var(--ion-background-color-rgb, 255, 255, 255), 0.4)), color-stop(50%, rgba(var(--ion-background-color-rgb, 255, 255, 255), 0.8)));background-image:linear-gradient(0deg, rgba(var(--ion-background-color-rgb, 255, 255, 255), 0.8), rgba(var(--ion-background-color-rgb, 255, 255, 255), 0.8) 100%), linear-gradient(0deg, rgba(var(--ion-background-color-rgb, 255, 255, 255), 0.4), rgba(var(--ion-background-color-rgb, 255, 255, 255), 0.4) 50%, rgba(var(--ion-background-color-rgb, 255, 255, 255), 0.8) 50%);background-repeat:no-repeat;background-position:top, bottom;background-size:100% calc(100% - 1px), 100% 1px;-webkit-backdrop-filter:saturate(120%);backdrop-filter:saturate(120%)}.action-sheet-translucent.sc-ion-action-sheet-ios-h .action-sheet-button.ion-activated.sc-ion-action-sheet-ios{background-color:rgba(var(--ion-background-color-rgb, 255, 255, 255), 0.7);background-image:none}.action-sheet-translucent.sc-ion-action-sheet-ios-h .action-sheet-cancel.sc-ion-action-sheet-ios{background:var(--button-background-selected)}}.action-sheet-title.sc-ion-action-sheet-ios{background:-webkit-gradient(linear, left bottom, left top, from(rgba(var(--ion-text-color-rgb, 0, 0, 0), 0.08)), color-stop(50%, rgba(var(--ion-text-color-rgb, 0, 0, 0), 0.08)), color-stop(50%, transparent)) bottom/100% 1px no-repeat transparent;background:linear-gradient(0deg, rgba(var(--ion-text-color-rgb, 0, 0, 0), 0.08), rgba(var(--ion-text-color-rgb, 0, 0, 0), 0.08) 50%, transparent 50%) bottom/100% 1px no-repeat transparent}.action-sheet-title.sc-ion-action-sheet-ios{-webkit-padding-start:10px;padding-inline-start:10px;-webkit-padding-end:10px;padding-inline-end:10px;padding-top:14px;padding-bottom:13px;color:var(--color, var(--ion-color-step-400, var(--ion-text-color-step-600, #999999)));font-size:max(13px, 0.8125rem);font-weight:400;text-align:center}.action-sheet-title.action-sheet-has-sub-title.sc-ion-action-sheet-ios{font-weight:600}.action-sheet-sub-title.sc-ion-action-sheet-ios{padding-left:0;padding-right:0;padding-top:6px;padding-bottom:0;font-size:max(13px, 0.8125rem);font-weight:400}.action-sheet-button.sc-ion-action-sheet-ios{-webkit-padding-start:14px;padding-inline-start:14px;-webkit-padding-end:14px;padding-inline-end:14px;padding-top:14px;padding-bottom:14px;min-height:56px;font-size:max(20px, 1.25rem);contain:content}.action-sheet-button.sc-ion-action-sheet-ios .action-sheet-icon.sc-ion-action-sheet-ios{-webkit-margin-end:0.3em;margin-inline-end:0.3em;font-size:max(28px, 1.75rem);pointer-events:none}.action-sheet-button.sc-ion-action-sheet-ios:last-child{background-image:none}.action-sheet-selected.sc-ion-action-sheet-ios{font-weight:bold}.action-sheet-cancel.sc-ion-action-sheet-ios{font-weight:600}.action-sheet-cancel.sc-ion-action-sheet-ios::after{background:var(--button-background-selected);opacity:var(--button-background-selected-opacity)}.action-sheet-destructive.sc-ion-action-sheet-ios,.action-sheet-destructive.ion-activated.sc-ion-action-sheet-ios,.action-sheet-destructive.ion-focused.sc-ion-action-sheet-ios{color:var(--ion-color-danger, #c5000f)}@media (any-hover: hover){.action-sheet-destructive.sc-ion-action-sheet-ios:hover{color:var(--ion-color-danger, #c5000f)}}\";\n\nconst actionSheetMdCss = \".sc-ion-action-sheet-md-h{--color:initial;--button-color-activated:var(--button-color);--button-color-focused:var(--button-color);--button-color-hover:var(--button-color);--button-color-selected:var(--button-color);--min-width:auto;--width:100%;--max-width:500px;--min-height:auto;--height:auto;--max-height:calc(100% - (var(--ion-safe-area-top) + var(--ion-safe-area-bottom)));-moz-osx-font-smoothing:grayscale;-webkit-font-smoothing:antialiased;left:0;right:0;top:0;bottom:0;display:block;position:fixed;outline:none;font-family:var(--ion-font-family, inherit);-ms-touch-action:none;touch-action:none;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none;z-index:1001}.overlay-hidden.sc-ion-action-sheet-md-h{display:none}.action-sheet-wrapper.sc-ion-action-sheet-md{left:0;right:0;bottom:0;-webkit-transform:translate3d(0,  100%,  0);transform:translate3d(0,  100%,  0);display:block;position:absolute;width:var(--width);min-width:var(--min-width);max-width:var(--max-width);height:var(--height);min-height:var(--min-height);max-height:var(--max-height);z-index:10;pointer-events:none}.action-sheet-button.sc-ion-action-sheet-md{display:block;position:relative;width:100%;border:0;outline:none;background:var(--button-background);color:var(--button-color);font-family:inherit;overflow:hidden}.action-sheet-button.sc-ion-action-sheet-md:disabled{color:var(--button-color-disabled);opacity:0.4}.action-sheet-button-inner.sc-ion-action-sheet-md{display:-ms-flexbox;display:flex;position:relative;-ms-flex-flow:row nowrap;flex-flow:row nowrap;-ms-flex-negative:0;flex-shrink:0;-ms-flex-align:center;align-items:center;-ms-flex-pack:center;justify-content:center;pointer-events:none;width:100%;height:100%;z-index:1}.action-sheet-container.sc-ion-action-sheet-md{display:-ms-flexbox;display:flex;-ms-flex-flow:column;flex-flow:column;-ms-flex-pack:end;justify-content:flex-end;height:100%;max-height:calc(100vh - (var(--ion-safe-area-top, 0) + var(--ion-safe-area-bottom, 0)));max-height:calc(100dvh - (var(--ion-safe-area-top, 0) + var(--ion-safe-area-bottom, 0)))}.action-sheet-group.sc-ion-action-sheet-md{-ms-flex-negative:2;flex-shrink:2;overscroll-behavior-y:contain;overflow-y:auto;-webkit-overflow-scrolling:touch;pointer-events:all;background:var(--background)}@media (any-pointer: coarse){.action-sheet-group.sc-ion-action-sheet-md::-webkit-scrollbar{display:none}}.action-sheet-group-cancel.sc-ion-action-sheet-md{-ms-flex-negative:0;flex-shrink:0;overflow:hidden}.action-sheet-button.sc-ion-action-sheet-md::after{left:0;right:0;top:0;bottom:0;position:absolute;content:\\\"\\\";opacity:0}.action-sheet-selected.sc-ion-action-sheet-md{color:var(--button-color-selected)}.action-sheet-selected.sc-ion-action-sheet-md::after{background:var(--button-background-selected);opacity:var(--button-background-selected-opacity)}.action-sheet-button.ion-activated.sc-ion-action-sheet-md{color:var(--button-color-activated)}.action-sheet-button.ion-activated.sc-ion-action-sheet-md::after{background:var(--button-background-activated);opacity:var(--button-background-activated-opacity)}.action-sheet-button.ion-focused.sc-ion-action-sheet-md{color:var(--button-color-focused)}.action-sheet-button.ion-focused.sc-ion-action-sheet-md::after{background:var(--button-background-focused);opacity:var(--button-background-focused-opacity)}@media (any-hover: hover){.action-sheet-button.sc-ion-action-sheet-md:not(:disabled):hover{color:var(--button-color-hover)}.action-sheet-button.sc-ion-action-sheet-md:not(:disabled):hover::after{background:var(--button-background-hover);opacity:var(--button-background-hover-opacity)}}.sc-ion-action-sheet-md-h{--background:var(--ion-overlay-background-color, var(--ion-background-color, #fff));--backdrop-opacity:var(--ion-backdrop-opacity, 0.32);--button-background:transparent;--button-background-selected:currentColor;--button-background-selected-opacity:0;--button-background-activated:transparent;--button-background-activated-opacity:0;--button-background-hover:currentColor;--button-background-hover-opacity:.04;--button-background-focused:currentColor;--button-background-focused-opacity:.12;--button-color:var(--ion-color-step-850, var(--ion-text-color-step-150, #262626));--button-color-disabled:var(--button-color);--color:rgba(var(--ion-text-color-rgb, 0, 0, 0), 0.54)}.action-sheet-wrapper.sc-ion-action-sheet-md{-webkit-margin-start:auto;margin-inline-start:auto;-webkit-margin-end:auto;margin-inline-end:auto;margin-top:var(--ion-safe-area-top, 0);margin-bottom:0}.action-sheet-title.sc-ion-action-sheet-md{-webkit-padding-start:16px;padding-inline-start:16px;-webkit-padding-end:16px;padding-inline-end:16px;padding-top:20px;padding-bottom:17px;min-height:60px;color:var(--color, rgba(var(--ion-text-color-rgb, 0, 0, 0), 0.54));font-size:1rem;text-align:start}.action-sheet-sub-title.sc-ion-action-sheet-md{padding-left:0;padding-right:0;padding-top:16px;padding-bottom:0;font-size:0.875rem}.action-sheet-group.sc-ion-action-sheet-md:first-child{padding-top:0}.action-sheet-group.sc-ion-action-sheet-md:last-child{padding-bottom:var(--ion-safe-area-bottom)}.action-sheet-button.sc-ion-action-sheet-md{-webkit-padding-start:16px;padding-inline-start:16px;-webkit-padding-end:16px;padding-inline-end:16px;padding-top:12px;padding-bottom:12px;position:relative;min-height:52px;font-size:1rem;text-align:start;contain:content;overflow:hidden}.action-sheet-icon.sc-ion-action-sheet-md{-webkit-margin-start:0;margin-inline-start:0;-webkit-margin-end:32px;margin-inline-end:32px;margin-top:0;margin-bottom:0;color:var(--color);font-size:1.5rem}.action-sheet-button-inner.sc-ion-action-sheet-md{-ms-flex-pack:start;justify-content:flex-start}.action-sheet-selected.sc-ion-action-sheet-md{font-weight:bold}\";\n\nconst ActionSheet = class {\n    constructor(hostRef) {\n        registerInstance(this, hostRef);\n        this.didPresent = createEvent(this, \"ionActionSheetDidPresent\", 7);\n        this.willPresent = createEvent(this, \"ionActionSheetWillPresent\", 7);\n        this.willDismiss = createEvent(this, \"ionActionSheetWillDismiss\", 7);\n        this.didDismiss = createEvent(this, \"ionActionSheetDidDismiss\", 7);\n        this.didPresentShorthand = createEvent(this, \"didPresent\", 7);\n        this.willPresentShorthand = createEvent(this, \"willPresent\", 7);\n        this.willDismissShorthand = createEvent(this, \"willDismiss\", 7);\n        this.didDismissShorthand = createEvent(this, \"didDismiss\", 7);\n        this.delegateController = createDelegateController(this);\n        this.lockController = createLockController();\n        this.triggerController = createTriggerController();\n        this.presented = false;\n        /** @internal */\n        this.hasController = false;\n        /**\n         * If `true`, the keyboard will be automatically dismissed when the overlay is presented.\n         */\n        this.keyboardClose = true;\n        /**\n         * An array of buttons for the action sheet.\n         */\n        this.buttons = [];\n        /**\n         * If `true`, the action sheet will be dismissed when the backdrop is clicked.\n         */\n        this.backdropDismiss = true;\n        /**\n         * If `true`, the action sheet will be translucent.\n         * Only applies when the mode is `\"ios\"` and the device supports\n         * [`backdrop-filter`](https://developer.mozilla.org/en-US/docs/Web/CSS/backdrop-filter#Browser_compatibility).\n         */\n        this.translucent = false;\n        /**\n         * If `true`, the action sheet will animate.\n         */\n        this.animated = true;\n        /**\n         * If `true`, the action sheet will open. If `false`, the action sheet will close.\n         * Use this if you need finer grained control over presentation, otherwise\n         * just use the actionSheetController or the `trigger` property.\n         * Note: `isOpen` will not automatically be set back to `false` when\n         * the action sheet dismisses. You will need to do that in your code.\n         */\n        this.isOpen = false;\n        this.onBackdropTap = () => {\n            this.dismiss(undefined, BACKDROP);\n        };\n        this.dispatchCancelHandler = (ev) => {\n            const role = ev.detail.role;\n            if (isCancel(role)) {\n                const cancelButton = this.getButtons().find((b) => b.role === 'cancel');\n                this.callButtonHandler(cancelButton);\n            }\n        };\n    }\n    onIsOpenChange(newValue, oldValue) {\n        if (newValue === true && oldValue === false) {\n            this.present();\n        }\n        else if (newValue === false && oldValue === true) {\n            this.dismiss();\n        }\n    }\n    triggerChanged() {\n        const { trigger, el, triggerController } = this;\n        if (trigger) {\n            triggerController.addClickListener(el, trigger);\n        }\n    }\n    /**\n     * Present the action sheet overlay after it has been created.\n     */\n    async present() {\n        const unlock = await this.lockController.lock();\n        await this.delegateController.attachViewToDom();\n        await present(this, 'actionSheetEnter', iosEnterAnimation, mdEnterAnimation);\n        unlock();\n    }\n    /**\n     * Dismiss the action sheet overlay after it has been presented.\n     * This is a no-op if the overlay has not been presented yet. If you want\n     * to remove an overlay from the DOM that was never presented, use the\n     * [remove](https://developer.mozilla.org/en-US/docs/Web/API/Element/remove) method.\n     *\n     * @param data Any data to emit in the dismiss events.\n     * @param role The role of the element that is dismissing the action sheet.\n     * This can be useful in a button handler for determining which button was\n     * clicked to dismiss the action sheet. Some examples include:\n     * `\"cancel\"`, `\"destructive\"`, `\"selected\"`, and `\"backdrop\"`.\n     */\n    async dismiss(data, role) {\n        const unlock = await this.lockController.lock();\n        const dismissed = await dismiss(this, data, role, 'actionSheetLeave', iosLeaveAnimation, mdLeaveAnimation);\n        if (dismissed) {\n            this.delegateController.removeViewFromDom();\n        }\n        unlock();\n        return dismissed;\n    }\n    /**\n     * Returns a promise that resolves when the action sheet did dismiss.\n     */\n    onDidDismiss() {\n        return eventMethod(this.el, 'ionActionSheetDidDismiss');\n    }\n    /**\n     * Returns a promise that resolves when the action sheet will dismiss.\n     *\n     */\n    onWillDismiss() {\n        return eventMethod(this.el, 'ionActionSheetWillDismiss');\n    }\n    async buttonClick(button) {\n        const role = button.role;\n        if (isCancel(role)) {\n            return this.dismiss(button.data, role);\n        }\n        const shouldDismiss = await this.callButtonHandler(button);\n        if (shouldDismiss) {\n            return this.dismiss(button.data, button.role);\n        }\n        return Promise.resolve();\n    }\n    async callButtonHandler(button) {\n        if (button) {\n            // a handler has been provided, execute it\n            // pass the handler the values from the inputs\n            const rtn = await safeCall(button.handler);\n            if (rtn === false) {\n                // if the return value of the handler is false then do not dismiss\n                return false;\n            }\n        }\n        return true;\n    }\n    getButtons() {\n        return this.buttons.map((b) => {\n            return typeof b === 'string' ? { text: b } : b;\n        });\n    }\n    connectedCallback() {\n        prepareOverlay(this.el);\n        this.triggerChanged();\n    }\n    disconnectedCallback() {\n        if (this.gesture) {\n            this.gesture.destroy();\n            this.gesture = undefined;\n        }\n        this.triggerController.removeClickListener();\n    }\n    componentWillLoad() {\n        var _a;\n        if (!((_a = this.htmlAttributes) === null || _a === void 0 ? void 0 : _a.id)) {\n            setOverlayId(this.el);\n        }\n    }\n    componentDidLoad() {\n        /**\n         * Only create gesture if:\n         * 1. A gesture does not already exist\n         * 2. App is running in iOS mode\n         * 3. A wrapper ref exists\n         * 4. A group ref exists\n         */\n        const { groupEl, wrapperEl } = this;\n        if (!this.gesture && getIonMode(this) === 'ios' && wrapperEl && groupEl) {\n            readTask(() => {\n                const isScrollable = groupEl.scrollHeight > groupEl.clientHeight;\n                if (!isScrollable) {\n                    this.gesture = createButtonActiveGesture(wrapperEl, (refEl) => refEl.classList.contains('action-sheet-button'));\n                    this.gesture.enable(true);\n                }\n            });\n        }\n        /**\n         * If action sheet was rendered with isOpen=\"true\"\n         * then we should open action sheet immediately.\n         */\n        if (this.isOpen === true) {\n            raf(() => this.present());\n        }\n        /**\n         * When binding values in frameworks such as Angular\n         * it is possible for the value to be set after the Web Component\n         * initializes but before the value watcher is set up in Stencil.\n         * As a result, the watcher callback may not be fired.\n         * We work around this by manually calling the watcher\n         * callback when the component has loaded and the watcher\n         * is configured.\n         */\n        this.triggerChanged();\n    }\n    render() {\n        const { header, htmlAttributes, overlayIndex } = this;\n        const mode = getIonMode(this);\n        const allButtons = this.getButtons();\n        const cancelButton = allButtons.find((b) => b.role === 'cancel');\n        const buttons = allButtons.filter((b) => b.role !== 'cancel');\n        const headerID = `action-sheet-${overlayIndex}-header`;\n        return (h(Host, Object.assign({ key: '9fef156b2a1f09ca4a6c1fe1f37c374139bde03c', role: \"dialog\", \"aria-modal\": \"true\", \"aria-labelledby\": header !== undefined ? headerID : null, tabindex: \"-1\" }, htmlAttributes, { style: {\n                zIndex: `${20000 + this.overlayIndex}`,\n            }, class: Object.assign(Object.assign({ [mode]: true }, getClassMap(this.cssClass)), { 'overlay-hidden': true, 'action-sheet-translucent': this.translucent }), onIonActionSheetWillDismiss: this.dispatchCancelHandler, onIonBackdropTap: this.onBackdropTap }), h(\"ion-backdrop\", { key: '81cf3f7d19864e041813987b46d2d115b8466819', tappable: this.backdropDismiss }), h(\"div\", { key: '791c6a976683646fc306a42c15c5078b6f06a45f', tabindex: \"0\", \"aria-hidden\": \"true\" }), h(\"div\", { key: 'a350b489ef7852eab9dc2227ce6d92da27dd9bf9', class: \"action-sheet-wrapper ion-overlay-wrapper\", ref: (el) => (this.wrapperEl = el) }, h(\"div\", { key: '69ba51ee13510c1a411d87cb4845b11b7302a36f', class: \"action-sheet-container\" }, h(\"div\", { key: 'bded15b8306c36591e526f0f99e1eeabcbab3915', class: \"action-sheet-group\", ref: (el) => (this.groupEl = el) }, header !== undefined && (h(\"div\", { key: '06b5147c0f6d9180fe8f12e75c9b4a0310226adc', id: headerID, class: {\n                'action-sheet-title': true,\n                'action-sheet-has-sub-title': this.subHeader !== undefined,\n            } }, header, this.subHeader && h(\"div\", { key: '54874362a75c679aba803bf4f8768f5404d2dd28', class: \"action-sheet-sub-title\" }, this.subHeader))), buttons.map((b) => (h(\"button\", Object.assign({}, b.htmlAttributes, { type: \"button\", id: b.id, class: buttonClass(b), onClick: () => this.buttonClick(b), disabled: b.disabled }), h(\"span\", { class: \"action-sheet-button-inner\" }, b.icon && h(\"ion-icon\", { icon: b.icon, \"aria-hidden\": \"true\", lazy: false, class: \"action-sheet-icon\" }), b.text), mode === 'md' && h(\"ion-ripple-effect\", null))))), cancelButton && (h(\"div\", { key: '67b0de298eb424f3dea846a841b7a06d70e3930d', class: \"action-sheet-group action-sheet-group-cancel\" }, h(\"button\", Object.assign({ key: 'e7e3f9a5495eea9b97dbf885ef36944f2e420eff' }, cancelButton.htmlAttributes, { type: \"button\", class: buttonClass(cancelButton), onClick: () => this.buttonClick(cancelButton) }), h(\"span\", { key: 'f889d29ed6c3d14bbc1d805888351d87f5122377', class: \"action-sheet-button-inner\" }, cancelButton.icon && (h(\"ion-icon\", { key: '7c05cf424b38c37fd40aaeb42a494387291571fb', icon: cancelButton.icon, \"aria-hidden\": \"true\", lazy: false, class: \"action-sheet-icon\" })), cancelButton.text), mode === 'md' && h(\"ion-ripple-effect\", { key: 'bed927b477dc2708a5123ef560274fca9819b3d6' })))))), h(\"div\", { key: 'c5df1b11dc15a93892d57065d3dd5fbe02e43b39', tabindex: \"0\", \"aria-hidden\": \"true\" })));\n    }\n    get el() { return getElement(this); }\n    static get watchers() { return {\n        \"isOpen\": [\"onIsOpenChange\"],\n        \"trigger\": [\"triggerChanged\"]\n    }; }\n};\nconst buttonClass = (button) => {\n    return Object.assign({ 'action-sheet-button': true, 'ion-activatable': !button.disabled, 'ion-focusable': !button.disabled, [`action-sheet-${button.role}`]: button.role !== undefined }, getClassMap(button.cssClass));\n};\nActionSheet.style = {\n    ios: actionSheetIosCss,\n    md: actionSheetMdCss\n};\n\nexport { ActionSheet as ion_action_sheet };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAqBA,IAAM,oBAAoB,CAAC,WAAW;AAClC,QAAM,gBAAgB,gBAAgB;AACtC,QAAM,oBAAoB,gBAAgB;AAC1C,QAAM,mBAAmB,gBAAgB;AACzC,oBACK,WAAW,OAAO,cAAc,cAAc,CAAC,EAC/C,OAAO,WAAW,MAAM,yBAAyB,EACjD,aAAa;AAAA,IACd,kBAAkB;AAAA,EACtB,CAAC,EACI,iBAAiB,CAAC,gBAAgB,CAAC;AACxC,mBACK,WAAW,OAAO,cAAc,uBAAuB,CAAC,EACxD,OAAO,aAAa,oBAAoB,gBAAgB;AAC7D,SAAO,cACF,WAAW,MAAM,EACjB,OAAO,6BAA6B,EACpC,SAAS,GAAG,EACZ,aAAa,CAAC,mBAAmB,gBAAgB,CAAC;AAC3D;AAKA,IAAM,oBAAoB,CAAC,WAAW;AAClC,QAAM,gBAAgB,gBAAgB;AACtC,QAAM,oBAAoB,gBAAgB;AAC1C,QAAM,mBAAmB,gBAAgB;AACzC,oBAAkB,WAAW,OAAO,cAAc,cAAc,CAAC,EAAE,OAAO,WAAW,2BAA2B,CAAC;AACjH,mBACK,WAAW,OAAO,cAAc,uBAAuB,CAAC,EACxD,OAAO,aAAa,kBAAkB,kBAAkB;AAC7D,SAAO,cACF,WAAW,MAAM,EACjB,OAAO,6BAA6B,EACpC,SAAS,GAAG,EACZ,aAAa,CAAC,mBAAmB,gBAAgB,CAAC;AAC3D;AAKA,IAAM,mBAAmB,CAAC,WAAW;AACjC,QAAM,gBAAgB,gBAAgB;AACtC,QAAM,oBAAoB,gBAAgB;AAC1C,QAAM,mBAAmB,gBAAgB;AACzC,oBACK,WAAW,OAAO,cAAc,cAAc,CAAC,EAC/C,OAAO,WAAW,MAAM,yBAAyB,EACjD,aAAa;AAAA,IACd,kBAAkB;AAAA,EACtB,CAAC,EACI,iBAAiB,CAAC,gBAAgB,CAAC;AACxC,mBACK,WAAW,OAAO,cAAc,uBAAuB,CAAC,EACxD,OAAO,aAAa,oBAAoB,gBAAgB;AAC7D,SAAO,cACF,WAAW,MAAM,EACjB,OAAO,6BAA6B,EACpC,SAAS,GAAG,EACZ,aAAa,CAAC,mBAAmB,gBAAgB,CAAC;AAC3D;AAKA,IAAM,mBAAmB,CAAC,WAAW;AACjC,QAAM,gBAAgB,gBAAgB;AACtC,QAAM,oBAAoB,gBAAgB;AAC1C,QAAM,mBAAmB,gBAAgB;AACzC,oBAAkB,WAAW,OAAO,cAAc,cAAc,CAAC,EAAE,OAAO,WAAW,2BAA2B,CAAC;AACjH,mBACK,WAAW,OAAO,cAAc,uBAAuB,CAAC,EACxD,OAAO,aAAa,kBAAkB,kBAAkB;AAC7D,SAAO,cACF,WAAW,MAAM,EACjB,OAAO,6BAA6B,EACpC,SAAS,GAAG,EACZ,aAAa,CAAC,mBAAmB,gBAAgB,CAAC;AAC3D;AAEA,IAAM,oBAAoB;AAE1B,IAAM,mBAAmB;AAEzB,IAAM,cAAc,MAAM;AAAA,EACtB,YAAY,SAAS;AACjB,qBAAiB,MAAM,OAAO;AAC9B,SAAK,aAAa,YAAY,MAAM,4BAA4B,CAAC;AACjE,SAAK,cAAc,YAAY,MAAM,6BAA6B,CAAC;AACnE,SAAK,cAAc,YAAY,MAAM,6BAA6B,CAAC;AACnE,SAAK,aAAa,YAAY,MAAM,4BAA4B,CAAC;AACjE,SAAK,sBAAsB,YAAY,MAAM,cAAc,CAAC;AAC5D,SAAK,uBAAuB,YAAY,MAAM,eAAe,CAAC;AAC9D,SAAK,uBAAuB,YAAY,MAAM,eAAe,CAAC;AAC9D,SAAK,sBAAsB,YAAY,MAAM,cAAc,CAAC;AAC5D,SAAK,qBAAqB,yBAAyB,IAAI;AACvD,SAAK,iBAAiB,qBAAqB;AAC3C,SAAK,oBAAoB,wBAAwB;AACjD,SAAK,YAAY;AAEjB,SAAK,gBAAgB;AAIrB,SAAK,gBAAgB;AAIrB,SAAK,UAAU,CAAC;AAIhB,SAAK,kBAAkB;AAMvB,SAAK,cAAc;AAInB,SAAK,WAAW;AAQhB,SAAK,SAAS;AACd,SAAK,gBAAgB,MAAM;AACvB,WAAK,QAAQ,QAAW,QAAQ;AAAA,IACpC;AACA,SAAK,wBAAwB,CAAC,OAAO;AACjC,YAAM,OAAO,GAAG,OAAO;AACvB,UAAI,SAAS,IAAI,GAAG;AAChB,cAAM,eAAe,KAAK,WAAW,EAAE,KAAK,CAAC,MAAM,EAAE,SAAS,QAAQ;AACtE,aAAK,kBAAkB,YAAY;AAAA,MACvC;AAAA,IACJ;AAAA,EACJ;AAAA,EACA,eAAe,UAAU,UAAU;AAC/B,QAAI,aAAa,QAAQ,aAAa,OAAO;AACzC,WAAK,QAAQ;AAAA,IACjB,WACS,aAAa,SAAS,aAAa,MAAM;AAC9C,WAAK,QAAQ;AAAA,IACjB;AAAA,EACJ;AAAA,EACA,iBAAiB;AACb,UAAM,EAAE,SAAS,IAAI,kBAAkB,IAAI;AAC3C,QAAI,SAAS;AACT,wBAAkB,iBAAiB,IAAI,OAAO;AAAA,IAClD;AAAA,EACJ;AAAA;AAAA;AAAA;AAAA,EAIM,UAAU;AAAA;AACZ,YAAM,SAAS,MAAM,KAAK,eAAe,KAAK;AAC9C,YAAM,KAAK,mBAAmB,gBAAgB;AAC9C,YAAM,QAAQ,MAAM,oBAAoB,mBAAmB,gBAAgB;AAC3E,aAAO;AAAA,IACX;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAaM,QAAQ,MAAM,MAAM;AAAA;AACtB,YAAM,SAAS,MAAM,KAAK,eAAe,KAAK;AAC9C,YAAM,YAAY,MAAM,QAAQ,MAAM,MAAM,MAAM,oBAAoB,mBAAmB,gBAAgB;AACzG,UAAI,WAAW;AACX,aAAK,mBAAmB,kBAAkB;AAAA,MAC9C;AACA,aAAO;AACP,aAAO;AAAA,IACX;AAAA;AAAA;AAAA;AAAA;AAAA,EAIA,eAAe;AACX,WAAO,YAAY,KAAK,IAAI,0BAA0B;AAAA,EAC1D;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,gBAAgB;AACZ,WAAO,YAAY,KAAK,IAAI,2BAA2B;AAAA,EAC3D;AAAA,EACM,YAAY,QAAQ;AAAA;AACtB,YAAM,OAAO,OAAO;AACpB,UAAI,SAAS,IAAI,GAAG;AAChB,eAAO,KAAK,QAAQ,OAAO,MAAM,IAAI;AAAA,MACzC;AACA,YAAM,gBAAgB,MAAM,KAAK,kBAAkB,MAAM;AACzD,UAAI,eAAe;AACf,eAAO,KAAK,QAAQ,OAAO,MAAM,OAAO,IAAI;AAAA,MAChD;AACA,aAAO,QAAQ,QAAQ;AAAA,IAC3B;AAAA;AAAA,EACM,kBAAkB,QAAQ;AAAA;AAC5B,UAAI,QAAQ;AAGR,cAAM,MAAM,MAAM,SAAS,OAAO,OAAO;AACzC,YAAI,QAAQ,OAAO;AAEf,iBAAO;AAAA,QACX;AAAA,MACJ;AACA,aAAO;AAAA,IACX;AAAA;AAAA,EACA,aAAa;AACT,WAAO,KAAK,QAAQ,IAAI,CAAC,MAAM;AAC3B,aAAO,OAAO,MAAM,WAAW,EAAE,MAAM,EAAE,IAAI;AAAA,IACjD,CAAC;AAAA,EACL;AAAA,EACA,oBAAoB;AAChB,mBAAe,KAAK,EAAE;AACtB,SAAK,eAAe;AAAA,EACxB;AAAA,EACA,uBAAuB;AACnB,QAAI,KAAK,SAAS;AACd,WAAK,QAAQ,QAAQ;AACrB,WAAK,UAAU;AAAA,IACnB;AACA,SAAK,kBAAkB,oBAAoB;AAAA,EAC/C;AAAA,EACA,oBAAoB;AAChB,QAAI;AACJ,QAAI,GAAG,KAAK,KAAK,oBAAoB,QAAQ,OAAO,SAAS,SAAS,GAAG,KAAK;AAC1E,mBAAa,KAAK,EAAE;AAAA,IACxB;AAAA,EACJ;AAAA,EACA,mBAAmB;AAQf,UAAM,EAAE,SAAS,UAAU,IAAI;AAC/B,QAAI,CAAC,KAAK,WAAW,WAAW,IAAI,MAAM,SAAS,aAAa,SAAS;AACrE,eAAS,MAAM;AACX,cAAM,eAAe,QAAQ,eAAe,QAAQ;AACpD,YAAI,CAAC,cAAc;AACf,eAAK,UAAU,0BAA0B,WAAW,CAAC,UAAU,MAAM,UAAU,SAAS,qBAAqB,CAAC;AAC9G,eAAK,QAAQ,OAAO,IAAI;AAAA,QAC5B;AAAA,MACJ,CAAC;AAAA,IACL;AAKA,QAAI,KAAK,WAAW,MAAM;AACtB,UAAI,MAAM,KAAK,QAAQ,CAAC;AAAA,IAC5B;AAUA,SAAK,eAAe;AAAA,EACxB;AAAA,EACA,SAAS;AACL,UAAM,EAAE,QAAQ,gBAAgB,aAAa,IAAI;AACjD,UAAM,OAAO,WAAW,IAAI;AAC5B,UAAM,aAAa,KAAK,WAAW;AACnC,UAAM,eAAe,WAAW,KAAK,CAAC,MAAM,EAAE,SAAS,QAAQ;AAC/D,UAAM,UAAU,WAAW,OAAO,CAAC,MAAM,EAAE,SAAS,QAAQ;AAC5D,UAAM,WAAW,gBAAgB,YAAY;AAC7C,WAAQ,EAAE,MAAM,OAAO,OAAO,EAAE,KAAK,4CAA4C,MAAM,UAAU,cAAc,QAAQ,mBAAmB,WAAW,SAAY,WAAW,MAAM,UAAU,KAAK,GAAG,gBAAgB,EAAE,OAAO;AAAA,MACrN,QAAQ,GAAG,MAAQ,KAAK,YAAY;AAAA,IACxC,GAAG,OAAO,OAAO,OAAO,OAAO,OAAO,EAAE,CAAC,IAAI,GAAG,KAAK,GAAG,YAAY,KAAK,QAAQ,CAAC,GAAG,EAAE,kBAAkB,MAAM,4BAA4B,KAAK,YAAY,CAAC,GAAG,6BAA6B,KAAK,uBAAuB,kBAAkB,KAAK,cAAc,CAAC,GAAG,EAAE,gBAAgB,EAAE,KAAK,4CAA4C,UAAU,KAAK,gBAAgB,CAAC,GAAG,EAAE,OAAO,EAAE,KAAK,4CAA4C,UAAU,KAAK,eAAe,OAAO,CAAC,GAAG,EAAE,OAAO,EAAE,KAAK,4CAA4C,OAAO,4CAA4C,KAAK,CAAC,OAAQ,KAAK,YAAY,GAAI,GAAG,EAAE,OAAO,EAAE,KAAK,4CAA4C,OAAO,yBAAyB,GAAG,EAAE,OAAO,EAAE,KAAK,4CAA4C,OAAO,sBAAsB,KAAK,CAAC,OAAQ,KAAK,UAAU,GAAI,GAAG,WAAW,UAAc,EAAE,OAAO,EAAE,KAAK,4CAA4C,IAAI,UAAU,OAAO;AAAA,MACt6B,sBAAsB;AAAA,MACtB,8BAA8B,KAAK,cAAc;AAAA,IACrD,EAAE,GAAG,QAAQ,KAAK,aAAa,EAAE,OAAO,EAAE,KAAK,4CAA4C,OAAO,yBAAyB,GAAG,KAAK,SAAS,CAAC,GAAI,QAAQ,IAAI,CAAC,MAAO,EAAE,UAAU,OAAO,OAAO,CAAC,GAAG,EAAE,gBAAgB,EAAE,MAAM,UAAU,IAAI,EAAE,IAAI,OAAO,YAAY,CAAC,GAAG,SAAS,MAAM,KAAK,YAAY,CAAC,GAAG,UAAU,EAAE,SAAS,CAAC,GAAG,EAAE,QAAQ,EAAE,OAAO,4BAA4B,GAAG,EAAE,QAAQ,EAAE,YAAY,EAAE,MAAM,EAAE,MAAM,eAAe,QAAQ,MAAM,OAAO,OAAO,oBAAoB,CAAC,GAAG,EAAE,IAAI,GAAG,SAAS,QAAQ,EAAE,qBAAqB,IAAI,CAAC,CAAE,CAAC,GAAG,gBAAiB,EAAE,OAAO,EAAE,KAAK,4CAA4C,OAAO,+CAA+C,GAAG,EAAE,UAAU,OAAO,OAAO,EAAE,KAAK,2CAA2C,GAAG,aAAa,gBAAgB,EAAE,MAAM,UAAU,OAAO,YAAY,YAAY,GAAG,SAAS,MAAM,KAAK,YAAY,YAAY,EAAE,CAAC,GAAG,EAAE,QAAQ,EAAE,KAAK,4CAA4C,OAAO,4BAA4B,GAAG,aAAa,QAAS,EAAE,YAAY,EAAE,KAAK,4CAA4C,MAAM,aAAa,MAAM,eAAe,QAAQ,MAAM,OAAO,OAAO,oBAAoB,CAAC,GAAI,aAAa,IAAI,GAAG,SAAS,QAAQ,EAAE,qBAAqB,EAAE,KAAK,2CAA2C,CAAC,CAAC,CAAC,CAAE,CAAC,GAAG,EAAE,OAAO,EAAE,KAAK,4CAA4C,UAAU,KAAK,eAAe,OAAO,CAAC,CAAC;AAAA,EAC/2C;AAAA,EACA,IAAI,KAAK;AAAE,WAAO,WAAW,IAAI;AAAA,EAAG;AAAA,EACpC,WAAW,WAAW;AAAE,WAAO;AAAA,MAC3B,UAAU,CAAC,gBAAgB;AAAA,MAC3B,WAAW,CAAC,gBAAgB;AAAA,IAChC;AAAA,EAAG;AACP;AACA,IAAM,cAAc,CAAC,WAAW;AAC5B,SAAO,OAAO,OAAO,EAAE,uBAAuB,MAAM,mBAAmB,CAAC,OAAO,UAAU,iBAAiB,CAAC,OAAO,UAAU,CAAC,gBAAgB,OAAO,IAAI,EAAE,GAAG,OAAO,SAAS,OAAU,GAAG,YAAY,OAAO,QAAQ,CAAC;AAC1N;AACA,YAAY,QAAQ;AAAA,EAChB,KAAK;AAAA,EACL,IAAI;AACR;", "names": []}