import { Component, OnInit } from '@angular/core';
import { IonicModule } from '@ionic/angular';
import { FormsModule } from '@angular/forms';
import { CommonModule } from '@angular/common';
import { SqliteService } from 'src/app/services/sqlite-crudop.service';
@Component({
  selector: 'app-sqlite-crudop',
  standalone: true,
  imports: [IonicModule, FormsModule, CommonModule],
  templateUrl: './sqlite-crudop.page.html',
  styleUrls: ['./sqlite-crudop.page.scss'],
})
export class SqliteCrudopPage implements OnInit {
  users: any[] = [];
  name = '';
  email = '';

  constructor(private sqlite: SqliteService) {}

  async ngOnInit() {
    await this.sqlite.initDB();
    this.loadUsers();
  }

  async loadUsers() {
    this.users = await this.sqlite.getUsers();
  }

  async addUser() {
    if (!this.name || !this.email) return;
    await this.sqlite.addUser(this.name, this.email);
    this.name = '';
    this.email = '';
    this.loadUsers();
  }

  async deleteUser(id: number) {
    await this.sqlite.deleteUser(id);
    this.loadUsers();
  }
}
