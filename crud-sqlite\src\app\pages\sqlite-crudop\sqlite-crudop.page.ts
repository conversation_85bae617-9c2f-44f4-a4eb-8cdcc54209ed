import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';

// ✅ Import standalone Ionic components directly
import { IonContent, IonHeader, IonTitle, IonToolbar, IonItem, IonInput, IonButton, IonList, IonLabel } from '@ionic/angular/standalone';

import { SqliteCrudopService } from 'src/app/services/sqlite-crudop.service';

@Component({
  selector: 'app-sqlite-crudop',
  standalone: true,
  templateUrl: './sqlite-crudop.page.html',
  styleUrls: ['./sqlite-crudop.page.scss'],
  imports: [
    CommonModule,
    FormsModule,
    IonContent,
    IonHeader,
    IonTitle,
    IonToolbar,
    IonItem,
    IonInput,
    IonButton,
    IonList,
    IonLabel
  ]
})
export class SqliteCrudopPage implements OnInit {
  users: any[] = [];
  name = '';
  email = '';
  editMode = false;
  editId!: number;

  constructor(private sqlite: SqliteCrudopService) {}

  async ngOnInit() {
    await this.sqlite.initDB();
    await this.loadUsers();
  }

  async loadUsers() {
    this.users = await this.sqlite.getAllUsers();
  }

  async saveUser() {
    if (this.editMode) {
      await this.sqlite.updateUser(this.editId, this.name, this.email);
      this.editMode = false;
    } else {
      await this.sqlite.addUser(this.name, this.email);
    }

    // Reset form
    this.name = '';
    this.email = '';

    await this.loadUsers();
  }

  editUser(user: any) {
    this.editMode = true;
    this.editId = user.id;
    this.name = user.name;
    this.email = user.email;
  }

  async deleteUser(id: number) {
    await this.sqlite.deleteUser(id);
    await this.loadUsers();
  }
}
