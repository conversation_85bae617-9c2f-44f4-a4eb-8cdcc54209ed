{"version": 3, "sources": ["../../../../../../node_modules/@ionic/core/components/animation.js", "../../../../../../node_modules/@ionic/core/components/index2.js"], "sourcesContent": ["/*!\n * (C) Ionic http://ionicframework.com - MIT License\n */\nimport { a as printIonError } from './index4.js';\nimport { w as win } from './index9.js';\n\nlet animationPrefix;\nconst getAnimationPrefix = (el) => {\n    if (animationPrefix === undefined) {\n        const supportsUnprefixed = el.style.animationName !== undefined;\n        const supportsWebkitPrefix = el.style.webkitAnimationName !== undefined;\n        animationPrefix = !supportsUnprefixed && supportsWebkitPrefix ? '-webkit-' : '';\n    }\n    return animationPrefix;\n};\nconst setStyleProperty = (element, propertyName, value) => {\n    const prefix = propertyName.startsWith('animation') ? getAnimationPrefix(element) : '';\n    element.style.setProperty(prefix + propertyName, value);\n};\nconst addClassToArray = (classes = [], className) => {\n    if (className !== undefined) {\n        const classNameToAppend = Array.isArray(className) ? className : [className];\n        return [...classes, ...classNameToAppend];\n    }\n    return classes;\n};\n\nconst createAnimation = (animationId) => {\n    let _delay;\n    let _duration;\n    let _easing;\n    let _iterations;\n    let _fill;\n    let _direction;\n    let _keyframes = [];\n    let beforeAddClasses = [];\n    let beforeRemoveClasses = [];\n    let initialized = false;\n    let parentAnimation;\n    let beforeStylesValue = {};\n    let afterAddClasses = [];\n    let afterRemoveClasses = [];\n    let afterStylesValue = {};\n    let numAnimationsRunning = 0;\n    let shouldForceLinearEasing = false;\n    let shouldForceSyncPlayback = false;\n    let forceDirectionValue;\n    let forceDurationValue;\n    let forceDelayValue;\n    let willComplete = true;\n    let finished = false;\n    let shouldCalculateNumAnimations = true;\n    let ani;\n    let paused = false;\n    const id = animationId;\n    const onFinishCallbacks = [];\n    const onFinishOneTimeCallbacks = [];\n    const onStopOneTimeCallbacks = [];\n    const elements = [];\n    const childAnimations = [];\n    const stylesheets = [];\n    const _beforeAddReadFunctions = [];\n    const _beforeAddWriteFunctions = [];\n    const _afterAddReadFunctions = [];\n    const _afterAddWriteFunctions = [];\n    const webAnimations = [];\n    const supportsAnimationEffect = typeof AnimationEffect === 'function' ||\n        (win !== undefined && typeof win.AnimationEffect === 'function');\n    /**\n     * This is a feature detection for Web Animations.\n     *\n     * Certain environments such as emulated browser environments for testing,\n     * do not support Web Animations. As a result, we need to check for support\n     * and provide a fallback to test certain functionality related to Web Animations.\n     */\n    const supportsWebAnimations = typeof Element === 'function' &&\n        typeof Element.prototype.animate === 'function' &&\n        supportsAnimationEffect;\n    const getWebAnimations = () => {\n        return webAnimations;\n    };\n    const destroy = (clearStyleSheets) => {\n        childAnimations.forEach((childAnimation) => {\n            childAnimation.destroy(clearStyleSheets);\n        });\n        cleanUp(clearStyleSheets);\n        elements.length = 0;\n        childAnimations.length = 0;\n        _keyframes.length = 0;\n        clearOnFinish();\n        initialized = false;\n        shouldCalculateNumAnimations = true;\n        return ani;\n    };\n    /**\n     * Cancels any Web Animations, removes\n     * any animation properties from the\n     * animation's elements, and removes the\n     * animation's stylesheets from the DOM.\n     */\n    const cleanUp = (clearStyleSheets) => {\n        cleanUpElements();\n        if (clearStyleSheets) {\n            cleanUpStyleSheets();\n        }\n    };\n    const resetFlags = () => {\n        shouldForceLinearEasing = false;\n        shouldForceSyncPlayback = false;\n        shouldCalculateNumAnimations = true;\n        forceDirectionValue = undefined;\n        forceDurationValue = undefined;\n        forceDelayValue = undefined;\n        numAnimationsRunning = 0;\n        finished = false;\n        willComplete = true;\n        paused = false;\n    };\n    const isRunning = () => {\n        return numAnimationsRunning !== 0 && !paused;\n    };\n    /**\n     * @internal\n     * Remove a callback from a chosen callback array\n     * @param callbackToRemove: A reference to the callback that should be removed\n     * @param callbackObjects: An array of callbacks that callbackToRemove should be removed from.\n     */\n    const clearCallback = (callbackToRemove, callbackObjects) => {\n        const index = callbackObjects.findIndex((callbackObject) => callbackObject.c === callbackToRemove);\n        if (index > -1) {\n            callbackObjects.splice(index, 1);\n        }\n    };\n    /**\n     * @internal\n     * Add a callback to be fired when an animation is stopped/cancelled.\n     * @param callback: A reference to the callback that should be fired\n     * @param opts: Any options associated with this particular callback\n     */\n    const onStop = (callback, opts) => {\n        onStopOneTimeCallbacks.push({ c: callback, o: opts });\n        return ani;\n    };\n    const onFinish = (callback, opts) => {\n        const callbacks = (opts === null || opts === void 0 ? void 0 : opts.oneTimeCallback) ? onFinishOneTimeCallbacks : onFinishCallbacks;\n        callbacks.push({ c: callback, o: opts });\n        return ani;\n    };\n    const clearOnFinish = () => {\n        onFinishCallbacks.length = 0;\n        onFinishOneTimeCallbacks.length = 0;\n        return ani;\n    };\n    /**\n     * Cancels any Web Animations and removes\n     * any animation properties from the\n     * the animation's elements.\n     */\n    const cleanUpElements = () => {\n        if (supportsWebAnimations) {\n            webAnimations.forEach((animation) => {\n                animation.cancel();\n            });\n            webAnimations.length = 0;\n        }\n    };\n    /**\n     * Removes the animation's stylesheets\n     * from the DOM.\n     */\n    const cleanUpStyleSheets = () => {\n        stylesheets.forEach((stylesheet) => {\n            /**\n             * When sharing stylesheets, it's possible\n             * for another animation to have already\n             * cleaned up a particular stylesheet\n             */\n            if (stylesheet === null || stylesheet === void 0 ? void 0 : stylesheet.parentNode) {\n                stylesheet.parentNode.removeChild(stylesheet);\n            }\n        });\n        stylesheets.length = 0;\n    };\n    const beforeAddRead = (readFn) => {\n        _beforeAddReadFunctions.push(readFn);\n        return ani;\n    };\n    const beforeAddWrite = (writeFn) => {\n        _beforeAddWriteFunctions.push(writeFn);\n        return ani;\n    };\n    const afterAddRead = (readFn) => {\n        _afterAddReadFunctions.push(readFn);\n        return ani;\n    };\n    const afterAddWrite = (writeFn) => {\n        _afterAddWriteFunctions.push(writeFn);\n        return ani;\n    };\n    const beforeAddClass = (className) => {\n        beforeAddClasses = addClassToArray(beforeAddClasses, className);\n        return ani;\n    };\n    const beforeRemoveClass = (className) => {\n        beforeRemoveClasses = addClassToArray(beforeRemoveClasses, className);\n        return ani;\n    };\n    /**\n     * Set CSS inline styles to the animation's\n     * elements before the animation begins.\n     */\n    const beforeStyles = (styles = {}) => {\n        beforeStylesValue = styles;\n        return ani;\n    };\n    /**\n     * Clear CSS inline styles from the animation's\n     * elements before the animation begins.\n     */\n    const beforeClearStyles = (propertyNames = []) => {\n        for (const property of propertyNames) {\n            beforeStylesValue[property] = '';\n        }\n        return ani;\n    };\n    const afterAddClass = (className) => {\n        afterAddClasses = addClassToArray(afterAddClasses, className);\n        return ani;\n    };\n    const afterRemoveClass = (className) => {\n        afterRemoveClasses = addClassToArray(afterRemoveClasses, className);\n        return ani;\n    };\n    const afterStyles = (styles = {}) => {\n        afterStylesValue = styles;\n        return ani;\n    };\n    const afterClearStyles = (propertyNames = []) => {\n        for (const property of propertyNames) {\n            afterStylesValue[property] = '';\n        }\n        return ani;\n    };\n    const getFill = () => {\n        if (_fill !== undefined) {\n            return _fill;\n        }\n        if (parentAnimation) {\n            return parentAnimation.getFill();\n        }\n        return 'both';\n    };\n    const getDirection = () => {\n        if (forceDirectionValue !== undefined) {\n            return forceDirectionValue;\n        }\n        if (_direction !== undefined) {\n            return _direction;\n        }\n        if (parentAnimation) {\n            return parentAnimation.getDirection();\n        }\n        return 'normal';\n    };\n    const getEasing = () => {\n        if (shouldForceLinearEasing) {\n            return 'linear';\n        }\n        if (_easing !== undefined) {\n            return _easing;\n        }\n        if (parentAnimation) {\n            return parentAnimation.getEasing();\n        }\n        return 'linear';\n    };\n    const getDuration = () => {\n        if (shouldForceSyncPlayback) {\n            return 0;\n        }\n        if (forceDurationValue !== undefined) {\n            return forceDurationValue;\n        }\n        if (_duration !== undefined) {\n            return _duration;\n        }\n        if (parentAnimation) {\n            return parentAnimation.getDuration();\n        }\n        return 0;\n    };\n    const getIterations = () => {\n        if (_iterations !== undefined) {\n            return _iterations;\n        }\n        if (parentAnimation) {\n            return parentAnimation.getIterations();\n        }\n        return 1;\n    };\n    const getDelay = () => {\n        if (forceDelayValue !== undefined) {\n            return forceDelayValue;\n        }\n        if (_delay !== undefined) {\n            return _delay;\n        }\n        if (parentAnimation) {\n            return parentAnimation.getDelay();\n        }\n        return 0;\n    };\n    const getKeyframes = () => {\n        return _keyframes;\n    };\n    const direction = (animationDirection) => {\n        _direction = animationDirection;\n        update(true);\n        return ani;\n    };\n    const fill = (animationFill) => {\n        _fill = animationFill;\n        update(true);\n        return ani;\n    };\n    const delay = (animationDelay) => {\n        _delay = animationDelay;\n        update(true);\n        return ani;\n    };\n    const easing = (animationEasing) => {\n        _easing = animationEasing;\n        update(true);\n        return ani;\n    };\n    const duration = (animationDuration) => {\n        /**\n         * CSS Animation Durations of 0ms work fine on Chrome\n         * but do not run on Safari, so force it to 1ms to\n         * get it to run on both platforms.\n         */\n        if (!supportsWebAnimations && animationDuration === 0) {\n            animationDuration = 1;\n        }\n        _duration = animationDuration;\n        update(true);\n        return ani;\n    };\n    const iterations = (animationIterations) => {\n        _iterations = animationIterations;\n        update(true);\n        return ani;\n    };\n    const parent = (animation) => {\n        parentAnimation = animation;\n        return ani;\n    };\n    const addElement = (el) => {\n        if (el != null) {\n            if (el.nodeType === 1) {\n                elements.push(el);\n            }\n            else if (el.length >= 0) {\n                for (let i = 0; i < el.length; i++) {\n                    elements.push(el[i]);\n                }\n            }\n            else {\n                printIonError('createAnimation - Invalid addElement value.');\n            }\n        }\n        return ani;\n    };\n    const addAnimation = (animationToAdd) => {\n        if (animationToAdd != null) {\n            if (Array.isArray(animationToAdd)) {\n                for (const animation of animationToAdd) {\n                    animation.parent(ani);\n                    childAnimations.push(animation);\n                }\n            }\n            else {\n                animationToAdd.parent(ani);\n                childAnimations.push(animationToAdd);\n            }\n        }\n        return ani;\n    };\n    const keyframes = (keyframeValues) => {\n        const different = _keyframes !== keyframeValues;\n        _keyframes = keyframeValues;\n        if (different) {\n            updateKeyframes(_keyframes);\n        }\n        return ani;\n    };\n    const updateKeyframes = (keyframeValues) => {\n        if (supportsWebAnimations) {\n            getWebAnimations().forEach((animation) => {\n                /**\n                 * animation.effect's type is AnimationEffect.\n                 * However, in this case we have a more specific\n                 * type of AnimationEffect called KeyframeEffect which\n                 * inherits from AnimationEffect. As a result,\n                 * we cast animation.effect to KeyframeEffect.\n                 */\n                const keyframeEffect = animation.effect;\n                /**\n                 * setKeyframes is not supported in all browser\n                 * versions that Ionic supports, so we need to\n                 * check for support before using it.\n                 */\n                // eslint-disable-next-line @typescript-eslint/strict-boolean-expressions\n                if (keyframeEffect.setKeyframes) {\n                    keyframeEffect.setKeyframes(keyframeValues);\n                }\n                else {\n                    const newEffect = new KeyframeEffect(keyframeEffect.target, keyframeValues, keyframeEffect.getTiming());\n                    animation.effect = newEffect;\n                }\n            });\n        }\n    };\n    /**\n     * Run all \"before\" animation hooks.\n     */\n    const beforeAnimation = () => {\n        // Runs all before read callbacks\n        _beforeAddReadFunctions.forEach((callback) => callback());\n        // Runs all before write callbacks\n        _beforeAddWriteFunctions.forEach((callback) => callback());\n        // Updates styles and classes before animation runs\n        const addClasses = beforeAddClasses;\n        const removeClasses = beforeRemoveClasses;\n        const styles = beforeStylesValue;\n        elements.forEach((el) => {\n            const elementClassList = el.classList;\n            addClasses.forEach((c) => elementClassList.add(c));\n            removeClasses.forEach((c) => elementClassList.remove(c));\n            for (const property in styles) {\n                // eslint-disable-next-line no-prototype-builtins\n                if (styles.hasOwnProperty(property)) {\n                    setStyleProperty(el, property, styles[property]);\n                }\n            }\n        });\n    };\n    /**\n     * Run all \"after\" animation hooks.\n     */\n    const afterAnimation = () => {\n        // Runs all after read callbacks\n        _afterAddReadFunctions.forEach((callback) => callback());\n        // Runs all after write callbacks\n        _afterAddWriteFunctions.forEach((callback) => callback());\n        // Updates styles and classes before animation ends\n        const currentStep = willComplete ? 1 : 0;\n        const addClasses = afterAddClasses;\n        const removeClasses = afterRemoveClasses;\n        const styles = afterStylesValue;\n        elements.forEach((el) => {\n            const elementClassList = el.classList;\n            addClasses.forEach((c) => elementClassList.add(c));\n            removeClasses.forEach((c) => elementClassList.remove(c));\n            for (const property in styles) {\n                // eslint-disable-next-line no-prototype-builtins\n                if (styles.hasOwnProperty(property)) {\n                    setStyleProperty(el, property, styles[property]);\n                }\n            }\n        });\n        /**\n         * Clean up any value coercion before\n         * the user callbacks fire otherwise\n         * they may get stale values. For example,\n         * if someone calls progressStart(0) the\n         * animation may still be reversed.\n         */\n        forceDurationValue = undefined;\n        forceDirectionValue = undefined;\n        forceDelayValue = undefined;\n        onFinishCallbacks.forEach((onFinishCallback) => {\n            return onFinishCallback.c(currentStep, ani);\n        });\n        onFinishOneTimeCallbacks.forEach((onFinishCallback) => {\n            return onFinishCallback.c(currentStep, ani);\n        });\n        onFinishOneTimeCallbacks.length = 0;\n        shouldCalculateNumAnimations = true;\n        if (willComplete) {\n            finished = true;\n        }\n        willComplete = true;\n    };\n    const animationFinish = () => {\n        if (numAnimationsRunning === 0) {\n            return;\n        }\n        numAnimationsRunning--;\n        if (numAnimationsRunning === 0) {\n            afterAnimation();\n            if (parentAnimation) {\n                parentAnimation.animationFinish();\n            }\n        }\n    };\n    const initializeWebAnimation = () => {\n        elements.forEach((element) => {\n            const animation = element.animate(_keyframes, {\n                id,\n                delay: getDelay(),\n                duration: getDuration(),\n                easing: getEasing(),\n                iterations: getIterations(),\n                fill: getFill(),\n                direction: getDirection(),\n            });\n            animation.pause();\n            webAnimations.push(animation);\n        });\n        if (webAnimations.length > 0) {\n            webAnimations[0].onfinish = () => {\n                animationFinish();\n            };\n        }\n    };\n    const initializeAnimation = () => {\n        beforeAnimation();\n        if (_keyframes.length > 0) {\n            if (supportsWebAnimations) {\n                initializeWebAnimation();\n            }\n        }\n        initialized = true;\n    };\n    const setAnimationStep = (step) => {\n        step = Math.min(Math.max(step, 0), 0.9999);\n        if (supportsWebAnimations) {\n            webAnimations.forEach((animation) => {\n                // When creating the animation the delay is guaranteed to be set to a number.\n                animation.currentTime = animation.effect.getComputedTiming().delay + getDuration() * step;\n                animation.pause();\n            });\n        }\n    };\n    const updateWebAnimation = (step) => {\n        webAnimations.forEach((animation) => {\n            animation.effect.updateTiming({\n                delay: getDelay(),\n                duration: getDuration(),\n                easing: getEasing(),\n                iterations: getIterations(),\n                fill: getFill(),\n                direction: getDirection(),\n            });\n        });\n        if (step !== undefined) {\n            setAnimationStep(step);\n        }\n    };\n    const update = (deep = false, toggleAnimationName = true, step) => {\n        if (deep) {\n            childAnimations.forEach((animation) => {\n                animation.update(deep, toggleAnimationName, step);\n            });\n        }\n        if (supportsWebAnimations) {\n            updateWebAnimation(step);\n        }\n        return ani;\n    };\n    const progressStart = (forceLinearEasing = false, step) => {\n        childAnimations.forEach((animation) => {\n            animation.progressStart(forceLinearEasing, step);\n        });\n        pauseAnimation();\n        shouldForceLinearEasing = forceLinearEasing;\n        if (!initialized) {\n            initializeAnimation();\n        }\n        update(false, true, step);\n        return ani;\n    };\n    const progressStep = (step) => {\n        childAnimations.forEach((animation) => {\n            animation.progressStep(step);\n        });\n        setAnimationStep(step);\n        return ani;\n    };\n    const progressEnd = (playTo, step, dur) => {\n        shouldForceLinearEasing = false;\n        childAnimations.forEach((animation) => {\n            animation.progressEnd(playTo, step, dur);\n        });\n        if (dur !== undefined) {\n            forceDurationValue = dur;\n        }\n        finished = false;\n        willComplete = true;\n        if (playTo === 0) {\n            forceDirectionValue = getDirection() === 'reverse' ? 'normal' : 'reverse';\n            if (forceDirectionValue === 'reverse') {\n                willComplete = false;\n            }\n            if (supportsWebAnimations) {\n                update();\n                setAnimationStep(1 - step);\n            }\n            else {\n                forceDelayValue = (1 - step) * getDuration() * -1;\n                update(false, false);\n            }\n        }\n        else if (playTo === 1) {\n            if (supportsWebAnimations) {\n                update();\n                setAnimationStep(step);\n            }\n            else {\n                forceDelayValue = step * getDuration() * -1;\n                update(false, false);\n            }\n        }\n        if (playTo !== undefined && !parentAnimation) {\n            play();\n        }\n        return ani;\n    };\n    const pauseAnimation = () => {\n        if (initialized) {\n            if (supportsWebAnimations) {\n                webAnimations.forEach((animation) => {\n                    animation.pause();\n                });\n            }\n            else {\n                elements.forEach((element) => {\n                    setStyleProperty(element, 'animation-play-state', 'paused');\n                });\n            }\n            paused = true;\n        }\n    };\n    const pause = () => {\n        childAnimations.forEach((animation) => {\n            animation.pause();\n        });\n        pauseAnimation();\n        return ani;\n    };\n    const playCSSAnimations = () => {\n        animationFinish();\n    };\n    const playWebAnimations = () => {\n        webAnimations.forEach((animation) => {\n            animation.play();\n        });\n        if (_keyframes.length === 0 || elements.length === 0) {\n            animationFinish();\n        }\n    };\n    const resetAnimation = () => {\n        if (supportsWebAnimations) {\n            setAnimationStep(0);\n            updateWebAnimation();\n        }\n    };\n    const play = (opts) => {\n        return new Promise((resolve) => {\n            if (opts === null || opts === void 0 ? void 0 : opts.sync) {\n                shouldForceSyncPlayback = true;\n                onFinish(() => (shouldForceSyncPlayback = false), { oneTimeCallback: true });\n            }\n            if (!initialized) {\n                initializeAnimation();\n            }\n            if (finished) {\n                resetAnimation();\n                finished = false;\n            }\n            if (shouldCalculateNumAnimations) {\n                numAnimationsRunning = childAnimations.length + 1;\n                shouldCalculateNumAnimations = false;\n            }\n            /**\n             * When one of these callbacks fires we\n             * need to clear the other's callback otherwise\n             * you can potentially get these callbacks\n             * firing multiple times if the play method\n             * is subsequently called.\n             * Example:\n             * animation.play() (onStop and onFinish callbacks are registered)\n             * animation.stop() (onStop callback is fired, onFinish is not)\n             * animation.play() (onStop and onFinish callbacks are registered)\n             * Total onStop callbacks: 1\n             * Total onFinish callbacks: 2\n             */\n            const onStopCallback = () => {\n                clearCallback(onFinishCallback, onFinishOneTimeCallbacks);\n                resolve();\n            };\n            const onFinishCallback = () => {\n                clearCallback(onStopCallback, onStopOneTimeCallbacks);\n                resolve();\n            };\n            /**\n             * The play method resolves when an animation\n             * run either finishes or is cancelled.\n             */\n            onFinish(onFinishCallback, { oneTimeCallback: true });\n            onStop(onStopCallback, { oneTimeCallback: true });\n            childAnimations.forEach((animation) => {\n                animation.play();\n            });\n            if (supportsWebAnimations) {\n                playWebAnimations();\n            }\n            else {\n                playCSSAnimations();\n            }\n            paused = false;\n        });\n    };\n    /**\n     * Stops an animation and resets it state to the\n     * beginning. This does not fire any onFinish\n     * callbacks because the animation did not finish.\n     * However, since the animation was not destroyed\n     * (i.e. the animation could run again) we do not\n     * clear the onFinish callbacks.\n     */\n    const stop = () => {\n        childAnimations.forEach((animation) => {\n            animation.stop();\n        });\n        if (initialized) {\n            cleanUpElements();\n            initialized = false;\n        }\n        resetFlags();\n        onStopOneTimeCallbacks.forEach((onStopCallback) => onStopCallback.c(0, ani));\n        onStopOneTimeCallbacks.length = 0;\n    };\n    const from = (property, value) => {\n        const firstFrame = _keyframes[0];\n        if (firstFrame !== undefined && (firstFrame.offset === undefined || firstFrame.offset === 0)) {\n            firstFrame[property] = value;\n        }\n        else {\n            _keyframes = [{ offset: 0, [property]: value }, ..._keyframes];\n        }\n        return ani;\n    };\n    const to = (property, value) => {\n        const lastFrame = _keyframes[_keyframes.length - 1];\n        if (lastFrame !== undefined && (lastFrame.offset === undefined || lastFrame.offset === 1)) {\n            lastFrame[property] = value;\n        }\n        else {\n            _keyframes = [..._keyframes, { offset: 1, [property]: value }];\n        }\n        return ani;\n    };\n    const fromTo = (property, fromValue, toValue) => {\n        return from(property, fromValue).to(property, toValue);\n    };\n    return (ani = {\n        parentAnimation,\n        elements,\n        childAnimations,\n        id,\n        animationFinish,\n        from,\n        to,\n        fromTo,\n        parent,\n        play,\n        pause,\n        stop,\n        destroy,\n        keyframes,\n        addAnimation,\n        addElement,\n        update,\n        fill,\n        direction,\n        iterations,\n        duration,\n        easing,\n        delay,\n        getWebAnimations,\n        getKeyframes,\n        getFill,\n        getDirection,\n        getDelay,\n        getIterations,\n        getEasing,\n        getDuration,\n        afterAddRead,\n        afterAddWrite,\n        afterClearStyles,\n        afterStyles,\n        afterRemoveClass,\n        afterAddClass,\n        beforeAddRead,\n        beforeAddWrite,\n        beforeClearStyles,\n        beforeStyles,\n        beforeRemoveClass,\n        beforeAddClass,\n        onFinish,\n        isRunning,\n        progressStart,\n        progressStep,\n        progressEnd,\n    });\n};\n\nexport { createAnimation as c };\n", "/*!\n * (C) Ionic http://ionicframework.com - MIT License\n */\nimport { c as config, p as printIonWarning } from './index4.js';\nimport { writeTask, Build } from '@stencil/core/internal/client';\nimport { r as raf } from './helpers.js';\n\nconst LIFECYCLE_WILL_ENTER = 'ionViewWillEnter';\nconst LIFECYCLE_DID_ENTER = 'ionViewDidEnter';\nconst LIFECYCLE_WILL_LEAVE = 'ionViewWillLeave';\nconst LIFECYCLE_DID_LEAVE = 'ionViewDidLeave';\nconst LIFECYCLE_WILL_UNLOAD = 'ionViewWillUnload';\n\n/**\n * Moves focus to a specified element. Note that we do not remove the tabindex\n * because that can result in an unintentional blur. Non-focusables can't be\n * focused, so the body will get focused again.\n */\nconst moveFocus = (el) => {\n    el.tabIndex = -1;\n    el.focus();\n};\n/**\n * Elements that are hidden using `display: none` should not be focused even if\n * they are present in the DOM.\n */\nconst isVisible = (el) => {\n    return el.offsetParent !== null;\n};\n/**\n * The focus controller allows us to manage focus within a view so assistive\n * technologies can inform users of changes to the navigation state. Traditional\n * native apps have a way of informing assistive technology about a navigation\n * state change. Mobile browsers have this too, but only when doing a full page\n * load. In a single page app we do not do that, so we need to build this\n * integration ourselves.\n */\nconst createFocusController = () => {\n    const saveViewFocus = (referenceEl) => {\n        const focusManagerEnabled = config.get('focusManagerPriority', false);\n        /**\n         * When going back to a previously visited page focus should typically be moved\n         * back to the element that was last focused when the user was on this view.\n         */\n        if (focusManagerEnabled) {\n            const activeEl = document.activeElement;\n            if (activeEl !== null && (referenceEl === null || referenceEl === void 0 ? void 0 : referenceEl.contains(activeEl))) {\n                activeEl.setAttribute(LAST_FOCUS, 'true');\n            }\n        }\n    };\n    const setViewFocus = (referenceEl) => {\n        const focusManagerPriorities = config.get('focusManagerPriority', false);\n        /**\n         * If the focused element is a descendant of the referenceEl then it's possible\n         * that the app developer manually moved focus, so we do not want to override that.\n         * This can happen with inputs the are focused when a view transitions in.\n         */\n        if (Array.isArray(focusManagerPriorities) && !referenceEl.contains(document.activeElement)) {\n            /**\n             * When going back to a previously visited view focus should always be moved back\n             * to the element that the user was last focused on when they were on this view.\n             */\n            const lastFocus = referenceEl.querySelector(`[${LAST_FOCUS}]`);\n            if (lastFocus && isVisible(lastFocus)) {\n                moveFocus(lastFocus);\n                return;\n            }\n            for (const priority of focusManagerPriorities) {\n                /**\n                 * For each recognized case (excluding the default case) make sure to return\n                 * so that the fallback focus behavior does not run.\n                 *\n                 * We intentionally query for specific roles/semantic elements so that the\n                 * transition manager can work with both Ionic and non-Ionic UI components.\n                 *\n                 * If new selectors are added, be sure to remove the outline ring by adding\n                 * new selectors to rule in core.scss.\n                 */\n                switch (priority) {\n                    case 'content':\n                        const content = referenceEl.querySelector('main, [role=\"main\"]');\n                        if (content && isVisible(content)) {\n                            moveFocus(content);\n                            return;\n                        }\n                        break;\n                    case 'heading':\n                        const headingOne = referenceEl.querySelector('h1, [role=\"heading\"][aria-level=\"1\"]');\n                        if (headingOne && isVisible(headingOne)) {\n                            moveFocus(headingOne);\n                            return;\n                        }\n                        break;\n                    case 'banner':\n                        const header = referenceEl.querySelector('header, [role=\"banner\"]');\n                        if (header && isVisible(header)) {\n                            moveFocus(header);\n                            return;\n                        }\n                        break;\n                    default:\n                        printIonWarning(`Unrecognized focus manager priority value ${priority}`);\n                        break;\n                }\n            }\n            /**\n             * If there is nothing to focus then focus the page so focus at least moves to\n             * the correct view. The browser will then determine where within the page to\n             * move focus to.\n             */\n            moveFocus(referenceEl);\n        }\n    };\n    return {\n        saveViewFocus,\n        setViewFocus,\n    };\n};\nconst LAST_FOCUS = 'ion-last-focus';\n\nconst iosTransitionAnimation = () => import('./ios.transition.js');\nconst mdTransitionAnimation = () => import('./md.transition.js');\nconst focusController = createFocusController();\n// TODO(FW-2832): types\nconst transition = (opts) => {\n    return new Promise((resolve, reject) => {\n        writeTask(() => {\n            beforeTransition(opts);\n            runTransition(opts).then((result) => {\n                if (result.animation) {\n                    result.animation.destroy();\n                }\n                afterTransition(opts);\n                resolve(result);\n            }, (error) => {\n                afterTransition(opts);\n                reject(error);\n            });\n        });\n    });\n};\nconst beforeTransition = (opts) => {\n    const enteringEl = opts.enteringEl;\n    const leavingEl = opts.leavingEl;\n    focusController.saveViewFocus(leavingEl);\n    setZIndex(enteringEl, leavingEl, opts.direction);\n    if (opts.showGoBack) {\n        enteringEl.classList.add('can-go-back');\n    }\n    else {\n        enteringEl.classList.remove('can-go-back');\n    }\n    setPageHidden(enteringEl, false);\n    /**\n     * When transitioning, the page should not\n     * respond to click events. This resolves small\n     * issues like users double tapping the ion-back-button.\n     * These pointer events are removed in `afterTransition`.\n     */\n    enteringEl.style.setProperty('pointer-events', 'none');\n    if (leavingEl) {\n        setPageHidden(leavingEl, false);\n        leavingEl.style.setProperty('pointer-events', 'none');\n    }\n};\nconst runTransition = async (opts) => {\n    const animationBuilder = await getAnimationBuilder(opts);\n    const ani = animationBuilder && Build.isBrowser ? animation(animationBuilder, opts) : noAnimation(opts); // fast path for no animation\n    return ani;\n};\nconst afterTransition = (opts) => {\n    const enteringEl = opts.enteringEl;\n    const leavingEl = opts.leavingEl;\n    enteringEl.classList.remove('ion-page-invisible');\n    enteringEl.style.removeProperty('pointer-events');\n    if (leavingEl !== undefined) {\n        leavingEl.classList.remove('ion-page-invisible');\n        leavingEl.style.removeProperty('pointer-events');\n    }\n    focusController.setViewFocus(enteringEl);\n};\nconst getAnimationBuilder = async (opts) => {\n    if (!opts.leavingEl || !opts.animated || opts.duration === 0) {\n        return undefined;\n    }\n    if (opts.animationBuilder) {\n        return opts.animationBuilder;\n    }\n    const getAnimation = opts.mode === 'ios'\n        ? (await iosTransitionAnimation()).iosTransitionAnimation\n        : (await mdTransitionAnimation()).mdTransitionAnimation;\n    return getAnimation;\n};\nconst animation = async (animationBuilder, opts) => {\n    await waitForReady(opts, true);\n    const trans = animationBuilder(opts.baseEl, opts);\n    fireWillEvents(opts.enteringEl, opts.leavingEl);\n    const didComplete = await playTransition(trans, opts);\n    if (opts.progressCallback) {\n        opts.progressCallback(undefined);\n    }\n    if (didComplete) {\n        fireDidEvents(opts.enteringEl, opts.leavingEl);\n    }\n    return {\n        hasCompleted: didComplete,\n        animation: trans,\n    };\n};\nconst noAnimation = async (opts) => {\n    const enteringEl = opts.enteringEl;\n    const leavingEl = opts.leavingEl;\n    const focusManagerEnabled = config.get('focusManagerPriority', false);\n    /**\n     * If the focus manager is enabled then we need to wait for Ionic components to be\n     * rendered otherwise the component to focus may not be focused because it is hidden.\n     */\n    await waitForReady(opts, focusManagerEnabled);\n    fireWillEvents(enteringEl, leavingEl);\n    fireDidEvents(enteringEl, leavingEl);\n    return {\n        hasCompleted: true,\n    };\n};\nconst waitForReady = async (opts, defaultDeep) => {\n    const deep = opts.deepWait !== undefined ? opts.deepWait : defaultDeep;\n    if (deep) {\n        await Promise.all([deepReady(opts.enteringEl), deepReady(opts.leavingEl)]);\n    }\n    await notifyViewReady(opts.viewIsReady, opts.enteringEl);\n};\nconst notifyViewReady = async (viewIsReady, enteringEl) => {\n    if (viewIsReady) {\n        await viewIsReady(enteringEl);\n    }\n};\nconst playTransition = (trans, opts) => {\n    const progressCallback = opts.progressCallback;\n    const promise = new Promise((resolve) => {\n        trans.onFinish((currentStep) => resolve(currentStep === 1));\n    });\n    // cool, let's do this, start the transition\n    if (progressCallback) {\n        // this is a swipe to go back, just get the transition progress ready\n        // kick off the swipe animation start\n        trans.progressStart(true);\n        progressCallback(trans);\n    }\n    else {\n        // only the top level transition should actually start \"play\"\n        // kick it off and let it play through\n        // ******** DOM WRITE ****************\n        trans.play();\n    }\n    // create a callback for when the animation is done\n    return promise;\n};\nconst fireWillEvents = (enteringEl, leavingEl) => {\n    lifecycle(leavingEl, LIFECYCLE_WILL_LEAVE);\n    lifecycle(enteringEl, LIFECYCLE_WILL_ENTER);\n};\nconst fireDidEvents = (enteringEl, leavingEl) => {\n    lifecycle(enteringEl, LIFECYCLE_DID_ENTER);\n    lifecycle(leavingEl, LIFECYCLE_DID_LEAVE);\n};\nconst lifecycle = (el, eventName) => {\n    if (el) {\n        const ev = new CustomEvent(eventName, {\n            bubbles: false,\n            cancelable: false,\n        });\n        el.dispatchEvent(ev);\n    }\n};\n/**\n * Wait two request animation frame loops.\n * This allows the framework implementations enough time to mount\n * the user-defined contents. This is often needed when using inline\n * modals and popovers that accept user components. For popover,\n * the contents must be mounted for the popover to be sized correctly.\n * For modals, the contents must be mounted for iOS to run the\n * transition correctly.\n *\n * On Angular and React, a single raf is enough time, but for Vue\n * we need to wait two rafs. As a result we are using two rafs for\n * all frameworks to ensure contents are mounted.\n */\nconst waitForMount = () => {\n    return new Promise((resolve) => raf(() => raf(() => resolve())));\n};\nconst deepReady = async (el) => {\n    const element = el;\n    if (element) {\n        if (element.componentOnReady != null) {\n            // eslint-disable-next-line custom-rules/no-component-on-ready-method\n            const stencilEl = await element.componentOnReady();\n            if (stencilEl != null) {\n                return;\n            }\n            /**\n             * Custom elements in Stencil will have __registerHost.\n             */\n        }\n        else if (element.__registerHost != null) {\n            /**\n             * Non-lazy loaded custom elements need to wait\n             * one frame for component to be loaded.\n             */\n            const waitForCustomElement = new Promise((resolve) => raf(resolve));\n            await waitForCustomElement;\n            return;\n        }\n        await Promise.all(Array.from(element.children).map(deepReady));\n    }\n};\nconst setPageHidden = (el, hidden) => {\n    if (hidden) {\n        el.setAttribute('aria-hidden', 'true');\n        el.classList.add('ion-page-hidden');\n    }\n    else {\n        el.hidden = false;\n        el.removeAttribute('aria-hidden');\n        el.classList.remove('ion-page-hidden');\n    }\n};\nconst setZIndex = (enteringEl, leavingEl, direction) => {\n    if (enteringEl !== undefined) {\n        enteringEl.style.zIndex = direction === 'back' ? '99' : '101';\n    }\n    if (leavingEl !== undefined) {\n        leavingEl.style.zIndex = '100';\n    }\n};\nconst getIonPageElement = (element) => {\n    if (element.classList.contains('ion-page')) {\n        return element;\n    }\n    const ionPage = element.querySelector(':scope > .ion-page, :scope > ion-nav, :scope > ion-tabs');\n    if (ionPage) {\n        return ionPage;\n    }\n    // idk, return the original element so at least something animates and we don't have a null pointer\n    return element;\n};\n\nexport { LIFECYCLE_WILL_ENTER as L, LIFECYCLE_DID_ENTER as a, LIFECYCLE_WILL_LEAVE as b, LIFECYCLE_DID_LEAVE as c, LIFECYCLE_WILL_UNLOAD as d, deepReady as e, getIonPageElement as g, lifecycle as l, setPageHidden as s, transition as t, waitForMount as w };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;AAMA,IAAI;AACJ,IAAM,qBAAqB,CAAC,OAAO;AAC/B,MAAI,oBAAoB,QAAW;AAC/B,UAAM,qBAAqB,GAAG,MAAM,kBAAkB;AACtD,UAAM,uBAAuB,GAAG,MAAM,wBAAwB;AAC9D,sBAAkB,CAAC,sBAAsB,uBAAuB,aAAa;AAAA,EACjF;AACA,SAAO;AACX;AACA,IAAM,mBAAmB,CAAC,SAAS,cAAc,UAAU;AACvD,QAAM,SAAS,aAAa,WAAW,WAAW,IAAI,mBAAmB,OAAO,IAAI;AACpF,UAAQ,MAAM,YAAY,SAAS,cAAc,KAAK;AAC1D;AACA,IAAM,kBAAkB,CAAC,UAAU,CAAC,GAAG,cAAc;AACjD,MAAI,cAAc,QAAW;AACzB,UAAM,oBAAoB,MAAM,QAAQ,SAAS,IAAI,YAAY,CAAC,SAAS;AAC3E,WAAO,CAAC,GAAG,SAAS,GAAG,iBAAiB;AAAA,EAC5C;AACA,SAAO;AACX;AAEA,IAAM,kBAAkB,CAAC,gBAAgB;AACrC,MAAI;AACJ,MAAI;AACJ,MAAI;AACJ,MAAI;AACJ,MAAI;AACJ,MAAI;AACJ,MAAI,aAAa,CAAC;AAClB,MAAI,mBAAmB,CAAC;AACxB,MAAI,sBAAsB,CAAC;AAC3B,MAAI,cAAc;AAClB,MAAI;AACJ,MAAI,oBAAoB,CAAC;AACzB,MAAI,kBAAkB,CAAC;AACvB,MAAI,qBAAqB,CAAC;AAC1B,MAAI,mBAAmB,CAAC;AACxB,MAAI,uBAAuB;AAC3B,MAAI,0BAA0B;AAC9B,MAAI,0BAA0B;AAC9B,MAAI;AACJ,MAAI;AACJ,MAAI;AACJ,MAAI,eAAe;AACnB,MAAI,WAAW;AACf,MAAI,+BAA+B;AACnC,MAAI;AACJ,MAAI,SAAS;AACb,QAAM,KAAK;AACX,QAAM,oBAAoB,CAAC;AAC3B,QAAM,2BAA2B,CAAC;AAClC,QAAM,yBAAyB,CAAC;AAChC,QAAM,WAAW,CAAC;AAClB,QAAM,kBAAkB,CAAC;AACzB,QAAM,cAAc,CAAC;AACrB,QAAM,0BAA0B,CAAC;AACjC,QAAM,2BAA2B,CAAC;AAClC,QAAM,yBAAyB,CAAC;AAChC,QAAM,0BAA0B,CAAC;AACjC,QAAM,gBAAgB,CAAC;AACvB,QAAM,0BAA0B,OAAO,oBAAoB,cACtD,QAAQ,UAAa,OAAO,IAAI,oBAAoB;AAQzD,QAAM,wBAAwB,OAAO,YAAY,cAC7C,OAAO,QAAQ,UAAU,YAAY,cACrC;AACJ,QAAM,mBAAmB,MAAM;AAC3B,WAAO;AAAA,EACX;AACA,QAAM,UAAU,CAAC,qBAAqB;AAClC,oBAAgB,QAAQ,CAAC,mBAAmB;AACxC,qBAAe,QAAQ,gBAAgB;AAAA,IAC3C,CAAC;AACD,YAAQ,gBAAgB;AACxB,aAAS,SAAS;AAClB,oBAAgB,SAAS;AACzB,eAAW,SAAS;AACpB,kBAAc;AACd,kBAAc;AACd,mCAA+B;AAC/B,WAAO;AAAA,EACX;AAOA,QAAM,UAAU,CAAC,qBAAqB;AAClC,oBAAgB;AAChB,QAAI,kBAAkB;AAClB,yBAAmB;AAAA,IACvB;AAAA,EACJ;AACA,QAAM,aAAa,MAAM;AACrB,8BAA0B;AAC1B,8BAA0B;AAC1B,mCAA+B;AAC/B,0BAAsB;AACtB,yBAAqB;AACrB,sBAAkB;AAClB,2BAAuB;AACvB,eAAW;AACX,mBAAe;AACf,aAAS;AAAA,EACb;AACA,QAAM,YAAY,MAAM;AACpB,WAAO,yBAAyB,KAAK,CAAC;AAAA,EAC1C;AAOA,QAAM,gBAAgB,CAAC,kBAAkB,oBAAoB;AACzD,UAAM,QAAQ,gBAAgB,UAAU,CAAC,mBAAmB,eAAe,MAAM,gBAAgB;AACjG,QAAI,QAAQ,IAAI;AACZ,sBAAgB,OAAO,OAAO,CAAC;AAAA,IACnC;AAAA,EACJ;AAOA,QAAM,SAAS,CAAC,UAAU,SAAS;AAC/B,2BAAuB,KAAK,EAAE,GAAG,UAAU,GAAG,KAAK,CAAC;AACpD,WAAO;AAAA,EACX;AACA,QAAM,WAAW,CAAC,UAAU,SAAS;AACjC,UAAM,aAAa,SAAS,QAAQ,SAAS,SAAS,SAAS,KAAK,mBAAmB,2BAA2B;AAClH,cAAU,KAAK,EAAE,GAAG,UAAU,GAAG,KAAK,CAAC;AACvC,WAAO;AAAA,EACX;AACA,QAAM,gBAAgB,MAAM;AACxB,sBAAkB,SAAS;AAC3B,6BAAyB,SAAS;AAClC,WAAO;AAAA,EACX;AAMA,QAAM,kBAAkB,MAAM;AAC1B,QAAI,uBAAuB;AACvB,oBAAc,QAAQ,CAACA,eAAc;AACjC,QAAAA,WAAU,OAAO;AAAA,MACrB,CAAC;AACD,oBAAc,SAAS;AAAA,IAC3B;AAAA,EACJ;AAKA,QAAM,qBAAqB,MAAM;AAC7B,gBAAY,QAAQ,CAAC,eAAe;AAMhC,UAAI,eAAe,QAAQ,eAAe,SAAS,SAAS,WAAW,YAAY;AAC/E,mBAAW,WAAW,YAAY,UAAU;AAAA,MAChD;AAAA,IACJ,CAAC;AACD,gBAAY,SAAS;AAAA,EACzB;AACA,QAAM,gBAAgB,CAAC,WAAW;AAC9B,4BAAwB,KAAK,MAAM;AACnC,WAAO;AAAA,EACX;AACA,QAAM,iBAAiB,CAAC,YAAY;AAChC,6BAAyB,KAAK,OAAO;AACrC,WAAO;AAAA,EACX;AACA,QAAM,eAAe,CAAC,WAAW;AAC7B,2BAAuB,KAAK,MAAM;AAClC,WAAO;AAAA,EACX;AACA,QAAM,gBAAgB,CAAC,YAAY;AAC/B,4BAAwB,KAAK,OAAO;AACpC,WAAO;AAAA,EACX;AACA,QAAM,iBAAiB,CAAC,cAAc;AAClC,uBAAmB,gBAAgB,kBAAkB,SAAS;AAC9D,WAAO;AAAA,EACX;AACA,QAAM,oBAAoB,CAAC,cAAc;AACrC,0BAAsB,gBAAgB,qBAAqB,SAAS;AACpE,WAAO;AAAA,EACX;AAKA,QAAM,eAAe,CAAC,SAAS,CAAC,MAAM;AAClC,wBAAoB;AACpB,WAAO;AAAA,EACX;AAKA,QAAM,oBAAoB,CAAC,gBAAgB,CAAC,MAAM;AAC9C,eAAW,YAAY,eAAe;AAClC,wBAAkB,QAAQ,IAAI;AAAA,IAClC;AACA,WAAO;AAAA,EACX;AACA,QAAM,gBAAgB,CAAC,cAAc;AACjC,sBAAkB,gBAAgB,iBAAiB,SAAS;AAC5D,WAAO;AAAA,EACX;AACA,QAAM,mBAAmB,CAAC,cAAc;AACpC,yBAAqB,gBAAgB,oBAAoB,SAAS;AAClE,WAAO;AAAA,EACX;AACA,QAAM,cAAc,CAAC,SAAS,CAAC,MAAM;AACjC,uBAAmB;AACnB,WAAO;AAAA,EACX;AACA,QAAM,mBAAmB,CAAC,gBAAgB,CAAC,MAAM;AAC7C,eAAW,YAAY,eAAe;AAClC,uBAAiB,QAAQ,IAAI;AAAA,IACjC;AACA,WAAO;AAAA,EACX;AACA,QAAM,UAAU,MAAM;AAClB,QAAI,UAAU,QAAW;AACrB,aAAO;AAAA,IACX;AACA,QAAI,iBAAiB;AACjB,aAAO,gBAAgB,QAAQ;AAAA,IACnC;AACA,WAAO;AAAA,EACX;AACA,QAAM,eAAe,MAAM;AACvB,QAAI,wBAAwB,QAAW;AACnC,aAAO;AAAA,IACX;AACA,QAAI,eAAe,QAAW;AAC1B,aAAO;AAAA,IACX;AACA,QAAI,iBAAiB;AACjB,aAAO,gBAAgB,aAAa;AAAA,IACxC;AACA,WAAO;AAAA,EACX;AACA,QAAM,YAAY,MAAM;AACpB,QAAI,yBAAyB;AACzB,aAAO;AAAA,IACX;AACA,QAAI,YAAY,QAAW;AACvB,aAAO;AAAA,IACX;AACA,QAAI,iBAAiB;AACjB,aAAO,gBAAgB,UAAU;AAAA,IACrC;AACA,WAAO;AAAA,EACX;AACA,QAAM,cAAc,MAAM;AACtB,QAAI,yBAAyB;AACzB,aAAO;AAAA,IACX;AACA,QAAI,uBAAuB,QAAW;AAClC,aAAO;AAAA,IACX;AACA,QAAI,cAAc,QAAW;AACzB,aAAO;AAAA,IACX;AACA,QAAI,iBAAiB;AACjB,aAAO,gBAAgB,YAAY;AAAA,IACvC;AACA,WAAO;AAAA,EACX;AACA,QAAM,gBAAgB,MAAM;AACxB,QAAI,gBAAgB,QAAW;AAC3B,aAAO;AAAA,IACX;AACA,QAAI,iBAAiB;AACjB,aAAO,gBAAgB,cAAc;AAAA,IACzC;AACA,WAAO;AAAA,EACX;AACA,QAAM,WAAW,MAAM;AACnB,QAAI,oBAAoB,QAAW;AAC/B,aAAO;AAAA,IACX;AACA,QAAI,WAAW,QAAW;AACtB,aAAO;AAAA,IACX;AACA,QAAI,iBAAiB;AACjB,aAAO,gBAAgB,SAAS;AAAA,IACpC;AACA,WAAO;AAAA,EACX;AACA,QAAM,eAAe,MAAM;AACvB,WAAO;AAAA,EACX;AACA,QAAM,YAAY,CAAC,uBAAuB;AACtC,iBAAa;AACb,WAAO,IAAI;AACX,WAAO;AAAA,EACX;AACA,QAAM,OAAO,CAAC,kBAAkB;AAC5B,YAAQ;AACR,WAAO,IAAI;AACX,WAAO;AAAA,EACX;AACA,QAAM,QAAQ,CAAC,mBAAmB;AAC9B,aAAS;AACT,WAAO,IAAI;AACX,WAAO;AAAA,EACX;AACA,QAAM,SAAS,CAAC,oBAAoB;AAChC,cAAU;AACV,WAAO,IAAI;AACX,WAAO;AAAA,EACX;AACA,QAAM,WAAW,CAAC,sBAAsB;AAMpC,QAAI,CAAC,yBAAyB,sBAAsB,GAAG;AACnD,0BAAoB;AAAA,IACxB;AACA,gBAAY;AACZ,WAAO,IAAI;AACX,WAAO;AAAA,EACX;AACA,QAAM,aAAa,CAAC,wBAAwB;AACxC,kBAAc;AACd,WAAO,IAAI;AACX,WAAO;AAAA,EACX;AACA,QAAM,SAAS,CAACA,eAAc;AAC1B,sBAAkBA;AAClB,WAAO;AAAA,EACX;AACA,QAAM,aAAa,CAAC,OAAO;AACvB,QAAI,MAAM,MAAM;AACZ,UAAI,GAAG,aAAa,GAAG;AACnB,iBAAS,KAAK,EAAE;AAAA,MACpB,WACS,GAAG,UAAU,GAAG;AACrB,iBAAS,IAAI,GAAG,IAAI,GAAG,QAAQ,KAAK;AAChC,mBAAS,KAAK,GAAG,CAAC,CAAC;AAAA,QACvB;AAAA,MACJ,OACK;AACD,sBAAc,6CAA6C;AAAA,MAC/D;AAAA,IACJ;AACA,WAAO;AAAA,EACX;AACA,QAAM,eAAe,CAAC,mBAAmB;AACrC,QAAI,kBAAkB,MAAM;AACxB,UAAI,MAAM,QAAQ,cAAc,GAAG;AAC/B,mBAAWA,cAAa,gBAAgB;AACpC,UAAAA,WAAU,OAAO,GAAG;AACpB,0BAAgB,KAAKA,UAAS;AAAA,QAClC;AAAA,MACJ,OACK;AACD,uBAAe,OAAO,GAAG;AACzB,wBAAgB,KAAK,cAAc;AAAA,MACvC;AAAA,IACJ;AACA,WAAO;AAAA,EACX;AACA,QAAM,YAAY,CAAC,mBAAmB;AAClC,UAAM,YAAY,eAAe;AACjC,iBAAa;AACb,QAAI,WAAW;AACX,sBAAgB,UAAU;AAAA,IAC9B;AACA,WAAO;AAAA,EACX;AACA,QAAM,kBAAkB,CAAC,mBAAmB;AACxC,QAAI,uBAAuB;AACvB,uBAAiB,EAAE,QAAQ,CAACA,eAAc;AAQtC,cAAM,iBAAiBA,WAAU;AAOjC,YAAI,eAAe,cAAc;AAC7B,yBAAe,aAAa,cAAc;AAAA,QAC9C,OACK;AACD,gBAAM,YAAY,IAAI,eAAe,eAAe,QAAQ,gBAAgB,eAAe,UAAU,CAAC;AACtG,UAAAA,WAAU,SAAS;AAAA,QACvB;AAAA,MACJ,CAAC;AAAA,IACL;AAAA,EACJ;AAIA,QAAM,kBAAkB,MAAM;AAE1B,4BAAwB,QAAQ,CAAC,aAAa,SAAS,CAAC;AAExD,6BAAyB,QAAQ,CAAC,aAAa,SAAS,CAAC;AAEzD,UAAM,aAAa;AACnB,UAAM,gBAAgB;AACtB,UAAM,SAAS;AACf,aAAS,QAAQ,CAAC,OAAO;AACrB,YAAM,mBAAmB,GAAG;AAC5B,iBAAW,QAAQ,CAAC,MAAM,iBAAiB,IAAI,CAAC,CAAC;AACjD,oBAAc,QAAQ,CAAC,MAAM,iBAAiB,OAAO,CAAC,CAAC;AACvD,iBAAW,YAAY,QAAQ;AAE3B,YAAI,OAAO,eAAe,QAAQ,GAAG;AACjC,2BAAiB,IAAI,UAAU,OAAO,QAAQ,CAAC;AAAA,QACnD;AAAA,MACJ;AAAA,IACJ,CAAC;AAAA,EACL;AAIA,QAAM,iBAAiB,MAAM;AAEzB,2BAAuB,QAAQ,CAAC,aAAa,SAAS,CAAC;AAEvD,4BAAwB,QAAQ,CAAC,aAAa,SAAS,CAAC;AAExD,UAAM,cAAc,eAAe,IAAI;AACvC,UAAM,aAAa;AACnB,UAAM,gBAAgB;AACtB,UAAM,SAAS;AACf,aAAS,QAAQ,CAAC,OAAO;AACrB,YAAM,mBAAmB,GAAG;AAC5B,iBAAW,QAAQ,CAAC,MAAM,iBAAiB,IAAI,CAAC,CAAC;AACjD,oBAAc,QAAQ,CAAC,MAAM,iBAAiB,OAAO,CAAC,CAAC;AACvD,iBAAW,YAAY,QAAQ;AAE3B,YAAI,OAAO,eAAe,QAAQ,GAAG;AACjC,2BAAiB,IAAI,UAAU,OAAO,QAAQ,CAAC;AAAA,QACnD;AAAA,MACJ;AAAA,IACJ,CAAC;AAQD,yBAAqB;AACrB,0BAAsB;AACtB,sBAAkB;AAClB,sBAAkB,QAAQ,CAAC,qBAAqB;AAC5C,aAAO,iBAAiB,EAAE,aAAa,GAAG;AAAA,IAC9C,CAAC;AACD,6BAAyB,QAAQ,CAAC,qBAAqB;AACnD,aAAO,iBAAiB,EAAE,aAAa,GAAG;AAAA,IAC9C,CAAC;AACD,6BAAyB,SAAS;AAClC,mCAA+B;AAC/B,QAAI,cAAc;AACd,iBAAW;AAAA,IACf;AACA,mBAAe;AAAA,EACnB;AACA,QAAM,kBAAkB,MAAM;AAC1B,QAAI,yBAAyB,GAAG;AAC5B;AAAA,IACJ;AACA;AACA,QAAI,yBAAyB,GAAG;AAC5B,qBAAe;AACf,UAAI,iBAAiB;AACjB,wBAAgB,gBAAgB;AAAA,MACpC;AAAA,IACJ;AAAA,EACJ;AACA,QAAM,yBAAyB,MAAM;AACjC,aAAS,QAAQ,CAAC,YAAY;AAC1B,YAAMA,aAAY,QAAQ,QAAQ,YAAY;AAAA,QAC1C;AAAA,QACA,OAAO,SAAS;AAAA,QAChB,UAAU,YAAY;AAAA,QACtB,QAAQ,UAAU;AAAA,QAClB,YAAY,cAAc;AAAA,QAC1B,MAAM,QAAQ;AAAA,QACd,WAAW,aAAa;AAAA,MAC5B,CAAC;AACD,MAAAA,WAAU,MAAM;AAChB,oBAAc,KAAKA,UAAS;AAAA,IAChC,CAAC;AACD,QAAI,cAAc,SAAS,GAAG;AAC1B,oBAAc,CAAC,EAAE,WAAW,MAAM;AAC9B,wBAAgB;AAAA,MACpB;AAAA,IACJ;AAAA,EACJ;AACA,QAAM,sBAAsB,MAAM;AAC9B,oBAAgB;AAChB,QAAI,WAAW,SAAS,GAAG;AACvB,UAAI,uBAAuB;AACvB,+BAAuB;AAAA,MAC3B;AAAA,IACJ;AACA,kBAAc;AAAA,EAClB;AACA,QAAM,mBAAmB,CAAC,SAAS;AAC/B,WAAO,KAAK,IAAI,KAAK,IAAI,MAAM,CAAC,GAAG,MAAM;AACzC,QAAI,uBAAuB;AACvB,oBAAc,QAAQ,CAACA,eAAc;AAEjC,QAAAA,WAAU,cAAcA,WAAU,OAAO,kBAAkB,EAAE,QAAQ,YAAY,IAAI;AACrF,QAAAA,WAAU,MAAM;AAAA,MACpB,CAAC;AAAA,IACL;AAAA,EACJ;AACA,QAAM,qBAAqB,CAAC,SAAS;AACjC,kBAAc,QAAQ,CAACA,eAAc;AACjC,MAAAA,WAAU,OAAO,aAAa;AAAA,QAC1B,OAAO,SAAS;AAAA,QAChB,UAAU,YAAY;AAAA,QACtB,QAAQ,UAAU;AAAA,QAClB,YAAY,cAAc;AAAA,QAC1B,MAAM,QAAQ;AAAA,QACd,WAAW,aAAa;AAAA,MAC5B,CAAC;AAAA,IACL,CAAC;AACD,QAAI,SAAS,QAAW;AACpB,uBAAiB,IAAI;AAAA,IACzB;AAAA,EACJ;AACA,QAAM,SAAS,CAAC,OAAO,OAAO,sBAAsB,MAAM,SAAS;AAC/D,QAAI,MAAM;AACN,sBAAgB,QAAQ,CAACA,eAAc;AACnC,QAAAA,WAAU,OAAO,MAAM,qBAAqB,IAAI;AAAA,MACpD,CAAC;AAAA,IACL;AACA,QAAI,uBAAuB;AACvB,yBAAmB,IAAI;AAAA,IAC3B;AACA,WAAO;AAAA,EACX;AACA,QAAM,gBAAgB,CAAC,oBAAoB,OAAO,SAAS;AACvD,oBAAgB,QAAQ,CAACA,eAAc;AACnC,MAAAA,WAAU,cAAc,mBAAmB,IAAI;AAAA,IACnD,CAAC;AACD,mBAAe;AACf,8BAA0B;AAC1B,QAAI,CAAC,aAAa;AACd,0BAAoB;AAAA,IACxB;AACA,WAAO,OAAO,MAAM,IAAI;AACxB,WAAO;AAAA,EACX;AACA,QAAM,eAAe,CAAC,SAAS;AAC3B,oBAAgB,QAAQ,CAACA,eAAc;AACnC,MAAAA,WAAU,aAAa,IAAI;AAAA,IAC/B,CAAC;AACD,qBAAiB,IAAI;AACrB,WAAO;AAAA,EACX;AACA,QAAM,cAAc,CAAC,QAAQ,MAAM,QAAQ;AACvC,8BAA0B;AAC1B,oBAAgB,QAAQ,CAACA,eAAc;AACnC,MAAAA,WAAU,YAAY,QAAQ,MAAM,GAAG;AAAA,IAC3C,CAAC;AACD,QAAI,QAAQ,QAAW;AACnB,2BAAqB;AAAA,IACzB;AACA,eAAW;AACX,mBAAe;AACf,QAAI,WAAW,GAAG;AACd,4BAAsB,aAAa,MAAM,YAAY,WAAW;AAChE,UAAI,wBAAwB,WAAW;AACnC,uBAAe;AAAA,MACnB;AACA,UAAI,uBAAuB;AACvB,eAAO;AACP,yBAAiB,IAAI,IAAI;AAAA,MAC7B,OACK;AACD,2BAAmB,IAAI,QAAQ,YAAY,IAAI;AAC/C,eAAO,OAAO,KAAK;AAAA,MACvB;AAAA,IACJ,WACS,WAAW,GAAG;AACnB,UAAI,uBAAuB;AACvB,eAAO;AACP,yBAAiB,IAAI;AAAA,MACzB,OACK;AACD,0BAAkB,OAAO,YAAY,IAAI;AACzC,eAAO,OAAO,KAAK;AAAA,MACvB;AAAA,IACJ;AACA,QAAI,WAAW,UAAa,CAAC,iBAAiB;AAC1C,WAAK;AAAA,IACT;AACA,WAAO;AAAA,EACX;AACA,QAAM,iBAAiB,MAAM;AACzB,QAAI,aAAa;AACb,UAAI,uBAAuB;AACvB,sBAAc,QAAQ,CAACA,eAAc;AACjC,UAAAA,WAAU,MAAM;AAAA,QACpB,CAAC;AAAA,MACL,OACK;AACD,iBAAS,QAAQ,CAAC,YAAY;AAC1B,2BAAiB,SAAS,wBAAwB,QAAQ;AAAA,QAC9D,CAAC;AAAA,MACL;AACA,eAAS;AAAA,IACb;AAAA,EACJ;AACA,QAAM,QAAQ,MAAM;AAChB,oBAAgB,QAAQ,CAACA,eAAc;AACnC,MAAAA,WAAU,MAAM;AAAA,IACpB,CAAC;AACD,mBAAe;AACf,WAAO;AAAA,EACX;AACA,QAAM,oBAAoB,MAAM;AAC5B,oBAAgB;AAAA,EACpB;AACA,QAAM,oBAAoB,MAAM;AAC5B,kBAAc,QAAQ,CAACA,eAAc;AACjC,MAAAA,WAAU,KAAK;AAAA,IACnB,CAAC;AACD,QAAI,WAAW,WAAW,KAAK,SAAS,WAAW,GAAG;AAClD,sBAAgB;AAAA,IACpB;AAAA,EACJ;AACA,QAAM,iBAAiB,MAAM;AACzB,QAAI,uBAAuB;AACvB,uBAAiB,CAAC;AAClB,yBAAmB;AAAA,IACvB;AAAA,EACJ;AACA,QAAM,OAAO,CAAC,SAAS;AACnB,WAAO,IAAI,QAAQ,CAAC,YAAY;AAC5B,UAAI,SAAS,QAAQ,SAAS,SAAS,SAAS,KAAK,MAAM;AACvD,kCAA0B;AAC1B,iBAAS,MAAO,0BAA0B,OAAQ,EAAE,iBAAiB,KAAK,CAAC;AAAA,MAC/E;AACA,UAAI,CAAC,aAAa;AACd,4BAAoB;AAAA,MACxB;AACA,UAAI,UAAU;AACV,uBAAe;AACf,mBAAW;AAAA,MACf;AACA,UAAI,8BAA8B;AAC9B,+BAAuB,gBAAgB,SAAS;AAChD,uCAA+B;AAAA,MACnC;AAcA,YAAM,iBAAiB,MAAM;AACzB,sBAAc,kBAAkB,wBAAwB;AACxD,gBAAQ;AAAA,MACZ;AACA,YAAM,mBAAmB,MAAM;AAC3B,sBAAc,gBAAgB,sBAAsB;AACpD,gBAAQ;AAAA,MACZ;AAKA,eAAS,kBAAkB,EAAE,iBAAiB,KAAK,CAAC;AACpD,aAAO,gBAAgB,EAAE,iBAAiB,KAAK,CAAC;AAChD,sBAAgB,QAAQ,CAACA,eAAc;AACnC,QAAAA,WAAU,KAAK;AAAA,MACnB,CAAC;AACD,UAAI,uBAAuB;AACvB,0BAAkB;AAAA,MACtB,OACK;AACD,0BAAkB;AAAA,MACtB;AACA,eAAS;AAAA,IACb,CAAC;AAAA,EACL;AASA,QAAM,OAAO,MAAM;AACf,oBAAgB,QAAQ,CAACA,eAAc;AACnC,MAAAA,WAAU,KAAK;AAAA,IACnB,CAAC;AACD,QAAI,aAAa;AACb,sBAAgB;AAChB,oBAAc;AAAA,IAClB;AACA,eAAW;AACX,2BAAuB,QAAQ,CAAC,mBAAmB,eAAe,EAAE,GAAG,GAAG,CAAC;AAC3E,2BAAuB,SAAS;AAAA,EACpC;AACA,QAAM,OAAO,CAAC,UAAU,UAAU;AAC9B,UAAM,aAAa,WAAW,CAAC;AAC/B,QAAI,eAAe,WAAc,WAAW,WAAW,UAAa,WAAW,WAAW,IAAI;AAC1F,iBAAW,QAAQ,IAAI;AAAA,IAC3B,OACK;AACD,mBAAa,CAAC,EAAE,QAAQ,GAAG,CAAC,QAAQ,GAAG,MAAM,GAAG,GAAG,UAAU;AAAA,IACjE;AACA,WAAO;AAAA,EACX;AACA,QAAM,KAAK,CAAC,UAAU,UAAU;AAC5B,UAAM,YAAY,WAAW,WAAW,SAAS,CAAC;AAClD,QAAI,cAAc,WAAc,UAAU,WAAW,UAAa,UAAU,WAAW,IAAI;AACvF,gBAAU,QAAQ,IAAI;AAAA,IAC1B,OACK;AACD,mBAAa,CAAC,GAAG,YAAY,EAAE,QAAQ,GAAG,CAAC,QAAQ,GAAG,MAAM,CAAC;AAAA,IACjE;AACA,WAAO;AAAA,EACX;AACA,QAAM,SAAS,CAAC,UAAU,WAAW,YAAY;AAC7C,WAAO,KAAK,UAAU,SAAS,EAAE,GAAG,UAAU,OAAO;AAAA,EACzD;AACA,SAAQ,MAAM;AAAA,IACV;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AACJ;;;AC1yBA,IAAM,uBAAuB;AAC7B,IAAM,sBAAsB;AAC5B,IAAM,uBAAuB;AAC7B,IAAM,sBAAsB;AAC5B,IAAM,wBAAwB;AAO9B,IAAM,YAAY,CAAC,OAAO;AACtB,KAAG,WAAW;AACd,KAAG,MAAM;AACb;AAKA,IAAM,YAAY,CAAC,OAAO;AACtB,SAAO,GAAG,iBAAiB;AAC/B;AASA,IAAM,wBAAwB,MAAM;AAChC,QAAM,gBAAgB,CAAC,gBAAgB;AACnC,UAAM,sBAAsB,OAAO,IAAI,wBAAwB,KAAK;AAKpE,QAAI,qBAAqB;AACrB,YAAM,WAAW,SAAS;AAC1B,UAAI,aAAa,SAAS,gBAAgB,QAAQ,gBAAgB,SAAS,SAAS,YAAY,SAAS,QAAQ,IAAI;AACjH,iBAAS,aAAa,YAAY,MAAM;AAAA,MAC5C;AAAA,IACJ;AAAA,EACJ;AACA,QAAM,eAAe,CAAC,gBAAgB;AAClC,UAAM,yBAAyB,OAAO,IAAI,wBAAwB,KAAK;AAMvE,QAAI,MAAM,QAAQ,sBAAsB,KAAK,CAAC,YAAY,SAAS,SAAS,aAAa,GAAG;AAKxF,YAAM,YAAY,YAAY,cAAc,IAAI,UAAU,GAAG;AAC7D,UAAI,aAAa,UAAU,SAAS,GAAG;AACnC,kBAAU,SAAS;AACnB;AAAA,MACJ;AACA,iBAAW,YAAY,wBAAwB;AAW3C,gBAAQ,UAAU;AAAA,UACd,KAAK;AACD,kBAAM,UAAU,YAAY,cAAc,qBAAqB;AAC/D,gBAAI,WAAW,UAAU,OAAO,GAAG;AAC/B,wBAAU,OAAO;AACjB;AAAA,YACJ;AACA;AAAA,UACJ,KAAK;AACD,kBAAM,aAAa,YAAY,cAAc,sCAAsC;AACnF,gBAAI,cAAc,UAAU,UAAU,GAAG;AACrC,wBAAU,UAAU;AACpB;AAAA,YACJ;AACA;AAAA,UACJ,KAAK;AACD,kBAAM,SAAS,YAAY,cAAc,yBAAyB;AAClE,gBAAI,UAAU,UAAU,MAAM,GAAG;AAC7B,wBAAU,MAAM;AAChB;AAAA,YACJ;AACA;AAAA,UACJ;AACI,4BAAgB,6CAA6C,QAAQ,EAAE;AACvE;AAAA,QACR;AAAA,MACJ;AAMA,gBAAU,WAAW;AAAA,IACzB;AAAA,EACJ;AACA,SAAO;AAAA,IACH;AAAA,IACA;AAAA,EACJ;AACJ;AACA,IAAM,aAAa;AAEnB,IAAM,yBAAyB,MAAM,OAAO,8BAAqB;AACjE,IAAM,wBAAwB,MAAM,OAAO,6BAAoB;AAC/D,IAAM,kBAAkB,sBAAsB;AAE9C,IAAM,aAAa,CAAC,SAAS;AACzB,SAAO,IAAI,QAAQ,CAAC,SAAS,WAAW;AACpC,cAAU,MAAM;AACZ,uBAAiB,IAAI;AACrB,oBAAc,IAAI,EAAE,KAAK,CAAC,WAAW;AACjC,YAAI,OAAO,WAAW;AAClB,iBAAO,UAAU,QAAQ;AAAA,QAC7B;AACA,wBAAgB,IAAI;AACpB,gBAAQ,MAAM;AAAA,MAClB,GAAG,CAAC,UAAU;AACV,wBAAgB,IAAI;AACpB,eAAO,KAAK;AAAA,MAChB,CAAC;AAAA,IACL,CAAC;AAAA,EACL,CAAC;AACL;AACA,IAAM,mBAAmB,CAAC,SAAS;AAC/B,QAAM,aAAa,KAAK;AACxB,QAAM,YAAY,KAAK;AACvB,kBAAgB,cAAc,SAAS;AACvC,YAAU,YAAY,WAAW,KAAK,SAAS;AAC/C,MAAI,KAAK,YAAY;AACjB,eAAW,UAAU,IAAI,aAAa;AAAA,EAC1C,OACK;AACD,eAAW,UAAU,OAAO,aAAa;AAAA,EAC7C;AACA,gBAAc,YAAY,KAAK;AAO/B,aAAW,MAAM,YAAY,kBAAkB,MAAM;AACrD,MAAI,WAAW;AACX,kBAAc,WAAW,KAAK;AAC9B,cAAU,MAAM,YAAY,kBAAkB,MAAM;AAAA,EACxD;AACJ;AACA,IAAM,gBAAgB,CAAO,SAAS;AAClC,QAAM,mBAAmB,MAAM,oBAAoB,IAAI;AACvD,QAAM,MAAM,oBAAoB,MAAM,YAAY,UAAU,kBAAkB,IAAI,IAAI,YAAY,IAAI;AACtG,SAAO;AACX;AACA,IAAM,kBAAkB,CAAC,SAAS;AAC9B,QAAM,aAAa,KAAK;AACxB,QAAM,YAAY,KAAK;AACvB,aAAW,UAAU,OAAO,oBAAoB;AAChD,aAAW,MAAM,eAAe,gBAAgB;AAChD,MAAI,cAAc,QAAW;AACzB,cAAU,UAAU,OAAO,oBAAoB;AAC/C,cAAU,MAAM,eAAe,gBAAgB;AAAA,EACnD;AACA,kBAAgB,aAAa,UAAU;AAC3C;AACA,IAAM,sBAAsB,CAAO,SAAS;AACxC,MAAI,CAAC,KAAK,aAAa,CAAC,KAAK,YAAY,KAAK,aAAa,GAAG;AAC1D,WAAO;AAAA,EACX;AACA,MAAI,KAAK,kBAAkB;AACvB,WAAO,KAAK;AAAA,EAChB;AACA,QAAM,eAAe,KAAK,SAAS,SAC5B,MAAM,uBAAuB,GAAG,0BAChC,MAAM,sBAAsB,GAAG;AACtC,SAAO;AACX;AACA,IAAM,YAAY,CAAO,kBAAkB,SAAS;AAChD,QAAM,aAAa,MAAM,IAAI;AAC7B,QAAM,QAAQ,iBAAiB,KAAK,QAAQ,IAAI;AAChD,iBAAe,KAAK,YAAY,KAAK,SAAS;AAC9C,QAAM,cAAc,MAAM,eAAe,OAAO,IAAI;AACpD,MAAI,KAAK,kBAAkB;AACvB,SAAK,iBAAiB,MAAS;AAAA,EACnC;AACA,MAAI,aAAa;AACb,kBAAc,KAAK,YAAY,KAAK,SAAS;AAAA,EACjD;AACA,SAAO;AAAA,IACH,cAAc;AAAA,IACd,WAAW;AAAA,EACf;AACJ;AACA,IAAM,cAAc,CAAO,SAAS;AAChC,QAAM,aAAa,KAAK;AACxB,QAAM,YAAY,KAAK;AACvB,QAAM,sBAAsB,OAAO,IAAI,wBAAwB,KAAK;AAKpE,QAAM,aAAa,MAAM,mBAAmB;AAC5C,iBAAe,YAAY,SAAS;AACpC,gBAAc,YAAY,SAAS;AACnC,SAAO;AAAA,IACH,cAAc;AAAA,EAClB;AACJ;AACA,IAAM,eAAe,CAAO,MAAM,gBAAgB;AAC9C,QAAM,OAAO,KAAK,aAAa,SAAY,KAAK,WAAW;AAC3D,MAAI,MAAM;AACN,UAAM,QAAQ,IAAI,CAAC,UAAU,KAAK,UAAU,GAAG,UAAU,KAAK,SAAS,CAAC,CAAC;AAAA,EAC7E;AACA,QAAM,gBAAgB,KAAK,aAAa,KAAK,UAAU;AAC3D;AACA,IAAM,kBAAkB,CAAO,aAAa,eAAe;AACvD,MAAI,aAAa;AACb,UAAM,YAAY,UAAU;AAAA,EAChC;AACJ;AACA,IAAM,iBAAiB,CAAC,OAAO,SAAS;AACpC,QAAM,mBAAmB,KAAK;AAC9B,QAAM,UAAU,IAAI,QAAQ,CAAC,YAAY;AACrC,UAAM,SAAS,CAAC,gBAAgB,QAAQ,gBAAgB,CAAC,CAAC;AAAA,EAC9D,CAAC;AAED,MAAI,kBAAkB;AAGlB,UAAM,cAAc,IAAI;AACxB,qBAAiB,KAAK;AAAA,EAC1B,OACK;AAID,UAAM,KAAK;AAAA,EACf;AAEA,SAAO;AACX;AACA,IAAM,iBAAiB,CAAC,YAAY,cAAc;AAC9C,YAAU,WAAW,oBAAoB;AACzC,YAAU,YAAY,oBAAoB;AAC9C;AACA,IAAM,gBAAgB,CAAC,YAAY,cAAc;AAC7C,YAAU,YAAY,mBAAmB;AACzC,YAAU,WAAW,mBAAmB;AAC5C;AACA,IAAM,YAAY,CAAC,IAAI,cAAc;AACjC,MAAI,IAAI;AACJ,UAAM,KAAK,IAAI,YAAY,WAAW;AAAA,MAClC,SAAS;AAAA,MACT,YAAY;AAAA,IAChB,CAAC;AACD,OAAG,cAAc,EAAE;AAAA,EACvB;AACJ;AAcA,IAAM,eAAe,MAAM;AACvB,SAAO,IAAI,QAAQ,CAAC,YAAY,IAAI,MAAM,IAAI,MAAM,QAAQ,CAAC,CAAC,CAAC;AACnE;AACA,IAAM,YAAY,CAAO,OAAO;AAC5B,QAAM,UAAU;AAChB,MAAI,SAAS;AACT,QAAI,QAAQ,oBAAoB,MAAM;AAElC,YAAM,YAAY,MAAM,QAAQ,iBAAiB;AACjD,UAAI,aAAa,MAAM;AACnB;AAAA,MACJ;AAAA,IAIJ,WACS,QAAQ,kBAAkB,MAAM;AAKrC,YAAM,uBAAuB,IAAI,QAAQ,CAAC,YAAY,IAAI,OAAO,CAAC;AAClE,YAAM;AACN;AAAA,IACJ;AACA,UAAM,QAAQ,IAAI,MAAM,KAAK,QAAQ,QAAQ,EAAE,IAAI,SAAS,CAAC;AAAA,EACjE;AACJ;AACA,IAAM,gBAAgB,CAAC,IAAI,WAAW;AAClC,MAAI,QAAQ;AACR,OAAG,aAAa,eAAe,MAAM;AACrC,OAAG,UAAU,IAAI,iBAAiB;AAAA,EACtC,OACK;AACD,OAAG,SAAS;AACZ,OAAG,gBAAgB,aAAa;AAChC,OAAG,UAAU,OAAO,iBAAiB;AAAA,EACzC;AACJ;AACA,IAAM,YAAY,CAAC,YAAY,WAAW,cAAc;AACpD,MAAI,eAAe,QAAW;AAC1B,eAAW,MAAM,SAAS,cAAc,SAAS,OAAO;AAAA,EAC5D;AACA,MAAI,cAAc,QAAW;AACzB,cAAU,MAAM,SAAS;AAAA,EAC7B;AACJ;AACA,IAAM,oBAAoB,CAAC,YAAY;AACnC,MAAI,QAAQ,UAAU,SAAS,UAAU,GAAG;AACxC,WAAO;AAAA,EACX;AACA,QAAM,UAAU,QAAQ,cAAc,yDAAyD;AAC/F,MAAI,SAAS;AACT,WAAO;AAAA,EACX;AAEA,SAAO;AACX;", "names": ["animation"]}