{"version": 3, "sources": ["../../../../../../node_modules/@ionic/core/dist/esm/ion-img.entry.js"], "sourcesContent": ["/*!\n * (C) Ionic http://ionicframework.com - MIT License\n */\nimport { r as registerInstance, d as createEvent, h, e as getIonMode, j as Host, k as getElement } from './index-B_U9CtaY.js';\nimport { b as inheritAttributes } from './helpers-1O4D2b7y.js';\n\nconst imgCss = \":host{display:block;-o-object-fit:contain;object-fit:contain}img{display:block;width:100%;height:100%;-o-object-fit:inherit;object-fit:inherit;-o-object-position:inherit;object-position:inherit}\";\n\nconst Img = class {\n    constructor(hostRef) {\n        registerInstance(this, hostRef);\n        this.ionImgWillLoad = createEvent(this, \"ionImgWillLoad\", 7);\n        this.ionImgDidLoad = createEvent(this, \"ionImgDidLoad\", 7);\n        this.ionError = createEvent(this, \"ionError\", 7);\n        this.inheritedAttributes = {};\n        this.onLoad = () => {\n            this.ionImgDidLoad.emit();\n        };\n        this.onError = () => {\n            this.ionError.emit();\n        };\n    }\n    srcChanged() {\n        this.addIO();\n    }\n    componentWillLoad() {\n        this.inheritedAttributes = inheritAttributes(this.el, ['draggable']);\n    }\n    componentDidLoad() {\n        this.addIO();\n    }\n    addIO() {\n        if (this.src === undefined) {\n            return;\n        }\n        if (typeof window !== 'undefined' &&\n            'IntersectionObserver' in window &&\n            'IntersectionObserverEntry' in window &&\n            'isIntersecting' in window.IntersectionObserverEntry.prototype) {\n            this.removeIO();\n            this.io = new IntersectionObserver((data) => {\n                /**\n                 * On slower devices, it is possible for an intersection observer entry to contain multiple\n                 * objects in the array. This happens when quickly scrolling an image into view and then out of\n                 * view. In this case, the last object represents the current state of the component.\n                 */\n                if (data[data.length - 1].isIntersecting) {\n                    this.load();\n                    this.removeIO();\n                }\n            });\n            this.io.observe(this.el);\n        }\n        else {\n            // fall back to setTimeout for Safari and IE\n            setTimeout(() => this.load(), 200);\n        }\n    }\n    load() {\n        this.loadError = this.onError;\n        this.loadSrc = this.src;\n        this.ionImgWillLoad.emit();\n    }\n    removeIO() {\n        if (this.io) {\n            this.io.disconnect();\n            this.io = undefined;\n        }\n    }\n    render() {\n        const { loadSrc, alt, onLoad, loadError, inheritedAttributes } = this;\n        const { draggable } = inheritedAttributes;\n        return (h(Host, { key: 'da600442894427dee1974a28e545613afac69fca', class: getIonMode(this) }, h(\"img\", { key: '16df0c7069af86c0fa7ce5af598bc0f63b4eb71a', decoding: \"async\", src: loadSrc, alt: alt, onLoad: onLoad, onError: loadError, part: \"image\", draggable: isDraggable(draggable) })));\n    }\n    get el() { return getElement(this); }\n    static get watchers() { return {\n        \"src\": [\"srcChanged\"]\n    }; }\n};\n/**\n * Enumerated strings must be set as booleans\n * as Stencil will not render 'false' in the DOM.\n * The need to explicitly render draggable=\"true\"\n * as only certain elements are draggable by default.\n * https://developer.mozilla.org/en-US/docs/Web/HTML/Global_attributes/draggable.\n */\nconst isDraggable = (draggable) => {\n    switch (draggable) {\n        case 'true':\n            return true;\n        case 'false':\n            return false;\n        default:\n            return undefined;\n    }\n};\nImg.style = imgCss;\n\nexport { Img as ion_img };\n"], "mappings": ";;;;;;;;;;;;;;AAMA,IAAM,SAAS;AAEf,IAAM,MAAM,MAAM;AAAA,EACd,YAAY,SAAS;AACjB,qBAAiB,MAAM,OAAO;AAC9B,SAAK,iBAAiB,YAAY,MAAM,kBAAkB,CAAC;AAC3D,SAAK,gBAAgB,YAAY,MAAM,iBAAiB,CAAC;AACzD,SAAK,WAAW,YAAY,MAAM,YAAY,CAAC;AAC/C,SAAK,sBAAsB,CAAC;AAC5B,SAAK,SAAS,MAAM;AAChB,WAAK,cAAc,KAAK;AAAA,IAC5B;AACA,SAAK,UAAU,MAAM;AACjB,WAAK,SAAS,KAAK;AAAA,IACvB;AAAA,EACJ;AAAA,EACA,aAAa;AACT,SAAK,MAAM;AAAA,EACf;AAAA,EACA,oBAAoB;AAChB,SAAK,sBAAsB,kBAAkB,KAAK,IAAI,CAAC,WAAW,CAAC;AAAA,EACvE;AAAA,EACA,mBAAmB;AACf,SAAK,MAAM;AAAA,EACf;AAAA,EACA,QAAQ;AACJ,QAAI,KAAK,QAAQ,QAAW;AACxB;AAAA,IACJ;AACA,QAAI,OAAO,WAAW,eAClB,0BAA0B,UAC1B,+BAA+B,UAC/B,oBAAoB,OAAO,0BAA0B,WAAW;AAChE,WAAK,SAAS;AACd,WAAK,KAAK,IAAI,qBAAqB,CAAC,SAAS;AAMzC,YAAI,KAAK,KAAK,SAAS,CAAC,EAAE,gBAAgB;AACtC,eAAK,KAAK;AACV,eAAK,SAAS;AAAA,QAClB;AAAA,MACJ,CAAC;AACD,WAAK,GAAG,QAAQ,KAAK,EAAE;AAAA,IAC3B,OACK;AAED,iBAAW,MAAM,KAAK,KAAK,GAAG,GAAG;AAAA,IACrC;AAAA,EACJ;AAAA,EACA,OAAO;AACH,SAAK,YAAY,KAAK;AACtB,SAAK,UAAU,KAAK;AACpB,SAAK,eAAe,KAAK;AAAA,EAC7B;AAAA,EACA,WAAW;AACP,QAAI,KAAK,IAAI;AACT,WAAK,GAAG,WAAW;AACnB,WAAK,KAAK;AAAA,IACd;AAAA,EACJ;AAAA,EACA,SAAS;AACL,UAAM,EAAE,SAAS,KAAK,QAAQ,WAAW,oBAAoB,IAAI;AACjE,UAAM,EAAE,UAAU,IAAI;AACtB,WAAQ,EAAE,MAAM,EAAE,KAAK,4CAA4C,OAAO,WAAW,IAAI,EAAE,GAAG,EAAE,OAAO,EAAE,KAAK,4CAA4C,UAAU,SAAS,KAAK,SAAS,KAAU,QAAgB,SAAS,WAAW,MAAM,SAAS,WAAW,YAAY,SAAS,EAAE,CAAC,CAAC;AAAA,EAChS;AAAA,EACA,IAAI,KAAK;AAAE,WAAO,WAAW,IAAI;AAAA,EAAG;AAAA,EACpC,WAAW,WAAW;AAAE,WAAO;AAAA,MAC3B,OAAO,CAAC,YAAY;AAAA,IACxB;AAAA,EAAG;AACP;AAQA,IAAM,cAAc,CAAC,cAAc;AAC/B,UAAQ,WAAW;AAAA,IACf,KAAK;AACD,aAAO;AAAA,IACX,KAAK;AACD,aAAO;AAAA,IACX;AACI,aAAO;AAAA,EACf;AACJ;AACA,IAAI,QAAQ;", "names": []}