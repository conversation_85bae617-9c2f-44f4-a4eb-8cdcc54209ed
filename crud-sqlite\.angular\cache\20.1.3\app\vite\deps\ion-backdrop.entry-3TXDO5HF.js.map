{"version": 3, "sources": ["../../../../../../node_modules/@ionic/core/dist/esm/ion-backdrop.entry.js"], "sourcesContent": ["/*!\n * (C) Ionic http://ionicframework.com - MIT License\n */\nimport { r as registerInstance, d as createEvent, e as getIonMode, h, j as Host } from './index-B_U9CtaY.js';\n\nconst backdropIosCss = \":host{left:0;right:0;top:0;bottom:0;display:block;position:absolute;-webkit-transform:translateZ(0);transform:translateZ(0);contain:strict;cursor:pointer;opacity:0.01;-ms-touch-action:none;touch-action:none;z-index:2}:host(.backdrop-hide){background:transparent}:host(.backdrop-no-tappable){cursor:auto}:host{background-color:var(--ion-backdrop-color, #000)}\";\n\nconst backdropMdCss = \":host{left:0;right:0;top:0;bottom:0;display:block;position:absolute;-webkit-transform:translateZ(0);transform:translateZ(0);contain:strict;cursor:pointer;opacity:0.01;-ms-touch-action:none;touch-action:none;z-index:2}:host(.backdrop-hide){background:transparent}:host(.backdrop-no-tappable){cursor:auto}:host{background-color:var(--ion-backdrop-color, #000)}\";\n\nconst Backdrop = class {\n    constructor(hostRef) {\n        registerInstance(this, hostRef);\n        this.ionBackdropTap = createEvent(this, \"ionBackdropTap\", 7);\n        /**\n         * If `true`, the backdrop will be visible.\n         */\n        this.visible = true;\n        /**\n         * If `true`, the backdrop will can be clicked and will emit the `ionBackdropTap` event.\n         */\n        this.tappable = true;\n        /**\n         * If `true`, the backdrop will stop propagation on tap.\n         */\n        this.stopPropagation = true;\n    }\n    onMouseDown(ev) {\n        this.emitTap(ev);\n    }\n    emitTap(ev) {\n        if (this.stopPropagation) {\n            ev.preventDefault();\n            ev.stopPropagation();\n        }\n        if (this.tappable) {\n            this.ionBackdropTap.emit();\n        }\n    }\n    render() {\n        const mode = getIonMode(this);\n        return (h(Host, { key: '7abaf2c310aa399607451b14063265e8a5846938', \"aria-hidden\": \"true\", class: {\n                [mode]: true,\n                'backdrop-hide': !this.visible,\n                'backdrop-no-tappable': !this.tappable,\n            } }));\n    }\n};\nBackdrop.style = {\n    ios: backdropIosCss,\n    md: backdropMdCss\n};\n\nexport { Backdrop as ion_backdrop };\n"], "mappings": ";;;;;;;;;;AAKA,IAAM,iBAAiB;AAEvB,IAAM,gBAAgB;AAEtB,IAAM,WAAW,MAAM;AAAA,EACnB,YAAY,SAAS;AACjB,qBAAiB,MAAM,OAAO;AAC9B,SAAK,iBAAiB,YAAY,MAAM,kBAAkB,CAAC;AAI3D,SAAK,UAAU;AAIf,SAAK,WAAW;AAIhB,SAAK,kBAAkB;AAAA,EAC3B;AAAA,EACA,YAAY,IAAI;AACZ,SAAK,QAAQ,EAAE;AAAA,EACnB;AAAA,EACA,QAAQ,IAAI;AACR,QAAI,KAAK,iBAAiB;AACtB,SAAG,eAAe;AAClB,SAAG,gBAAgB;AAAA,IACvB;AACA,QAAI,KAAK,UAAU;AACf,WAAK,eAAe,KAAK;AAAA,IAC7B;AAAA,EACJ;AAAA,EACA,SAAS;AACL,UAAM,OAAO,WAAW,IAAI;AAC5B,WAAQ,EAAE,MAAM,EAAE,KAAK,4CAA4C,eAAe,QAAQ,OAAO;AAAA,MACzF,CAAC,IAAI,GAAG;AAAA,MACR,iBAAiB,CAAC,KAAK;AAAA,MACvB,wBAAwB,CAAC,KAAK;AAAA,IAClC,EAAE,CAAC;AAAA,EACX;AACJ;AACA,SAAS,QAAQ;AAAA,EACb,KAAK;AAAA,EACL,IAAI;AACR;", "names": []}