{"version": 3, "sources": ["../../../../../../node_modules/@ionic/core/dist/esm/theme-DiVJyqlX.js"], "sourcesContent": ["/*!\n * (C) Ionic http://ionicframework.com - MIT License\n */\nconst hostContext = (selector, el) => {\n    return el.closest(selector) !== null;\n};\n/**\n * Create the mode and color classes for the component based on the classes passed in\n */\nconst createColorClasses = (color, cssClassMap) => {\n    return typeof color === 'string' && color.length > 0\n        ? Object.assign({ 'ion-color': true, [`ion-color-${color}`]: true }, cssClassMap) : cssClassMap;\n};\nconst getClassList = (classes) => {\n    if (classes !== undefined) {\n        const array = Array.isArray(classes) ? classes : classes.split(' ');\n        return array\n            .filter((c) => c != null)\n            .map((c) => c.trim())\n            .filter((c) => c !== '');\n    }\n    return [];\n};\nconst getClassMap = (classes) => {\n    const map = {};\n    getClassList(classes).forEach((c) => (map[c] = true));\n    return map;\n};\nconst SCHEME = /^[a-z][a-z0-9+\\-.]*:/;\nconst openURL = async (url, ev, direction, animation) => {\n    if (url != null && url[0] !== '#' && !SCHEME.test(url)) {\n        const router = document.querySelector('ion-router');\n        if (router) {\n            if (ev != null) {\n                ev.preventDefault();\n            }\n            return router.push(url, direction, animation);\n        }\n    }\n    return false;\n};\n\nexport { createColorClasses as c, getClassMap as g, hostContext as h, openURL as o };\n"], "mappings": ";;;;;AAGA,IAAM,cAAc,CAAC,UAAU,OAAO;AAClC,SAAO,GAAG,QAAQ,QAAQ,MAAM;AACpC;AAIA,IAAM,qBAAqB,CAAC,OAAO,gBAAgB;AAC/C,SAAO,OAAO,UAAU,YAAY,MAAM,SAAS,IAC7C,OAAO,OAAO,EAAE,aAAa,MAAM,CAAC,aAAa,KAAK,EAAE,GAAG,KAAK,GAAG,WAAW,IAAI;AAC5F;AACA,IAAM,eAAe,CAAC,YAAY;AAC9B,MAAI,YAAY,QAAW;AACvB,UAAM,QAAQ,MAAM,QAAQ,OAAO,IAAI,UAAU,QAAQ,MAAM,GAAG;AAClE,WAAO,MACF,OAAO,CAAC,MAAM,KAAK,IAAI,EACvB,IAAI,CAAC,MAAM,EAAE,KAAK,CAAC,EACnB,OAAO,CAAC,MAAM,MAAM,EAAE;AAAA,EAC/B;AACA,SAAO,CAAC;AACZ;AACA,IAAM,cAAc,CAAC,YAAY;AAC7B,QAAM,MAAM,CAAC;AACb,eAAa,OAAO,EAAE,QAAQ,CAAC,MAAO,IAAI,CAAC,IAAI,IAAK;AACpD,SAAO;AACX;AACA,IAAM,SAAS;AACf,IAAM,UAAU,CAAO,KAAK,IAAI,WAAW,cAAc;AACrD,MAAI,OAAO,QAAQ,IAAI,CAAC,MAAM,OAAO,CAAC,OAAO,KAAK,GAAG,GAAG;AACpD,UAAM,SAAS,SAAS,cAAc,YAAY;AAClD,QAAI,QAAQ;AACR,UAAI,MAAM,MAAM;AACZ,WAAG,eAAe;AAAA,MACtB;AACA,aAAO,OAAO,KAAK,KAAK,WAAW,SAAS;AAAA,IAChD;AAAA,EACJ;AACA,SAAO;AACX;", "names": []}