{"version": 3, "sources": ["../../../../../../node_modules/@ionic/core/dist/esm/ion-radio_2.entry.js"], "sourcesContent": ["/*!\n * (C) Ionic http://ionicframework.com - MIT License\n */\nimport { r as registerInstance, d as createEvent, h, e as getIonMode, j as Host, k as getElement } from './index-B_U9CtaY.js';\nimport { f as addEventListener, m as removeEventListener, a as renderHiddenInput } from './helpers-1O4D2b7y.js';\nimport { i as isOptionSelected } from './compare-with-utils-sObYyvOy.js';\nimport { h as hostContext, c as createColorClasses } from './theme-DiVJyqlX.js';\n\nconst radioIosCss = \":host{--inner-border-radius:50%;display:inline-block;position:relative;max-width:100%;min-height:inherit;cursor:pointer;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none;z-index:2;-webkit-box-sizing:border-box;box-sizing:border-box}:host(.radio-disabled){pointer-events:none}.radio-icon{display:-ms-flexbox;display:flex;-ms-flex-align:center;align-items:center;-ms-flex-pack:center;justify-content:center;width:100%;height:100%;contain:layout size style}.radio-icon,.radio-inner{-webkit-box-sizing:border-box;box-sizing:border-box}input{position:absolute;top:0;left:0;right:0;bottom:0;width:100%;height:100%;margin:0;padding:0;border:0;outline:0;clip:rect(0 0 0 0);opacity:0;overflow:hidden;-webkit-appearance:none;-moz-appearance:none}:host(:focus){outline:none}:host(.in-item){-ms-flex:1 1 0px;flex:1 1 0;width:100%;height:100%}:host([slot=start]),:host([slot=end]){-ms-flex:initial;flex:initial;width:auto}.radio-wrapper{display:-ms-flexbox;display:flex;position:relative;-ms-flex-positive:1;flex-grow:1;-ms-flex-align:center;align-items:center;-ms-flex-pack:justify;justify-content:space-between;height:inherit;min-height:inherit;cursor:inherit}.label-text-wrapper{text-overflow:ellipsis;white-space:nowrap;overflow:hidden}:host(.in-item) .label-text-wrapper{margin-top:10px;margin-bottom:10px}:host(.in-item.radio-label-placement-stacked) .label-text-wrapper{margin-top:10px;margin-bottom:16px}:host(.in-item.radio-label-placement-stacked) .native-wrapper{margin-bottom:10px}.label-text-wrapper-hidden{display:none}.native-wrapper{display:-ms-flexbox;display:flex;-ms-flex-align:center;align-items:center}:host(.radio-justify-space-between) .radio-wrapper{-ms-flex-pack:justify;justify-content:space-between}:host(.radio-justify-start) .radio-wrapper{-ms-flex-pack:start;justify-content:start}:host(.radio-justify-end) .radio-wrapper{-ms-flex-pack:end;justify-content:end}:host(.radio-alignment-start) .radio-wrapper{-ms-flex-align:start;align-items:start}:host(.radio-alignment-center) .radio-wrapper{-ms-flex-align:center;align-items:center}:host(.radio-justify-space-between),:host(.radio-justify-start),:host(.radio-justify-end),:host(.radio-alignment-start),:host(.radio-alignment-center){display:block}:host(.radio-label-placement-start) .radio-wrapper{-ms-flex-direction:row;flex-direction:row}:host(.radio-label-placement-start) .label-text-wrapper{-webkit-margin-start:0;margin-inline-start:0;-webkit-margin-end:16px;margin-inline-end:16px}:host(.radio-label-placement-end) .radio-wrapper{-ms-flex-direction:row-reverse;flex-direction:row-reverse}:host(.radio-label-placement-end) .label-text-wrapper{-webkit-margin-start:16px;margin-inline-start:16px;-webkit-margin-end:0;margin-inline-end:0}:host(.radio-label-placement-fixed) .label-text-wrapper{-webkit-margin-start:0;margin-inline-start:0;-webkit-margin-end:16px;margin-inline-end:16px}:host(.radio-label-placement-fixed) .label-text-wrapper{-ms-flex:0 0 100px;flex:0 0 100px;width:100px;min-width:100px}:host(.radio-label-placement-stacked) .radio-wrapper{-ms-flex-direction:column;flex-direction:column}:host(.radio-label-placement-stacked) .label-text-wrapper{-webkit-transform:scale(0.75);transform:scale(0.75);margin-left:0;margin-right:0;margin-bottom:16px;max-width:calc(100% / 0.75)}:host(.radio-label-placement-stacked.radio-alignment-start) .label-text-wrapper{-webkit-transform-origin:left top;transform-origin:left top}:host-context([dir=rtl]):host(.radio-label-placement-stacked.radio-alignment-start) .label-text-wrapper,:host-context([dir=rtl]).radio-label-placement-stacked.radio-alignment-start .label-text-wrapper{-webkit-transform-origin:right top;transform-origin:right top}@supports selector(:dir(rtl)){:host(.radio-label-placement-stacked.radio-alignment-start:dir(rtl)) .label-text-wrapper{-webkit-transform-origin:right top;transform-origin:right top}}:host(.radio-label-placement-stacked.radio-alignment-center) .label-text-wrapper{-webkit-transform-origin:center top;transform-origin:center top}:host-context([dir=rtl]):host(.radio-label-placement-stacked.radio-alignment-center) .label-text-wrapper,:host-context([dir=rtl]).radio-label-placement-stacked.radio-alignment-center .label-text-wrapper{-webkit-transform-origin:calc(100% - center) top;transform-origin:calc(100% - center) top}@supports selector(:dir(rtl)){:host(.radio-label-placement-stacked.radio-alignment-center:dir(rtl)) .label-text-wrapper{-webkit-transform-origin:calc(100% - center) top;transform-origin:calc(100% - center) top}}:host{--color-checked:var(--ion-color-primary, #0054e9)}:host(.ion-color.radio-checked) .radio-inner{border-color:var(--ion-color-base)}.item-radio.item-ios ion-label{-webkit-margin-start:0;margin-inline-start:0}.radio-inner{width:33%;height:50%}:host(.radio-checked) .radio-inner{-webkit-transform:rotate(45deg);transform:rotate(45deg);border-width:0.125rem;border-top-width:0;border-left-width:0;border-style:solid;border-color:var(--color-checked)}:host(.radio-disabled){opacity:0.3}:host(.ion-focused) .radio-icon::after{border-radius:var(--inner-border-radius);top:-8px;display:block;position:absolute;width:36px;height:36px;background:var(--ion-color-primary-tint, #1a65eb);content:\\\"\\\";opacity:0.2}:host(.ion-focused) .radio-icon::after{inset-inline-start:-9px}.native-wrapper .radio-icon{width:0.9375rem;height:1.5rem}\";\n\nconst radioMdCss = \":host{--inner-border-radius:50%;display:inline-block;position:relative;max-width:100%;min-height:inherit;cursor:pointer;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none;z-index:2;-webkit-box-sizing:border-box;box-sizing:border-box}:host(.radio-disabled){pointer-events:none}.radio-icon{display:-ms-flexbox;display:flex;-ms-flex-align:center;align-items:center;-ms-flex-pack:center;justify-content:center;width:100%;height:100%;contain:layout size style}.radio-icon,.radio-inner{-webkit-box-sizing:border-box;box-sizing:border-box}input{position:absolute;top:0;left:0;right:0;bottom:0;width:100%;height:100%;margin:0;padding:0;border:0;outline:0;clip:rect(0 0 0 0);opacity:0;overflow:hidden;-webkit-appearance:none;-moz-appearance:none}:host(:focus){outline:none}:host(.in-item){-ms-flex:1 1 0px;flex:1 1 0;width:100%;height:100%}:host([slot=start]),:host([slot=end]){-ms-flex:initial;flex:initial;width:auto}.radio-wrapper{display:-ms-flexbox;display:flex;position:relative;-ms-flex-positive:1;flex-grow:1;-ms-flex-align:center;align-items:center;-ms-flex-pack:justify;justify-content:space-between;height:inherit;min-height:inherit;cursor:inherit}.label-text-wrapper{text-overflow:ellipsis;white-space:nowrap;overflow:hidden}:host(.in-item) .label-text-wrapper{margin-top:10px;margin-bottom:10px}:host(.in-item.radio-label-placement-stacked) .label-text-wrapper{margin-top:10px;margin-bottom:16px}:host(.in-item.radio-label-placement-stacked) .native-wrapper{margin-bottom:10px}.label-text-wrapper-hidden{display:none}.native-wrapper{display:-ms-flexbox;display:flex;-ms-flex-align:center;align-items:center}:host(.radio-justify-space-between) .radio-wrapper{-ms-flex-pack:justify;justify-content:space-between}:host(.radio-justify-start) .radio-wrapper{-ms-flex-pack:start;justify-content:start}:host(.radio-justify-end) .radio-wrapper{-ms-flex-pack:end;justify-content:end}:host(.radio-alignment-start) .radio-wrapper{-ms-flex-align:start;align-items:start}:host(.radio-alignment-center) .radio-wrapper{-ms-flex-align:center;align-items:center}:host(.radio-justify-space-between),:host(.radio-justify-start),:host(.radio-justify-end),:host(.radio-alignment-start),:host(.radio-alignment-center){display:block}:host(.radio-label-placement-start) .radio-wrapper{-ms-flex-direction:row;flex-direction:row}:host(.radio-label-placement-start) .label-text-wrapper{-webkit-margin-start:0;margin-inline-start:0;-webkit-margin-end:16px;margin-inline-end:16px}:host(.radio-label-placement-end) .radio-wrapper{-ms-flex-direction:row-reverse;flex-direction:row-reverse}:host(.radio-label-placement-end) .label-text-wrapper{-webkit-margin-start:16px;margin-inline-start:16px;-webkit-margin-end:0;margin-inline-end:0}:host(.radio-label-placement-fixed) .label-text-wrapper{-webkit-margin-start:0;margin-inline-start:0;-webkit-margin-end:16px;margin-inline-end:16px}:host(.radio-label-placement-fixed) .label-text-wrapper{-ms-flex:0 0 100px;flex:0 0 100px;width:100px;min-width:100px}:host(.radio-label-placement-stacked) .radio-wrapper{-ms-flex-direction:column;flex-direction:column}:host(.radio-label-placement-stacked) .label-text-wrapper{-webkit-transform:scale(0.75);transform:scale(0.75);margin-left:0;margin-right:0;margin-bottom:16px;max-width:calc(100% / 0.75)}:host(.radio-label-placement-stacked.radio-alignment-start) .label-text-wrapper{-webkit-transform-origin:left top;transform-origin:left top}:host-context([dir=rtl]):host(.radio-label-placement-stacked.radio-alignment-start) .label-text-wrapper,:host-context([dir=rtl]).radio-label-placement-stacked.radio-alignment-start .label-text-wrapper{-webkit-transform-origin:right top;transform-origin:right top}@supports selector(:dir(rtl)){:host(.radio-label-placement-stacked.radio-alignment-start:dir(rtl)) .label-text-wrapper{-webkit-transform-origin:right top;transform-origin:right top}}:host(.radio-label-placement-stacked.radio-alignment-center) .label-text-wrapper{-webkit-transform-origin:center top;transform-origin:center top}:host-context([dir=rtl]):host(.radio-label-placement-stacked.radio-alignment-center) .label-text-wrapper,:host-context([dir=rtl]).radio-label-placement-stacked.radio-alignment-center .label-text-wrapper{-webkit-transform-origin:calc(100% - center) top;transform-origin:calc(100% - center) top}@supports selector(:dir(rtl)){:host(.radio-label-placement-stacked.radio-alignment-center:dir(rtl)) .label-text-wrapper{-webkit-transform-origin:calc(100% - center) top;transform-origin:calc(100% - center) top}}:host{--color:rgb(var(--ion-text-color-rgb, 0, 0, 0), 0.6);--color-checked:var(--ion-color-primary, #0054e9);--border-width:0.125rem;--border-style:solid;--border-radius:50%}:host(.ion-color) .radio-inner{background:var(--ion-color-base)}:host(.ion-color.radio-checked) .radio-icon{border-color:var(--ion-color-base)}.radio-icon{margin-left:0;margin-right:0;margin-top:0;margin-bottom:0;border-radius:var(--border-radius);border-width:var(--border-width);border-style:var(--border-style);border-color:var(--color)}.radio-inner{border-radius:var(--inner-border-radius);width:calc(50% + var(--border-width));height:calc(50% + var(--border-width));-webkit-transform:scale3d(0, 0, 0);transform:scale3d(0, 0, 0);-webkit-transition:-webkit-transform 280ms cubic-bezier(0.4, 0, 0.2, 1);transition:-webkit-transform 280ms cubic-bezier(0.4, 0, 0.2, 1);transition:transform 280ms cubic-bezier(0.4, 0, 0.2, 1);transition:transform 280ms cubic-bezier(0.4, 0, 0.2, 1), -webkit-transform 280ms cubic-bezier(0.4, 0, 0.2, 1);background:var(--color-checked)}:host(.radio-checked) .radio-icon{border-color:var(--color-checked)}:host(.radio-checked) .radio-inner{-webkit-transform:scale3d(1, 1, 1);transform:scale3d(1, 1, 1)}:host(.radio-disabled) .label-text-wrapper{opacity:0.38}:host(.radio-disabled) .native-wrapper{opacity:0.63}:host(.ion-focused) .radio-icon::after{border-radius:var(--inner-border-radius);display:block;position:absolute;width:36px;height:36px;background:var(--ion-color-primary-tint, #1a65eb);content:\\\"\\\";opacity:0.2}.native-wrapper .radio-icon{width:1.25rem;height:1.25rem}\";\n\nconst Radio = class {\n    constructor(hostRef) {\n        registerInstance(this, hostRef);\n        this.ionFocus = createEvent(this, \"ionFocus\", 7);\n        this.ionBlur = createEvent(this, \"ionBlur\", 7);\n        this.inputId = `ion-rb-${radioButtonIds++}`;\n        this.radioGroup = null;\n        /**\n         * If `true`, the radio is selected.\n         */\n        this.checked = false;\n        /**\n         * The tabindex of the radio button.\n         * @internal\n         */\n        this.buttonTabindex = -1;\n        /**\n         * The name of the control, which is submitted with the form data.\n         */\n        this.name = this.inputId;\n        /**\n         * If `true`, the user cannot interact with the radio.\n         */\n        this.disabled = false;\n        /**\n         * Where to place the label relative to the radio.\n         * `\"start\"`: The label will appear to the left of the radio in LTR and to the right in RTL.\n         * `\"end\"`: The label will appear to the right of the radio in LTR and to the left in RTL.\n         * `\"fixed\"`: The label has the same behavior as `\"start\"` except it also has a fixed width. Long text will be truncated with ellipses (\"...\").\n         * `\"stacked\"`: The label will appear above the radio regardless of the direction. The alignment of the label can be controlled with the `alignment` property.\n         */\n        this.labelPlacement = 'start';\n        this.updateState = () => {\n            if (this.radioGroup) {\n                const { compareWith, value: radioGroupValue } = this.radioGroup;\n                this.checked = isOptionSelected(radioGroupValue, this.value, compareWith);\n            }\n        };\n        this.onClick = () => {\n            const { radioGroup, checked, disabled } = this;\n            if (disabled) {\n                return;\n            }\n            /**\n             * The modern control does not use a native input\n             * inside of the radio host, so we cannot rely on the\n             * ev.preventDefault() behavior above. If the radio\n             * is checked and the parent radio group allows for empty\n             * selection, then we can set the checked state to false.\n             * Otherwise, the checked state should always be set\n             * to true because the checked state cannot be toggled.\n             */\n            if (checked && (radioGroup === null || radioGroup === void 0 ? void 0 : radioGroup.allowEmptySelection)) {\n                this.checked = false;\n            }\n            else {\n                this.checked = true;\n            }\n        };\n        this.onFocus = () => {\n            this.ionFocus.emit();\n        };\n        this.onBlur = () => {\n            this.ionBlur.emit();\n        };\n    }\n    valueChanged() {\n        /**\n         * The new value of the radio may\n         * match the radio group's value,\n         * so we see if it should be checked.\n         */\n        this.updateState();\n    }\n    componentDidLoad() {\n        /**\n         * The value may be `undefined` if it\n         * gets set before the radio is\n         * rendered. This ensures that the radio\n         * is checked if the value matches. This\n         * happens most often when Angular is\n         * rendering the radio.\n         */\n        this.updateState();\n    }\n    /** @internal */\n    async setFocus(ev) {\n        if (ev !== undefined) {\n            ev.stopPropagation();\n            ev.preventDefault();\n        }\n        this.el.focus();\n    }\n    /** @internal */\n    async setButtonTabindex(value) {\n        this.buttonTabindex = value;\n    }\n    connectedCallback() {\n        if (this.value === undefined) {\n            this.value = this.inputId;\n        }\n        const radioGroup = (this.radioGroup = this.el.closest('ion-radio-group'));\n        if (radioGroup) {\n            this.updateState();\n            addEventListener(radioGroup, 'ionValueChange', this.updateState);\n        }\n    }\n    disconnectedCallback() {\n        const radioGroup = this.radioGroup;\n        if (radioGroup) {\n            removeEventListener(radioGroup, 'ionValueChange', this.updateState);\n            this.radioGroup = null;\n        }\n    }\n    get hasLabel() {\n        return this.el.textContent !== '';\n    }\n    renderRadioControl() {\n        return (h(\"div\", { class: \"radio-icon\", part: \"container\" }, h(\"div\", { class: \"radio-inner\", part: \"mark\" }), h(\"div\", { class: \"radio-ripple\" })));\n    }\n    render() {\n        const { checked, disabled, color, el, justify, labelPlacement, hasLabel, buttonTabindex, alignment } = this;\n        const mode = getIonMode(this);\n        const inItem = hostContext('ion-item', el);\n        return (h(Host, { key: '3353b28172b7f837d4b38964169b5b5f4ba02788', onFocus: this.onFocus, onBlur: this.onBlur, onClick: this.onClick, class: createColorClasses(color, {\n                [mode]: true,\n                'in-item': inItem,\n                'radio-checked': checked,\n                'radio-disabled': disabled,\n                [`radio-justify-${justify}`]: justify !== undefined,\n                [`radio-alignment-${alignment}`]: alignment !== undefined,\n                [`radio-label-placement-${labelPlacement}`]: true,\n                // Focus and active styling should not apply when the radio is in an item\n                'ion-activatable': !inItem,\n                'ion-focusable': !inItem,\n            }), role: \"radio\", \"aria-checked\": checked ? 'true' : 'false', \"aria-disabled\": disabled ? 'true' : null, tabindex: buttonTabindex }, h(\"label\", { key: '418a0a48366ff900e97da123abf665bbbda87fb7', class: \"radio-wrapper\" }, h(\"div\", { key: '6e5acdd8c8f5d0ad26632a65396afef8094153d1', class: {\n                'label-text-wrapper': true,\n                'label-text-wrapper-hidden': !hasLabel,\n            }, part: \"label\" }, h(\"slot\", { key: '10b157162cd283d624153c747679609cf0bbf11e' })), h(\"div\", { key: '4c45cca95cb105cd6df1025a26e3c045272184a0', class: \"native-wrapper\" }, this.renderRadioControl()))));\n    }\n    get el() { return getElement(this); }\n    static get watchers() { return {\n        \"value\": [\"valueChanged\"]\n    }; }\n};\nlet radioButtonIds = 0;\nRadio.style = {\n    ios: radioIosCss,\n    md: radioMdCss\n};\n\nconst radioGroupIosCss = \"ion-radio-group{vertical-align:top}.radio-group-wrapper{display:inline}.radio-group-top{line-height:1.5}.radio-group-top .error-text{display:none;color:var(--ion-color-danger, #c5000f)}.radio-group-top .helper-text{display:block;color:var(--ion-color-step-700, var(--ion-text-color-step-300, #4d4d4d))}.ion-touched.ion-invalid .radio-group-top .error-text{display:block}.ion-touched.ion-invalid .radio-group-top .helper-text{display:none}ion-list .radio-group-top{-webkit-padding-start:16px;padding-inline-start:16px;-webkit-padding-end:16px;padding-inline-end:16px}\";\n\nconst radioGroupMdCss = \"ion-radio-group{vertical-align:top}.radio-group-wrapper{display:inline}.radio-group-top{line-height:1.5}.radio-group-top .error-text{display:none;color:var(--ion-color-danger, #c5000f)}.radio-group-top .helper-text{display:block;color:var(--ion-color-step-700, var(--ion-text-color-step-300, #4d4d4d))}.ion-touched.ion-invalid .radio-group-top .error-text{display:block}.ion-touched.ion-invalid .radio-group-top .helper-text{display:none}ion-list .radio-group-top{-webkit-padding-start:16px;padding-inline-start:16px;-webkit-padding-end:16px;padding-inline-end:16px}\";\n\nconst RadioGroup = class {\n    constructor(hostRef) {\n        registerInstance(this, hostRef);\n        this.ionChange = createEvent(this, \"ionChange\", 7);\n        this.ionValueChange = createEvent(this, \"ionValueChange\", 7);\n        this.inputId = `ion-rg-${radioGroupIds++}`;\n        this.helperTextId = `${this.inputId}-helper-text`;\n        this.errorTextId = `${this.inputId}-error-text`;\n        this.labelId = `${this.inputId}-lbl`;\n        /**\n         * If `true`, the radios can be deselected.\n         */\n        this.allowEmptySelection = false;\n        /**\n         * The name of the control, which is submitted with the form data.\n         */\n        this.name = this.inputId;\n        this.setRadioTabindex = (value) => {\n            const radios = this.getRadios();\n            // Get the first radio that is not disabled and the checked one\n            const first = radios.find((radio) => !radio.disabled);\n            const checked = radios.find((radio) => radio.value === value && !radio.disabled);\n            if (!first && !checked) {\n                return;\n            }\n            // If an enabled checked radio exists, set it to be the focusable radio\n            // otherwise we default to focus the first radio\n            const focusable = checked || first;\n            for (const radio of radios) {\n                const tabindex = radio === focusable ? 0 : -1;\n                radio.setButtonTabindex(tabindex);\n            }\n        };\n        this.onClick = (ev) => {\n            ev.preventDefault();\n            /**\n             * The Radio Group component mandates that only one radio button\n             * within the group can be selected at any given time. Since `ion-radio`\n             * is a shadow DOM component, it cannot natively perform this behavior\n             * using the `name` attribute.\n             */\n            const selectedRadio = ev.target && ev.target.closest('ion-radio');\n            /**\n             * Our current disabled prop definition causes Stencil to mark it\n             * as optional. While this is not desired, fixing this behavior\n             * in Stencil is a significant breaking change, so this effort is\n             * being de-risked in STENCIL-917. Until then, we compromise\n             * here by checking for falsy `disabled` values instead of strictly\n             * checking `disabled === false`.\n             */\n            if (selectedRadio && !selectedRadio.disabled) {\n                const currentValue = this.value;\n                const newValue = selectedRadio.value;\n                if (newValue !== currentValue) {\n                    this.value = newValue;\n                    this.emitValueChange(ev);\n                }\n                else if (this.allowEmptySelection) {\n                    this.value = undefined;\n                    this.emitValueChange(ev);\n                }\n            }\n        };\n    }\n    valueChanged(value) {\n        this.setRadioTabindex(value);\n        this.ionValueChange.emit({ value });\n    }\n    componentDidLoad() {\n        /**\n         * There's an issue when assigning a value to the radio group\n         * within the Angular primary content (rendering within the\n         * app component template). When the template is isolated to a route,\n         * the value is assigned correctly.\n         * To address this issue, we need to ensure that the watcher is\n         * called after the component has finished loading,\n         * allowing the emit to be dispatched correctly.\n         */\n        this.valueChanged(this.value);\n    }\n    async connectedCallback() {\n        // Get the list header if it exists and set the id\n        // this is used to set aria-labelledby\n        const header = this.el.querySelector('ion-list-header') || this.el.querySelector('ion-item-divider');\n        if (header) {\n            const label = (this.label = header.querySelector('ion-label'));\n            if (label) {\n                this.labelId = label.id = this.name + '-lbl';\n            }\n        }\n    }\n    getRadios() {\n        return Array.from(this.el.querySelectorAll('ion-radio'));\n    }\n    /**\n     * Emits an `ionChange` event.\n     *\n     * This API should be called for user committed changes.\n     * This API should not be used for external value changes.\n     */\n    emitValueChange(event) {\n        const { value } = this;\n        this.ionChange.emit({ value, event });\n    }\n    onKeydown(ev) {\n        // We don't want the value to automatically change/emit when the radio group is part of a select interface\n        // as this will cause the interface to close when navigating through the radio group options\n        const inSelectInterface = !!this.el.closest('ion-select-popover') || !!this.el.closest('ion-select-modal');\n        if (ev.target && !this.el.contains(ev.target)) {\n            return;\n        }\n        // Get all radios inside of the radio group and then\n        // filter out disabled radios since we need to skip those\n        const radios = this.getRadios().filter((radio) => !radio.disabled);\n        // Only move the radio if the current focus is in the radio group\n        if (ev.target && radios.includes(ev.target)) {\n            const index = radios.findIndex((radio) => radio === ev.target);\n            const current = radios[index];\n            let next;\n            // If hitting arrow down or arrow right, move to the next radio\n            // If we're on the last radio, move to the first radio\n            if (['ArrowDown', 'ArrowRight'].includes(ev.key)) {\n                next = index === radios.length - 1 ? radios[0] : radios[index + 1];\n            }\n            // If hitting arrow up or arrow left, move to the previous radio\n            // If we're on the first radio, move to the last radio\n            if (['ArrowUp', 'ArrowLeft'].includes(ev.key)) {\n                next = index === 0 ? radios[radios.length - 1] : radios[index - 1];\n            }\n            if (next && radios.includes(next)) {\n                next.setFocus(ev);\n                if (!inSelectInterface) {\n                    this.value = next.value;\n                    this.emitValueChange(ev);\n                }\n            }\n            // Update the radio group value when a user presses the\n            // space bar on top of a selected radio\n            if ([' '].includes(ev.key)) {\n                const previousValue = this.value;\n                this.value = this.allowEmptySelection && this.value !== undefined ? undefined : current.value;\n                if (previousValue !== this.value || this.allowEmptySelection) {\n                    /**\n                     * Value change should only be emitted if the value is different,\n                     * such as selecting a new radio with the space bar or if\n                     * the radio group allows for empty selection and the user\n                     * is deselecting a checked radio.\n                     */\n                    this.emitValueChange(ev);\n                }\n                // Prevent browsers from jumping\n                // to the bottom of the screen\n                ev.preventDefault();\n            }\n        }\n    }\n    /** @internal */\n    async setFocus() {\n        const radioToFocus = this.getRadios().find((r) => r.tabIndex !== -1);\n        radioToFocus === null || radioToFocus === void 0 ? void 0 : radioToFocus.setFocus();\n    }\n    /**\n     * Renders the helper text or error text values\n     */\n    renderHintText() {\n        const { helperText, errorText, helperTextId, errorTextId } = this;\n        const hasHintText = !!helperText || !!errorText;\n        if (!hasHintText) {\n            return;\n        }\n        return (h(\"div\", { class: \"radio-group-top\" }, h(\"div\", { id: helperTextId, class: \"helper-text\" }, helperText), h(\"div\", { id: errorTextId, class: \"error-text\" }, errorText)));\n    }\n    getHintTextID() {\n        const { el, helperText, errorText, helperTextId, errorTextId } = this;\n        if (el.classList.contains('ion-touched') && el.classList.contains('ion-invalid') && errorText) {\n            return errorTextId;\n        }\n        if (helperText) {\n            return helperTextId;\n        }\n        return undefined;\n    }\n    render() {\n        const { label, labelId, el, name, value } = this;\n        const mode = getIonMode(this);\n        renderHiddenInput(true, el, name, value, false);\n        return (h(Host, { key: '81b8ebc96b2f383c36717f290d2959cc921ad6e8', role: \"radiogroup\", \"aria-labelledby\": label ? labelId : null, \"aria-describedby\": this.getHintTextID(), \"aria-invalid\": this.getHintTextID() === this.errorTextId, onClick: this.onClick, class: mode }, this.renderHintText(), h(\"div\", { key: '45b09efc10776b889a8f372cba80d25a3fc849da', class: \"radio-group-wrapper\" }, h(\"slot\", { key: '58714934542c2fdd7396de160364f3f06b32e8f8' }))));\n    }\n    get el() { return getElement(this); }\n    static get watchers() { return {\n        \"value\": [\"valueChanged\"]\n    }; }\n};\nlet radioGroupIds = 0;\nRadioGroup.style = {\n    ios: radioGroupIosCss,\n    md: radioGroupMdCss\n};\n\nexport { Radio as ion_radio, RadioGroup as ion_radio_group };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;AAQA,IAAM,cAAc;AAEpB,IAAM,aAAa;AAEnB,IAAM,QAAQ,MAAM;AAAA,EAChB,YAAY,SAAS;AACjB,qBAAiB,MAAM,OAAO;AAC9B,SAAK,WAAW,YAAY,MAAM,YAAY,CAAC;AAC/C,SAAK,UAAU,YAAY,MAAM,WAAW,CAAC;AAC7C,SAAK,UAAU,UAAU,gBAAgB;AACzC,SAAK,aAAa;AAIlB,SAAK,UAAU;AAKf,SAAK,iBAAiB;AAItB,SAAK,OAAO,KAAK;AAIjB,SAAK,WAAW;AAQhB,SAAK,iBAAiB;AACtB,SAAK,cAAc,MAAM;AACrB,UAAI,KAAK,YAAY;AACjB,cAAM,EAAE,aAAa,OAAO,gBAAgB,IAAI,KAAK;AACrD,aAAK,UAAU,iBAAiB,iBAAiB,KAAK,OAAO,WAAW;AAAA,MAC5E;AAAA,IACJ;AACA,SAAK,UAAU,MAAM;AACjB,YAAM,EAAE,YAAY,SAAS,SAAS,IAAI;AAC1C,UAAI,UAAU;AACV;AAAA,MACJ;AAUA,UAAI,YAAY,eAAe,QAAQ,eAAe,SAAS,SAAS,WAAW,sBAAsB;AACrG,aAAK,UAAU;AAAA,MACnB,OACK;AACD,aAAK,UAAU;AAAA,MACnB;AAAA,IACJ;AACA,SAAK,UAAU,MAAM;AACjB,WAAK,SAAS,KAAK;AAAA,IACvB;AACA,SAAK,SAAS,MAAM;AAChB,WAAK,QAAQ,KAAK;AAAA,IACtB;AAAA,EACJ;AAAA,EACA,eAAe;AAMX,SAAK,YAAY;AAAA,EACrB;AAAA,EACA,mBAAmB;AASf,SAAK,YAAY;AAAA,EACrB;AAAA;AAAA,EAEM,SAAS,IAAI;AAAA;AACf,UAAI,OAAO,QAAW;AAClB,WAAG,gBAAgB;AACnB,WAAG,eAAe;AAAA,MACtB;AACA,WAAK,GAAG,MAAM;AAAA,IAClB;AAAA;AAAA;AAAA,EAEM,kBAAkB,OAAO;AAAA;AAC3B,WAAK,iBAAiB;AAAA,IAC1B;AAAA;AAAA,EACA,oBAAoB;AAChB,QAAI,KAAK,UAAU,QAAW;AAC1B,WAAK,QAAQ,KAAK;AAAA,IACtB;AACA,UAAM,aAAc,KAAK,aAAa,KAAK,GAAG,QAAQ,iBAAiB;AACvE,QAAI,YAAY;AACZ,WAAK,YAAY;AACjB,uBAAiB,YAAY,kBAAkB,KAAK,WAAW;AAAA,IACnE;AAAA,EACJ;AAAA,EACA,uBAAuB;AACnB,UAAM,aAAa,KAAK;AACxB,QAAI,YAAY;AACZ,0BAAoB,YAAY,kBAAkB,KAAK,WAAW;AAClE,WAAK,aAAa;AAAA,IACtB;AAAA,EACJ;AAAA,EACA,IAAI,WAAW;AACX,WAAO,KAAK,GAAG,gBAAgB;AAAA,EACnC;AAAA,EACA,qBAAqB;AACjB,WAAQ,EAAE,OAAO,EAAE,OAAO,cAAc,MAAM,YAAY,GAAG,EAAE,OAAO,EAAE,OAAO,eAAe,MAAM,OAAO,CAAC,GAAG,EAAE,OAAO,EAAE,OAAO,eAAe,CAAC,CAAC;AAAA,EACtJ;AAAA,EACA,SAAS;AACL,UAAM,EAAE,SAAS,UAAU,OAAO,IAAI,SAAS,gBAAgB,UAAU,gBAAgB,UAAU,IAAI;AACvG,UAAM,OAAO,WAAW,IAAI;AAC5B,UAAM,SAAS,YAAY,YAAY,EAAE;AACzC,WAAQ,EAAE,MAAM,EAAE,KAAK,4CAA4C,SAAS,KAAK,SAAS,QAAQ,KAAK,QAAQ,SAAS,KAAK,SAAS,OAAO,mBAAmB,OAAO;AAAA,MAC/J,CAAC,IAAI,GAAG;AAAA,MACR,WAAW;AAAA,MACX,iBAAiB;AAAA,MACjB,kBAAkB;AAAA,MAClB,CAAC,iBAAiB,OAAO,EAAE,GAAG,YAAY;AAAA,MAC1C,CAAC,mBAAmB,SAAS,EAAE,GAAG,cAAc;AAAA,MAChD,CAAC,yBAAyB,cAAc,EAAE,GAAG;AAAA;AAAA,MAE7C,mBAAmB,CAAC;AAAA,MACpB,iBAAiB,CAAC;AAAA,IACtB,CAAC,GAAG,MAAM,SAAS,gBAAgB,UAAU,SAAS,SAAS,iBAAiB,WAAW,SAAS,MAAM,UAAU,eAAe,GAAG,EAAE,SAAS,EAAE,KAAK,4CAA4C,OAAO,gBAAgB,GAAG,EAAE,OAAO,EAAE,KAAK,4CAA4C,OAAO;AAAA,MAC7R,sBAAsB;AAAA,MACtB,6BAA6B,CAAC;AAAA,IAClC,GAAG,MAAM,QAAQ,GAAG,EAAE,QAAQ,EAAE,KAAK,2CAA2C,CAAC,CAAC,GAAG,EAAE,OAAO,EAAE,KAAK,4CAA4C,OAAO,iBAAiB,GAAG,KAAK,mBAAmB,CAAC,CAAC,CAAC;AAAA,EAC/M;AAAA,EACA,IAAI,KAAK;AAAE,WAAO,WAAW,IAAI;AAAA,EAAG;AAAA,EACpC,WAAW,WAAW;AAAE,WAAO;AAAA,MAC3B,SAAS,CAAC,cAAc;AAAA,IAC5B;AAAA,EAAG;AACP;AACA,IAAI,iBAAiB;AACrB,MAAM,QAAQ;AAAA,EACV,KAAK;AAAA,EACL,IAAI;AACR;AAEA,IAAM,mBAAmB;AAEzB,IAAM,kBAAkB;AAExB,IAAM,aAAa,MAAM;AAAA,EACrB,YAAY,SAAS;AACjB,qBAAiB,MAAM,OAAO;AAC9B,SAAK,YAAY,YAAY,MAAM,aAAa,CAAC;AACjD,SAAK,iBAAiB,YAAY,MAAM,kBAAkB,CAAC;AAC3D,SAAK,UAAU,UAAU,eAAe;AACxC,SAAK,eAAe,GAAG,KAAK,OAAO;AACnC,SAAK,cAAc,GAAG,KAAK,OAAO;AAClC,SAAK,UAAU,GAAG,KAAK,OAAO;AAI9B,SAAK,sBAAsB;AAI3B,SAAK,OAAO,KAAK;AACjB,SAAK,mBAAmB,CAAC,UAAU;AAC/B,YAAM,SAAS,KAAK,UAAU;AAE9B,YAAM,QAAQ,OAAO,KAAK,CAAC,UAAU,CAAC,MAAM,QAAQ;AACpD,YAAM,UAAU,OAAO,KAAK,CAAC,UAAU,MAAM,UAAU,SAAS,CAAC,MAAM,QAAQ;AAC/E,UAAI,CAAC,SAAS,CAAC,SAAS;AACpB;AAAA,MACJ;AAGA,YAAM,YAAY,WAAW;AAC7B,iBAAW,SAAS,QAAQ;AACxB,cAAM,WAAW,UAAU,YAAY,IAAI;AAC3C,cAAM,kBAAkB,QAAQ;AAAA,MACpC;AAAA,IACJ;AACA,SAAK,UAAU,CAAC,OAAO;AACnB,SAAG,eAAe;AAOlB,YAAM,gBAAgB,GAAG,UAAU,GAAG,OAAO,QAAQ,WAAW;AAShE,UAAI,iBAAiB,CAAC,cAAc,UAAU;AAC1C,cAAM,eAAe,KAAK;AAC1B,cAAM,WAAW,cAAc;AAC/B,YAAI,aAAa,cAAc;AAC3B,eAAK,QAAQ;AACb,eAAK,gBAAgB,EAAE;AAAA,QAC3B,WACS,KAAK,qBAAqB;AAC/B,eAAK,QAAQ;AACb,eAAK,gBAAgB,EAAE;AAAA,QAC3B;AAAA,MACJ;AAAA,IACJ;AAAA,EACJ;AAAA,EACA,aAAa,OAAO;AAChB,SAAK,iBAAiB,KAAK;AAC3B,SAAK,eAAe,KAAK,EAAE,MAAM,CAAC;AAAA,EACtC;AAAA,EACA,mBAAmB;AAUf,SAAK,aAAa,KAAK,KAAK;AAAA,EAChC;AAAA,EACM,oBAAoB;AAAA;AAGtB,YAAM,SAAS,KAAK,GAAG,cAAc,iBAAiB,KAAK,KAAK,GAAG,cAAc,kBAAkB;AACnG,UAAI,QAAQ;AACR,cAAM,QAAS,KAAK,QAAQ,OAAO,cAAc,WAAW;AAC5D,YAAI,OAAO;AACP,eAAK,UAAU,MAAM,KAAK,KAAK,OAAO;AAAA,QAC1C;AAAA,MACJ;AAAA,IACJ;AAAA;AAAA,EACA,YAAY;AACR,WAAO,MAAM,KAAK,KAAK,GAAG,iBAAiB,WAAW,CAAC;AAAA,EAC3D;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,gBAAgB,OAAO;AACnB,UAAM,EAAE,MAAM,IAAI;AAClB,SAAK,UAAU,KAAK,EAAE,OAAO,MAAM,CAAC;AAAA,EACxC;AAAA,EACA,UAAU,IAAI;AAGV,UAAM,oBAAoB,CAAC,CAAC,KAAK,GAAG,QAAQ,oBAAoB,KAAK,CAAC,CAAC,KAAK,GAAG,QAAQ,kBAAkB;AACzG,QAAI,GAAG,UAAU,CAAC,KAAK,GAAG,SAAS,GAAG,MAAM,GAAG;AAC3C;AAAA,IACJ;AAGA,UAAM,SAAS,KAAK,UAAU,EAAE,OAAO,CAAC,UAAU,CAAC,MAAM,QAAQ;AAEjE,QAAI,GAAG,UAAU,OAAO,SAAS,GAAG,MAAM,GAAG;AACzC,YAAM,QAAQ,OAAO,UAAU,CAAC,UAAU,UAAU,GAAG,MAAM;AAC7D,YAAM,UAAU,OAAO,KAAK;AAC5B,UAAI;AAGJ,UAAI,CAAC,aAAa,YAAY,EAAE,SAAS,GAAG,GAAG,GAAG;AAC9C,eAAO,UAAU,OAAO,SAAS,IAAI,OAAO,CAAC,IAAI,OAAO,QAAQ,CAAC;AAAA,MACrE;AAGA,UAAI,CAAC,WAAW,WAAW,EAAE,SAAS,GAAG,GAAG,GAAG;AAC3C,eAAO,UAAU,IAAI,OAAO,OAAO,SAAS,CAAC,IAAI,OAAO,QAAQ,CAAC;AAAA,MACrE;AACA,UAAI,QAAQ,OAAO,SAAS,IAAI,GAAG;AAC/B,aAAK,SAAS,EAAE;AAChB,YAAI,CAAC,mBAAmB;AACpB,eAAK,QAAQ,KAAK;AAClB,eAAK,gBAAgB,EAAE;AAAA,QAC3B;AAAA,MACJ;AAGA,UAAI,CAAC,GAAG,EAAE,SAAS,GAAG,GAAG,GAAG;AACxB,cAAM,gBAAgB,KAAK;AAC3B,aAAK,QAAQ,KAAK,uBAAuB,KAAK,UAAU,SAAY,SAAY,QAAQ;AACxF,YAAI,kBAAkB,KAAK,SAAS,KAAK,qBAAqB;AAO1D,eAAK,gBAAgB,EAAE;AAAA,QAC3B;AAGA,WAAG,eAAe;AAAA,MACtB;AAAA,IACJ;AAAA,EACJ;AAAA;AAAA,EAEM,WAAW;AAAA;AACb,YAAM,eAAe,KAAK,UAAU,EAAE,KAAK,CAAC,MAAM,EAAE,aAAa,EAAE;AACnE,uBAAiB,QAAQ,iBAAiB,SAAS,SAAS,aAAa,SAAS;AAAA,IACtF;AAAA;AAAA;AAAA;AAAA;AAAA,EAIA,iBAAiB;AACb,UAAM,EAAE,YAAY,WAAW,cAAc,YAAY,IAAI;AAC7D,UAAM,cAAc,CAAC,CAAC,cAAc,CAAC,CAAC;AACtC,QAAI,CAAC,aAAa;AACd;AAAA,IACJ;AACA,WAAQ,EAAE,OAAO,EAAE,OAAO,kBAAkB,GAAG,EAAE,OAAO,EAAE,IAAI,cAAc,OAAO,cAAc,GAAG,UAAU,GAAG,EAAE,OAAO,EAAE,IAAI,aAAa,OAAO,aAAa,GAAG,SAAS,CAAC;AAAA,EAClL;AAAA,EACA,gBAAgB;AACZ,UAAM,EAAE,IAAI,YAAY,WAAW,cAAc,YAAY,IAAI;AACjE,QAAI,GAAG,UAAU,SAAS,aAAa,KAAK,GAAG,UAAU,SAAS,aAAa,KAAK,WAAW;AAC3F,aAAO;AAAA,IACX;AACA,QAAI,YAAY;AACZ,aAAO;AAAA,IACX;AACA,WAAO;AAAA,EACX;AAAA,EACA,SAAS;AACL,UAAM,EAAE,OAAO,SAAS,IAAI,MAAM,MAAM,IAAI;AAC5C,UAAM,OAAO,WAAW,IAAI;AAC5B,sBAAkB,MAAM,IAAI,MAAM,OAAO,KAAK;AAC9C,WAAQ,EAAE,MAAM,EAAE,KAAK,4CAA4C,MAAM,cAAc,mBAAmB,QAAQ,UAAU,MAAM,oBAAoB,KAAK,cAAc,GAAG,gBAAgB,KAAK,cAAc,MAAM,KAAK,aAAa,SAAS,KAAK,SAAS,OAAO,KAAK,GAAG,KAAK,eAAe,GAAG,EAAE,OAAO,EAAE,KAAK,4CAA4C,OAAO,sBAAsB,GAAG,EAAE,QAAQ,EAAE,KAAK,2CAA2C,CAAC,CAAC,CAAC;AAAA,EACnc;AAAA,EACA,IAAI,KAAK;AAAE,WAAO,WAAW,IAAI;AAAA,EAAG;AAAA,EACpC,WAAW,WAAW;AAAE,WAAO;AAAA,MAC3B,SAAS,CAAC,cAAc;AAAA,IAC5B;AAAA,EAAG;AACP;AACA,IAAI,gBAAgB;AACpB,WAAW,QAAQ;AAAA,EACf,KAAK;AAAA,EACL,IAAI;AACR;", "names": []}