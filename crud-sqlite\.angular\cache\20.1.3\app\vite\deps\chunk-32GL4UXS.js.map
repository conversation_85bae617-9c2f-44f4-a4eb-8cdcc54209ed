{"version": 3, "sources": ["../../../../../../node_modules/@ionic/core/dist/esm/haptic-DzAMWJuk.js"], "sourcesContent": ["/*!\n * (C) Ionic http://ionicframework.com - MIT License\n */\nimport { g as getCapacitor } from './capacitor-CFERIeaU.js';\n\nvar ImpactStyle;\n(function (ImpactStyle) {\n    /**\n     * A collision between large, heavy user interface elements\n     *\n     * @since 1.0.0\n     */\n    ImpactStyle[\"Heavy\"] = \"HEAVY\";\n    /**\n     * A collision between moderately sized user interface elements\n     *\n     * @since 1.0.0\n     */\n    ImpactStyle[\"Medium\"] = \"MEDIUM\";\n    /**\n     * A collision between small, light user interface elements\n     *\n     * @since 1.0.0\n     */\n    ImpactStyle[\"Light\"] = \"LIGHT\";\n})(ImpactStyle || (ImpactStyle = {}));\nvar NotificationType;\n(function (NotificationType) {\n    /**\n     * A notification feedback type indicating that a task has completed successfully\n     *\n     * @since 1.0.0\n     */\n    NotificationType[\"Success\"] = \"SUCCESS\";\n    /**\n     * A notification feedback type indicating that a task has produced a warning\n     *\n     * @since 1.0.0\n     */\n    NotificationType[\"Warning\"] = \"WARNING\";\n    /**\n     * A notification feedback type indicating that a task has failed\n     *\n     * @since 1.0.0\n     */\n    NotificationType[\"Error\"] = \"ERROR\";\n})(NotificationType || (NotificationType = {}));\nconst HapticEngine = {\n    getEngine() {\n        const capacitor = getCapacitor();\n        if (capacitor === null || capacitor === void 0 ? void 0 : capacitor.isPluginAvailable('Haptics')) {\n            // Capacitor\n            return capacitor.Plugins.Haptics;\n        }\n        return undefined;\n    },\n    available() {\n        const engine = this.getEngine();\n        if (!engine) {\n            return false;\n        }\n        const capacitor = getCapacitor();\n        /**\n         * Developers can manually import the\n         * Haptics plugin in their app which will cause\n         * getEngine to return the Haptics engine. However,\n         * the Haptics engine will throw an error if\n         * used in a web browser that does not support\n         * the Vibrate API. This check avoids that error\n         * if the browser does not support the Vibrate API.\n         */\n        if ((capacitor === null || capacitor === void 0 ? void 0 : capacitor.getPlatform()) === 'web') {\n            // eslint-disable-next-line @typescript-eslint/prefer-optional-chain\n            return typeof navigator !== 'undefined' && navigator.vibrate !== undefined;\n        }\n        return true;\n    },\n    impact(options) {\n        const engine = this.getEngine();\n        if (!engine) {\n            return;\n        }\n        engine.impact({ style: options.style });\n    },\n    notification(options) {\n        const engine = this.getEngine();\n        if (!engine) {\n            return;\n        }\n        engine.notification({ type: options.type });\n    },\n    selection() {\n        this.impact({ style: ImpactStyle.Light });\n    },\n    selectionStart() {\n        const engine = this.getEngine();\n        if (!engine) {\n            return;\n        }\n        engine.selectionStart();\n    },\n    selectionChanged() {\n        const engine = this.getEngine();\n        if (!engine) {\n            return;\n        }\n        engine.selectionChanged();\n    },\n    selectionEnd() {\n        const engine = this.getEngine();\n        if (!engine) {\n            return;\n        }\n        engine.selectionEnd();\n    },\n};\n/**\n * Check to see if the Haptic Plugin is available\n * @return Returns `true` or false if the plugin is available\n */\nconst hapticAvailable = () => {\n    return HapticEngine.available();\n};\n/**\n * Trigger a selection changed haptic event. Good for one-time events\n * (not for gestures)\n */\nconst hapticSelection = () => {\n    hapticAvailable() && HapticEngine.selection();\n};\n/**\n * Tell the haptic engine that a gesture for a selection change is starting.\n */\nconst hapticSelectionStart = () => {\n    hapticAvailable() && HapticEngine.selectionStart();\n};\n/**\n * Tell the haptic engine that a selection changed during a gesture.\n */\nconst hapticSelectionChanged = () => {\n    hapticAvailable() && HapticEngine.selectionChanged();\n};\n/**\n * Tell the haptic engine we are done with a gesture. This needs to be\n * called lest resources are not properly recycled.\n */\nconst hapticSelectionEnd = () => {\n    hapticAvailable() && HapticEngine.selectionEnd();\n};\n/**\n * Use this to indicate success/failure/warning to the user.\n * options should be of the type `{ style: ImpactStyle.LIGHT }` (or `MEDIUM`/`HEAVY`)\n */\nconst hapticImpact = (options) => {\n    hapticAvailable() && HapticEngine.impact(options);\n};\n\nexport { ImpactStyle as I, hapticSelectionChanged as a, hapticSelectionStart as b, hapticSelection as c, hapticImpact as d, hapticSelectionEnd as h };\n"], "mappings": ";;;;;AAKA,IAAI;AAAA,CACH,SAAUA,cAAa;AAMpB,EAAAA,aAAY,OAAO,IAAI;AAMvB,EAAAA,aAAY,QAAQ,IAAI;AAMxB,EAAAA,aAAY,OAAO,IAAI;AAC3B,GAAG,gBAAgB,cAAc,CAAC,EAAE;AACpC,IAAI;AAAA,CACH,SAAUC,mBAAkB;AAMzB,EAAAA,kBAAiB,SAAS,IAAI;AAM9B,EAAAA,kBAAiB,SAAS,IAAI;AAM9B,EAAAA,kBAAiB,OAAO,IAAI;AAChC,GAAG,qBAAqB,mBAAmB,CAAC,EAAE;AAC9C,IAAM,eAAe;AAAA,EACjB,YAAY;AACR,UAAM,YAAY,aAAa;AAC/B,QAAI,cAAc,QAAQ,cAAc,SAAS,SAAS,UAAU,kBAAkB,SAAS,GAAG;AAE9F,aAAO,UAAU,QAAQ;AAAA,IAC7B;AACA,WAAO;AAAA,EACX;AAAA,EACA,YAAY;AACR,UAAM,SAAS,KAAK,UAAU;AAC9B,QAAI,CAAC,QAAQ;AACT,aAAO;AAAA,IACX;AACA,UAAM,YAAY,aAAa;AAU/B,SAAK,cAAc,QAAQ,cAAc,SAAS,SAAS,UAAU,YAAY,OAAO,OAAO;AAE3F,aAAO,OAAO,cAAc,eAAe,UAAU,YAAY;AAAA,IACrE;AACA,WAAO;AAAA,EACX;AAAA,EACA,OAAO,SAAS;AACZ,UAAM,SAAS,KAAK,UAAU;AAC9B,QAAI,CAAC,QAAQ;AACT;AAAA,IACJ;AACA,WAAO,OAAO,EAAE,OAAO,QAAQ,MAAM,CAAC;AAAA,EAC1C;AAAA,EACA,aAAa,SAAS;AAClB,UAAM,SAAS,KAAK,UAAU;AAC9B,QAAI,CAAC,QAAQ;AACT;AAAA,IACJ;AACA,WAAO,aAAa,EAAE,MAAM,QAAQ,KAAK,CAAC;AAAA,EAC9C;AAAA,EACA,YAAY;AACR,SAAK,OAAO,EAAE,OAAO,YAAY,MAAM,CAAC;AAAA,EAC5C;AAAA,EACA,iBAAiB;AACb,UAAM,SAAS,KAAK,UAAU;AAC9B,QAAI,CAAC,QAAQ;AACT;AAAA,IACJ;AACA,WAAO,eAAe;AAAA,EAC1B;AAAA,EACA,mBAAmB;AACf,UAAM,SAAS,KAAK,UAAU;AAC9B,QAAI,CAAC,QAAQ;AACT;AAAA,IACJ;AACA,WAAO,iBAAiB;AAAA,EAC5B;AAAA,EACA,eAAe;AACX,UAAM,SAAS,KAAK,UAAU;AAC9B,QAAI,CAAC,QAAQ;AACT;AAAA,IACJ;AACA,WAAO,aAAa;AAAA,EACxB;AACJ;AAKA,IAAM,kBAAkB,MAAM;AAC1B,SAAO,aAAa,UAAU;AAClC;AAKA,IAAM,kBAAkB,MAAM;AAC1B,kBAAgB,KAAK,aAAa,UAAU;AAChD;AAIA,IAAM,uBAAuB,MAAM;AAC/B,kBAAgB,KAAK,aAAa,eAAe;AACrD;AAIA,IAAM,yBAAyB,MAAM;AACjC,kBAAgB,KAAK,aAAa,iBAAiB;AACvD;AAKA,IAAM,qBAAqB,MAAM;AAC7B,kBAAgB,KAAK,aAAa,aAAa;AACnD;AAKA,IAAM,eAAe,CAAC,YAAY;AAC9B,kBAAgB,KAAK,aAAa,OAAO,OAAO;AACpD;", "names": ["ImpactStyle", "NotificationType"]}