{"version": 3, "sources": ["../../../../../../node_modules/@ionic/core/dist/esm/ion-input-otp.entry.js"], "sourcesContent": ["/*!\n * (C) Ionic http://ionicframework.com - MIT License\n */\nimport { r as registerInstance, d as createEvent, m as printIonWarning, e as getIonMode, h, F as Fragment, j as Host, k as getElement } from './index-B_U9CtaY.js';\nimport { i as inheritAriaAttributes } from './helpers-1O4D2b7y.js';\nimport { i as isRTL } from './dir-C53feagD.js';\nimport { c as createColorClasses } from './theme-DiVJyqlX.js';\n\nconst inputOtpIosCss = \".sc-ion-input-otp-ios-h{--margin-top:0;--margin-end:0;--margin-bottom:0;--margin-start:0;--padding-top:16px;--padding-end:0;--padding-bottom:16px;--padding-start:0;--color:initial;--min-width:40px;--separator-width:8px;--separator-height:var(--separator-width);--separator-border-radius:999px;--separator-color:var(--ion-color-step-150, var(--ion-background-color-step-150, #d9d9d9));--highlight-color-focused:var(--ion-color-primary, #0054e9);--highlight-color-valid:var(--ion-color-success, #2dd55b);--highlight-color-invalid:var(--ion-color-danger, #c5000f);--highlight-color:var(--highlight-color-focused);display:block;position:relative;font-size:0.875rem}.input-otp-group.sc-ion-input-otp-ios{-webkit-margin-start:var(--margin-start);margin-inline-start:var(--margin-start);-webkit-margin-end:var(--margin-end);margin-inline-end:var(--margin-end);margin-top:var(--margin-top);margin-bottom:var(--margin-bottom);-webkit-padding-start:var(--padding-start);padding-inline-start:var(--padding-start);-webkit-padding-end:var(--padding-end);padding-inline-end:var(--padding-end);padding-top:var(--padding-top);padding-bottom:var(--padding-bottom);display:-ms-flexbox;display:flex;-ms-flex-align:center;align-items:center;-ms-flex-pack:center;justify-content:center}.native-wrapper.sc-ion-input-otp-ios{display:-ms-flexbox;display:flex;-ms-flex-align:center;align-items:center;-ms-flex-pack:center;justify-content:center;min-width:var(--min-width)}.native-input.sc-ion-input-otp-ios{border-radius:var(--border-radius);width:var(--width);min-width:inherit;height:var(--height);border-width:var(--border-width);border-style:solid;border-color:var(--border-color);background:var(--background);color:var(--color);font-size:inherit;text-align:center;-webkit-appearance:none;-moz-appearance:none;appearance:none}.has-focus.sc-ion-input-otp-ios-h .native-input.sc-ion-input-otp-ios{caret-color:var(--highlight-color)}.input-otp-description.sc-ion-input-otp-ios{color:var(--ion-color-step-700, var(--ion-text-color-step-300, #4d4d4d));font-size:0.75rem;line-height:1.25rem;text-align:center}.input-otp-description-hidden.sc-ion-input-otp-ios{display:none}.input-otp-separator.sc-ion-input-otp-ios{border-radius:var(--separator-border-radius);-ms-flex-negative:0;flex-shrink:0;width:var(--separator-width);height:var(--separator-height);background:var(--separator-color)}.input-otp-size-small.sc-ion-input-otp-ios-h{--width:40px;--height:40px}.input-otp-size-small.sc-ion-input-otp-ios-h .input-otp-group.sc-ion-input-otp-ios{gap:8px}.input-otp-size-medium.sc-ion-input-otp-ios-h{--width:48px;--height:48px}.input-otp-size-large.sc-ion-input-otp-ios-h{--width:56px;--height:56px}.input-otp-size-medium.sc-ion-input-otp-ios-h .input-otp-group.sc-ion-input-otp-ios,.input-otp-size-large.sc-ion-input-otp-ios-h .input-otp-group.sc-ion-input-otp-ios{gap:12px}.input-otp-shape-round.sc-ion-input-otp-ios-h{--border-radius:16px}.input-otp-shape-soft.sc-ion-input-otp-ios-h{--border-radius:8px}.input-otp-shape-rectangular.sc-ion-input-otp-ios-h{--border-radius:0}.input-otp-fill-outline.sc-ion-input-otp-ios-h{--background:none}.input-otp-fill-solid.sc-ion-input-otp-ios-h{--border-color:var(--ion-color-step-50, var(--ion-background-color-step-50, #f2f2f2));--background:var(--ion-color-step-50, var(--ion-background-color-step-50, #f2f2f2))}.input-otp-disabled.sc-ion-input-otp-ios-h{--color:var(--ion-color-step-350, var(--ion-text-color-step-650, #a6a6a6))}.input-otp-fill-outline.input-otp-disabled.sc-ion-input-otp-ios-h{--background:var(--ion-color-step-50, var(--ion-background-color-step-50, #f2f2f2));--border-color:var(--ion-color-step-100, var(--ion-background-color-step-100, #e6e6e6))}.input-otp-disabled.sc-ion-input-otp-ios-h,.input-otp-disabled.sc-ion-input-otp-ios-h .native-input.sc-ion-input-otp-ios:disabled{cursor:not-allowed}.has-focus.sc-ion-input-otp-ios-h .native-input.sc-ion-input-otp-ios:focus{--border-color:var(--highlight-color);outline:none}.input-otp-fill-outline.input-otp-readonly.sc-ion-input-otp-ios-h{--background:var(--ion-color-step-50, var(--ion-background-color-step-50, #f2f2f2))}.input-otp-fill-solid.input-otp-disabled.sc-ion-input-otp-ios-h,.input-otp-fill-solid.input-otp-readonly.sc-ion-input-otp-ios-h{--border-color:var(--ion-color-step-100, var(--ion-background-color-step-100, #e6e6e6));--background:var(--ion-color-step-100, var(--ion-background-color-step-100, #e6e6e6))}.ion-touched.ion-invalid.sc-ion-input-otp-ios-h{--highlight-color:var(--highlight-color-invalid)}.ion-valid.sc-ion-input-otp-ios-h{--highlight-color:var(--highlight-color-valid)}.has-focus.ion-valid.sc-ion-input-otp-ios-h,.ion-touched.ion-invalid.sc-ion-input-otp-ios-h{--border-color:var(--highlight-color)}.ion-color.sc-ion-input-otp-ios-h{--highlight-color-focused:var(--ion-color-base)}.input-otp-fill-outline.ion-color.sc-ion-input-otp-ios-h .native-input.sc-ion-input-otp-ios,.input-otp-fill-solid.ion-color.sc-ion-input-otp-ios-h .native-input.sc-ion-input-otp-ios:focus{border-color:rgba(var(--ion-color-base-rgb), 0.6)}.input-otp-fill-outline.ion-color.ion-invalid.sc-ion-input-otp-ios-h .native-input.sc-ion-input-otp-ios,.input-otp-fill-solid.ion-color.ion-invalid.sc-ion-input-otp-ios-h .native-input.sc-ion-input-otp-ios,.input-otp-fill-outline.ion-color.has-focus.ion-invalid.sc-ion-input-otp-ios-h .native-input.sc-ion-input-otp-ios,.input-otp-fill-solid.ion-color.has-focus.ion-invalid.sc-ion-input-otp-ios-h .native-input.sc-ion-input-otp-ios{border-color:var(--ion-color-danger, #c5000f)}.input-otp-fill-outline.ion-color.ion-valid.sc-ion-input-otp-ios-h .native-input.sc-ion-input-otp-ios,.input-otp-fill-solid.ion-color.ion-valid.sc-ion-input-otp-ios-h .native-input.sc-ion-input-otp-ios,.input-otp-fill-outline.ion-color.has-focus.ion-valid.sc-ion-input-otp-ios-h .native-input.sc-ion-input-otp-ios,.input-otp-fill-solid.ion-color.has-focus.ion-valid.sc-ion-input-otp-ios-h .native-input.sc-ion-input-otp-ios{border-color:var(--ion-color-success, #2dd55b)}.input-otp-fill-outline.input-otp-disabled.ion-color.sc-ion-input-otp-ios-h .native-input.sc-ion-input-otp-ios{border-color:rgba(var(--ion-color-base-rgb), 0.3)}.sc-ion-input-otp-ios-h{--border-width:0.55px}.has-focus.sc-ion-input-otp-ios-h .native-input.sc-ion-input-otp-ios:focus{--border-width:1px}.input-otp-fill-outline.sc-ion-input-otp-ios-h{--border-color:var(--ion-item-border-color, var(--ion-border-color, var(--ion-color-step-250, var(--ion-background-color-step-250, #c8c7cc))))}\";\n\nconst inputOtpMdCss = \".sc-ion-input-otp-md-h{--margin-top:0;--margin-end:0;--margin-bottom:0;--margin-start:0;--padding-top:16px;--padding-end:0;--padding-bottom:16px;--padding-start:0;--color:initial;--min-width:40px;--separator-width:8px;--separator-height:var(--separator-width);--separator-border-radius:999px;--separator-color:var(--ion-color-step-150, var(--ion-background-color-step-150, #d9d9d9));--highlight-color-focused:var(--ion-color-primary, #0054e9);--highlight-color-valid:var(--ion-color-success, #2dd55b);--highlight-color-invalid:var(--ion-color-danger, #c5000f);--highlight-color:var(--highlight-color-focused);display:block;position:relative;font-size:0.875rem}.input-otp-group.sc-ion-input-otp-md{-webkit-margin-start:var(--margin-start);margin-inline-start:var(--margin-start);-webkit-margin-end:var(--margin-end);margin-inline-end:var(--margin-end);margin-top:var(--margin-top);margin-bottom:var(--margin-bottom);-webkit-padding-start:var(--padding-start);padding-inline-start:var(--padding-start);-webkit-padding-end:var(--padding-end);padding-inline-end:var(--padding-end);padding-top:var(--padding-top);padding-bottom:var(--padding-bottom);display:-ms-flexbox;display:flex;-ms-flex-align:center;align-items:center;-ms-flex-pack:center;justify-content:center}.native-wrapper.sc-ion-input-otp-md{display:-ms-flexbox;display:flex;-ms-flex-align:center;align-items:center;-ms-flex-pack:center;justify-content:center;min-width:var(--min-width)}.native-input.sc-ion-input-otp-md{border-radius:var(--border-radius);width:var(--width);min-width:inherit;height:var(--height);border-width:var(--border-width);border-style:solid;border-color:var(--border-color);background:var(--background);color:var(--color);font-size:inherit;text-align:center;-webkit-appearance:none;-moz-appearance:none;appearance:none}.has-focus.sc-ion-input-otp-md-h .native-input.sc-ion-input-otp-md{caret-color:var(--highlight-color)}.input-otp-description.sc-ion-input-otp-md{color:var(--ion-color-step-700, var(--ion-text-color-step-300, #4d4d4d));font-size:0.75rem;line-height:1.25rem;text-align:center}.input-otp-description-hidden.sc-ion-input-otp-md{display:none}.input-otp-separator.sc-ion-input-otp-md{border-radius:var(--separator-border-radius);-ms-flex-negative:0;flex-shrink:0;width:var(--separator-width);height:var(--separator-height);background:var(--separator-color)}.input-otp-size-small.sc-ion-input-otp-md-h{--width:40px;--height:40px}.input-otp-size-small.sc-ion-input-otp-md-h .input-otp-group.sc-ion-input-otp-md{gap:8px}.input-otp-size-medium.sc-ion-input-otp-md-h{--width:48px;--height:48px}.input-otp-size-large.sc-ion-input-otp-md-h{--width:56px;--height:56px}.input-otp-size-medium.sc-ion-input-otp-md-h .input-otp-group.sc-ion-input-otp-md,.input-otp-size-large.sc-ion-input-otp-md-h .input-otp-group.sc-ion-input-otp-md{gap:12px}.input-otp-shape-round.sc-ion-input-otp-md-h{--border-radius:16px}.input-otp-shape-soft.sc-ion-input-otp-md-h{--border-radius:8px}.input-otp-shape-rectangular.sc-ion-input-otp-md-h{--border-radius:0}.input-otp-fill-outline.sc-ion-input-otp-md-h{--background:none}.input-otp-fill-solid.sc-ion-input-otp-md-h{--border-color:var(--ion-color-step-50, var(--ion-background-color-step-50, #f2f2f2));--background:var(--ion-color-step-50, var(--ion-background-color-step-50, #f2f2f2))}.input-otp-disabled.sc-ion-input-otp-md-h{--color:var(--ion-color-step-350, var(--ion-text-color-step-650, #a6a6a6))}.input-otp-fill-outline.input-otp-disabled.sc-ion-input-otp-md-h{--background:var(--ion-color-step-50, var(--ion-background-color-step-50, #f2f2f2));--border-color:var(--ion-color-step-100, var(--ion-background-color-step-100, #e6e6e6))}.input-otp-disabled.sc-ion-input-otp-md-h,.input-otp-disabled.sc-ion-input-otp-md-h .native-input.sc-ion-input-otp-md:disabled{cursor:not-allowed}.has-focus.sc-ion-input-otp-md-h .native-input.sc-ion-input-otp-md:focus{--border-color:var(--highlight-color);outline:none}.input-otp-fill-outline.input-otp-readonly.sc-ion-input-otp-md-h{--background:var(--ion-color-step-50, var(--ion-background-color-step-50, #f2f2f2))}.input-otp-fill-solid.input-otp-disabled.sc-ion-input-otp-md-h,.input-otp-fill-solid.input-otp-readonly.sc-ion-input-otp-md-h{--border-color:var(--ion-color-step-100, var(--ion-background-color-step-100, #e6e6e6));--background:var(--ion-color-step-100, var(--ion-background-color-step-100, #e6e6e6))}.ion-touched.ion-invalid.sc-ion-input-otp-md-h{--highlight-color:var(--highlight-color-invalid)}.ion-valid.sc-ion-input-otp-md-h{--highlight-color:var(--highlight-color-valid)}.has-focus.ion-valid.sc-ion-input-otp-md-h,.ion-touched.ion-invalid.sc-ion-input-otp-md-h{--border-color:var(--highlight-color)}.ion-color.sc-ion-input-otp-md-h{--highlight-color-focused:var(--ion-color-base)}.input-otp-fill-outline.ion-color.sc-ion-input-otp-md-h .native-input.sc-ion-input-otp-md,.input-otp-fill-solid.ion-color.sc-ion-input-otp-md-h .native-input.sc-ion-input-otp-md:focus{border-color:rgba(var(--ion-color-base-rgb), 0.6)}.input-otp-fill-outline.ion-color.ion-invalid.sc-ion-input-otp-md-h .native-input.sc-ion-input-otp-md,.input-otp-fill-solid.ion-color.ion-invalid.sc-ion-input-otp-md-h .native-input.sc-ion-input-otp-md,.input-otp-fill-outline.ion-color.has-focus.ion-invalid.sc-ion-input-otp-md-h .native-input.sc-ion-input-otp-md,.input-otp-fill-solid.ion-color.has-focus.ion-invalid.sc-ion-input-otp-md-h .native-input.sc-ion-input-otp-md{border-color:var(--ion-color-danger, #c5000f)}.input-otp-fill-outline.ion-color.ion-valid.sc-ion-input-otp-md-h .native-input.sc-ion-input-otp-md,.input-otp-fill-solid.ion-color.ion-valid.sc-ion-input-otp-md-h .native-input.sc-ion-input-otp-md,.input-otp-fill-outline.ion-color.has-focus.ion-valid.sc-ion-input-otp-md-h .native-input.sc-ion-input-otp-md,.input-otp-fill-solid.ion-color.has-focus.ion-valid.sc-ion-input-otp-md-h .native-input.sc-ion-input-otp-md{border-color:var(--ion-color-success, #2dd55b)}.input-otp-fill-outline.input-otp-disabled.ion-color.sc-ion-input-otp-md-h .native-input.sc-ion-input-otp-md{border-color:rgba(var(--ion-color-base-rgb), 0.3)}.sc-ion-input-otp-md-h{--border-width:1px}.has-focus.sc-ion-input-otp-md-h .native-input.sc-ion-input-otp-md:focus{--border-width:2px}.input-otp-fill-outline.sc-ion-input-otp-md-h{--border-color:var(--ion-color-step-300, var(--ion-background-color-step-300, #b3b3b3))}\";\n\nconst InputOTP = class {\n    constructor(hostRef) {\n        registerInstance(this, hostRef);\n        this.ionInput = createEvent(this, \"ionInput\", 7);\n        this.ionChange = createEvent(this, \"ionChange\", 7);\n        this.ionComplete = createEvent(this, \"ionComplete\", 7);\n        this.ionBlur = createEvent(this, \"ionBlur\", 7);\n        this.ionFocus = createEvent(this, \"ionFocus\", 7);\n        this.inheritedAttributes = {};\n        this.inputRefs = [];\n        this.inputId = `ion-input-otp-${inputIds++}`;\n        this.parsedSeparators = [];\n        /**\n         * Tracks whether the user is navigating through input boxes using keyboard navigation\n         * (arrow keys, tab) versus mouse clicks. This is used to determine the appropriate\n         * focus behavior when an input box is focused.\n         */\n        this.isKeyboardNavigation = false;\n        this.inputValues = [];\n        this.hasFocus = false;\n        this.previousInputValues = [];\n        /**\n         * Indicates whether and how the text value should be automatically capitalized as it is entered/edited by the user.\n         * Available options: `\"off\"`, `\"none\"`, `\"on\"`, `\"sentences\"`, `\"words\"`, `\"characters\"`.\n         */\n        this.autocapitalize = 'off';\n        /**\n         * If `true`, the user cannot interact with the input.\n         */\n        this.disabled = false;\n        /**\n         * The fill for the input boxes. If `\"solid\"` the input boxes will have a background. If\n         * `\"outline\"` the input boxes will be transparent with a border.\n         */\n        this.fill = 'outline';\n        /**\n         * The number of input boxes to display.\n         */\n        this.length = 4;\n        /**\n         * If `true`, the user cannot modify the value.\n         */\n        this.readonly = false;\n        /**\n         * The shape of the input boxes.\n         * If \"round\" they will have an increased border radius.\n         * If \"rectangular\" they will have no border radius.\n         * If \"soft\" they will have a soft border radius.\n         */\n        this.shape = 'round';\n        /**\n         * The size of the input boxes.\n         */\n        this.size = 'medium';\n        /**\n         * The type of input allowed in the input boxes.\n         */\n        this.type = 'number';\n        /**\n         * The value of the input group.\n         */\n        this.value = '';\n        /**\n         * Handles the focus behavior for the input OTP component.\n         *\n         * Focus behavior:\n         * 1. Keyboard navigation: Allow normal focus movement\n         * 2. Mouse click:\n         *    - If clicked box has value: Focus that box\n         *    - If clicked box is empty: Focus first empty box\n         *\n         * Emits the `ionFocus` event when the input group gains focus.\n         */\n        this.onFocus = (index) => (event) => {\n            var _a;\n            const { inputRefs } = this;\n            // Only emit ionFocus and set the focusedValue when the\n            // component first gains focus\n            if (!this.hasFocus) {\n                this.ionFocus.emit(event);\n                this.focusedValue = this.value;\n            }\n            this.hasFocus = true;\n            let finalIndex = index;\n            if (!this.isKeyboardNavigation) {\n                // If the clicked box has a value, focus it\n                // Otherwise focus the first empty box\n                const targetIndex = this.inputValues[index] ? index : this.getFirstEmptyIndex();\n                finalIndex = targetIndex === -1 ? this.length - 1 : targetIndex;\n                // Focus the target box\n                (_a = this.inputRefs[finalIndex]) === null || _a === void 0 ? void 0 : _a.focus();\n            }\n            // Update tabIndexes to match the focused box\n            inputRefs.forEach((input, i) => {\n                input.tabIndex = i === finalIndex ? 0 : -1;\n            });\n            // Reset the keyboard navigation flag\n            this.isKeyboardNavigation = false;\n        };\n        /**\n         * Handles the blur behavior for the input OTP component.\n         * Emits the `ionBlur` event when the input group loses focus.\n         */\n        this.onBlur = (event) => {\n            const { inputRefs } = this;\n            const relatedTarget = event.relatedTarget;\n            // Do not emit blur if we're moving to another input box in the same component\n            const isInternalFocus = relatedTarget != null && inputRefs.includes(relatedTarget);\n            if (!isInternalFocus) {\n                this.hasFocus = false;\n                // Reset tabIndexes when focus leaves the component\n                this.updateTabIndexes();\n                // Always emit ionBlur when focus leaves the component\n                this.ionBlur.emit(event);\n                // Only emit ionChange if the value has actually changed\n                if (this.focusedValue !== this.value) {\n                    this.emitIonChange(event);\n                }\n            }\n        };\n        /**\n         * Handles keyboard navigation for the OTP component.\n         *\n         * Navigation:\n         * - Backspace: Clears current input and moves to previous box if empty\n         * - Arrow Left/Right: Moves focus between input boxes\n         * - Tab: Allows normal tab navigation between components\n         */\n        this.onKeyDown = (index) => (event) => {\n            const { length } = this;\n            const rtl = isRTL(this.el);\n            const input = event.target;\n            // Meta shortcuts are used to copy, paste, and select text\n            // We don't want to handle these keys here\n            const metaShortcuts = ['a', 'c', 'v', 'x', 'r', 'z', 'y'];\n            const isTextSelection = input.selectionStart !== input.selectionEnd;\n            // Return if the key is a meta shortcut or the input value\n            // text is selected and let the onPaste / onInput handler manage it\n            if (isTextSelection || ((event.metaKey || event.ctrlKey) && metaShortcuts.includes(event.key.toLowerCase()))) {\n                return;\n            }\n            if (event.key === 'Backspace') {\n                if (this.inputValues[index]) {\n                    // Shift all values to the right of the current index left by one\n                    for (let i = index; i < length - 1; i++) {\n                        this.inputValues[i] = this.inputValues[i + 1];\n                    }\n                    // Clear the last box\n                    this.inputValues[length - 1] = '';\n                    // Update all inputRefs to match inputValues\n                    for (let i = 0; i < length; i++) {\n                        this.inputRefs[i].value = this.inputValues[i] || '';\n                    }\n                    this.updateValue(event);\n                    event.preventDefault();\n                }\n                else if (!this.inputValues[index] && index > 0) {\n                    // If current input is empty, move to previous input\n                    this.focusPrevious(index);\n                }\n            }\n            else if (event.key === 'ArrowLeft' || event.key === 'ArrowRight') {\n                this.isKeyboardNavigation = true;\n                event.preventDefault();\n                const isLeft = event.key === 'ArrowLeft';\n                const shouldMoveNext = (isLeft && rtl) || (!isLeft && !rtl);\n                // Only allow moving to the next input if the current has a value\n                if (shouldMoveNext) {\n                    if (this.inputValues[index] && index < length - 1) {\n                        this.focusNext(index);\n                    }\n                }\n                else {\n                    this.focusPrevious(index);\n                }\n            }\n            else if (event.key === 'Tab') {\n                this.isKeyboardNavigation = true;\n                // Let all tab events proceed normally\n                return;\n            }\n        };\n        /**\n         * Processes all input scenarios for each input box.\n         *\n         * This function manages:\n         * 1. Autofill handling\n         * 2. Input validation\n         * 3. Full selection replacement or typing in an empty box\n         * 4. Inserting in the middle with available space (shifting)\n         * 5. Single character replacement\n         */\n        this.onInput = (index) => (event) => {\n            var _a, _b;\n            const { length, validKeyPattern } = this;\n            const input = event.target;\n            const value = input.value;\n            const previousValue = this.previousInputValues[index] || '';\n            // 1. Autofill handling\n            // If the length of the value increases by more than 1 from the previous\n            // value, treat this as autofill. This is to prevent the case where the\n            // user is typing a single character into an input box containing a value\n            // as that will trigger this function with a value length of 2 characters.\n            const isAutofill = value.length - previousValue.length > 1;\n            if (isAutofill) {\n                // Distribute valid characters across input boxes\n                const validChars = value\n                    .split('')\n                    .filter((char) => validKeyPattern.test(char))\n                    .slice(0, length);\n                // If there are no valid characters coming from the\n                // autofill, all input refs have to be cleared after the\n                // browser has finished the autofill behavior\n                if (validChars.length === 0) {\n                    requestAnimationFrame(() => {\n                        this.inputRefs.forEach((input) => {\n                            input.value = '';\n                        });\n                    });\n                }\n                for (let i = 0; i < length; i++) {\n                    this.inputValues[i] = validChars[i] || '';\n                    this.inputRefs[i].value = validChars[i] || '';\n                }\n                this.updateValue(event);\n                // Focus the first empty input box or the last input box if all boxes\n                // are filled after a small delay to ensure the input boxes have been\n                // updated before moving the focus\n                setTimeout(() => {\n                    var _a;\n                    const nextIndex = validChars.length < length ? validChars.length : length - 1;\n                    (_a = this.inputRefs[nextIndex]) === null || _a === void 0 ? void 0 : _a.focus();\n                }, 20);\n                this.previousInputValues = [...this.inputValues];\n                return;\n            }\n            // 2. Input validation\n            // If the character entered is invalid (does not match the pattern),\n            // restore the previous value and exit\n            if (value.length > 0 && !validKeyPattern.test(value[value.length - 1])) {\n                input.value = this.inputValues[index] || '';\n                this.previousInputValues = [...this.inputValues];\n                return;\n            }\n            // 3. Full selection replacement or typing in an empty box\n            // If the user selects all text in the input box and types, or if the\n            // input box is empty, replace only this input box. If the box is empty,\n            // move to the next box, otherwise stay focused on this box.\n            const isAllSelected = input.selectionStart === 0 && input.selectionEnd === value.length;\n            const isEmpty = !this.inputValues[index];\n            if (isAllSelected || isEmpty) {\n                this.inputValues[index] = value;\n                input.value = value;\n                this.updateValue(event);\n                this.focusNext(index);\n                this.previousInputValues = [...this.inputValues];\n                return;\n            }\n            // 4. Inserting in the middle with available space (shifting)\n            // If typing in a filled input box and there are empty boxes at the end,\n            // shift all values starting at the current box to the right, and insert\n            // the new character at the current box.\n            const hasAvailableBoxAtEnd = this.inputValues[this.inputValues.length - 1] === '';\n            if (this.inputValues[index] && hasAvailableBoxAtEnd && value.length === 2) {\n                // Get the inserted character (from event or by diffing value/previousValue)\n                let newChar = event.data;\n                if (!newChar) {\n                    newChar = value.split('').find((c, i) => c !== previousValue[i]) || value[value.length - 1];\n                }\n                // Validate the new character before shifting\n                if (!validKeyPattern.test(newChar)) {\n                    input.value = this.inputValues[index] || '';\n                    this.previousInputValues = [...this.inputValues];\n                    return;\n                }\n                // Shift values right from the end to the insertion point\n                for (let i = this.inputValues.length - 1; i > index; i--) {\n                    this.inputValues[i] = this.inputValues[i - 1];\n                    this.inputRefs[i].value = this.inputValues[i] || '';\n                }\n                this.inputValues[index] = newChar;\n                this.inputRefs[index].value = newChar;\n                this.updateValue(event);\n                this.previousInputValues = [...this.inputValues];\n                return;\n            }\n            // 5. Single character replacement\n            // Handles replacing a single character in a box containing a value based\n            // on the cursor position. We need the cursor position to determine which\n            // character was the last character typed. For example, if the user types \"2\"\n            // in an input box with the cursor at the beginning of the value of \"6\",\n            // the value will be \"26\", but we want to grab the \"2\" as the last character\n            // typed.\n            const cursorPos = (_a = input.selectionStart) !== null && _a !== void 0 ? _a : value.length;\n            const newCharIndex = cursorPos - 1;\n            const newChar = (_b = value[newCharIndex]) !== null && _b !== void 0 ? _b : value[0];\n            // Check if the new character is valid before updating the value\n            if (!validKeyPattern.test(newChar)) {\n                input.value = this.inputValues[index] || '';\n                this.previousInputValues = [...this.inputValues];\n                return;\n            }\n            this.inputValues[index] = newChar;\n            input.value = newChar;\n            this.updateValue(event);\n            this.previousInputValues = [...this.inputValues];\n        };\n        /**\n         * Handles pasting text into the input OTP component.\n         * This function prevents the default paste behavior and\n         * validates the pasted text against the allowed pattern.\n         * It then updates the value of the input group and focuses\n         * the next empty input after pasting.\n         */\n        this.onPaste = (event) => {\n            var _a, _b;\n            const { inputRefs, length, validKeyPattern } = this;\n            event.preventDefault();\n            const pastedText = (_a = event.clipboardData) === null || _a === void 0 ? void 0 : _a.getData('text');\n            // If there is no pasted text, still emit the input change event\n            // because this is how the native input element behaves\n            // but return early because there is nothing to paste.\n            if (!pastedText) {\n                this.emitIonInput(event);\n                return;\n            }\n            const validChars = pastedText\n                .split('')\n                .filter((char) => validKeyPattern.test(char))\n                .slice(0, length);\n            // Always paste starting at the first box\n            validChars.forEach((char, index) => {\n                if (index < length) {\n                    this.inputRefs[index].value = char;\n                    this.inputValues[index] = char;\n                }\n            });\n            // Update the value so that all input boxes are updated\n            this.value = validChars.join('');\n            this.updateValue(event);\n            // Focus the next empty input after pasting\n            // If all boxes are filled, focus the last input\n            const nextEmptyIndex = validChars.length < length ? validChars.length : length - 1;\n            (_b = inputRefs[nextEmptyIndex]) === null || _b === void 0 ? void 0 : _b.focus();\n        };\n    }\n    /**\n     * Sets focus to an input box.\n     * @param index - The index of the input box to focus (0-based).\n     * If provided and the input box has a value, the input box at that index will be focused.\n     * Otherwise, the first empty input box or the last input if all are filled will be focused.\n     */\n    async setFocus(index) {\n        var _a, _b;\n        if (typeof index === 'number') {\n            const validIndex = Math.max(0, Math.min(index, this.length - 1));\n            (_a = this.inputRefs[validIndex]) === null || _a === void 0 ? void 0 : _a.focus();\n        }\n        else {\n            const tabbableIndex = this.getTabbableIndex();\n            (_b = this.inputRefs[tabbableIndex]) === null || _b === void 0 ? void 0 : _b.focus();\n        }\n    }\n    valueChanged() {\n        this.initializeValues();\n        this.updateTabIndexes();\n    }\n    /**\n     * Processes the separators prop into an array of numbers.\n     *\n     * If the separators prop is not provided, returns an empty array.\n     * If the separators prop is 'all', returns an array of all valid positions (1 to length-1).\n     * If the separators prop is an array, returns it as is.\n     * If the separators prop is a string, splits it by commas and parses each part as a number.\n     *\n     * If the separators are greater than the input length, it will warn and ignore the separators.\n     */\n    processSeparators() {\n        const { separators, length } = this;\n        if (separators === undefined) {\n            this.parsedSeparators = [];\n            return;\n        }\n        if (typeof separators === 'string' && separators !== 'all') {\n            const isValidFormat = /^(\\d+)(,\\d+)*$/.test(separators);\n            if (!isValidFormat) {\n                printIonWarning(`[ion-input-otp] - Invalid separators format. Expected a comma-separated list of numbers, an array of numbers, or \"all\". Received: ${separators}`, this.el);\n                this.parsedSeparators = [];\n                return;\n            }\n        }\n        let separatorValues;\n        if (separators === 'all') {\n            separatorValues = Array.from({ length: length - 1 }, (_, i) => i + 1);\n        }\n        else if (Array.isArray(separators)) {\n            separatorValues = separators;\n        }\n        else {\n            separatorValues = separators\n                .split(',')\n                .map((pos) => parseInt(pos, 10))\n                .filter((pos) => !isNaN(pos));\n        }\n        // Check for duplicate separator positions\n        const duplicates = separatorValues.filter((pos, index) => separatorValues.indexOf(pos) !== index);\n        if (duplicates.length > 0) {\n            printIonWarning(`[ion-input-otp] - Duplicate separator positions are not allowed. Received: ${separators}`, this.el);\n        }\n        const invalidSeparators = separatorValues.filter((pos) => pos > length);\n        if (invalidSeparators.length > 0) {\n            printIonWarning(`[ion-input-otp] - The following separator positions are greater than the input length (${length}): ${invalidSeparators.join(', ')}. These separators will be ignored.`, this.el);\n        }\n        this.parsedSeparators = separatorValues.filter((pos) => pos <= length);\n    }\n    componentWillLoad() {\n        this.inheritedAttributes = inheritAriaAttributes(this.el);\n        this.processSeparators();\n        this.initializeValues();\n    }\n    componentDidLoad() {\n        this.updateTabIndexes();\n    }\n    /**\n     * Get the regex pattern for allowed characters.\n     * If a pattern is provided, use it to create a regex pattern\n     * Otherwise, use the default regex pattern based on type\n     */\n    get validKeyPattern() {\n        return new RegExp(`^${this.getPattern()}$`, 'u');\n    }\n    /**\n     * Gets the string pattern to pass to the input element\n     * and use in the regex for allowed characters.\n     */\n    getPattern() {\n        const { pattern, type } = this;\n        if (pattern) {\n            return pattern;\n        }\n        return type === 'number' ? '[\\\\p{N}]' : '[\\\\p{L}\\\\p{N}]';\n    }\n    /**\n     * Get the default value for inputmode.\n     * If inputmode is provided, use it.\n     * Otherwise, use the default inputmode based on type\n     */\n    getInputmode() {\n        const { inputmode } = this;\n        if (inputmode) {\n            return inputmode;\n        }\n        if (this.type == 'number') {\n            return 'numeric';\n        }\n        else {\n            return 'text';\n        }\n    }\n    /**\n     * Initializes the input values array based on the current value prop.\n     * This splits the value into individual characters and validates them against\n     * the allowed pattern. The values are then used as the values in the native\n     * input boxes and the value of the input group is updated.\n     */\n    initializeValues() {\n        // Clear all input values\n        this.inputValues = Array(this.length).fill('');\n        // If the value is null, undefined, or an empty string, return\n        if (this.value == null || String(this.value).length === 0) {\n            return;\n        }\n        // Split the value into individual characters and validate\n        // them against the allowed pattern\n        const chars = String(this.value).split('').slice(0, this.length);\n        chars.forEach((char, index) => {\n            if (this.validKeyPattern.test(char)) {\n                this.inputValues[index] = char;\n            }\n        });\n        // Update the value without emitting events\n        this.value = this.inputValues.join('');\n        this.previousInputValues = [...this.inputValues];\n    }\n    /**\n     * Updates the value of the input group.\n     * This updates the value of the input group and emits an `ionChange` event.\n     * If all of the input boxes are filled, it emits an `ionComplete` event.\n     */\n    updateValue(event) {\n        const { inputValues, length } = this;\n        const newValue = inputValues.join('');\n        this.value = newValue;\n        this.emitIonInput(event);\n        if (newValue.length === length) {\n            this.ionComplete.emit({ value: newValue });\n        }\n    }\n    /**\n     * Emits an `ionChange` event.\n     * This API should be called for user committed changes.\n     * This API should not be used for external value changes.\n     */\n    emitIonChange(event) {\n        const { value } = this;\n        // Checks for both null and undefined values\n        const newValue = value == null ? value : value.toString();\n        this.ionChange.emit({ value: newValue, event });\n    }\n    /**\n     * Emits an `ionInput` event.\n     * This is used to emit the input value when the user types,\n     * backspaces, or pastes.\n     */\n    emitIonInput(event) {\n        const { value } = this;\n        // Checks for both null and undefined values\n        const newValue = value == null ? value : value.toString();\n        this.ionInput.emit({ value: newValue, event });\n    }\n    /**\n     * Focuses the next input box.\n     */\n    focusNext(currentIndex) {\n        var _a;\n        const { inputRefs, length } = this;\n        if (currentIndex < length - 1) {\n            (_a = inputRefs[currentIndex + 1]) === null || _a === void 0 ? void 0 : _a.focus();\n        }\n    }\n    /**\n     * Focuses the previous input box.\n     */\n    focusPrevious(currentIndex) {\n        var _a;\n        const { inputRefs } = this;\n        if (currentIndex > 0) {\n            (_a = inputRefs[currentIndex - 1]) === null || _a === void 0 ? void 0 : _a.focus();\n        }\n    }\n    /**\n     * Searches through the input values and returns the index\n     * of the first empty input.\n     * Returns -1 if all inputs are filled.\n     */\n    getFirstEmptyIndex() {\n        var _a;\n        const { inputValues, length } = this;\n        // Create an array of the same length as the input OTP\n        // and fill it with the input values\n        const values = Array.from({ length }, (_, i) => inputValues[i] || '');\n        return (_a = values.findIndex((value) => !value || value === '')) !== null && _a !== void 0 ? _a : -1;\n    }\n    /**\n     * Returns the index of the input that should be tabbed to.\n     * If all inputs are filled, returns the last input's index.\n     * Otherwise, returns the index of the first empty input.\n     */\n    getTabbableIndex() {\n        const { length } = this;\n        const firstEmptyIndex = this.getFirstEmptyIndex();\n        return firstEmptyIndex === -1 ? length - 1 : firstEmptyIndex;\n    }\n    /**\n     * Updates the tabIndexes for the input boxes.\n     * This is used to ensure that the correct input is\n     * focused when the user navigates using the tab key.\n     */\n    updateTabIndexes() {\n        const { inputRefs, inputValues, length } = this;\n        // Find first empty index after any filled boxes\n        let firstEmptyIndex = -1;\n        for (let i = 0; i < length; i++) {\n            if (!inputValues[i] || inputValues[i] === '') {\n                firstEmptyIndex = i;\n                break;\n            }\n        }\n        // Update tabIndex and aria-hidden for all inputs\n        inputRefs.forEach((input, index) => {\n            const shouldBeTabbable = firstEmptyIndex === -1 ? index === length - 1 : firstEmptyIndex === index;\n            input.tabIndex = shouldBeTabbable ? 0 : -1;\n            // If the input is empty and not the first empty input,\n            // it should be hidden from screen readers.\n            const isEmpty = !inputValues[index] || inputValues[index] === '';\n            input.setAttribute('aria-hidden', isEmpty && !shouldBeTabbable ? 'true' : 'false');\n        });\n    }\n    /**\n     * Determines if a separator should be shown for a given index by\n     * checking if the index is included in the parsed separators array.\n     */\n    showSeparator(index) {\n        const { length } = this;\n        return this.parsedSeparators.includes(index + 1) && index < length - 1;\n    }\n    render() {\n        var _a, _b;\n        const { autocapitalize, color, disabled, el, fill, hasFocus, inheritedAttributes, inputId, inputRefs, inputValues, length, readonly, shape, size, } = this;\n        const mode = getIonMode(this);\n        const inputmode = this.getInputmode();\n        const tabbableIndex = this.getTabbableIndex();\n        const pattern = this.getPattern();\n        const hasDescription = ((_b = (_a = el.querySelector('.input-otp-description')) === null || _a === void 0 ? void 0 : _a.textContent) === null || _b === void 0 ? void 0 : _b.trim()) !== '';\n        return (h(Host, { key: 'f15a29fb17b681ef55885ca36d3d5f899cbaca83', class: createColorClasses(color, {\n                [mode]: true,\n                'has-focus': hasFocus,\n                [`input-otp-size-${size}`]: true,\n                [`input-otp-shape-${shape}`]: true,\n                [`input-otp-fill-${fill}`]: true,\n                'input-otp-disabled': disabled,\n                'input-otp-readonly': readonly,\n            }) }, h(\"div\", Object.assign({ key: 'd7e1d4edd8aafcf2ed4313301287282e90fc7e82', role: \"group\", \"aria-label\": \"One-time password input\", class: \"input-otp-group\" }, inheritedAttributes), Array.from({ length }).map((_, index) => (h(Fragment, null, h(\"div\", { class: \"native-wrapper\" }, h(\"input\", { class: \"native-input\", id: `${inputId}-${index}`, \"aria-label\": `Input ${index + 1} of ${length}`, type: \"text\", autoCapitalize: autocapitalize, inputmode: inputmode, pattern: pattern, disabled: disabled, readOnly: readonly, tabIndex: index === tabbableIndex ? 0 : -1, value: inputValues[index] || '', autocomplete: \"one-time-code\", ref: (el) => (inputRefs[index] = el), onInput: this.onInput(index), onBlur: this.onBlur, onFocus: this.onFocus(index), onKeyDown: this.onKeyDown(index), onPaste: this.onPaste })), this.showSeparator(index) && h(\"div\", { class: \"input-otp-separator\" }))))), h(\"div\", { key: '3724a3159d02860971879a906092f9965f5a7c47', class: {\n                'input-otp-description': true,\n                'input-otp-description-hidden': !hasDescription,\n            } }, h(\"slot\", { key: '11baa2624926a08274508afe0833d9237a8dc35c' }))));\n    }\n    get el() { return getElement(this); }\n    static get watchers() { return {\n        \"value\": [\"valueChanged\"],\n        \"separators\": [\"processSeparators\"],\n        \"length\": [\"processSeparators\"]\n    }; }\n};\nlet inputIds = 0;\nInputOTP.style = {\n    ios: inputOtpIosCss,\n    md: inputOtpMdCss\n};\n\nexport { InputOTP as ion_input_otp };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;AAQA,IAAM,iBAAiB;AAEvB,IAAM,gBAAgB;AAEtB,IAAM,WAAW,MAAM;AAAA,EACnB,YAAY,SAAS;AACjB,qBAAiB,MAAM,OAAO;AAC9B,SAAK,WAAW,YAAY,MAAM,YAAY,CAAC;AAC/C,SAAK,YAAY,YAAY,MAAM,aAAa,CAAC;AACjD,SAAK,cAAc,YAAY,MAAM,eAAe,CAAC;AACrD,SAAK,UAAU,YAAY,MAAM,WAAW,CAAC;AAC7C,SAAK,WAAW,YAAY,MAAM,YAAY,CAAC;AAC/C,SAAK,sBAAsB,CAAC;AAC5B,SAAK,YAAY,CAAC;AAClB,SAAK,UAAU,iBAAiB,UAAU;AAC1C,SAAK,mBAAmB,CAAC;AAMzB,SAAK,uBAAuB;AAC5B,SAAK,cAAc,CAAC;AACpB,SAAK,WAAW;AAChB,SAAK,sBAAsB,CAAC;AAK5B,SAAK,iBAAiB;AAItB,SAAK,WAAW;AAKhB,SAAK,OAAO;AAIZ,SAAK,SAAS;AAId,SAAK,WAAW;AAOhB,SAAK,QAAQ;AAIb,SAAK,OAAO;AAIZ,SAAK,OAAO;AAIZ,SAAK,QAAQ;AAYb,SAAK,UAAU,CAAC,UAAU,CAAC,UAAU;AACjC,UAAI;AACJ,YAAM,EAAE,UAAU,IAAI;AAGtB,UAAI,CAAC,KAAK,UAAU;AAChB,aAAK,SAAS,KAAK,KAAK;AACxB,aAAK,eAAe,KAAK;AAAA,MAC7B;AACA,WAAK,WAAW;AAChB,UAAI,aAAa;AACjB,UAAI,CAAC,KAAK,sBAAsB;AAG5B,cAAM,cAAc,KAAK,YAAY,KAAK,IAAI,QAAQ,KAAK,mBAAmB;AAC9E,qBAAa,gBAAgB,KAAK,KAAK,SAAS,IAAI;AAEpD,SAAC,KAAK,KAAK,UAAU,UAAU,OAAO,QAAQ,OAAO,SAAS,SAAS,GAAG,MAAM;AAAA,MACpF;AAEA,gBAAU,QAAQ,CAAC,OAAO,MAAM;AAC5B,cAAM,WAAW,MAAM,aAAa,IAAI;AAAA,MAC5C,CAAC;AAED,WAAK,uBAAuB;AAAA,IAChC;AAKA,SAAK,SAAS,CAAC,UAAU;AACrB,YAAM,EAAE,UAAU,IAAI;AACtB,YAAM,gBAAgB,MAAM;AAE5B,YAAM,kBAAkB,iBAAiB,QAAQ,UAAU,SAAS,aAAa;AACjF,UAAI,CAAC,iBAAiB;AAClB,aAAK,WAAW;AAEhB,aAAK,iBAAiB;AAEtB,aAAK,QAAQ,KAAK,KAAK;AAEvB,YAAI,KAAK,iBAAiB,KAAK,OAAO;AAClC,eAAK,cAAc,KAAK;AAAA,QAC5B;AAAA,MACJ;AAAA,IACJ;AASA,SAAK,YAAY,CAAC,UAAU,CAAC,UAAU;AACnC,YAAM,EAAE,OAAO,IAAI;AACnB,YAAM,MAAM,MAAM,KAAK,EAAE;AACzB,YAAM,QAAQ,MAAM;AAGpB,YAAM,gBAAgB,CAAC,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,GAAG;AACxD,YAAM,kBAAkB,MAAM,mBAAmB,MAAM;AAGvD,UAAI,oBAAqB,MAAM,WAAW,MAAM,YAAY,cAAc,SAAS,MAAM,IAAI,YAAY,CAAC,GAAI;AAC1G;AAAA,MACJ;AACA,UAAI,MAAM,QAAQ,aAAa;AAC3B,YAAI,KAAK,YAAY,KAAK,GAAG;AAEzB,mBAAS,IAAI,OAAO,IAAI,SAAS,GAAG,KAAK;AACrC,iBAAK,YAAY,CAAC,IAAI,KAAK,YAAY,IAAI,CAAC;AAAA,UAChD;AAEA,eAAK,YAAY,SAAS,CAAC,IAAI;AAE/B,mBAAS,IAAI,GAAG,IAAI,QAAQ,KAAK;AAC7B,iBAAK,UAAU,CAAC,EAAE,QAAQ,KAAK,YAAY,CAAC,KAAK;AAAA,UACrD;AACA,eAAK,YAAY,KAAK;AACtB,gBAAM,eAAe;AAAA,QACzB,WACS,CAAC,KAAK,YAAY,KAAK,KAAK,QAAQ,GAAG;AAE5C,eAAK,cAAc,KAAK;AAAA,QAC5B;AAAA,MACJ,WACS,MAAM,QAAQ,eAAe,MAAM,QAAQ,cAAc;AAC9D,aAAK,uBAAuB;AAC5B,cAAM,eAAe;AACrB,cAAM,SAAS,MAAM,QAAQ;AAC7B,cAAM,iBAAkB,UAAU,OAAS,CAAC,UAAU,CAAC;AAEvD,YAAI,gBAAgB;AAChB,cAAI,KAAK,YAAY,KAAK,KAAK,QAAQ,SAAS,GAAG;AAC/C,iBAAK,UAAU,KAAK;AAAA,UACxB;AAAA,QACJ,OACK;AACD,eAAK,cAAc,KAAK;AAAA,QAC5B;AAAA,MACJ,WACS,MAAM,QAAQ,OAAO;AAC1B,aAAK,uBAAuB;AAE5B;AAAA,MACJ;AAAA,IACJ;AAWA,SAAK,UAAU,CAAC,UAAU,CAAC,UAAU;AACjC,UAAI,IAAI;AACR,YAAM,EAAE,QAAQ,gBAAgB,IAAI;AACpC,YAAM,QAAQ,MAAM;AACpB,YAAM,QAAQ,MAAM;AACpB,YAAM,gBAAgB,KAAK,oBAAoB,KAAK,KAAK;AAMzD,YAAM,aAAa,MAAM,SAAS,cAAc,SAAS;AACzD,UAAI,YAAY;AAEZ,cAAM,aAAa,MACd,MAAM,EAAE,EACR,OAAO,CAAC,SAAS,gBAAgB,KAAK,IAAI,CAAC,EAC3C,MAAM,GAAG,MAAM;AAIpB,YAAI,WAAW,WAAW,GAAG;AACzB,gCAAsB,MAAM;AACxB,iBAAK,UAAU,QAAQ,CAACA,WAAU;AAC9B,cAAAA,OAAM,QAAQ;AAAA,YAClB,CAAC;AAAA,UACL,CAAC;AAAA,QACL;AACA,iBAAS,IAAI,GAAG,IAAI,QAAQ,KAAK;AAC7B,eAAK,YAAY,CAAC,IAAI,WAAW,CAAC,KAAK;AACvC,eAAK,UAAU,CAAC,EAAE,QAAQ,WAAW,CAAC,KAAK;AAAA,QAC/C;AACA,aAAK,YAAY,KAAK;AAItB,mBAAW,MAAM;AACb,cAAIC;AACJ,gBAAM,YAAY,WAAW,SAAS,SAAS,WAAW,SAAS,SAAS;AAC5E,WAACA,MAAK,KAAK,UAAU,SAAS,OAAO,QAAQA,QAAO,SAAS,SAASA,IAAG,MAAM;AAAA,QACnF,GAAG,EAAE;AACL,aAAK,sBAAsB,CAAC,GAAG,KAAK,WAAW;AAC/C;AAAA,MACJ;AAIA,UAAI,MAAM,SAAS,KAAK,CAAC,gBAAgB,KAAK,MAAM,MAAM,SAAS,CAAC,CAAC,GAAG;AACpE,cAAM,QAAQ,KAAK,YAAY,KAAK,KAAK;AACzC,aAAK,sBAAsB,CAAC,GAAG,KAAK,WAAW;AAC/C;AAAA,MACJ;AAKA,YAAM,gBAAgB,MAAM,mBAAmB,KAAK,MAAM,iBAAiB,MAAM;AACjF,YAAM,UAAU,CAAC,KAAK,YAAY,KAAK;AACvC,UAAI,iBAAiB,SAAS;AAC1B,aAAK,YAAY,KAAK,IAAI;AAC1B,cAAM,QAAQ;AACd,aAAK,YAAY,KAAK;AACtB,aAAK,UAAU,KAAK;AACpB,aAAK,sBAAsB,CAAC,GAAG,KAAK,WAAW;AAC/C;AAAA,MACJ;AAKA,YAAM,uBAAuB,KAAK,YAAY,KAAK,YAAY,SAAS,CAAC,MAAM;AAC/E,UAAI,KAAK,YAAY,KAAK,KAAK,wBAAwB,MAAM,WAAW,GAAG;AAEvE,YAAIC,WAAU,MAAM;AACpB,YAAI,CAACA,UAAS;AACV,UAAAA,WAAU,MAAM,MAAM,EAAE,EAAE,KAAK,CAAC,GAAG,MAAM,MAAM,cAAc,CAAC,CAAC,KAAK,MAAM,MAAM,SAAS,CAAC;AAAA,QAC9F;AAEA,YAAI,CAAC,gBAAgB,KAAKA,QAAO,GAAG;AAChC,gBAAM,QAAQ,KAAK,YAAY,KAAK,KAAK;AACzC,eAAK,sBAAsB,CAAC,GAAG,KAAK,WAAW;AAC/C;AAAA,QACJ;AAEA,iBAAS,IAAI,KAAK,YAAY,SAAS,GAAG,IAAI,OAAO,KAAK;AACtD,eAAK,YAAY,CAAC,IAAI,KAAK,YAAY,IAAI,CAAC;AAC5C,eAAK,UAAU,CAAC,EAAE,QAAQ,KAAK,YAAY,CAAC,KAAK;AAAA,QACrD;AACA,aAAK,YAAY,KAAK,IAAIA;AAC1B,aAAK,UAAU,KAAK,EAAE,QAAQA;AAC9B,aAAK,YAAY,KAAK;AACtB,aAAK,sBAAsB,CAAC,GAAG,KAAK,WAAW;AAC/C;AAAA,MACJ;AAQA,YAAM,aAAa,KAAK,MAAM,oBAAoB,QAAQ,OAAO,SAAS,KAAK,MAAM;AACrF,YAAM,eAAe,YAAY;AACjC,YAAM,WAAW,KAAK,MAAM,YAAY,OAAO,QAAQ,OAAO,SAAS,KAAK,MAAM,CAAC;AAEnF,UAAI,CAAC,gBAAgB,KAAK,OAAO,GAAG;AAChC,cAAM,QAAQ,KAAK,YAAY,KAAK,KAAK;AACzC,aAAK,sBAAsB,CAAC,GAAG,KAAK,WAAW;AAC/C;AAAA,MACJ;AACA,WAAK,YAAY,KAAK,IAAI;AAC1B,YAAM,QAAQ;AACd,WAAK,YAAY,KAAK;AACtB,WAAK,sBAAsB,CAAC,GAAG,KAAK,WAAW;AAAA,IACnD;AAQA,SAAK,UAAU,CAAC,UAAU;AACtB,UAAI,IAAI;AACR,YAAM,EAAE,WAAW,QAAQ,gBAAgB,IAAI;AAC/C,YAAM,eAAe;AACrB,YAAM,cAAc,KAAK,MAAM,mBAAmB,QAAQ,OAAO,SAAS,SAAS,GAAG,QAAQ,MAAM;AAIpG,UAAI,CAAC,YAAY;AACb,aAAK,aAAa,KAAK;AACvB;AAAA,MACJ;AACA,YAAM,aAAa,WACd,MAAM,EAAE,EACR,OAAO,CAAC,SAAS,gBAAgB,KAAK,IAAI,CAAC,EAC3C,MAAM,GAAG,MAAM;AAEpB,iBAAW,QAAQ,CAAC,MAAM,UAAU;AAChC,YAAI,QAAQ,QAAQ;AAChB,eAAK,UAAU,KAAK,EAAE,QAAQ;AAC9B,eAAK,YAAY,KAAK,IAAI;AAAA,QAC9B;AAAA,MACJ,CAAC;AAED,WAAK,QAAQ,WAAW,KAAK,EAAE;AAC/B,WAAK,YAAY,KAAK;AAGtB,YAAM,iBAAiB,WAAW,SAAS,SAAS,WAAW,SAAS,SAAS;AACjF,OAAC,KAAK,UAAU,cAAc,OAAO,QAAQ,OAAO,SAAS,SAAS,GAAG,MAAM;AAAA,IACnF;AAAA,EACJ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOM,SAAS,OAAO;AAAA;AAClB,UAAI,IAAI;AACR,UAAI,OAAO,UAAU,UAAU;AAC3B,cAAM,aAAa,KAAK,IAAI,GAAG,KAAK,IAAI,OAAO,KAAK,SAAS,CAAC,CAAC;AAC/D,SAAC,KAAK,KAAK,UAAU,UAAU,OAAO,QAAQ,OAAO,SAAS,SAAS,GAAG,MAAM;AAAA,MACpF,OACK;AACD,cAAM,gBAAgB,KAAK,iBAAiB;AAC5C,SAAC,KAAK,KAAK,UAAU,aAAa,OAAO,QAAQ,OAAO,SAAS,SAAS,GAAG,MAAM;AAAA,MACvF;AAAA,IACJ;AAAA;AAAA,EACA,eAAe;AACX,SAAK,iBAAiB;AACtB,SAAK,iBAAiB;AAAA,EAC1B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAWA,oBAAoB;AAChB,UAAM,EAAE,YAAY,OAAO,IAAI;AAC/B,QAAI,eAAe,QAAW;AAC1B,WAAK,mBAAmB,CAAC;AACzB;AAAA,IACJ;AACA,QAAI,OAAO,eAAe,YAAY,eAAe,OAAO;AACxD,YAAM,gBAAgB,iBAAiB,KAAK,UAAU;AACtD,UAAI,CAAC,eAAe;AAChB,wBAAgB,qIAAqI,UAAU,IAAI,KAAK,EAAE;AAC1K,aAAK,mBAAmB,CAAC;AACzB;AAAA,MACJ;AAAA,IACJ;AACA,QAAI;AACJ,QAAI,eAAe,OAAO;AACtB,wBAAkB,MAAM,KAAK,EAAE,QAAQ,SAAS,EAAE,GAAG,CAAC,GAAG,MAAM,IAAI,CAAC;AAAA,IACxE,WACS,MAAM,QAAQ,UAAU,GAAG;AAChC,wBAAkB;AAAA,IACtB,OACK;AACD,wBAAkB,WACb,MAAM,GAAG,EACT,IAAI,CAAC,QAAQ,SAAS,KAAK,EAAE,CAAC,EAC9B,OAAO,CAAC,QAAQ,CAAC,MAAM,GAAG,CAAC;AAAA,IACpC;AAEA,UAAM,aAAa,gBAAgB,OAAO,CAAC,KAAK,UAAU,gBAAgB,QAAQ,GAAG,MAAM,KAAK;AAChG,QAAI,WAAW,SAAS,GAAG;AACvB,sBAAgB,8EAA8E,UAAU,IAAI,KAAK,EAAE;AAAA,IACvH;AACA,UAAM,oBAAoB,gBAAgB,OAAO,CAAC,QAAQ,MAAM,MAAM;AACtE,QAAI,kBAAkB,SAAS,GAAG;AAC9B,sBAAgB,0FAA0F,MAAM,MAAM,kBAAkB,KAAK,IAAI,CAAC,uCAAuC,KAAK,EAAE;AAAA,IACpM;AACA,SAAK,mBAAmB,gBAAgB,OAAO,CAAC,QAAQ,OAAO,MAAM;AAAA,EACzE;AAAA,EACA,oBAAoB;AAChB,SAAK,sBAAsB,sBAAsB,KAAK,EAAE;AACxD,SAAK,kBAAkB;AACvB,SAAK,iBAAiB;AAAA,EAC1B;AAAA,EACA,mBAAmB;AACf,SAAK,iBAAiB;AAAA,EAC1B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,IAAI,kBAAkB;AAClB,WAAO,IAAI,OAAO,IAAI,KAAK,WAAW,CAAC,KAAK,GAAG;AAAA,EACnD;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,aAAa;AACT,UAAM,EAAE,SAAS,KAAK,IAAI;AAC1B,QAAI,SAAS;AACT,aAAO;AAAA,IACX;AACA,WAAO,SAAS,WAAW,aAAa;AAAA,EAC5C;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,eAAe;AACX,UAAM,EAAE,UAAU,IAAI;AACtB,QAAI,WAAW;AACX,aAAO;AAAA,IACX;AACA,QAAI,KAAK,QAAQ,UAAU;AACvB,aAAO;AAAA,IACX,OACK;AACD,aAAO;AAAA,IACX;AAAA,EACJ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,mBAAmB;AAEf,SAAK,cAAc,MAAM,KAAK,MAAM,EAAE,KAAK,EAAE;AAE7C,QAAI,KAAK,SAAS,QAAQ,OAAO,KAAK,KAAK,EAAE,WAAW,GAAG;AACvD;AAAA,IACJ;AAGA,UAAM,QAAQ,OAAO,KAAK,KAAK,EAAE,MAAM,EAAE,EAAE,MAAM,GAAG,KAAK,MAAM;AAC/D,UAAM,QAAQ,CAAC,MAAM,UAAU;AAC3B,UAAI,KAAK,gBAAgB,KAAK,IAAI,GAAG;AACjC,aAAK,YAAY,KAAK,IAAI;AAAA,MAC9B;AAAA,IACJ,CAAC;AAED,SAAK,QAAQ,KAAK,YAAY,KAAK,EAAE;AACrC,SAAK,sBAAsB,CAAC,GAAG,KAAK,WAAW;AAAA,EACnD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,YAAY,OAAO;AACf,UAAM,EAAE,aAAa,OAAO,IAAI;AAChC,UAAM,WAAW,YAAY,KAAK,EAAE;AACpC,SAAK,QAAQ;AACb,SAAK,aAAa,KAAK;AACvB,QAAI,SAAS,WAAW,QAAQ;AAC5B,WAAK,YAAY,KAAK,EAAE,OAAO,SAAS,CAAC;AAAA,IAC7C;AAAA,EACJ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,cAAc,OAAO;AACjB,UAAM,EAAE,MAAM,IAAI;AAElB,UAAM,WAAW,SAAS,OAAO,QAAQ,MAAM,SAAS;AACxD,SAAK,UAAU,KAAK,EAAE,OAAO,UAAU,MAAM,CAAC;AAAA,EAClD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,aAAa,OAAO;AAChB,UAAM,EAAE,MAAM,IAAI;AAElB,UAAM,WAAW,SAAS,OAAO,QAAQ,MAAM,SAAS;AACxD,SAAK,SAAS,KAAK,EAAE,OAAO,UAAU,MAAM,CAAC;AAAA,EACjD;AAAA;AAAA;AAAA;AAAA,EAIA,UAAU,cAAc;AACpB,QAAI;AACJ,UAAM,EAAE,WAAW,OAAO,IAAI;AAC9B,QAAI,eAAe,SAAS,GAAG;AAC3B,OAAC,KAAK,UAAU,eAAe,CAAC,OAAO,QAAQ,OAAO,SAAS,SAAS,GAAG,MAAM;AAAA,IACrF;AAAA,EACJ;AAAA;AAAA;AAAA;AAAA,EAIA,cAAc,cAAc;AACxB,QAAI;AACJ,UAAM,EAAE,UAAU,IAAI;AACtB,QAAI,eAAe,GAAG;AAClB,OAAC,KAAK,UAAU,eAAe,CAAC,OAAO,QAAQ,OAAO,SAAS,SAAS,GAAG,MAAM;AAAA,IACrF;AAAA,EACJ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,qBAAqB;AACjB,QAAI;AACJ,UAAM,EAAE,aAAa,OAAO,IAAI;AAGhC,UAAM,SAAS,MAAM,KAAK,EAAE,OAAO,GAAG,CAAC,GAAG,MAAM,YAAY,CAAC,KAAK,EAAE;AACpE,YAAQ,KAAK,OAAO,UAAU,CAAC,UAAU,CAAC,SAAS,UAAU,EAAE,OAAO,QAAQ,OAAO,SAAS,KAAK;AAAA,EACvG;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,mBAAmB;AACf,UAAM,EAAE,OAAO,IAAI;AACnB,UAAM,kBAAkB,KAAK,mBAAmB;AAChD,WAAO,oBAAoB,KAAK,SAAS,IAAI;AAAA,EACjD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,mBAAmB;AACf,UAAM,EAAE,WAAW,aAAa,OAAO,IAAI;AAE3C,QAAI,kBAAkB;AACtB,aAAS,IAAI,GAAG,IAAI,QAAQ,KAAK;AAC7B,UAAI,CAAC,YAAY,CAAC,KAAK,YAAY,CAAC,MAAM,IAAI;AAC1C,0BAAkB;AAClB;AAAA,MACJ;AAAA,IACJ;AAEA,cAAU,QAAQ,CAAC,OAAO,UAAU;AAChC,YAAM,mBAAmB,oBAAoB,KAAK,UAAU,SAAS,IAAI,oBAAoB;AAC7F,YAAM,WAAW,mBAAmB,IAAI;AAGxC,YAAM,UAAU,CAAC,YAAY,KAAK,KAAK,YAAY,KAAK,MAAM;AAC9D,YAAM,aAAa,eAAe,WAAW,CAAC,mBAAmB,SAAS,OAAO;AAAA,IACrF,CAAC;AAAA,EACL;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,cAAc,OAAO;AACjB,UAAM,EAAE,OAAO,IAAI;AACnB,WAAO,KAAK,iBAAiB,SAAS,QAAQ,CAAC,KAAK,QAAQ,SAAS;AAAA,EACzE;AAAA,EACA,SAAS;AACL,QAAI,IAAI;AACR,UAAM,EAAE,gBAAgB,OAAO,UAAU,IAAI,MAAM,UAAU,qBAAqB,SAAS,WAAW,aAAa,QAAQ,UAAU,OAAO,KAAM,IAAI;AACtJ,UAAM,OAAO,WAAW,IAAI;AAC5B,UAAM,YAAY,KAAK,aAAa;AACpC,UAAM,gBAAgB,KAAK,iBAAiB;AAC5C,UAAM,UAAU,KAAK,WAAW;AAChC,UAAM,mBAAmB,MAAM,KAAK,GAAG,cAAc,wBAAwB,OAAO,QAAQ,OAAO,SAAS,SAAS,GAAG,iBAAiB,QAAQ,OAAO,SAAS,SAAS,GAAG,KAAK,OAAO;AACzL,WAAQ,EAAE,MAAM,EAAE,KAAK,4CAA4C,OAAO,mBAAmB,OAAO;AAAA,MAC5F,CAAC,IAAI,GAAG;AAAA,MACR,aAAa;AAAA,MACb,CAAC,kBAAkB,IAAI,EAAE,GAAG;AAAA,MAC5B,CAAC,mBAAmB,KAAK,EAAE,GAAG;AAAA,MAC9B,CAAC,kBAAkB,IAAI,EAAE,GAAG;AAAA,MAC5B,sBAAsB;AAAA,MACtB,sBAAsB;AAAA,IAC1B,CAAC,EAAE,GAAG,EAAE,OAAO,OAAO,OAAO,EAAE,KAAK,4CAA4C,MAAM,SAAS,cAAc,2BAA2B,OAAO,kBAAkB,GAAG,mBAAmB,GAAG,MAAM,KAAK,EAAE,OAAO,CAAC,EAAE,IAAI,CAAC,GAAG,UAAW,EAAE,UAAU,MAAM,EAAE,OAAO,EAAE,OAAO,iBAAiB,GAAG,EAAE,SAAS,EAAE,OAAO,gBAAgB,IAAI,GAAG,OAAO,IAAI,KAAK,IAAI,cAAc,SAAS,QAAQ,CAAC,OAAO,MAAM,IAAI,MAAM,QAAQ,gBAAgB,gBAAgB,WAAsB,SAAkB,UAAoB,UAAU,UAAU,UAAU,UAAU,gBAAgB,IAAI,IAAI,OAAO,YAAY,KAAK,KAAK,IAAI,cAAc,iBAAiB,KAAK,CAACC,QAAQ,UAAU,KAAK,IAAIA,KAAK,SAAS,KAAK,QAAQ,KAAK,GAAG,QAAQ,KAAK,QAAQ,SAAS,KAAK,QAAQ,KAAK,GAAG,WAAW,KAAK,UAAU,KAAK,GAAG,SAAS,KAAK,QAAQ,CAAC,CAAC,GAAG,KAAK,cAAc,KAAK,KAAK,EAAE,OAAO,EAAE,OAAO,sBAAsB,CAAC,CAAC,CAAE,CAAC,GAAG,EAAE,OAAO,EAAE,KAAK,4CAA4C,OAAO;AAAA,MACt7B,yBAAyB;AAAA,MACzB,gCAAgC,CAAC;AAAA,IACrC,EAAE,GAAG,EAAE,QAAQ,EAAE,KAAK,2CAA2C,CAAC,CAAC,CAAC;AAAA,EAC5E;AAAA,EACA,IAAI,KAAK;AAAE,WAAO,WAAW,IAAI;AAAA,EAAG;AAAA,EACpC,WAAW,WAAW;AAAE,WAAO;AAAA,MAC3B,SAAS,CAAC,cAAc;AAAA,MACxB,cAAc,CAAC,mBAAmB;AAAA,MAClC,UAAU,CAAC,mBAAmB;AAAA,IAClC;AAAA,EAAG;AACP;AACA,IAAI,WAAW;AACf,SAAS,QAAQ;AAAA,EACb,KAAK;AAAA,EACL,IAAI;AACR;", "names": ["input", "_a", "newChar", "el"]}