{"version": 3, "sources": ["../../../../../../node_modules/@ionic/core/components/index4.js"], "sourcesContent": ["/*!\n * (C) Ionic http://ionicframework.com - MIT License\n */\n// TODO(FW-2832): types\nclass Config {\n    constructor() {\n        this.m = new Map();\n    }\n    reset(configObj) {\n        this.m = new Map(Object.entries(configObj));\n    }\n    get(key, fallback) {\n        const value = this.m.get(key);\n        return value !== undefined ? value : fallback;\n    }\n    getBoolean(key, fallback = false) {\n        const val = this.m.get(key);\n        if (val === undefined) {\n            return fallback;\n        }\n        if (typeof val === 'string') {\n            return val === 'true';\n        }\n        return !!val;\n    }\n    getNumber(key, fallback) {\n        const val = parseFloat(this.m.get(key));\n        return isNaN(val) ? (fallback !== undefined ? fallback : NaN) : val;\n    }\n    set(key, value) {\n        this.m.set(key, value);\n    }\n}\nconst config = /*@__PURE__*/ new Config();\nconst configFromSession = (win) => {\n    try {\n        const configStr = win.sessionStorage.getItem(IONIC_SESSION_KEY);\n        return configStr !== null ? JSON.parse(configStr) : {};\n    }\n    catch (e) {\n        return {};\n    }\n};\nconst saveConfig = (win, c) => {\n    try {\n        win.sessionStorage.setItem(IONIC_SESSION_KEY, JSON.stringify(c));\n    }\n    catch (e) {\n        return;\n    }\n};\nconst configFromURL = (win) => {\n    const configObj = {};\n    win.location.search\n        .slice(1)\n        .split('&')\n        .map((entry) => entry.split('='))\n        .map(([key, value]) => {\n        try {\n            return [decodeURIComponent(key), decodeURIComponent(value)];\n        }\n        catch (e) {\n            return ['', ''];\n        }\n    })\n        .filter(([key]) => startsWith(key, IONIC_PREFIX))\n        .map(([key, value]) => [key.slice(IONIC_PREFIX.length), value])\n        .forEach(([key, value]) => {\n        configObj[key] = value;\n    });\n    return configObj;\n};\nconst startsWith = (input, search) => {\n    return input.substr(0, search.length) === search;\n};\nconst IONIC_PREFIX = 'ionic:';\nconst IONIC_SESSION_KEY = 'ionic-persist-config';\n\nvar LogLevel;\n(function (LogLevel) {\n    LogLevel[\"OFF\"] = \"OFF\";\n    LogLevel[\"ERROR\"] = \"ERROR\";\n    LogLevel[\"WARN\"] = \"WARN\";\n})(LogLevel || (LogLevel = {}));\n/**\n * Logs a warning to the console with an Ionic prefix\n * to indicate the library that is warning the developer.\n *\n * @param message - The string message to be logged to the console.\n */\nconst printIonWarning = (message, ...params) => {\n    const logLevel = config.get('logLevel', LogLevel.WARN);\n    if ([LogLevel.WARN].includes(logLevel)) {\n        return console.warn(`[Ionic Warning]: ${message}`, ...params);\n    }\n};\n/**\n * Logs an error to the console with an Ionic prefix\n * to indicate the library that is warning the developer.\n *\n * @param message - The string message to be logged to the console.\n * @param params - Additional arguments to supply to the console.error.\n */\nconst printIonError = (message, ...params) => {\n    const logLevel = config.get('logLevel', LogLevel.ERROR);\n    if ([LogLevel.ERROR, LogLevel.WARN].includes(logLevel)) {\n        return console.error(`[Ionic Error]: ${message}`, ...params);\n    }\n};\n/**\n * Prints an error informing developers that an implementation requires an element to be used\n * within a specific selector.\n *\n * @param el The web component element this is requiring the element.\n * @param targetSelectors The selector or selectors that were not found.\n */\nconst printRequiredElementError = (el, ...targetSelectors) => {\n    return console.error(`<${el.tagName.toLowerCase()}> must be used inside ${targetSelectors.join(' or ')}.`);\n};\n\nexport { LogLevel as L, printIonError as a, configFromSession as b, config as c, configFromURL as d, printRequiredElementError as e, printIonWarning as p, saveConfig as s };\n"], "mappings": ";AAIA,IAAM,SAAN,MAAa;AAAA,EACT,cAAc;AACV,SAAK,IAAI,oBAAI,IAAI;AAAA,EACrB;AAAA,EACA,MAAM,WAAW;AACb,SAAK,IAAI,IAAI,IAAI,OAAO,QAAQ,SAAS,CAAC;AAAA,EAC9C;AAAA,EACA,IAAI,KAAK,UAAU;AACf,UAAM,QAAQ,KAAK,EAAE,IAAI,GAAG;AAC5B,WAAO,UAAU,SAAY,QAAQ;AAAA,EACzC;AAAA,EACA,WAAW,KAAK,WAAW,OAAO;AAC9B,UAAM,MAAM,KAAK,EAAE,IAAI,GAAG;AAC1B,QAAI,QAAQ,QAAW;AACnB,aAAO;AAAA,IACX;AACA,QAAI,OAAO,QAAQ,UAAU;AACzB,aAAO,QAAQ;AAAA,IACnB;AACA,WAAO,CAAC,CAAC;AAAA,EACb;AAAA,EACA,UAAU,KAAK,UAAU;AACrB,UAAM,MAAM,WAAW,KAAK,EAAE,IAAI,GAAG,CAAC;AACtC,WAAO,MAAM,GAAG,IAAK,aAAa,SAAY,WAAW,MAAO;AAAA,EACpE;AAAA,EACA,IAAI,KAAK,OAAO;AACZ,SAAK,EAAE,IAAI,KAAK,KAAK;AAAA,EACzB;AACJ;AACA,IAAM,SAAuB,IAAI,OAAO;AACxC,IAAM,oBAAoB,CAAC,QAAQ;AAC/B,MAAI;AACA,UAAM,YAAY,IAAI,eAAe,QAAQ,iBAAiB;AAC9D,WAAO,cAAc,OAAO,KAAK,MAAM,SAAS,IAAI,CAAC;AAAA,EACzD,SACO,GAAG;AACN,WAAO,CAAC;AAAA,EACZ;AACJ;AACA,IAAM,aAAa,CAAC,KAAK,MAAM;AAC3B,MAAI;AACA,QAAI,eAAe,QAAQ,mBAAmB,KAAK,UAAU,CAAC,CAAC;AAAA,EACnE,SACO,GAAG;AACN;AAAA,EACJ;AACJ;AACA,IAAM,gBAAgB,CAAC,QAAQ;AAC3B,QAAM,YAAY,CAAC;AACnB,MAAI,SAAS,OACR,MAAM,CAAC,EACP,MAAM,GAAG,EACT,IAAI,CAAC,UAAU,MAAM,MAAM,GAAG,CAAC,EAC/B,IAAI,CAAC,CAAC,KAAK,KAAK,MAAM;AACvB,QAAI;AACA,aAAO,CAAC,mBAAmB,GAAG,GAAG,mBAAmB,KAAK,CAAC;AAAA,IAC9D,SACO,GAAG;AACN,aAAO,CAAC,IAAI,EAAE;AAAA,IAClB;AAAA,EACJ,CAAC,EACI,OAAO,CAAC,CAAC,GAAG,MAAM,WAAW,KAAK,YAAY,CAAC,EAC/C,IAAI,CAAC,CAAC,KAAK,KAAK,MAAM,CAAC,IAAI,MAAM,aAAa,MAAM,GAAG,KAAK,CAAC,EAC7D,QAAQ,CAAC,CAAC,KAAK,KAAK,MAAM;AAC3B,cAAU,GAAG,IAAI;AAAA,EACrB,CAAC;AACD,SAAO;AACX;AACA,IAAM,aAAa,CAAC,OAAO,WAAW;AAClC,SAAO,MAAM,OAAO,GAAG,OAAO,MAAM,MAAM;AAC9C;AACA,IAAM,eAAe;AACrB,IAAM,oBAAoB;AAE1B,IAAI;AAAA,CACH,SAAUA,WAAU;AACjB,EAAAA,UAAS,KAAK,IAAI;AAClB,EAAAA,UAAS,OAAO,IAAI;AACpB,EAAAA,UAAS,MAAM,IAAI;AACvB,GAAG,aAAa,WAAW,CAAC,EAAE;AAO9B,IAAM,kBAAkB,CAAC,YAAY,WAAW;AAC5C,QAAM,WAAW,OAAO,IAAI,YAAY,SAAS,IAAI;AACrD,MAAI,CAAC,SAAS,IAAI,EAAE,SAAS,QAAQ,GAAG;AACpC,WAAO,QAAQ,KAAK,oBAAoB,OAAO,IAAI,GAAG,MAAM;AAAA,EAChE;AACJ;AAQA,IAAM,gBAAgB,CAAC,YAAY,WAAW;AAC1C,QAAM,WAAW,OAAO,IAAI,YAAY,SAAS,KAAK;AACtD,MAAI,CAAC,SAAS,OAAO,SAAS,IAAI,EAAE,SAAS,QAAQ,GAAG;AACpD,WAAO,QAAQ,MAAM,kBAAkB,OAAO,IAAI,GAAG,MAAM;AAAA,EAC/D;AACJ;AAQA,IAAM,4BAA4B,CAAC,OAAO,oBAAoB;AAC1D,SAAO,QAAQ,MAAM,IAAI,GAAG,QAAQ,YAAY,CAAC,yBAAyB,gBAAgB,KAAK,MAAM,CAAC,GAAG;AAC7G;", "names": ["LogLevel"]}