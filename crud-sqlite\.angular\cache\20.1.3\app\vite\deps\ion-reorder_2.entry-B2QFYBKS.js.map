{"version": 3, "sources": ["../../../../../../node_modules/@ionic/core/dist/esm/ion-reorder_2.entry.js"], "sourcesContent": ["/*!\n * (C) Ionic http://ionicframework.com - MIT License\n */\nimport { r as registerInstance, e as getIonMode, h, j as Host, k as getElement, d as createEvent } from './index-B_U9CtaY.js';\nimport { j as reorderThreeOutline, k as reorderTwoSharp } from './index-BLV6ykCk.js';\nimport { f as findClosestIonContent, g as getScrollElement } from './index-BlJTBdxG.js';\nimport { r as raf } from './helpers-1O4D2b7y.js';\nimport { b as hapticSelectionStart, a as hapticSelectionChanged, h as hapticSelectionEnd } from './haptic-DzAMWJuk.js';\nimport './capacitor-CFERIeaU.js';\nimport './index-ZjP4CjeZ.js';\n\nconst reorderIosCss = \":host([slot]){display:none;line-height:0;z-index:100}.reorder-icon{display:block}::slotted(ion-icon){font-size:dynamic-font(16px)}.reorder-icon{font-size:2.125rem;opacity:0.4}\";\n\nconst reorderMdCss = \":host([slot]){display:none;line-height:0;z-index:100}.reorder-icon{display:block}::slotted(ion-icon){font-size:dynamic-font(16px)}.reorder-icon{font-size:1.9375rem;opacity:0.3}\";\n\nconst Reorder = class {\n    constructor(hostRef) {\n        registerInstance(this, hostRef);\n    }\n    onClick(ev) {\n        const reorderGroup = this.el.closest('ion-reorder-group');\n        ev.preventDefault();\n        // Only stop event propagation if the reorder is inside of an enabled\n        // reorder group. This allows interaction with clickable children components.\n        if (!reorderGroup || !reorderGroup.disabled) {\n            ev.stopImmediatePropagation();\n        }\n    }\n    render() {\n        const mode = getIonMode(this);\n        const reorderIcon = mode === 'ios' ? reorderThreeOutline : reorderTwoSharp;\n        return (h(Host, { key: 'e6807bb349725682e99e791ac65e729a360d64e8', class: mode }, h(\"slot\", { key: '1c691cdbffa6427ba08dc12184c69559ed5d5506' }, h(\"ion-icon\", { key: '8b4150302cdca475379582b2251737b5e74079b1', icon: reorderIcon, lazy: false, class: \"reorder-icon\", part: \"icon\", \"aria-hidden\": \"true\" }))));\n    }\n    get el() { return getElement(this); }\n};\nReorder.style = {\n    ios: reorderIosCss,\n    md: reorderMdCss\n};\n\nconst reorderGroupCss = \".reorder-list-active>*{display:block;-webkit-transition:-webkit-transform 300ms;transition:-webkit-transform 300ms;transition:transform 300ms;transition:transform 300ms, -webkit-transform 300ms;will-change:transform}.reorder-enabled{-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none}.reorder-enabled ion-reorder{display:block;cursor:-webkit-grab;cursor:grab;pointer-events:all;-ms-touch-action:none;touch-action:none}.reorder-selected,.reorder-selected ion-reorder{cursor:-webkit-grabbing;cursor:grabbing}.reorder-selected{position:relative;-webkit-transition:none !important;transition:none !important;-webkit-box-shadow:0 0 10px rgba(0, 0, 0, 0.4);box-shadow:0 0 10px rgba(0, 0, 0, 0.4);opacity:0.8;z-index:100}.reorder-visible ion-reorder .reorder-icon{-webkit-transform:translate3d(0,  0,  0);transform:translate3d(0,  0,  0)}\";\n\nconst ReorderGroup = class {\n    constructor(hostRef) {\n        registerInstance(this, hostRef);\n        this.ionItemReorder = createEvent(this, \"ionItemReorder\", 7);\n        this.lastToIndex = -1;\n        this.cachedHeights = [];\n        this.scrollElTop = 0;\n        this.scrollElBottom = 0;\n        this.scrollElInitial = 0;\n        this.containerTop = 0;\n        this.containerBottom = 0;\n        this.state = 0 /* ReorderGroupState.Idle */;\n        /**\n         * If `true`, the reorder will be hidden.\n         */\n        this.disabled = true;\n    }\n    disabledChanged() {\n        if (this.gesture) {\n            this.gesture.enable(!this.disabled);\n        }\n    }\n    async connectedCallback() {\n        const contentEl = findClosestIonContent(this.el);\n        if (contentEl) {\n            this.scrollEl = await getScrollElement(contentEl);\n        }\n        this.gesture = (await import('./index-CfgBF1SE.js')).createGesture({\n            el: this.el,\n            gestureName: 'reorder',\n            gesturePriority: 110,\n            threshold: 0,\n            direction: 'y',\n            passive: false,\n            canStart: (detail) => this.canStart(detail),\n            onStart: (ev) => this.onStart(ev),\n            onMove: (ev) => this.onMove(ev),\n            onEnd: () => this.onEnd(),\n        });\n        this.disabledChanged();\n    }\n    disconnectedCallback() {\n        this.onEnd();\n        if (this.gesture) {\n            this.gesture.destroy();\n            this.gesture = undefined;\n        }\n    }\n    /**\n     * Completes the reorder operation. Must be called by the `ionItemReorder` event.\n     * If a list of items is passed, the list will be reordered and returned in the\n     * proper order.\n     *\n     * If no parameters are passed or if `true` is passed in, the reorder will complete\n     * and the item will remain in the position it was dragged to. If `false` is passed,\n     * the reorder will complete and the item will bounce back to its original position.\n     *\n     * @param listOrReorder A list of items to be sorted and returned in the new order or a\n     * boolean of whether or not the reorder should reposition the item.\n     */\n    complete(listOrReorder) {\n        return Promise.resolve(this.completeReorder(listOrReorder));\n    }\n    canStart(ev) {\n        if (this.selectedItemEl || this.state !== 0 /* ReorderGroupState.Idle */) {\n            return false;\n        }\n        const target = ev.event.target;\n        const reorderEl = target.closest('ion-reorder');\n        if (!reorderEl) {\n            return false;\n        }\n        const item = findReorderItem(reorderEl, this.el);\n        if (!item) {\n            return false;\n        }\n        ev.data = item;\n        return true;\n    }\n    onStart(ev) {\n        ev.event.preventDefault();\n        const item = (this.selectedItemEl = ev.data);\n        const heights = this.cachedHeights;\n        heights.length = 0;\n        const el = this.el;\n        const children = el.children;\n        if (!children || children.length === 0) {\n            return;\n        }\n        let sum = 0;\n        for (let i = 0; i < children.length; i++) {\n            const child = children[i];\n            sum += child.offsetHeight;\n            heights.push(sum);\n            child.$ionIndex = i;\n        }\n        const box = el.getBoundingClientRect();\n        this.containerTop = box.top;\n        this.containerBottom = box.bottom;\n        if (this.scrollEl) {\n            const scrollBox = this.scrollEl.getBoundingClientRect();\n            this.scrollElInitial = this.scrollEl.scrollTop;\n            this.scrollElTop = scrollBox.top + AUTO_SCROLL_MARGIN;\n            this.scrollElBottom = scrollBox.bottom - AUTO_SCROLL_MARGIN;\n        }\n        else {\n            this.scrollElInitial = 0;\n            this.scrollElTop = 0;\n            this.scrollElBottom = 0;\n        }\n        this.lastToIndex = indexForItem(item);\n        this.selectedItemHeight = item.offsetHeight;\n        this.state = 1 /* ReorderGroupState.Active */;\n        item.classList.add(ITEM_REORDER_SELECTED);\n        hapticSelectionStart();\n    }\n    onMove(ev) {\n        const selectedItem = this.selectedItemEl;\n        if (!selectedItem) {\n            return;\n        }\n        // Scroll if we reach the scroll margins\n        const scroll = this.autoscroll(ev.currentY);\n        // // Get coordinate\n        const top = this.containerTop - scroll;\n        const bottom = this.containerBottom - scroll;\n        const currentY = Math.max(top, Math.min(ev.currentY, bottom));\n        const deltaY = scroll + currentY - ev.startY;\n        const normalizedY = currentY - top;\n        const toIndex = this.itemIndexForTop(normalizedY);\n        if (toIndex !== this.lastToIndex) {\n            const fromIndex = indexForItem(selectedItem);\n            this.lastToIndex = toIndex;\n            hapticSelectionChanged();\n            this.reorderMove(fromIndex, toIndex);\n        }\n        // Update selected item position\n        selectedItem.style.transform = `translateY(${deltaY}px)`;\n    }\n    onEnd() {\n        const selectedItemEl = this.selectedItemEl;\n        this.state = 2 /* ReorderGroupState.Complete */;\n        if (!selectedItemEl) {\n            this.state = 0 /* ReorderGroupState.Idle */;\n            return;\n        }\n        const toIndex = this.lastToIndex;\n        const fromIndex = indexForItem(selectedItemEl);\n        if (toIndex === fromIndex) {\n            this.completeReorder();\n        }\n        else {\n            this.ionItemReorder.emit({\n                from: fromIndex,\n                to: toIndex,\n                complete: this.completeReorder.bind(this),\n            });\n        }\n        hapticSelectionEnd();\n    }\n    completeReorder(listOrReorder) {\n        const selectedItemEl = this.selectedItemEl;\n        if (selectedItemEl && this.state === 2 /* ReorderGroupState.Complete */) {\n            const children = this.el.children;\n            const len = children.length;\n            const toIndex = this.lastToIndex;\n            const fromIndex = indexForItem(selectedItemEl);\n            /**\n             * insertBefore and setting the transform\n             * needs to happen in the same frame otherwise\n             * there will be a duplicate transition. This primarily\n             * impacts Firefox where insertBefore and transform operations\n             * are happening in two separate frames.\n             */\n            raf(() => {\n                if (toIndex !== fromIndex && (listOrReorder === undefined || listOrReorder === true)) {\n                    const ref = fromIndex < toIndex ? children[toIndex + 1] : children[toIndex];\n                    this.el.insertBefore(selectedItemEl, ref);\n                }\n                for (let i = 0; i < len; i++) {\n                    children[i].style['transform'] = '';\n                }\n            });\n            if (Array.isArray(listOrReorder)) {\n                listOrReorder = reorderArray(listOrReorder, fromIndex, toIndex);\n            }\n            selectedItemEl.style.transition = '';\n            selectedItemEl.classList.remove(ITEM_REORDER_SELECTED);\n            this.selectedItemEl = undefined;\n            this.state = 0 /* ReorderGroupState.Idle */;\n        }\n        return listOrReorder;\n    }\n    itemIndexForTop(deltaY) {\n        const heights = this.cachedHeights;\n        for (let i = 0; i < heights.length; i++) {\n            if (heights[i] > deltaY) {\n                return i;\n            }\n        }\n        return heights.length - 1;\n    }\n    /********* DOM WRITE ********* */\n    reorderMove(fromIndex, toIndex) {\n        const itemHeight = this.selectedItemHeight;\n        const children = this.el.children;\n        for (let i = 0; i < children.length; i++) {\n            const style = children[i].style;\n            let value = '';\n            if (i > fromIndex && i <= toIndex) {\n                value = `translateY(${-itemHeight}px)`;\n            }\n            else if (i < fromIndex && i >= toIndex) {\n                value = `translateY(${itemHeight}px)`;\n            }\n            style['transform'] = value;\n        }\n    }\n    autoscroll(posY) {\n        if (!this.scrollEl) {\n            return 0;\n        }\n        let amount = 0;\n        if (posY < this.scrollElTop) {\n            amount = -10;\n        }\n        else if (posY > this.scrollElBottom) {\n            amount = SCROLL_JUMP;\n        }\n        if (amount !== 0) {\n            this.scrollEl.scrollBy(0, amount);\n        }\n        return this.scrollEl.scrollTop - this.scrollElInitial;\n    }\n    render() {\n        const mode = getIonMode(this);\n        return (h(Host, { key: 'dfcdc3a6aa1b2fba15f861ec868d6a11e667c9de', class: {\n                [mode]: true,\n                'reorder-enabled': !this.disabled,\n                'reorder-list-active': this.state !== 0 /* ReorderGroupState.Idle */,\n            } }));\n    }\n    get el() { return getElement(this); }\n    static get watchers() { return {\n        \"disabled\": [\"disabledChanged\"]\n    }; }\n};\nconst indexForItem = (element) => {\n    return element['$ionIndex'];\n};\nconst findReorderItem = (node, container) => {\n    let parent;\n    while (node) {\n        parent = node.parentElement;\n        if (parent === container) {\n            return node;\n        }\n        node = parent;\n    }\n    return undefined;\n};\nconst AUTO_SCROLL_MARGIN = 60;\nconst SCROLL_JUMP = 10;\nconst ITEM_REORDER_SELECTED = 'reorder-selected';\nconst reorderArray = (array, from, to) => {\n    const element = array[from];\n    array.splice(from, 1);\n    array.splice(to, 0, element);\n    return array.slice();\n};\nReorderGroup.style = reorderGroupCss;\n\nexport { Reorder as ion_reorder, ReorderGroup as ion_reorder_group };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAWA,IAAM,gBAAgB;AAEtB,IAAM,eAAe;AAErB,IAAM,UAAU,MAAM;AAAA,EAClB,YAAY,SAAS;AACjB,qBAAiB,MAAM,OAAO;AAAA,EAClC;AAAA,EACA,QAAQ,IAAI;AACR,UAAM,eAAe,KAAK,GAAG,QAAQ,mBAAmB;AACxD,OAAG,eAAe;AAGlB,QAAI,CAAC,gBAAgB,CAAC,aAAa,UAAU;AACzC,SAAG,yBAAyB;AAAA,IAChC;AAAA,EACJ;AAAA,EACA,SAAS;AACL,UAAM,OAAO,WAAW,IAAI;AAC5B,UAAM,cAAc,SAAS,QAAQ,sBAAsB;AAC3D,WAAQ,EAAE,MAAM,EAAE,KAAK,4CAA4C,OAAO,KAAK,GAAG,EAAE,QAAQ,EAAE,KAAK,2CAA2C,GAAG,EAAE,YAAY,EAAE,KAAK,4CAA4C,MAAM,aAAa,MAAM,OAAO,OAAO,gBAAgB,MAAM,QAAQ,eAAe,OAAO,CAAC,CAAC,CAAC;AAAA,EACpT;AAAA,EACA,IAAI,KAAK;AAAE,WAAO,WAAW,IAAI;AAAA,EAAG;AACxC;AACA,QAAQ,QAAQ;AAAA,EACZ,KAAK;AAAA,EACL,IAAI;AACR;AAEA,IAAM,kBAAkB;AAExB,IAAM,eAAe,MAAM;AAAA,EACvB,YAAY,SAAS;AACjB,qBAAiB,MAAM,OAAO;AAC9B,SAAK,iBAAiB,YAAY,MAAM,kBAAkB,CAAC;AAC3D,SAAK,cAAc;AACnB,SAAK,gBAAgB,CAAC;AACtB,SAAK,cAAc;AACnB,SAAK,iBAAiB;AACtB,SAAK,kBAAkB;AACvB,SAAK,eAAe;AACpB,SAAK,kBAAkB;AACvB,SAAK,QAAQ;AAIb,SAAK,WAAW;AAAA,EACpB;AAAA,EACA,kBAAkB;AACd,QAAI,KAAK,SAAS;AACd,WAAK,QAAQ,OAAO,CAAC,KAAK,QAAQ;AAAA,IACtC;AAAA,EACJ;AAAA,EACM,oBAAoB;AAAA;AACtB,YAAM,YAAY,sBAAsB,KAAK,EAAE;AAC/C,UAAI,WAAW;AACX,aAAK,WAAW,MAAM,iBAAiB,SAAS;AAAA,MACpD;AACA,WAAK,WAAW,MAAM,OAAO,8BAAqB,GAAG,cAAc;AAAA,QAC/D,IAAI,KAAK;AAAA,QACT,aAAa;AAAA,QACb,iBAAiB;AAAA,QACjB,WAAW;AAAA,QACX,WAAW;AAAA,QACX,SAAS;AAAA,QACT,UAAU,CAAC,WAAW,KAAK,SAAS,MAAM;AAAA,QAC1C,SAAS,CAAC,OAAO,KAAK,QAAQ,EAAE;AAAA,QAChC,QAAQ,CAAC,OAAO,KAAK,OAAO,EAAE;AAAA,QAC9B,OAAO,MAAM,KAAK,MAAM;AAAA,MAC5B,CAAC;AACD,WAAK,gBAAgB;AAAA,IACzB;AAAA;AAAA,EACA,uBAAuB;AACnB,SAAK,MAAM;AACX,QAAI,KAAK,SAAS;AACd,WAAK,QAAQ,QAAQ;AACrB,WAAK,UAAU;AAAA,IACnB;AAAA,EACJ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAaA,SAAS,eAAe;AACpB,WAAO,QAAQ,QAAQ,KAAK,gBAAgB,aAAa,CAAC;AAAA,EAC9D;AAAA,EACA,SAAS,IAAI;AACT,QAAI,KAAK,kBAAkB,KAAK,UAAU,GAAgC;AACtE,aAAO;AAAA,IACX;AACA,UAAM,SAAS,GAAG,MAAM;AACxB,UAAM,YAAY,OAAO,QAAQ,aAAa;AAC9C,QAAI,CAAC,WAAW;AACZ,aAAO;AAAA,IACX;AACA,UAAM,OAAO,gBAAgB,WAAW,KAAK,EAAE;AAC/C,QAAI,CAAC,MAAM;AACP,aAAO;AAAA,IACX;AACA,OAAG,OAAO;AACV,WAAO;AAAA,EACX;AAAA,EACA,QAAQ,IAAI;AACR,OAAG,MAAM,eAAe;AACxB,UAAM,OAAQ,KAAK,iBAAiB,GAAG;AACvC,UAAM,UAAU,KAAK;AACrB,YAAQ,SAAS;AACjB,UAAM,KAAK,KAAK;AAChB,UAAM,WAAW,GAAG;AACpB,QAAI,CAAC,YAAY,SAAS,WAAW,GAAG;AACpC;AAAA,IACJ;AACA,QAAI,MAAM;AACV,aAAS,IAAI,GAAG,IAAI,SAAS,QAAQ,KAAK;AACtC,YAAM,QAAQ,SAAS,CAAC;AACxB,aAAO,MAAM;AACb,cAAQ,KAAK,GAAG;AAChB,YAAM,YAAY;AAAA,IACtB;AACA,UAAM,MAAM,GAAG,sBAAsB;AACrC,SAAK,eAAe,IAAI;AACxB,SAAK,kBAAkB,IAAI;AAC3B,QAAI,KAAK,UAAU;AACf,YAAM,YAAY,KAAK,SAAS,sBAAsB;AACtD,WAAK,kBAAkB,KAAK,SAAS;AACrC,WAAK,cAAc,UAAU,MAAM;AACnC,WAAK,iBAAiB,UAAU,SAAS;AAAA,IAC7C,OACK;AACD,WAAK,kBAAkB;AACvB,WAAK,cAAc;AACnB,WAAK,iBAAiB;AAAA,IAC1B;AACA,SAAK,cAAc,aAAa,IAAI;AACpC,SAAK,qBAAqB,KAAK;AAC/B,SAAK,QAAQ;AACb,SAAK,UAAU,IAAI,qBAAqB;AACxC,yBAAqB;AAAA,EACzB;AAAA,EACA,OAAO,IAAI;AACP,UAAM,eAAe,KAAK;AAC1B,QAAI,CAAC,cAAc;AACf;AAAA,IACJ;AAEA,UAAM,SAAS,KAAK,WAAW,GAAG,QAAQ;AAE1C,UAAM,MAAM,KAAK,eAAe;AAChC,UAAM,SAAS,KAAK,kBAAkB;AACtC,UAAM,WAAW,KAAK,IAAI,KAAK,KAAK,IAAI,GAAG,UAAU,MAAM,CAAC;AAC5D,UAAM,SAAS,SAAS,WAAW,GAAG;AACtC,UAAM,cAAc,WAAW;AAC/B,UAAM,UAAU,KAAK,gBAAgB,WAAW;AAChD,QAAI,YAAY,KAAK,aAAa;AAC9B,YAAM,YAAY,aAAa,YAAY;AAC3C,WAAK,cAAc;AACnB,6BAAuB;AACvB,WAAK,YAAY,WAAW,OAAO;AAAA,IACvC;AAEA,iBAAa,MAAM,YAAY,cAAc,MAAM;AAAA,EACvD;AAAA,EACA,QAAQ;AACJ,UAAM,iBAAiB,KAAK;AAC5B,SAAK,QAAQ;AACb,QAAI,CAAC,gBAAgB;AACjB,WAAK,QAAQ;AACb;AAAA,IACJ;AACA,UAAM,UAAU,KAAK;AACrB,UAAM,YAAY,aAAa,cAAc;AAC7C,QAAI,YAAY,WAAW;AACvB,WAAK,gBAAgB;AAAA,IACzB,OACK;AACD,WAAK,eAAe,KAAK;AAAA,QACrB,MAAM;AAAA,QACN,IAAI;AAAA,QACJ,UAAU,KAAK,gBAAgB,KAAK,IAAI;AAAA,MAC5C,CAAC;AAAA,IACL;AACA,uBAAmB;AAAA,EACvB;AAAA,EACA,gBAAgB,eAAe;AAC3B,UAAM,iBAAiB,KAAK;AAC5B,QAAI,kBAAkB,KAAK,UAAU,GAAoC;AACrE,YAAM,WAAW,KAAK,GAAG;AACzB,YAAM,MAAM,SAAS;AACrB,YAAM,UAAU,KAAK;AACrB,YAAM,YAAY,aAAa,cAAc;AAQ7C,UAAI,MAAM;AACN,YAAI,YAAY,cAAc,kBAAkB,UAAa,kBAAkB,OAAO;AAClF,gBAAM,MAAM,YAAY,UAAU,SAAS,UAAU,CAAC,IAAI,SAAS,OAAO;AAC1E,eAAK,GAAG,aAAa,gBAAgB,GAAG;AAAA,QAC5C;AACA,iBAAS,IAAI,GAAG,IAAI,KAAK,KAAK;AAC1B,mBAAS,CAAC,EAAE,MAAM,WAAW,IAAI;AAAA,QACrC;AAAA,MACJ,CAAC;AACD,UAAI,MAAM,QAAQ,aAAa,GAAG;AAC9B,wBAAgB,aAAa,eAAe,WAAW,OAAO;AAAA,MAClE;AACA,qBAAe,MAAM,aAAa;AAClC,qBAAe,UAAU,OAAO,qBAAqB;AACrD,WAAK,iBAAiB;AACtB,WAAK,QAAQ;AAAA,IACjB;AACA,WAAO;AAAA,EACX;AAAA,EACA,gBAAgB,QAAQ;AACpB,UAAM,UAAU,KAAK;AACrB,aAAS,IAAI,GAAG,IAAI,QAAQ,QAAQ,KAAK;AACrC,UAAI,QAAQ,CAAC,IAAI,QAAQ;AACrB,eAAO;AAAA,MACX;AAAA,IACJ;AACA,WAAO,QAAQ,SAAS;AAAA,EAC5B;AAAA;AAAA,EAEA,YAAY,WAAW,SAAS;AAC5B,UAAM,aAAa,KAAK;AACxB,UAAM,WAAW,KAAK,GAAG;AACzB,aAAS,IAAI,GAAG,IAAI,SAAS,QAAQ,KAAK;AACtC,YAAM,QAAQ,SAAS,CAAC,EAAE;AAC1B,UAAI,QAAQ;AACZ,UAAI,IAAI,aAAa,KAAK,SAAS;AAC/B,gBAAQ,cAAc,CAAC,UAAU;AAAA,MACrC,WACS,IAAI,aAAa,KAAK,SAAS;AACpC,gBAAQ,cAAc,UAAU;AAAA,MACpC;AACA,YAAM,WAAW,IAAI;AAAA,IACzB;AAAA,EACJ;AAAA,EACA,WAAW,MAAM;AACb,QAAI,CAAC,KAAK,UAAU;AAChB,aAAO;AAAA,IACX;AACA,QAAI,SAAS;AACb,QAAI,OAAO,KAAK,aAAa;AACzB,eAAS;AAAA,IACb,WACS,OAAO,KAAK,gBAAgB;AACjC,eAAS;AAAA,IACb;AACA,QAAI,WAAW,GAAG;AACd,WAAK,SAAS,SAAS,GAAG,MAAM;AAAA,IACpC;AACA,WAAO,KAAK,SAAS,YAAY,KAAK;AAAA,EAC1C;AAAA,EACA,SAAS;AACL,UAAM,OAAO,WAAW,IAAI;AAC5B,WAAQ,EAAE,MAAM,EAAE,KAAK,4CAA4C,OAAO;AAAA,MAClE,CAAC,IAAI,GAAG;AAAA,MACR,mBAAmB,CAAC,KAAK;AAAA,MACzB,uBAAuB,KAAK,UAAU;AAAA,IAC1C,EAAE,CAAC;AAAA,EACX;AAAA,EACA,IAAI,KAAK;AAAE,WAAO,WAAW,IAAI;AAAA,EAAG;AAAA,EACpC,WAAW,WAAW;AAAE,WAAO;AAAA,MAC3B,YAAY,CAAC,iBAAiB;AAAA,IAClC;AAAA,EAAG;AACP;AACA,IAAM,eAAe,CAAC,YAAY;AAC9B,SAAO,QAAQ,WAAW;AAC9B;AACA,IAAM,kBAAkB,CAAC,MAAM,cAAc;AACzC,MAAI;AACJ,SAAO,MAAM;AACT,aAAS,KAAK;AACd,QAAI,WAAW,WAAW;AACtB,aAAO;AAAA,IACX;AACA,WAAO;AAAA,EACX;AACA,SAAO;AACX;AACA,IAAM,qBAAqB;AAC3B,IAAM,cAAc;AACpB,IAAM,wBAAwB;AAC9B,IAAM,eAAe,CAAC,OAAO,MAAM,OAAO;AACtC,QAAM,UAAU,MAAM,IAAI;AAC1B,QAAM,OAAO,MAAM,CAAC;AACpB,QAAM,OAAO,IAAI,GAAG,OAAO;AAC3B,SAAO,MAAM,MAAM;AACvB;AACA,aAAa,QAAQ;", "names": []}