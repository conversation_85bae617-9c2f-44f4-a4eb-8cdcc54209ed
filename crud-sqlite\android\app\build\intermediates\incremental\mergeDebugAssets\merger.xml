<?xml version="1.0" encoding="utf-8"?>
<merger version="3"><dataSet config=":capacitor-cordova-android-plugins" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\OneDrive\Desktop\FRONTEND DEVELOPER\crud-sqlite\android\capacitor-cordova-android-plugins\build\intermediates\assets\debug\mergeDebugAssets"/></dataSet><dataSet config=":capacitor-android" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\OneDrive\Desktop\FRONTEND DEVELOPER\crud-sqlite\node_modules\@capacitor\android\capacitor\build\intermediates\assets\debug\mergeDebugAssets"><file name="native-bridge.js" path="C:\Users\<USER>\OneDrive\Desktop\FRONTEND DEVELOPER\crud-sqlite\node_modules\@capacitor\android\capacitor\build\intermediates\assets\debug\mergeDebugAssets\native-bridge.js"/></source></dataSet><dataSet config=":capacitor-status-bar" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\OneDrive\Desktop\FRONTEND DEVELOPER\crud-sqlite\node_modules\@capacitor\status-bar\android\build\intermediates\assets\debug\mergeDebugAssets"/></dataSet><dataSet config=":capacitor-keyboard" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\OneDrive\Desktop\FRONTEND DEVELOPER\crud-sqlite\node_modules\@capacitor\keyboard\android\build\intermediates\assets\debug\mergeDebugAssets"/></dataSet><dataSet config=":capacitor-haptics" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\OneDrive\Desktop\FRONTEND DEVELOPER\crud-sqlite\node_modules\@capacitor\haptics\android\build\intermediates\assets\debug\mergeDebugAssets"/></dataSet><dataSet config=":capacitor-app" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\OneDrive\Desktop\FRONTEND DEVELOPER\crud-sqlite\node_modules\@capacitor\app\android\build\intermediates\assets\debug\mergeDebugAssets"/></dataSet><dataSet config=":capacitor-community-sqlite" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\OneDrive\Desktop\FRONTEND DEVELOPER\crud-sqlite\node_modules\@capacitor-community\sqlite\android\build\intermediates\assets\debug\mergeDebugAssets"/></dataSet><dataSet config="main" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\OneDrive\Desktop\FRONTEND DEVELOPER\crud-sqlite\android\app\src\main\assets"><file name="capacitor.config.json" path="C:\Users\<USER>\OneDrive\Desktop\FRONTEND DEVELOPER\crud-sqlite\android\app\src\main\assets\capacitor.config.json"/><file name="capacitor.plugins.json" path="C:\Users\<USER>\OneDrive\Desktop\FRONTEND DEVELOPER\crud-sqlite\android\app\src\main\assets\capacitor.plugins.json"/><file name="public/3rdpartylicenses.txt" path="C:\Users\<USER>\OneDrive\Desktop\FRONTEND DEVELOPER\crud-sqlite\android\app\src\main\assets\public\3rdpartylicenses.txt"/><file name="public/assets/icon/favicon.png" path="C:\Users\<USER>\OneDrive\Desktop\FRONTEND DEVELOPER\crud-sqlite\android\app\src\main\assets\public\assets\icon\favicon.png"/><file name="public/assets/shapes.svg" path="C:\Users\<USER>\OneDrive\Desktop\FRONTEND DEVELOPER\crud-sqlite\android\app\src\main\assets\public\assets\shapes.svg"/><file name="public/chunk-2YSZFPCQ.js" path="C:\Users\<USER>\OneDrive\Desktop\FRONTEND DEVELOPER\crud-sqlite\android\app\src\main\assets\public\chunk-2YSZFPCQ.js"/><file name="public/chunk-3IDTDWAV.js" path="C:\Users\<USER>\OneDrive\Desktop\FRONTEND DEVELOPER\crud-sqlite\android\app\src\main\assets\public\chunk-3IDTDWAV.js"/><file name="public/chunk-42C7ZIID.js" path="C:\Users\<USER>\OneDrive\Desktop\FRONTEND DEVELOPER\crud-sqlite\android\app\src\main\assets\public\chunk-42C7ZIID.js"/><file name="public/chunk-42K7QRLD.js" path="C:\Users\<USER>\OneDrive\Desktop\FRONTEND DEVELOPER\crud-sqlite\android\app\src\main\assets\public\chunk-42K7QRLD.js"/><file name="public/chunk-4WFVMWDK.js" path="C:\Users\<USER>\OneDrive\Desktop\FRONTEND DEVELOPER\crud-sqlite\android\app\src\main\assets\public\chunk-4WFVMWDK.js"/><file name="public/chunk-57YRIO75.js" path="C:\Users\<USER>\OneDrive\Desktop\FRONTEND DEVELOPER\crud-sqlite\android\app\src\main\assets\public\chunk-57YRIO75.js"/><file name="public/chunk-5ZJQD5L7.js" path="C:\Users\<USER>\OneDrive\Desktop\FRONTEND DEVELOPER\crud-sqlite\android\app\src\main\assets\public\chunk-5ZJQD5L7.js"/><file name="public/chunk-6WAW2KHA.js" path="C:\Users\<USER>\OneDrive\Desktop\FRONTEND DEVELOPER\crud-sqlite\android\app\src\main\assets\public\chunk-6WAW2KHA.js"/><file name="public/chunk-7ZT7QFGG.js" path="C:\Users\<USER>\OneDrive\Desktop\FRONTEND DEVELOPER\crud-sqlite\android\app\src\main\assets\public\chunk-7ZT7QFGG.js"/><file name="public/chunk-C5RQ2IC2.js" path="C:\Users\<USER>\OneDrive\Desktop\FRONTEND DEVELOPER\crud-sqlite\android\app\src\main\assets\public\chunk-C5RQ2IC2.js"/><file name="public/chunk-CJYL5T3I.js" path="C:\Users\<USER>\OneDrive\Desktop\FRONTEND DEVELOPER\crud-sqlite\android\app\src\main\assets\public\chunk-CJYL5T3I.js"/><file name="public/chunk-EN5Y6YEK.js" path="C:\Users\<USER>\OneDrive\Desktop\FRONTEND DEVELOPER\crud-sqlite\android\app\src\main\assets\public\chunk-EN5Y6YEK.js"/><file name="public/chunk-FA25AEA4.js" path="C:\Users\<USER>\OneDrive\Desktop\FRONTEND DEVELOPER\crud-sqlite\android\app\src\main\assets\public\chunk-FA25AEA4.js"/><file name="public/chunk-ICSGBKZQ.js" path="C:\Users\<USER>\OneDrive\Desktop\FRONTEND DEVELOPER\crud-sqlite\android\app\src\main\assets\public\chunk-ICSGBKZQ.js"/><file name="public/chunk-JE2PG4HF.js" path="C:\Users\<USER>\OneDrive\Desktop\FRONTEND DEVELOPER\crud-sqlite\android\app\src\main\assets\public\chunk-JE2PG4HF.js"/><file name="public/chunk-JHI3MBHO.js" path="C:\Users\<USER>\OneDrive\Desktop\FRONTEND DEVELOPER\crud-sqlite\android\app\src\main\assets\public\chunk-JHI3MBHO.js"/><file name="public/chunk-K7XANT4P.js" path="C:\Users\<USER>\OneDrive\Desktop\FRONTEND DEVELOPER\crud-sqlite\android\app\src\main\assets\public\chunk-K7XANT4P.js"/><file name="public/chunk-M2X7KQLB.js" path="C:\Users\<USER>\OneDrive\Desktop\FRONTEND DEVELOPER\crud-sqlite\android\app\src\main\assets\public\chunk-M2X7KQLB.js"/><file name="public/chunk-QDU6VP7L.js" path="C:\Users\<USER>\OneDrive\Desktop\FRONTEND DEVELOPER\crud-sqlite\android\app\src\main\assets\public\chunk-QDU6VP7L.js"/><file name="public/chunk-QUM6RZVN.js" path="C:\Users\<USER>\OneDrive\Desktop\FRONTEND DEVELOPER\crud-sqlite\android\app\src\main\assets\public\chunk-QUM6RZVN.js"/><file name="public/chunk-R5HL6L5F.js" path="C:\Users\<USER>\OneDrive\Desktop\FRONTEND DEVELOPER\crud-sqlite\android\app\src\main\assets\public\chunk-R5HL6L5F.js"/><file name="public/chunk-R5SHVWC7.js" path="C:\Users\<USER>\OneDrive\Desktop\FRONTEND DEVELOPER\crud-sqlite\android\app\src\main\assets\public\chunk-R5SHVWC7.js"/><file name="public/chunk-REYR55MP.js" path="C:\Users\<USER>\OneDrive\Desktop\FRONTEND DEVELOPER\crud-sqlite\android\app\src\main\assets\public\chunk-REYR55MP.js"/><file name="public/chunk-ULUBRAOE.js" path="C:\Users\<USER>\OneDrive\Desktop\FRONTEND DEVELOPER\crud-sqlite\android\app\src\main\assets\public\chunk-ULUBRAOE.js"/><file name="public/chunk-VCHLP75Z.js" path="C:\Users\<USER>\OneDrive\Desktop\FRONTEND DEVELOPER\crud-sqlite\android\app\src\main\assets\public\chunk-VCHLP75Z.js"/><file name="public/chunk-VNBY24TM.js" path="C:\Users\<USER>\OneDrive\Desktop\FRONTEND DEVELOPER\crud-sqlite\android\app\src\main\assets\public\chunk-VNBY24TM.js"/><file name="public/chunk-YJ7VB3TT.js" path="C:\Users\<USER>\OneDrive\Desktop\FRONTEND DEVELOPER\crud-sqlite\android\app\src\main\assets\public\chunk-YJ7VB3TT.js"/><file name="public/chunk-YSN7XVTD.js" path="C:\Users\<USER>\OneDrive\Desktop\FRONTEND DEVELOPER\crud-sqlite\android\app\src\main\assets\public\chunk-YSN7XVTD.js"/><file name="public/chunk-ZCM4LAKF.js" path="C:\Users\<USER>\OneDrive\Desktop\FRONTEND DEVELOPER\crud-sqlite\android\app\src\main\assets\public\chunk-ZCM4LAKF.js"/><file name="public/chunk-ZXCQVRLE.js" path="C:\Users\<USER>\OneDrive\Desktop\FRONTEND DEVELOPER\crud-sqlite\android\app\src\main\assets\public\chunk-ZXCQVRLE.js"/><file name="public/cordova.js" path="C:\Users\<USER>\OneDrive\Desktop\FRONTEND DEVELOPER\crud-sqlite\android\app\src\main\assets\public\cordova.js"/><file name="public/cordova_plugins.js" path="C:\Users\<USER>\OneDrive\Desktop\FRONTEND DEVELOPER\crud-sqlite\android\app\src\main\assets\public\cordova_plugins.js"/><file name="public/index.html" path="C:\Users\<USER>\OneDrive\Desktop\FRONTEND DEVELOPER\crud-sqlite\android\app\src\main\assets\public\index.html"/><file name="public/main-QFUYR53X.js" path="C:\Users\<USER>\OneDrive\Desktop\FRONTEND DEVELOPER\crud-sqlite\android\app\src\main\assets\public\main-QFUYR53X.js"/><file name="public/polyfills-6BIPK22E.js" path="C:\Users\<USER>\OneDrive\Desktop\FRONTEND DEVELOPER\crud-sqlite\android\app\src\main\assets\public\polyfills-6BIPK22E.js"/><file name="public/prerendered-routes.json" path="C:\Users\<USER>\OneDrive\Desktop\FRONTEND DEVELOPER\crud-sqlite\android\app\src\main\assets\public\prerendered-routes.json"/><file name="public/styles-JQSYE4E5.css" path="C:\Users\<USER>\OneDrive\Desktop\FRONTEND DEVELOPER\crud-sqlite\android\app\src\main\assets\public\styles-JQSYE4E5.css"/></source></dataSet><dataSet config="debug" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\OneDrive\Desktop\FRONTEND DEVELOPER\crud-sqlite\android\app\src\debug\assets"/></dataSet><dataSet config="generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\OneDrive\Desktop\FRONTEND DEVELOPER\crud-sqlite\android\app\build\intermediates\shader_assets\debug\compileDebugShaders\out"/></dataSet></merger>