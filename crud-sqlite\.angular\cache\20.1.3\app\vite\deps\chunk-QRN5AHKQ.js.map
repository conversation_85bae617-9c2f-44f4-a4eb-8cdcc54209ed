{"version": 3, "sources": ["../../../../../../node_modules/@ionic/core/dist/esm/focus-visible-BmVRXR1y.js"], "sourcesContent": ["/*!\n * (C) Ionic http://ionicframework.com - MIT License\n */\nconst ION_FOCUSED = 'ion-focused';\nconst ION_FOCUSABLE = 'ion-focusable';\nconst FOCUS_KEYS = [\n    'Tab',\n    'ArrowDown',\n    'Space',\n    'Escape',\n    ' ',\n    'Shift',\n    'Enter',\n    'ArrowLeft',\n    'ArrowRight',\n    'ArrowUp',\n    'Home',\n    'End',\n];\nconst startFocusVisible = (rootEl) => {\n    let currentFocus = [];\n    let keyboardMode = true;\n    const ref = rootEl ? rootEl.shadowRoot : document;\n    const root = rootEl ? rootEl : document.body;\n    const setFocus = (elements) => {\n        currentFocus.forEach((el) => el.classList.remove(ION_FOCUSED));\n        elements.forEach((el) => el.classList.add(ION_FOCUSED));\n        currentFocus = elements;\n    };\n    const pointerDown = () => {\n        keyboardMode = false;\n        setFocus([]);\n    };\n    const onKeydown = (ev) => {\n        keyboardMode = FOCUS_KEYS.includes(ev.key);\n        if (!keyboardMode) {\n            setFocus([]);\n        }\n    };\n    const onFocusin = (ev) => {\n        if (keyboardMode && ev.composedPath !== undefined) {\n            const toFocus = ev.composedPath().filter((el) => {\n                // TODO(FW-2832): type\n                if (el.classList) {\n                    return el.classList.contains(ION_FOCUSABLE);\n                }\n                return false;\n            });\n            setFocus(toFocus);\n        }\n    };\n    const onFocusout = () => {\n        if (ref.activeElement === root) {\n            setFocus([]);\n        }\n    };\n    ref.addEventListener('keydown', onKeydown);\n    ref.addEventListener('focusin', onFocusin);\n    ref.addEventListener('focusout', onFocusout);\n    ref.addEventListener('touchstart', pointerDown, { passive: true });\n    ref.addEventListener('mousedown', pointerDown);\n    const destroy = () => {\n        ref.removeEventListener('keydown', onKeydown);\n        ref.removeEventListener('focusin', onFocusin);\n        ref.removeEventListener('focusout', onFocusout);\n        ref.removeEventListener('touchstart', pointerDown);\n        ref.removeEventListener('mousedown', pointerDown);\n    };\n    return {\n        destroy,\n        setFocus,\n    };\n};\n\nexport { startFocusVisible };\n"], "mappings": ";AAGA,IAAM,cAAc;AACpB,IAAM,gBAAgB;AACtB,IAAM,aAAa;AAAA,EACf;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACJ;AACA,IAAM,oBAAoB,CAAC,WAAW;AAClC,MAAI,eAAe,CAAC;AACpB,MAAI,eAAe;AACnB,QAAM,MAAM,SAAS,OAAO,aAAa;AACzC,QAAM,OAAO,SAAS,SAAS,SAAS;AACxC,QAAM,WAAW,CAAC,aAAa;AAC3B,iBAAa,QAAQ,CAAC,OAAO,GAAG,UAAU,OAAO,WAAW,CAAC;AAC7D,aAAS,QAAQ,CAAC,OAAO,GAAG,UAAU,IAAI,WAAW,CAAC;AACtD,mBAAe;AAAA,EACnB;AACA,QAAM,cAAc,MAAM;AACtB,mBAAe;AACf,aAAS,CAAC,CAAC;AAAA,EACf;AACA,QAAM,YAAY,CAAC,OAAO;AACtB,mBAAe,WAAW,SAAS,GAAG,GAAG;AACzC,QAAI,CAAC,cAAc;AACf,eAAS,CAAC,CAAC;AAAA,IACf;AAAA,EACJ;AACA,QAAM,YAAY,CAAC,OAAO;AACtB,QAAI,gBAAgB,GAAG,iBAAiB,QAAW;AAC/C,YAAM,UAAU,GAAG,aAAa,EAAE,OAAO,CAAC,OAAO;AAE7C,YAAI,GAAG,WAAW;AACd,iBAAO,GAAG,UAAU,SAAS,aAAa;AAAA,QAC9C;AACA,eAAO;AAAA,MACX,CAAC;AACD,eAAS,OAAO;AAAA,IACpB;AAAA,EACJ;AACA,QAAM,aAAa,MAAM;AACrB,QAAI,IAAI,kBAAkB,MAAM;AAC5B,eAAS,CAAC,CAAC;AAAA,IACf;AAAA,EACJ;AACA,MAAI,iBAAiB,WAAW,SAAS;AACzC,MAAI,iBAAiB,WAAW,SAAS;AACzC,MAAI,iBAAiB,YAAY,UAAU;AAC3C,MAAI,iBAAiB,cAAc,aAAa,EAAE,SAAS,KAAK,CAAC;AACjE,MAAI,iBAAiB,aAAa,WAAW;AAC7C,QAAM,UAAU,MAAM;AAClB,QAAI,oBAAoB,WAAW,SAAS;AAC5C,QAAI,oBAAoB,WAAW,SAAS;AAC5C,QAAI,oBAAoB,YAAY,UAAU;AAC9C,QAAI,oBAAoB,cAAc,WAAW;AACjD,QAAI,oBAAoB,aAAa,WAAW;AAAA,EACpD;AACA,SAAO;AAAA,IACH;AAAA,IACA;AAAA,EACJ;AACJ;", "names": []}