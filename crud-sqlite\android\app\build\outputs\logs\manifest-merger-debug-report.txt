-- Merging decision tree log ---
provider#androidx.core.content.FileProvider
INJECTED from C:\Users\<USER>\OneDrive\Desktop\FRONTEND DEVELOPER\crud-sqlite\android\app\src\main\AndroidManifest.xml:27:9-35:20
	android:grantUriPermissions
		ADDED from C:\Users\<USER>\OneDrive\Desktop\FRONTEND DEVELOPER\crud-sqlite\android\app\src\main\AndroidManifest.xml:31:13-47
	android:authorities
		INJECTED from C:\Users\<USER>\OneDrive\Desktop\FRONTEND DEVELOPER\crud-sqlite\android\app\src\main\AndroidManifest.xml
		ADDED from C:\Users\<USER>\OneDrive\Desktop\FRONTEND DEVELOPER\crud-sqlite\android\app\src\main\AndroidManifest.xml:29:13-64
	android:exported
		ADDED from C:\Users\<USER>\OneDrive\Desktop\FRONTEND DEVELOPER\crud-sqlite\android\app\src\main\AndroidManifest.xml:30:13-37
	android:name
		ADDED from C:\Users\<USER>\OneDrive\Desktop\FRONTEND DEVELOPER\crud-sqlite\android\app\src\main\AndroidManifest.xml:28:13-62
manifest
ADDED from C:\Users\<USER>\OneDrive\Desktop\FRONTEND DEVELOPER\crud-sqlite\android\app\src\main\AndroidManifest.xml:2:1-41:12
INJECTED from C:\Users\<USER>\OneDrive\Desktop\FRONTEND DEVELOPER\crud-sqlite\android\app\src\main\AndroidManifest.xml:2:1-41:12
INJECTED from C:\Users\<USER>\OneDrive\Desktop\FRONTEND DEVELOPER\crud-sqlite\android\app\src\main\AndroidManifest.xml:2:1-41:12
INJECTED from C:\Users\<USER>\OneDrive\Desktop\FRONTEND DEVELOPER\crud-sqlite\android\app\src\main\AndroidManifest.xml:2:1-41:12
MERGED from [:capacitor-community-sqlite] C:\Users\<USER>\OneDrive\Desktop\FRONTEND DEVELOPER\crud-sqlite\node_modules\@capacitor-community\sqlite\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:capacitor-app] C:\Users\<USER>\OneDrive\Desktop\FRONTEND DEVELOPER\crud-sqlite\node_modules\@capacitor\app\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:capacitor-haptics] C:\Users\<USER>\OneDrive\Desktop\FRONTEND DEVELOPER\crud-sqlite\node_modules\@capacitor\haptics\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-9:12
MERGED from [:capacitor-keyboard] C:\Users\<USER>\OneDrive\Desktop\FRONTEND DEVELOPER\crud-sqlite\node_modules\@capacitor\keyboard\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:capacitor-status-bar] C:\Users\<USER>\OneDrive\Desktop\FRONTEND DEVELOPER\crud-sqlite\node_modules\@capacitor\status-bar\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:capacitor-android] C:\Users\<USER>\OneDrive\Desktop\FRONTEND DEVELOPER\crud-sqlite\node_modules\@capacitor\android\capacitor\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:capacitor-cordova-android-plugins] C:\Users\<USER>\OneDrive\Desktop\FRONTEND DEVELOPER\crud-sqlite\android\capacitor-cordova-android-plugins\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-11:12
MERGED from [androidx.appcompat:appcompat-resources:1.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\0d0aa8469f3a45927795e5e14c85ad05\transformed\appcompat-resources-1.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.biometric:biometric:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\eaaca94d50affd3c3a86a8212751e60b\transformed\biometric-1.1.0\AndroidManifest.xml:17:1-29:12
MERGED from [androidx.appcompat:appcompat:1.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\e2542f07e03e83966d61d2ce34c14b7c\transformed\appcompat-1.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\7d963e06227f9c96672f16e0a743fb69\transformed\coordinatorlayout-1.2.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.core:core-splashscreen:1.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\53f40aa6f6d840a54b93ab2747ad6132\transformed\core-splashscreen-1.0.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.fragment:fragment:1.8.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\914c90e97af2aebbb6598590579e1b39\transformed\fragment-1.8.4\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.activity:activity:1.9.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\1851baad953a72983870260c24fb7d2a\transformed\activity-1.9.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1259f746d2b244f0e744518a197a9f06\transformed\cursoradapter-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.drawerlayout:drawerlayout:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\5a3e3a0aad6c7d3886d8ce00c57ca545\transformed\drawerlayout-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.emoji2:emoji2-views-helper:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\14165bf8eec30afa3e583ffa928fb932\transformed\emoji2-views-helper-1.3.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\e9683f504198d3d554b1474ed6ab66f7\transformed\emoji2-1.3.0\AndroidManifest.xml:17:1-35:12
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\eefc99772d271117ff6795f04cb066ff\transformed\viewpager-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.customview:customview:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\44e09d89c7501e0762d4ba24920b621b\transformed\customview-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.webkit:webkit:1.12.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\bcb73a203268f8e615381d06480e2c0c\transformed\webkit-1.12.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\beb5f06eff38a1c8e2c1945cc1d591bb\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\b167bcf7245e7d679842599db51547d1\transformed\vectordrawable-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\4ee185d8f5138a37e2052cba61bdfce2\transformed\savedstate-1.2.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\21f41f9029d98c5fe631598d215fb26f\transformed\loader-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-livedata:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\88653deaec603d633e4afa694d403d76\transformed\lifecycle-livedata-2.6.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\1a40dcf1dbe43aa4d3fcc9c9bef1a483\transformed\lifecycle-viewmodel-2.6.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\7fcaa2d94568f1111aa0d45f264971fb\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:17:1-35:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\11589e438a78fb6de52e886596aaaa7b\transformed\lifecycle-livedata-core-2.6.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\11c075f13e834455872b5f2e6a7d1f09\transformed\lifecycle-viewmodel-savedstate-2.6.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.core:core-ktx:1.15.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\a2eff8b0468781740ada75bfca18b6fc\transformed\core-ktx-1.15.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ea82f0e4740fea90ca38a885280cdee8\transformed\core-1.15.0\AndroidManifest.xml:17:1-30:12
MERGED from [androidx.lifecycle:lifecycle-runtime:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\99cf7c220a2a9f2a8cb7b1ae85acd02c\transformed\lifecycle-runtime-2.6.2\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\9004adbda7bfb1b3286dec38d922dd38\transformed\profileinstaller-1.3.1\AndroidManifest.xml:17:1-55:12
MERGED from [androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\bc6d32dbdad3a4b2c0ef6b4bde666a61\transformed\room-runtime-2.6.1\AndroidManifest.xml:17:1-31:12
MERGED from [androidx.sqlite:sqlite-framework:2.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\0c74dc3dc468678fcded4dbd7e17823a\transformed\sqlite-framework-2.4.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.sqlite:sqlite:2.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ee7d53abeb21c3cdf634393d4f175d4b\transformed\sqlite-2.4.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.security:security-crypto:1.1.0-alpha06] C:\Users\<USER>\.gradle\caches\8.13\transforms\fe76c8ca9816e5969645718f260e9a6f\transformed\security-crypto-1.1.0-alpha06\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\13d35c67cb3a04e7e3e833e3d793d5df\transformed\core-runtime-2.2.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\381b4363b5175c342310fb1bad1257c8\transformed\startup-runtime-1.1.1\AndroidManifest.xml:17:1-33:12
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\cfac28fcbcac30a18528a44d10260582\transformed\interpolator-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.tracing:tracing:1.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3ee10dd1fe31539ff0654f253cb5fcd3\transformed\tracing-1.2.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\b8c874ccc67c40b30f22c360ea452f17\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:17:1-27:12
MERGED from [androidx.annotation:annotation-experimental:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\e55552fb3405c226de11947ae024032c\transformed\annotation-experimental-1.4.1\AndroidManifest.xml:2:1-7:12
MERGED from [org.apache.cordova:framework:10.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\c058547ff78c83913e5ab28ab457a86d\transformed\framework-10.1.1\AndroidManifest.xml:20:1-27:12
MERGED from [net.zetetic:sqlcipher-android:4.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\8844262158cc7282a5b059e3855a2ef7\transformed\sqlcipher-android-4.6.1\AndroidManifest.xml:2:1-7:12
	package
		INJECTED from C:\Users\<USER>\OneDrive\Desktop\FRONTEND DEVELOPER\crud-sqlite\android\app\src\main\AndroidManifest.xml
	android:versionName
		INJECTED from C:\Users\<USER>\OneDrive\Desktop\FRONTEND DEVELOPER\crud-sqlite\android\app\src\main\AndroidManifest.xml
	android:versionCode
		INJECTED from C:\Users\<USER>\OneDrive\Desktop\FRONTEND DEVELOPER\crud-sqlite\android\app\src\main\AndroidManifest.xml
	xmlns:android
		ADDED from C:\Users\<USER>\OneDrive\Desktop\FRONTEND DEVELOPER\crud-sqlite\android\app\src\main\AndroidManifest.xml:2:11-69
application
ADDED from C:\Users\<USER>\OneDrive\Desktop\FRONTEND DEVELOPER\crud-sqlite\android\app\src\main\AndroidManifest.xml:4:5-36:19
INJECTED from C:\Users\<USER>\OneDrive\Desktop\FRONTEND DEVELOPER\crud-sqlite\android\app\src\main\AndroidManifest.xml:4:5-36:19
MERGED from [:capacitor-cordova-android-plugins] C:\Users\<USER>\OneDrive\Desktop\FRONTEND DEVELOPER\crud-sqlite\android\capacitor-cordova-android-plugins\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:5-9:19
MERGED from [:capacitor-cordova-android-plugins] C:\Users\<USER>\OneDrive\Desktop\FRONTEND DEVELOPER\crud-sqlite\android\capacitor-cordova-android-plugins\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:5-9:19
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\e9683f504198d3d554b1474ed6ab66f7\transformed\emoji2-1.3.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\e9683f504198d3d554b1474ed6ab66f7\transformed\emoji2-1.3.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\7fcaa2d94568f1111aa0d45f264971fb\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\7fcaa2d94568f1111aa0d45f264971fb\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ea82f0e4740fea90ca38a885280cdee8\transformed\core-1.15.0\AndroidManifest.xml:28:5-89
MERGED from [androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ea82f0e4740fea90ca38a885280cdee8\transformed\core-1.15.0\AndroidManifest.xml:28:5-89
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\9004adbda7bfb1b3286dec38d922dd38\transformed\profileinstaller-1.3.1\AndroidManifest.xml:23:5-53:19
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\9004adbda7bfb1b3286dec38d922dd38\transformed\profileinstaller-1.3.1\AndroidManifest.xml:23:5-53:19
MERGED from [androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\bc6d32dbdad3a4b2c0ef6b4bde666a61\transformed\room-runtime-2.6.1\AndroidManifest.xml:23:5-29:19
MERGED from [androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\bc6d32dbdad3a4b2c0ef6b4bde666a61\transformed\room-runtime-2.6.1\AndroidManifest.xml:23:5-29:19
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\381b4363b5175c342310fb1bad1257c8\transformed\startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\381b4363b5175c342310fb1bad1257c8\transformed\startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\b8c874ccc67c40b30f22c360ea452f17\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\b8c874ccc67c40b30f22c360ea452f17\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
	android:extractNativeLibs
		INJECTED from C:\Users\<USER>\OneDrive\Desktop\FRONTEND DEVELOPER\crud-sqlite\android\app\src\main\AndroidManifest.xml
	android:appComponentFactory
		ADDED from [androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ea82f0e4740fea90ca38a885280cdee8\transformed\core-1.15.0\AndroidManifest.xml:28:18-86
	android:supportsRtl
		ADDED from C:\Users\<USER>\OneDrive\Desktop\FRONTEND DEVELOPER\crud-sqlite\android\app\src\main\AndroidManifest.xml:9:9-35
	android:label
		ADDED from C:\Users\<USER>\OneDrive\Desktop\FRONTEND DEVELOPER\crud-sqlite\android\app\src\main\AndroidManifest.xml:7:9-41
	android:roundIcon
		ADDED from C:\Users\<USER>\OneDrive\Desktop\FRONTEND DEVELOPER\crud-sqlite\android\app\src\main\AndroidManifest.xml:8:9-54
	android:icon
		ADDED from C:\Users\<USER>\OneDrive\Desktop\FRONTEND DEVELOPER\crud-sqlite\android\app\src\main\AndroidManifest.xml:6:9-43
	android:allowBackup
		ADDED from C:\Users\<USER>\OneDrive\Desktop\FRONTEND DEVELOPER\crud-sqlite\android\app\src\main\AndroidManifest.xml:5:9-35
	android:theme
		ADDED from C:\Users\<USER>\OneDrive\Desktop\FRONTEND DEVELOPER\crud-sqlite\android\app\src\main\AndroidManifest.xml:10:9-40
activity#io.ionic.starter.MainActivity
ADDED from C:\Users\<USER>\OneDrive\Desktop\FRONTEND DEVELOPER\crud-sqlite\android\app\src\main\AndroidManifest.xml:12:9-25:20
	android:label
		ADDED from C:\Users\<USER>\OneDrive\Desktop\FRONTEND DEVELOPER\crud-sqlite\android\app\src\main\AndroidManifest.xml:15:13-56
	android:launchMode
		ADDED from C:\Users\<USER>\OneDrive\Desktop\FRONTEND DEVELOPER\crud-sqlite\android\app\src\main\AndroidManifest.xml:17:13-44
	android:exported
		ADDED from C:\Users\<USER>\OneDrive\Desktop\FRONTEND DEVELOPER\crud-sqlite\android\app\src\main\AndroidManifest.xml:18:13-36
	android:configChanges
		ADDED from C:\Users\<USER>\OneDrive\Desktop\FRONTEND DEVELOPER\crud-sqlite\android\app\src\main\AndroidManifest.xml:13:13-140
	android:theme
		ADDED from C:\Users\<USER>\OneDrive\Desktop\FRONTEND DEVELOPER\crud-sqlite\android\app\src\main\AndroidManifest.xml:16:13-62
	android:name
		ADDED from C:\Users\<USER>\OneDrive\Desktop\FRONTEND DEVELOPER\crud-sqlite\android\app\src\main\AndroidManifest.xml:14:13-41
intent-filter#action:name:android.intent.action.MAIN+category:name:android.intent.category.LAUNCHER
ADDED from C:\Users\<USER>\OneDrive\Desktop\FRONTEND DEVELOPER\crud-sqlite\android\app\src\main\AndroidManifest.xml:20:13-23:29
action#android.intent.action.MAIN
ADDED from C:\Users\<USER>\OneDrive\Desktop\FRONTEND DEVELOPER\crud-sqlite\android\app\src\main\AndroidManifest.xml:21:17-69
	android:name
		ADDED from C:\Users\<USER>\OneDrive\Desktop\FRONTEND DEVELOPER\crud-sqlite\android\app\src\main\AndroidManifest.xml:21:25-66
category#android.intent.category.LAUNCHER
ADDED from C:\Users\<USER>\OneDrive\Desktop\FRONTEND DEVELOPER\crud-sqlite\android\app\src\main\AndroidManifest.xml:22:17-77
	android:name
		ADDED from C:\Users\<USER>\OneDrive\Desktop\FRONTEND DEVELOPER\crud-sqlite\android\app\src\main\AndroidManifest.xml:22:27-74
uses-permission#android.permission.INTERNET
ADDED from C:\Users\<USER>\OneDrive\Desktop\FRONTEND DEVELOPER\crud-sqlite\android\app\src\main\AndroidManifest.xml:40:5-67
	android:name
		ADDED from C:\Users\<USER>\OneDrive\Desktop\FRONTEND DEVELOPER\crud-sqlite\android\app\src\main\AndroidManifest.xml:40:22-64
meta-data#android.support.FILE_PROVIDER_PATHS
ADDED from C:\Users\<USER>\OneDrive\Desktop\FRONTEND DEVELOPER\crud-sqlite\android\app\src\main\AndroidManifest.xml:32:13-34:64
	android:resource
		ADDED from C:\Users\<USER>\OneDrive\Desktop\FRONTEND DEVELOPER\crud-sqlite\android\app\src\main\AndroidManifest.xml:34:17-51
	android:name
		ADDED from C:\Users\<USER>\OneDrive\Desktop\FRONTEND DEVELOPER\crud-sqlite\android\app\src\main\AndroidManifest.xml:33:17-67
uses-sdk
INJECTED from C:\Users\<USER>\OneDrive\Desktop\FRONTEND DEVELOPER\crud-sqlite\android\app\src\main\AndroidManifest.xml reason: use-sdk injection requested
INJECTED from C:\Users\<USER>\OneDrive\Desktop\FRONTEND DEVELOPER\crud-sqlite\android\app\src\main\AndroidManifest.xml
INJECTED from C:\Users\<USER>\OneDrive\Desktop\FRONTEND DEVELOPER\crud-sqlite\android\app\src\main\AndroidManifest.xml
MERGED from [:capacitor-community-sqlite] C:\Users\<USER>\OneDrive\Desktop\FRONTEND DEVELOPER\crud-sqlite\node_modules\@capacitor-community\sqlite\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:capacitor-community-sqlite] C:\Users\<USER>\OneDrive\Desktop\FRONTEND DEVELOPER\crud-sqlite\node_modules\@capacitor-community\sqlite\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:capacitor-app] C:\Users\<USER>\OneDrive\Desktop\FRONTEND DEVELOPER\crud-sqlite\node_modules\@capacitor\app\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:capacitor-app] C:\Users\<USER>\OneDrive\Desktop\FRONTEND DEVELOPER\crud-sqlite\node_modules\@capacitor\app\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:capacitor-haptics] C:\Users\<USER>\OneDrive\Desktop\FRONTEND DEVELOPER\crud-sqlite\node_modules\@capacitor\haptics\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:capacitor-haptics] C:\Users\<USER>\OneDrive\Desktop\FRONTEND DEVELOPER\crud-sqlite\node_modules\@capacitor\haptics\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:capacitor-keyboard] C:\Users\<USER>\OneDrive\Desktop\FRONTEND DEVELOPER\crud-sqlite\node_modules\@capacitor\keyboard\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:capacitor-keyboard] C:\Users\<USER>\OneDrive\Desktop\FRONTEND DEVELOPER\crud-sqlite\node_modules\@capacitor\keyboard\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:capacitor-status-bar] C:\Users\<USER>\OneDrive\Desktop\FRONTEND DEVELOPER\crud-sqlite\node_modules\@capacitor\status-bar\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:capacitor-status-bar] C:\Users\<USER>\OneDrive\Desktop\FRONTEND DEVELOPER\crud-sqlite\node_modules\@capacitor\status-bar\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:capacitor-android] C:\Users\<USER>\OneDrive\Desktop\FRONTEND DEVELOPER\crud-sqlite\node_modules\@capacitor\android\capacitor\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:capacitor-android] C:\Users\<USER>\OneDrive\Desktop\FRONTEND DEVELOPER\crud-sqlite\node_modules\@capacitor\android\capacitor\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:capacitor-cordova-android-plugins] C:\Users\<USER>\OneDrive\Desktop\FRONTEND DEVELOPER\crud-sqlite\android\capacitor-cordova-android-plugins\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:6:5-44
MERGED from [:capacitor-cordova-android-plugins] C:\Users\<USER>\OneDrive\Desktop\FRONTEND DEVELOPER\crud-sqlite\android\capacitor-cordova-android-plugins\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:6:5-44
MERGED from [androidx.appcompat:appcompat-resources:1.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\0d0aa8469f3a45927795e5e14c85ad05\transformed\appcompat-resources-1.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.appcompat:appcompat-resources:1.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\0d0aa8469f3a45927795e5e14c85ad05\transformed\appcompat-resources-1.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.biometric:biometric:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\eaaca94d50affd3c3a86a8212751e60b\transformed\biometric-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.biometric:biometric:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\eaaca94d50affd3c3a86a8212751e60b\transformed\biometric-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.appcompat:appcompat:1.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\e2542f07e03e83966d61d2ce34c14b7c\transformed\appcompat-1.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.appcompat:appcompat:1.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\e2542f07e03e83966d61d2ce34c14b7c\transformed\appcompat-1.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\7d963e06227f9c96672f16e0a743fb69\transformed\coordinatorlayout-1.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\7d963e06227f9c96672f16e0a743fb69\transformed\coordinatorlayout-1.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.core:core-splashscreen:1.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\53f40aa6f6d840a54b93ab2747ad6132\transformed\core-splashscreen-1.0.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.core:core-splashscreen:1.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\53f40aa6f6d840a54b93ab2747ad6132\transformed\core-splashscreen-1.0.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.fragment:fragment:1.8.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\914c90e97af2aebbb6598590579e1b39\transformed\fragment-1.8.4\AndroidManifest.xml:5:5-44
MERGED from [androidx.fragment:fragment:1.8.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\914c90e97af2aebbb6598590579e1b39\transformed\fragment-1.8.4\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity:1.9.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\1851baad953a72983870260c24fb7d2a\transformed\activity-1.9.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity:1.9.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\1851baad953a72983870260c24fb7d2a\transformed\activity-1.9.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1259f746d2b244f0e744518a197a9f06\transformed\cursoradapter-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1259f746d2b244f0e744518a197a9f06\transformed\cursoradapter-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.drawerlayout:drawerlayout:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\5a3e3a0aad6c7d3886d8ce00c57ca545\transformed\drawerlayout-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.drawerlayout:drawerlayout:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\5a3e3a0aad6c7d3886d8ce00c57ca545\transformed\drawerlayout-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.emoji2:emoji2-views-helper:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\14165bf8eec30afa3e583ffa928fb932\transformed\emoji2-views-helper-1.3.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2-views-helper:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\14165bf8eec30afa3e583ffa928fb932\transformed\emoji2-views-helper-1.3.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\e9683f504198d3d554b1474ed6ab66f7\transformed\emoji2-1.3.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\e9683f504198d3d554b1474ed6ab66f7\transformed\emoji2-1.3.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\eefc99772d271117ff6795f04cb066ff\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\eefc99772d271117ff6795f04cb066ff\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.customview:customview:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\44e09d89c7501e0762d4ba24920b621b\transformed\customview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.customview:customview:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\44e09d89c7501e0762d4ba24920b621b\transformed\customview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.webkit:webkit:1.12.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\bcb73a203268f8e615381d06480e2c0c\transformed\webkit-1.12.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.webkit:webkit:1.12.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\bcb73a203268f8e615381d06480e2c0c\transformed\webkit-1.12.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\beb5f06eff38a1c8e2c1945cc1d591bb\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\beb5f06eff38a1c8e2c1945cc1d591bb\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\b167bcf7245e7d679842599db51547d1\transformed\vectordrawable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\b167bcf7245e7d679842599db51547d1\transformed\vectordrawable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\4ee185d8f5138a37e2052cba61bdfce2\transformed\savedstate-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\4ee185d8f5138a37e2052cba61bdfce2\transformed\savedstate-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\21f41f9029d98c5fe631598d215fb26f\transformed\loader-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\21f41f9029d98c5fe631598d215fb26f\transformed\loader-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\88653deaec603d633e4afa694d403d76\transformed\lifecycle-livedata-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\88653deaec603d633e4afa694d403d76\transformed\lifecycle-livedata-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\1a40dcf1dbe43aa4d3fcc9c9bef1a483\transformed\lifecycle-viewmodel-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\1a40dcf1dbe43aa4d3fcc9c9bef1a483\transformed\lifecycle-viewmodel-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\7fcaa2d94568f1111aa0d45f264971fb\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\7fcaa2d94568f1111aa0d45f264971fb\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\11589e438a78fb6de52e886596aaaa7b\transformed\lifecycle-livedata-core-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\11589e438a78fb6de52e886596aaaa7b\transformed\lifecycle-livedata-core-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\11c075f13e834455872b5f2e6a7d1f09\transformed\lifecycle-viewmodel-savedstate-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\11c075f13e834455872b5f2e6a7d1f09\transformed\lifecycle-viewmodel-savedstate-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core-ktx:1.15.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\a2eff8b0468781740ada75bfca18b6fc\transformed\core-ktx-1.15.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core-ktx:1.15.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\a2eff8b0468781740ada75bfca18b6fc\transformed\core-ktx-1.15.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ea82f0e4740fea90ca38a885280cdee8\transformed\core-1.15.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ea82f0e4740fea90ca38a885280cdee8\transformed\core-1.15.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\99cf7c220a2a9f2a8cb7b1ae85acd02c\transformed\lifecycle-runtime-2.6.2\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\99cf7c220a2a9f2a8cb7b1ae85acd02c\transformed\lifecycle-runtime-2.6.2\AndroidManifest.xml:5:5-44
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\9004adbda7bfb1b3286dec38d922dd38\transformed\profileinstaller-1.3.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\9004adbda7bfb1b3286dec38d922dd38\transformed\profileinstaller-1.3.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\bc6d32dbdad3a4b2c0ef6b4bde666a61\transformed\room-runtime-2.6.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\bc6d32dbdad3a4b2c0ef6b4bde666a61\transformed\room-runtime-2.6.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.sqlite:sqlite-framework:2.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\0c74dc3dc468678fcded4dbd7e17823a\transformed\sqlite-framework-2.4.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.sqlite:sqlite-framework:2.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\0c74dc3dc468678fcded4dbd7e17823a\transformed\sqlite-framework-2.4.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.sqlite:sqlite:2.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ee7d53abeb21c3cdf634393d4f175d4b\transformed\sqlite-2.4.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.sqlite:sqlite:2.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ee7d53abeb21c3cdf634393d4f175d4b\transformed\sqlite-2.4.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.security:security-crypto:1.1.0-alpha06] C:\Users\<USER>\.gradle\caches\8.13\transforms\fe76c8ca9816e5969645718f260e9a6f\transformed\security-crypto-1.1.0-alpha06\AndroidManifest.xml:20:5-44
MERGED from [androidx.security:security-crypto:1.1.0-alpha06] C:\Users\<USER>\.gradle\caches\8.13\transforms\fe76c8ca9816e5969645718f260e9a6f\transformed\security-crypto-1.1.0-alpha06\AndroidManifest.xml:20:5-44
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\13d35c67cb3a04e7e3e833e3d793d5df\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\13d35c67cb3a04e7e3e833e3d793d5df\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\381b4363b5175c342310fb1bad1257c8\transformed\startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\381b4363b5175c342310fb1bad1257c8\transformed\startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\cfac28fcbcac30a18528a44d10260582\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\cfac28fcbcac30a18528a44d10260582\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.tracing:tracing:1.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3ee10dd1fe31539ff0654f253cb5fcd3\transformed\tracing-1.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.tracing:tracing:1.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3ee10dd1fe31539ff0654f253cb5fcd3\transformed\tracing-1.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\b8c874ccc67c40b30f22c360ea452f17\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\b8c874ccc67c40b30f22c360ea452f17\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.annotation:annotation-experimental:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\e55552fb3405c226de11947ae024032c\transformed\annotation-experimental-1.4.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.annotation:annotation-experimental:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\e55552fb3405c226de11947ae024032c\transformed\annotation-experimental-1.4.1\AndroidManifest.xml:5:5-44
MERGED from [org.apache.cordova:framework:10.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\c058547ff78c83913e5ab28ab457a86d\transformed\framework-10.1.1\AndroidManifest.xml:25:5-44
MERGED from [org.apache.cordova:framework:10.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\c058547ff78c83913e5ab28ab457a86d\transformed\framework-10.1.1\AndroidManifest.xml:25:5-44
MERGED from [net.zetetic:sqlcipher-android:4.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\8844262158cc7282a5b059e3855a2ef7\transformed\sqlcipher-android-4.6.1\AndroidManifest.xml:5:5-44
MERGED from [net.zetetic:sqlcipher-android:4.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\8844262158cc7282a5b059e3855a2ef7\transformed\sqlcipher-android-4.6.1\AndroidManifest.xml:5:5-44
	android:targetSdkVersion
		INJECTED from C:\Users\<USER>\OneDrive\Desktop\FRONTEND DEVELOPER\crud-sqlite\android\app\src\main\AndroidManifest.xml
	android:minSdkVersion
		INJECTED from C:\Users\<USER>\OneDrive\Desktop\FRONTEND DEVELOPER\crud-sqlite\android\app\src\main\AndroidManifest.xml
uses-permission#android.permission.VIBRATE
ADDED from [:capacitor-haptics] C:\Users\<USER>\OneDrive\Desktop\FRONTEND DEVELOPER\crud-sqlite\node_modules\@capacitor\haptics\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-66
	android:name
		ADDED from [:capacitor-haptics] C:\Users\<USER>\OneDrive\Desktop\FRONTEND DEVELOPER\crud-sqlite\node_modules\@capacitor\haptics\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:22-63
uses-permission#android.permission.USE_BIOMETRIC
ADDED from [androidx.biometric:biometric:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\eaaca94d50affd3c3a86a8212751e60b\transformed\biometric-1.1.0\AndroidManifest.xml:24:5-72
	android:name
		ADDED from [androidx.biometric:biometric:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\eaaca94d50affd3c3a86a8212751e60b\transformed\biometric-1.1.0\AndroidManifest.xml:24:22-69
uses-permission#android.permission.USE_FINGERPRINT
ADDED from [androidx.biometric:biometric:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\eaaca94d50affd3c3a86a8212751e60b\transformed\biometric-1.1.0\AndroidManifest.xml:27:5-74
	android:name
		ADDED from [androidx.biometric:biometric:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\eaaca94d50affd3c3a86a8212751e60b\transformed\biometric-1.1.0\AndroidManifest.xml:27:22-71
provider#androidx.startup.InitializationProvider
ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\e9683f504198d3d554b1474ed6ab66f7\transformed\emoji2-1.3.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\7fcaa2d94568f1111aa0d45f264971fb\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\7fcaa2d94568f1111aa0d45f264971fb\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\9004adbda7bfb1b3286dec38d922dd38\transformed\profileinstaller-1.3.1\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\9004adbda7bfb1b3286dec38d922dd38\transformed\profileinstaller-1.3.1\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\381b4363b5175c342310fb1bad1257c8\transformed\startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\381b4363b5175c342310fb1bad1257c8\transformed\startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
	tools:node
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\e9683f504198d3d554b1474ed6ab66f7\transformed\emoji2-1.3.0\AndroidManifest.xml:28:13-31
	android:authorities
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\e9683f504198d3d554b1474ed6ab66f7\transformed\emoji2-1.3.0\AndroidManifest.xml:26:13-68
	android:exported
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\e9683f504198d3d554b1474ed6ab66f7\transformed\emoji2-1.3.0\AndroidManifest.xml:27:13-37
	android:name
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\e9683f504198d3d554b1474ed6ab66f7\transformed\emoji2-1.3.0\AndroidManifest.xml:25:13-67
meta-data#androidx.emoji2.text.EmojiCompatInitializer
ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\e9683f504198d3d554b1474ed6ab66f7\transformed\emoji2-1.3.0\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\e9683f504198d3d554b1474ed6ab66f7\transformed\emoji2-1.3.0\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\e9683f504198d3d554b1474ed6ab66f7\transformed\emoji2-1.3.0\AndroidManifest.xml:30:17-75
meta-data#androidx.lifecycle.ProcessLifecycleInitializer
ADDED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\7fcaa2d94568f1111aa0d45f264971fb\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\7fcaa2d94568f1111aa0d45f264971fb\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\7fcaa2d94568f1111aa0d45f264971fb\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:30:17-78
permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ea82f0e4740fea90ca38a885280cdee8\transformed\core-1.15.0\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ea82f0e4740fea90ca38a885280cdee8\transformed\core-1.15.0\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ea82f0e4740fea90ca38a885280cdee8\transformed\core-1.15.0\AndroidManifest.xml:23:9-81
permission#io.ionic.starter.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ea82f0e4740fea90ca38a885280cdee8\transformed\core-1.15.0\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ea82f0e4740fea90ca38a885280cdee8\transformed\core-1.15.0\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ea82f0e4740fea90ca38a885280cdee8\transformed\core-1.15.0\AndroidManifest.xml:23:9-81
uses-permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ea82f0e4740fea90ca38a885280cdee8\transformed\core-1.15.0\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ea82f0e4740fea90ca38a885280cdee8\transformed\core-1.15.0\AndroidManifest.xml:26:22-94
uses-permission#io.ionic.starter.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ea82f0e4740fea90ca38a885280cdee8\transformed\core-1.15.0\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ea82f0e4740fea90ca38a885280cdee8\transformed\core-1.15.0\AndroidManifest.xml:26:22-94
meta-data#androidx.profileinstaller.ProfileInstallerInitializer
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\9004adbda7bfb1b3286dec38d922dd38\transformed\profileinstaller-1.3.1\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\9004adbda7bfb1b3286dec38d922dd38\transformed\profileinstaller-1.3.1\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\9004adbda7bfb1b3286dec38d922dd38\transformed\profileinstaller-1.3.1\AndroidManifest.xml:30:17-85
receiver#androidx.profileinstaller.ProfileInstallReceiver
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\9004adbda7bfb1b3286dec38d922dd38\transformed\profileinstaller-1.3.1\AndroidManifest.xml:34:9-52:20
	android:enabled
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\9004adbda7bfb1b3286dec38d922dd38\transformed\profileinstaller-1.3.1\AndroidManifest.xml:37:13-35
	android:exported
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\9004adbda7bfb1b3286dec38d922dd38\transformed\profileinstaller-1.3.1\AndroidManifest.xml:38:13-36
	android:permission
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\9004adbda7bfb1b3286dec38d922dd38\transformed\profileinstaller-1.3.1\AndroidManifest.xml:39:13-57
	android:directBootAware
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\9004adbda7bfb1b3286dec38d922dd38\transformed\profileinstaller-1.3.1\AndroidManifest.xml:36:13-44
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\9004adbda7bfb1b3286dec38d922dd38\transformed\profileinstaller-1.3.1\AndroidManifest.xml:35:13-76
intent-filter#action:name:androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\9004adbda7bfb1b3286dec38d922dd38\transformed\profileinstaller-1.3.1\AndroidManifest.xml:40:13-42:29
action#androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\9004adbda7bfb1b3286dec38d922dd38\transformed\profileinstaller-1.3.1\AndroidManifest.xml:41:17-91
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\9004adbda7bfb1b3286dec38d922dd38\transformed\profileinstaller-1.3.1\AndroidManifest.xml:41:25-88
intent-filter#action:name:androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\9004adbda7bfb1b3286dec38d922dd38\transformed\profileinstaller-1.3.1\AndroidManifest.xml:43:13-45:29
action#androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\9004adbda7bfb1b3286dec38d922dd38\transformed\profileinstaller-1.3.1\AndroidManifest.xml:44:17-85
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\9004adbda7bfb1b3286dec38d922dd38\transformed\profileinstaller-1.3.1\AndroidManifest.xml:44:25-82
intent-filter#action:name:androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\9004adbda7bfb1b3286dec38d922dd38\transformed\profileinstaller-1.3.1\AndroidManifest.xml:46:13-48:29
action#androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\9004adbda7bfb1b3286dec38d922dd38\transformed\profileinstaller-1.3.1\AndroidManifest.xml:47:17-88
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\9004adbda7bfb1b3286dec38d922dd38\transformed\profileinstaller-1.3.1\AndroidManifest.xml:47:25-85
intent-filter#action:name:androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\9004adbda7bfb1b3286dec38d922dd38\transformed\profileinstaller-1.3.1\AndroidManifest.xml:49:13-51:29
action#androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\9004adbda7bfb1b3286dec38d922dd38\transformed\profileinstaller-1.3.1\AndroidManifest.xml:50:17-95
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\9004adbda7bfb1b3286dec38d922dd38\transformed\profileinstaller-1.3.1\AndroidManifest.xml:50:25-92
service#androidx.room.MultiInstanceInvalidationService
ADDED from [androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\bc6d32dbdad3a4b2c0ef6b4bde666a61\transformed\room-runtime-2.6.1\AndroidManifest.xml:24:9-28:63
	android:exported
		ADDED from [androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\bc6d32dbdad3a4b2c0ef6b4bde666a61\transformed\room-runtime-2.6.1\AndroidManifest.xml:27:13-37
	tools:ignore
		ADDED from [androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\bc6d32dbdad3a4b2c0ef6b4bde666a61\transformed\room-runtime-2.6.1\AndroidManifest.xml:28:13-60
	android:directBootAware
		ADDED from [androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\bc6d32dbdad3a4b2c0ef6b4bde666a61\transformed\room-runtime-2.6.1\AndroidManifest.xml:26:13-43
	android:name
		ADDED from [androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\bc6d32dbdad3a4b2c0ef6b4bde666a61\transformed\room-runtime-2.6.1\AndroidManifest.xml:25:13-74
