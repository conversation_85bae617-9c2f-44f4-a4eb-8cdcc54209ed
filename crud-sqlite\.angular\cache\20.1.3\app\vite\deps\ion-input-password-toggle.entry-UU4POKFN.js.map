{"version": 3, "sources": ["../../../../../../node_modules/@ionic/core/dist/esm/ion-input-password-toggle.entry.js"], "sourcesContent": ["/*!\n * (C) Ionic http://ionicframework.com - MIT License\n */\nimport { r as registerInstance, m as printIonWarning, e as getIonMode, h, j as Host, k as getElement } from './index-B_U9CtaY.js';\nimport { c as createColorClasses } from './theme-DiVJyqlX.js';\nimport { x as eyeOff, y as eye } from './index-BLV6ykCk.js';\n\nconst iosInputPasswordToggleCss = \"\";\n\nconst mdInputPasswordToggleCss = \"\";\n\nconst InputPasswordToggle = class {\n    constructor(hostRef) {\n        registerInstance(this, hostRef);\n        /**\n         * @internal\n         */\n        this.type = 'password';\n        this.togglePasswordVisibility = () => {\n            const { inputElRef } = this;\n            if (!inputElRef) {\n                return;\n            }\n            inputElRef.type = inputElRef.type === 'text' ? 'password' : 'text';\n        };\n    }\n    /**\n     * Whenever the input type changes we need to re-run validation to ensure the password\n     * toggle is being used with the correct input type. If the application changes the type\n     * outside of this component we also need to re-render so the correct icon is shown.\n     */\n    onTypeChange(newValue) {\n        if (newValue !== 'text' && newValue !== 'password') {\n            printIonWarning(`[ion-input-password-toggle] - Only inputs of type \"text\" or \"password\" are supported. Input of type \"${newValue}\" is not compatible.`, this.el);\n            return;\n        }\n    }\n    connectedCallback() {\n        const { el } = this;\n        const inputElRef = (this.inputElRef = el.closest('ion-input'));\n        if (!inputElRef) {\n            printIonWarning('[ion-input-password-toggle] - No ancestor ion-input found. This component must be slotted inside of an ion-input.', el);\n            return;\n        }\n        /**\n         * Important: Set the type in connectedCallback because the default value\n         * of this.type may not always be accurate. Usually inputs have the \"password\" type\n         * but it is possible to have the input to initially have the \"text\" type. In that scenario\n         * the wrong icon will show briefly before switching to the correct icon. Setting the\n         * type here allows us to avoid that flicker.\n         */\n        this.type = inputElRef.type;\n    }\n    disconnectedCallback() {\n        this.inputElRef = null;\n    }\n    render() {\n        var _a, _b;\n        const { color, type } = this;\n        const mode = getIonMode(this);\n        const showPasswordIcon = (_a = this.showIcon) !== null && _a !== void 0 ? _a : eye;\n        const hidePasswordIcon = (_b = this.hideIcon) !== null && _b !== void 0 ? _b : eyeOff;\n        const isPasswordVisible = type === 'text';\n        return (h(Host, { key: '91bc55664d496fe457518bd112865dd7811d0c17', class: createColorClasses(color, {\n                [mode]: true,\n            }) }, h(\"ion-button\", { key: 'f3e436422110c9cb4d5c0b83500255b24ab4cdef', mode: mode, color: color, fill: \"clear\", shape: \"round\", \"aria-checked\": isPasswordVisible ? 'true' : 'false', \"aria-label\": isPasswordVisible ? 'Hide password' : 'Show password', role: \"switch\", type: \"button\", onPointerDown: (ev) => {\n                /**\n                 * This prevents mobile browsers from\n                 * blurring the input when the password toggle\n                 * button is activated.\n                 */\n                ev.preventDefault();\n            }, onClick: this.togglePasswordVisibility }, h(\"ion-icon\", { key: '5c8b121153f148f92aa7cba0447673a4f6f3ad1e', slot: \"icon-only\", \"aria-hidden\": \"true\", icon: isPasswordVisible ? hidePasswordIcon : showPasswordIcon }))));\n    }\n    get el() { return getElement(this); }\n    static get watchers() { return {\n        \"type\": [\"onTypeChange\"]\n    }; }\n};\nInputPasswordToggle.style = {\n    ios: iosInputPasswordToggleCss,\n    md: mdInputPasswordToggleCss\n};\n\nexport { InputPasswordToggle as ion_input_password_toggle };\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAOA,IAAM,4BAA4B;AAElC,IAAM,2BAA2B;AAEjC,IAAM,sBAAsB,MAAM;AAAA,EAC9B,YAAY,SAAS;AACjB,qBAAiB,MAAM,OAAO;AAI9B,SAAK,OAAO;AACZ,SAAK,2BAA2B,MAAM;AAClC,YAAM,EAAE,WAAW,IAAI;AACvB,UAAI,CAAC,YAAY;AACb;AAAA,MACJ;AACA,iBAAW,OAAO,WAAW,SAAS,SAAS,aAAa;AAAA,IAChE;AAAA,EACJ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,aAAa,UAAU;AACnB,QAAI,aAAa,UAAU,aAAa,YAAY;AAChD,sBAAgB,wGAAwG,QAAQ,wBAAwB,KAAK,EAAE;AAC/J;AAAA,IACJ;AAAA,EACJ;AAAA,EACA,oBAAoB;AAChB,UAAM,EAAE,GAAG,IAAI;AACf,UAAM,aAAc,KAAK,aAAa,GAAG,QAAQ,WAAW;AAC5D,QAAI,CAAC,YAAY;AACb,sBAAgB,qHAAqH,EAAE;AACvI;AAAA,IACJ;AAQA,SAAK,OAAO,WAAW;AAAA,EAC3B;AAAA,EACA,uBAAuB;AACnB,SAAK,aAAa;AAAA,EACtB;AAAA,EACA,SAAS;AACL,QAAI,IAAI;AACR,UAAM,EAAE,OAAO,KAAK,IAAI;AACxB,UAAM,OAAO,WAAW,IAAI;AAC5B,UAAM,oBAAoB,KAAK,KAAK,cAAc,QAAQ,OAAO,SAAS,KAAK;AAC/E,UAAM,oBAAoB,KAAK,KAAK,cAAc,QAAQ,OAAO,SAAS,KAAK;AAC/E,UAAM,oBAAoB,SAAS;AACnC,WAAQ,EAAE,MAAM,EAAE,KAAK,4CAA4C,OAAO,mBAAmB,OAAO;AAAA,MAC5F,CAAC,IAAI,GAAG;AAAA,IACZ,CAAC,EAAE,GAAG,EAAE,cAAc,EAAE,KAAK,4CAA4C,MAAY,OAAc,MAAM,SAAS,OAAO,SAAS,gBAAgB,oBAAoB,SAAS,SAAS,cAAc,oBAAoB,kBAAkB,iBAAiB,MAAM,UAAU,MAAM,UAAU,eAAe,CAAC,OAAO;AAMhT,SAAG,eAAe;AAAA,IACtB,GAAG,SAAS,KAAK,yBAAyB,GAAG,EAAE,YAAY,EAAE,KAAK,4CAA4C,MAAM,aAAa,eAAe,QAAQ,MAAM,oBAAoB,mBAAmB,iBAAiB,CAAC,CAAC,CAAC;AAAA,EACjO;AAAA,EACA,IAAI,KAAK;AAAE,WAAO,WAAW,IAAI;AAAA,EAAG;AAAA,EACpC,WAAW,WAAW;AAAE,WAAO;AAAA,MAC3B,QAAQ,CAAC,cAAc;AAAA,IAC3B;AAAA,EAAG;AACP;AACA,oBAAoB,QAAQ;AAAA,EACxB,KAAK;AAAA,EACL,IAAI;AACR;", "names": []}