{"version": 3, "sources": ["../../../../../../node_modules/@ionic/core/dist/esm/ion-breadcrumb_2.entry.js"], "sourcesContent": ["/*!\n * (C) Ionic http://ionicframework.com - MIT License\n */\nimport { r as registerInstance, d as createEvent, e as getIonMode, h, j as Host, k as getElement } from './index-B_U9CtaY.js';\nimport { i as inheritAriaAttributes } from './helpers-1O4D2b7y.js';\nimport { c as createColorClasses, o as openURL, h as hostContext } from './theme-DiVJyqlX.js';\nimport { m as chevronForwardOutline, n as ellipsisHorizontal } from './index-BLV6ykCk.js';\n\nconst breadcrumbIosCss = \":host{display:-ms-flexbox;display:flex;-ms-flex:0 0 auto;flex:0 0 auto;-ms-flex-align:center;align-items:center;color:var(--color);font-size:1rem;font-weight:400;line-height:1.5}.breadcrumb-native{font-family:inherit;font-size:inherit;font-style:inherit;font-weight:inherit;letter-spacing:inherit;text-decoration:inherit;text-indent:inherit;text-overflow:inherit;text-transform:inherit;text-align:inherit;white-space:inherit;color:inherit;padding-left:0;padding-right:0;padding-top:0;padding-bottom:0;margin-left:0;margin-right:0;margin-top:0;margin-bottom:0;display:-ms-flexbox;display:flex;-ms-flex-align:center;align-items:center;width:100%;outline:none;background:inherit}:host(.breadcrumb-disabled){cursor:default;opacity:0.5;pointer-events:none}:host(.breadcrumb-active){color:var(--color-active)}:host(.ion-focused){color:var(--color-focused)}:host(.ion-focused) .breadcrumb-native{background:var(--background-focused)}@media (any-hover: hover){:host(.ion-activatable:hover){color:var(--color-hover)}:host(.ion-activatable.in-breadcrumbs-color:hover),:host(.ion-activatable.ion-color:hover){color:var(--ion-color-shade)}}.breadcrumb-separator{display:-ms-inline-flexbox;display:inline-flex}:host(.breadcrumb-collapsed) .breadcrumb-native{display:none}:host(.in-breadcrumbs-color),:host(.in-breadcrumbs-color.breadcrumb-active){color:var(--ion-color-base)}:host(.in-breadcrumbs-color) .breadcrumb-separator{color:var(--ion-color-base)}:host(.ion-color){color:var(--ion-color-base)}:host(.in-toolbar-color),:host(.in-toolbar-color) .breadcrumb-separator{color:rgba(var(--ion-color-contrast-rgb), 0.8)}:host(.in-toolbar-color.breadcrumb-active){color:var(--ion-color-contrast)}.breadcrumbs-collapsed-indicator{padding-left:0;padding-right:0;padding-top:0;padding-bottom:0;-webkit-margin-start:14px;margin-inline-start:14px;-webkit-margin-end:14px;margin-inline-end:14px;margin-top:0;margin-bottom:0;display:-ms-flexbox;display:flex;-ms-flex:1 1 100%;flex:1 1 100%;-ms-flex-align:center;align-items:center;-ms-flex-pack:center;justify-content:center;width:32px;height:18px;border:0;outline:none;cursor:pointer;-webkit-appearance:none;-moz-appearance:none;appearance:none}.breadcrumbs-collapsed-indicator ion-icon{margin-top:1px;font-size:1.375rem}:host{--color:var(--ion-color-step-850, var(--ion-text-color-step-150, #2d4665));--color-active:var(--ion-text-color, #03060b);--color-hover:var(--ion-text-color, #03060b);--color-focused:var(--color-active);--background-focused:var(--ion-color-step-50, var(--ion-background-color-step-50, rgba(233, 237, 243, 0.7)));font-size:clamp(16px, 1rem, 22px)}:host(.breadcrumb-active){font-weight:600}.breadcrumb-native{border-radius:4px;-webkit-padding-start:12px;padding-inline-start:12px;-webkit-padding-end:12px;padding-inline-end:12px;padding-top:5px;padding-bottom:5px;border:1px solid transparent}:host(.ion-focused) .breadcrumb-native{border-radius:8px}:host(.in-breadcrumbs-color.ion-focused) .breadcrumb-native,:host(.ion-color.ion-focused) .breadcrumb-native{background:rgba(var(--ion-color-base-rgb), 0.1);color:var(--ion-color-base)}:host(.ion-focused) ::slotted(ion-icon),:host(.in-breadcrumbs-color.ion-focused) ::slotted(ion-icon),:host(.ion-color.ion-focused) ::slotted(ion-icon){color:var(--ion-color-step-750, var(--ion-text-color-step-250, #445b78))}.breadcrumb-separator{color:var(--ion-color-step-550, var(--ion-text-color-step-450, #73849a))}::slotted(ion-icon){color:var(--ion-color-step-400, var(--ion-text-color-step-600, #92a0b3));font-size:min(1.125rem, 21.6px)}::slotted(ion-icon[slot=start]){-webkit-margin-end:8px;margin-inline-end:8px}::slotted(ion-icon[slot=end]){-webkit-margin-start:8px;margin-inline-start:8px}:host(.breadcrumb-active) ::slotted(ion-icon){color:var(--ion-color-step-850, var(--ion-text-color-step-150, #242d39))}.breadcrumbs-collapsed-indicator{border-radius:4px;background:var(--ion-color-step-100, var(--ion-background-color-step-100, #e9edf3));color:var(--ion-color-step-550, var(--ion-text-color-step-450, #73849a))}.breadcrumbs-collapsed-indicator:hover{opacity:0.45}.breadcrumbs-collapsed-indicator:focus{background:var(--ion-color-step-150, var(--ion-background-color-step-150, #d9e0ea))}.breadcrumbs-collapsed-indicator ion-icon{font-size:min(1.375rem, 22px)}\";\n\nconst breadcrumbMdCss = \":host{display:-ms-flexbox;display:flex;-ms-flex:0 0 auto;flex:0 0 auto;-ms-flex-align:center;align-items:center;color:var(--color);font-size:1rem;font-weight:400;line-height:1.5}.breadcrumb-native{font-family:inherit;font-size:inherit;font-style:inherit;font-weight:inherit;letter-spacing:inherit;text-decoration:inherit;text-indent:inherit;text-overflow:inherit;text-transform:inherit;text-align:inherit;white-space:inherit;color:inherit;padding-left:0;padding-right:0;padding-top:0;padding-bottom:0;margin-left:0;margin-right:0;margin-top:0;margin-bottom:0;display:-ms-flexbox;display:flex;-ms-flex-align:center;align-items:center;width:100%;outline:none;background:inherit}:host(.breadcrumb-disabled){cursor:default;opacity:0.5;pointer-events:none}:host(.breadcrumb-active){color:var(--color-active)}:host(.ion-focused){color:var(--color-focused)}:host(.ion-focused) .breadcrumb-native{background:var(--background-focused)}@media (any-hover: hover){:host(.ion-activatable:hover){color:var(--color-hover)}:host(.ion-activatable.in-breadcrumbs-color:hover),:host(.ion-activatable.ion-color:hover){color:var(--ion-color-shade)}}.breadcrumb-separator{display:-ms-inline-flexbox;display:inline-flex}:host(.breadcrumb-collapsed) .breadcrumb-native{display:none}:host(.in-breadcrumbs-color),:host(.in-breadcrumbs-color.breadcrumb-active){color:var(--ion-color-base)}:host(.in-breadcrumbs-color) .breadcrumb-separator{color:var(--ion-color-base)}:host(.ion-color){color:var(--ion-color-base)}:host(.in-toolbar-color),:host(.in-toolbar-color) .breadcrumb-separator{color:rgba(var(--ion-color-contrast-rgb), 0.8)}:host(.in-toolbar-color.breadcrumb-active){color:var(--ion-color-contrast)}.breadcrumbs-collapsed-indicator{padding-left:0;padding-right:0;padding-top:0;padding-bottom:0;-webkit-margin-start:14px;margin-inline-start:14px;-webkit-margin-end:14px;margin-inline-end:14px;margin-top:0;margin-bottom:0;display:-ms-flexbox;display:flex;-ms-flex:1 1 100%;flex:1 1 100%;-ms-flex-align:center;align-items:center;-ms-flex-pack:center;justify-content:center;width:32px;height:18px;border:0;outline:none;cursor:pointer;-webkit-appearance:none;-moz-appearance:none;appearance:none}.breadcrumbs-collapsed-indicator ion-icon{margin-top:1px;font-size:1.375rem}:host{--color:var(--ion-color-step-600, var(--ion-text-color-step-400, #677483));--color-active:var(--ion-text-color, #03060b);--color-hover:var(--ion-text-color, #03060b);--color-focused:var(--ion-color-step-800, var(--ion-text-color-step-200, #35404e));--background-focused:var(--ion-color-step-50, var(--ion-background-color-step-50, #fff))}:host(.breadcrumb-active){font-weight:500}.breadcrumb-native{-webkit-padding-start:12px;padding-inline-start:12px;-webkit-padding-end:12px;padding-inline-end:12px;padding-top:6px;padding-bottom:6px}.breadcrumb-separator{-webkit-margin-start:10px;margin-inline-start:10px;-webkit-margin-end:10px;margin-inline-end:10px;margin-top:-1px}:host(.ion-focused) .breadcrumb-native{border-radius:4px;-webkit-box-shadow:0px 1px 2px rgba(0, 0, 0, 0.2), 0px 2px 8px rgba(0, 0, 0, 0.12);box-shadow:0px 1px 2px rgba(0, 0, 0, 0.2), 0px 2px 8px rgba(0, 0, 0, 0.12)}.breadcrumb-separator{color:var(--ion-color-step-550, var(--ion-text-color-step-450, #73849a))}::slotted(ion-icon){color:var(--ion-color-step-550, var(--ion-text-color-step-450, #7d8894));font-size:1.125rem}::slotted(ion-icon[slot=start]){-webkit-margin-end:8px;margin-inline-end:8px}::slotted(ion-icon[slot=end]){-webkit-margin-start:8px;margin-inline-start:8px}:host(.breadcrumb-active) ::slotted(ion-icon){color:var(--ion-color-step-850, var(--ion-text-color-step-150, #222d3a))}.breadcrumbs-collapsed-indicator{border-radius:2px;background:var(--ion-color-step-100, var(--ion-background-color-step-100, #eef1f3));color:var(--ion-color-step-550, var(--ion-text-color-step-450, #73849a))}.breadcrumbs-collapsed-indicator:hover{opacity:0.7}.breadcrumbs-collapsed-indicator:focus{background:var(--ion-color-step-150, var(--ion-background-color-step-150, #dfe5e8))}\";\n\nconst Breadcrumb = class {\n    constructor(hostRef) {\n        registerInstance(this, hostRef);\n        this.ionFocus = createEvent(this, \"ionFocus\", 7);\n        this.ionBlur = createEvent(this, \"ionBlur\", 7);\n        this.collapsedClick = createEvent(this, \"collapsedClick\", 7);\n        this.inheritedAttributes = {};\n        /** @internal */\n        this.collapsed = false;\n        /**\n         * If `true`, the breadcrumb will take on a different look to show that\n         * it is the currently active breadcrumb. Defaults to `true` for the\n         * last breadcrumb if it is not set on any.\n         */\n        this.active = false;\n        /**\n         * If `true`, the user cannot interact with the breadcrumb.\n         */\n        this.disabled = false;\n        /**\n         * When using a router, it specifies the transition direction when navigating to\n         * another page using `href`.\n         */\n        this.routerDirection = 'forward';\n        this.onFocus = () => {\n            this.ionFocus.emit();\n        };\n        this.onBlur = () => {\n            this.ionBlur.emit();\n        };\n        this.collapsedIndicatorClick = () => {\n            this.collapsedClick.emit({ ionShadowTarget: this.collapsedRef });\n        };\n    }\n    componentWillLoad() {\n        this.inheritedAttributes = inheritAriaAttributes(this.el);\n    }\n    isClickable() {\n        return this.href !== undefined;\n    }\n    render() {\n        const { color, active, collapsed, disabled, download, el, inheritedAttributes, last, routerAnimation, routerDirection, separator, showCollapsedIndicator, target, } = this;\n        const clickable = this.isClickable();\n        const TagType = this.href === undefined ? 'span' : 'a';\n        // Links can still be tabbed to when set to disabled if they have an href\n        // in order to truly disable them we can keep it as an anchor but remove the href\n        const href = disabled ? undefined : this.href;\n        const mode = getIonMode(this);\n        const attrs = TagType === 'span'\n            ? {}\n            : {\n                download,\n                href,\n                target,\n            };\n        // If the breadcrumb is collapsed, check if it contains the collapsed indicator\n        // to show the separator as long as it isn't also the last breadcrumb\n        // otherwise if not collapsed use the value in separator\n        const showSeparator = last ? false : collapsed ? (showCollapsedIndicator && !last ? true : false) : separator;\n        return (h(Host, { key: '32ca61c83721dff52b5e97171ed449dce3584a55', onClick: (ev) => openURL(href, ev, routerDirection, routerAnimation), \"aria-disabled\": disabled ? 'true' : null, class: createColorClasses(color, {\n                [mode]: true,\n                'breadcrumb-active': active,\n                'breadcrumb-collapsed': collapsed,\n                'breadcrumb-disabled': disabled,\n                'in-breadcrumbs-color': hostContext('ion-breadcrumbs[color]', el),\n                'in-toolbar': hostContext('ion-toolbar', this.el),\n                'in-toolbar-color': hostContext('ion-toolbar[color]', this.el),\n                'ion-activatable': clickable,\n                'ion-focusable': clickable,\n            }) }, h(TagType, Object.assign({ key: '479feb845f4a6d8009d5422b33eb423730b9722b' }, attrs, { class: \"breadcrumb-native\", part: \"native\", disabled: disabled, onFocus: this.onFocus, onBlur: this.onBlur }, inheritedAttributes), h(\"slot\", { key: '3c5dcaeb0d258235d1b7707868026ff1d1404099', name: \"start\" }), h(\"slot\", { key: 'f1cfb934443cd97dc220882c5e3596ea879d66cf' }), h(\"slot\", { key: '539710121b5b1f3ee8d4c24a9651b67c2ae08add', name: \"end\" })), showCollapsedIndicator && (h(\"button\", { key: 'ed53a95ccd89022c8b7bee0658a221ec62a5c73b', part: \"collapsed-indicator\", \"aria-label\": \"Show more breadcrumbs\", onClick: () => this.collapsedIndicatorClick(), ref: (collapsedEl) => (this.collapsedRef = collapsedEl), class: {\n                'breadcrumbs-collapsed-indicator': true,\n            } }, h(\"ion-icon\", { key: 'a849e1142a86f06f207cf11662fa2a560ab7fc6a', \"aria-hidden\": \"true\", icon: ellipsisHorizontal, lazy: false }))), showSeparator && (\n        /**\n         * Separators should not be announced by narrators.\n         * We add aria-hidden on the span so that this applies\n         * to any custom separators too.\n         */\n        h(\"span\", { key: 'fc3c741cb01fafef8b26046c7ee5b190efc69a7c', class: \"breadcrumb-separator\", part: \"separator\", \"aria-hidden\": \"true\" }, h(\"slot\", { key: '4871932ae1dae520767e0713e7cee2d11b0bba6d', name: \"separator\" }, mode === 'ios' ? (h(\"ion-icon\", { icon: chevronForwardOutline, lazy: false, \"flip-rtl\": true })) : (h(\"span\", null, \"/\")))))));\n    }\n    get el() { return getElement(this); }\n};\nBreadcrumb.style = {\n    ios: breadcrumbIosCss,\n    md: breadcrumbMdCss\n};\n\nconst breadcrumbsIosCss = \":host{-moz-osx-font-smoothing:grayscale;-webkit-font-smoothing:antialiased;display:-ms-flexbox;display:flex;-ms-flex-wrap:wrap;flex-wrap:wrap;-ms-flex-align:center;align-items:center}:host(.in-toolbar-color),:host(.in-toolbar-color) .breadcrumbs-collapsed-indicator ion-icon{color:var(--ion-color-contrast)}:host(.in-toolbar-color) .breadcrumbs-collapsed-indicator{background:rgba(var(--ion-color-contrast-rgb), 0.11)}:host(.in-toolbar){-webkit-padding-start:20px;padding-inline-start:20px;-webkit-padding-end:20px;padding-inline-end:20px;padding-top:0;padding-bottom:0;-ms-flex-pack:center;justify-content:center}\";\n\nconst breadcrumbsMdCss = \":host{-moz-osx-font-smoothing:grayscale;-webkit-font-smoothing:antialiased;display:-ms-flexbox;display:flex;-ms-flex-wrap:wrap;flex-wrap:wrap;-ms-flex-align:center;align-items:center}:host(.in-toolbar-color),:host(.in-toolbar-color) .breadcrumbs-collapsed-indicator ion-icon{color:var(--ion-color-contrast)}:host(.in-toolbar-color) .breadcrumbs-collapsed-indicator{background:rgba(var(--ion-color-contrast-rgb), 0.11)}:host(.in-toolbar){-webkit-padding-start:8px;padding-inline-start:8px;-webkit-padding-end:8px;padding-inline-end:8px;padding-top:0;padding-bottom:0}\";\n\nconst Breadcrumbs = class {\n    constructor(hostRef) {\n        registerInstance(this, hostRef);\n        this.ionCollapsedClick = createEvent(this, \"ionCollapsedClick\", 7);\n        /**\n         * The number of breadcrumbs to show before the collapsed indicator.\n         * If `itemsBeforeCollapse` + `itemsAfterCollapse` is greater than `maxItems`,\n         * the breadcrumbs will not be collapsed.\n         */\n        this.itemsBeforeCollapse = 1;\n        /**\n         * The number of breadcrumbs to show after the collapsed indicator.\n         * If `itemsBeforeCollapse` + `itemsAfterCollapse` is greater than `maxItems`,\n         * the breadcrumbs will not be collapsed.\n         */\n        this.itemsAfterCollapse = 1;\n        this.breadcrumbsInit = () => {\n            this.setBreadcrumbSeparator();\n            this.setMaxItems();\n        };\n        this.resetActiveBreadcrumb = () => {\n            const breadcrumbs = this.getBreadcrumbs();\n            // Only reset the active breadcrumb if we were the ones to change it\n            // otherwise use the one set on the component\n            const activeBreadcrumb = breadcrumbs.find((breadcrumb) => breadcrumb.active);\n            if (activeBreadcrumb && this.activeChanged) {\n                activeBreadcrumb.active = false;\n            }\n        };\n        this.setMaxItems = () => {\n            const { itemsAfterCollapse, itemsBeforeCollapse, maxItems } = this;\n            const breadcrumbs = this.getBreadcrumbs();\n            for (const breadcrumb of breadcrumbs) {\n                breadcrumb.showCollapsedIndicator = false;\n                breadcrumb.collapsed = false;\n            }\n            // If the number of breadcrumbs exceeds the maximum number of items\n            // that should show and the items before / after collapse do not\n            // exceed the maximum items then we need to collapse the breadcrumbs\n            const shouldCollapse = maxItems !== undefined && breadcrumbs.length > maxItems && itemsBeforeCollapse + itemsAfterCollapse <= maxItems;\n            if (shouldCollapse) {\n                // Show the collapsed indicator in the first breadcrumb that collapses\n                breadcrumbs.forEach((breadcrumb, index) => {\n                    if (index === itemsBeforeCollapse) {\n                        breadcrumb.showCollapsedIndicator = true;\n                    }\n                    // Collapse all breadcrumbs that have an index greater than or equal to\n                    // the number before collapse and an index less than the total number\n                    // of breadcrumbs minus the items that should show after the collapse\n                    if (index >= itemsBeforeCollapse && index < breadcrumbs.length - itemsAfterCollapse) {\n                        breadcrumb.collapsed = true;\n                    }\n                });\n            }\n        };\n        this.setBreadcrumbSeparator = () => {\n            const { itemsAfterCollapse, itemsBeforeCollapse, maxItems } = this;\n            const breadcrumbs = this.getBreadcrumbs();\n            // Check if an active breadcrumb exists already\n            const active = breadcrumbs.find((breadcrumb) => breadcrumb.active);\n            // Set the separator on all but the last breadcrumb\n            for (const breadcrumb of breadcrumbs) {\n                // The only time the last breadcrumb changes is when\n                // itemsAfterCollapse is set to 0, in this case the\n                // last breadcrumb will be the collapsed indicator\n                const last = maxItems !== undefined && itemsAfterCollapse === 0\n                    ? breadcrumb === breadcrumbs[itemsBeforeCollapse]\n                    : breadcrumb === breadcrumbs[breadcrumbs.length - 1];\n                breadcrumb.last = last;\n                // If the breadcrumb has defined whether or not to show the\n                // separator then use that value, otherwise check if it's the\n                // last breadcrumb\n                const separator = breadcrumb.separator !== undefined ? breadcrumb.separator : last ? undefined : true;\n                breadcrumb.separator = separator;\n                // If there is not an active breadcrumb already\n                // set the last one to active\n                if (!active && last) {\n                    breadcrumb.active = true;\n                    this.activeChanged = true;\n                }\n            }\n        };\n        this.getBreadcrumbs = () => {\n            return Array.from(this.el.querySelectorAll('ion-breadcrumb'));\n        };\n        this.slotChanged = () => {\n            this.resetActiveBreadcrumb();\n            this.breadcrumbsInit();\n        };\n    }\n    onCollapsedClick(ev) {\n        const breadcrumbs = this.getBreadcrumbs();\n        const collapsedBreadcrumbs = breadcrumbs.filter((breadcrumb) => breadcrumb.collapsed);\n        this.ionCollapsedClick.emit(Object.assign(Object.assign({}, ev.detail), { collapsedBreadcrumbs }));\n    }\n    maxItemsChanged() {\n        this.resetActiveBreadcrumb();\n        this.breadcrumbsInit();\n    }\n    componentWillLoad() {\n        this.breadcrumbsInit();\n    }\n    render() {\n        const { color, collapsed } = this;\n        const mode = getIonMode(this);\n        return (h(Host, { key: 'fe64e9cdf597ede2db140bf5fa05a0359d82db57', class: createColorClasses(color, {\n                [mode]: true,\n                'in-toolbar': hostContext('ion-toolbar', this.el),\n                'in-toolbar-color': hostContext('ion-toolbar[color]', this.el),\n                'breadcrumbs-collapsed': collapsed,\n            }) }, h(\"slot\", { key: 'a2c99b579e339055c50a613d5c6b61032f5ddffe', onSlotchange: this.slotChanged })));\n    }\n    get el() { return getElement(this); }\n    static get watchers() { return {\n        \"maxItems\": [\"maxItemsChanged\"],\n        \"itemsBeforeCollapse\": [\"maxItemsChanged\"],\n        \"itemsAfterCollapse\": [\"maxItemsChanged\"]\n    }; }\n};\nBreadcrumbs.style = {\n    ios: breadcrumbsIosCss,\n    md: breadcrumbsMdCss\n};\n\nexport { Breadcrumb as ion_breadcrumb, Breadcrumbs as ion_breadcrumbs };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;AAQA,IAAM,mBAAmB;AAEzB,IAAM,kBAAkB;AAExB,IAAM,aAAa,MAAM;AAAA,EACrB,YAAY,SAAS;AACjB,qBAAiB,MAAM,OAAO;AAC9B,SAAK,WAAW,YAAY,MAAM,YAAY,CAAC;AAC/C,SAAK,UAAU,YAAY,MAAM,WAAW,CAAC;AAC7C,SAAK,iBAAiB,YAAY,MAAM,kBAAkB,CAAC;AAC3D,SAAK,sBAAsB,CAAC;AAE5B,SAAK,YAAY;AAMjB,SAAK,SAAS;AAId,SAAK,WAAW;AAKhB,SAAK,kBAAkB;AACvB,SAAK,UAAU,MAAM;AACjB,WAAK,SAAS,KAAK;AAAA,IACvB;AACA,SAAK,SAAS,MAAM;AAChB,WAAK,QAAQ,KAAK;AAAA,IACtB;AACA,SAAK,0BAA0B,MAAM;AACjC,WAAK,eAAe,KAAK,EAAE,iBAAiB,KAAK,aAAa,CAAC;AAAA,IACnE;AAAA,EACJ;AAAA,EACA,oBAAoB;AAChB,SAAK,sBAAsB,sBAAsB,KAAK,EAAE;AAAA,EAC5D;AAAA,EACA,cAAc;AACV,WAAO,KAAK,SAAS;AAAA,EACzB;AAAA,EACA,SAAS;AACL,UAAM,EAAE,OAAO,QAAQ,WAAW,UAAU,UAAU,IAAI,qBAAqB,MAAM,iBAAiB,iBAAiB,WAAW,wBAAwB,OAAQ,IAAI;AACtK,UAAM,YAAY,KAAK,YAAY;AACnC,UAAM,UAAU,KAAK,SAAS,SAAY,SAAS;AAGnD,UAAM,OAAO,WAAW,SAAY,KAAK;AACzC,UAAM,OAAO,WAAW,IAAI;AAC5B,UAAM,QAAQ,YAAY,SACpB,CAAC,IACD;AAAA,MACE;AAAA,MACA;AAAA,MACA;AAAA,IACJ;AAIJ,UAAM,gBAAgB,OAAO,QAAQ,YAAa,0BAA0B,CAAC,OAAO,OAAO,QAAS;AACpG,WAAQ,EAAE,MAAM,EAAE,KAAK,4CAA4C,SAAS,CAAC,OAAO,QAAQ,MAAM,IAAI,iBAAiB,eAAe,GAAG,iBAAiB,WAAW,SAAS,MAAM,OAAO,mBAAmB,OAAO;AAAA,MAC7M,CAAC,IAAI,GAAG;AAAA,MACR,qBAAqB;AAAA,MACrB,wBAAwB;AAAA,MACxB,uBAAuB;AAAA,MACvB,wBAAwB,YAAY,0BAA0B,EAAE;AAAA,MAChE,cAAc,YAAY,eAAe,KAAK,EAAE;AAAA,MAChD,oBAAoB,YAAY,sBAAsB,KAAK,EAAE;AAAA,MAC7D,mBAAmB;AAAA,MACnB,iBAAiB;AAAA,IACrB,CAAC,EAAE,GAAG,EAAE,SAAS,OAAO,OAAO,EAAE,KAAK,2CAA2C,GAAG,OAAO,EAAE,OAAO,qBAAqB,MAAM,UAAU,UAAoB,SAAS,KAAK,SAAS,QAAQ,KAAK,OAAO,GAAG,mBAAmB,GAAG,EAAE,QAAQ,EAAE,KAAK,4CAA4C,MAAM,QAAQ,CAAC,GAAG,EAAE,QAAQ,EAAE,KAAK,2CAA2C,CAAC,GAAG,EAAE,QAAQ,EAAE,KAAK,4CAA4C,MAAM,MAAM,CAAC,CAAC,GAAG,0BAA2B,EAAE,UAAU,EAAE,KAAK,4CAA4C,MAAM,uBAAuB,cAAc,yBAAyB,SAAS,MAAM,KAAK,wBAAwB,GAAG,KAAK,CAAC,gBAAiB,KAAK,eAAe,aAAc,OAAO;AAAA,MACvsB,mCAAmC;AAAA,IACvC,EAAE,GAAG,EAAE,YAAY,EAAE,KAAK,4CAA4C,eAAe,QAAQ,MAAM,oBAAoB,MAAM,MAAM,CAAC,CAAC,GAAI;AAAA;AAAA;AAAA;AAAA;AAAA,IAM7I,EAAE,QAAQ,EAAE,KAAK,4CAA4C,OAAO,wBAAwB,MAAM,aAAa,eAAe,OAAO,GAAG,EAAE,QAAQ,EAAE,KAAK,4CAA4C,MAAM,YAAY,GAAG,SAAS,QAAS,EAAE,YAAY,EAAE,MAAM,uBAAuB,MAAM,OAAO,YAAY,KAAK,CAAC,IAAM,EAAE,QAAQ,MAAM,GAAG,CAAE,CAAC,CAAE;AAAA,EAC1V;AAAA,EACA,IAAI,KAAK;AAAE,WAAO,WAAW,IAAI;AAAA,EAAG;AACxC;AACA,WAAW,QAAQ;AAAA,EACf,KAAK;AAAA,EACL,IAAI;AACR;AAEA,IAAM,oBAAoB;AAE1B,IAAM,mBAAmB;AAEzB,IAAM,cAAc,MAAM;AAAA,EACtB,YAAY,SAAS;AACjB,qBAAiB,MAAM,OAAO;AAC9B,SAAK,oBAAoB,YAAY,MAAM,qBAAqB,CAAC;AAMjE,SAAK,sBAAsB;AAM3B,SAAK,qBAAqB;AAC1B,SAAK,kBAAkB,MAAM;AACzB,WAAK,uBAAuB;AAC5B,WAAK,YAAY;AAAA,IACrB;AACA,SAAK,wBAAwB,MAAM;AAC/B,YAAM,cAAc,KAAK,eAAe;AAGxC,YAAM,mBAAmB,YAAY,KAAK,CAAC,eAAe,WAAW,MAAM;AAC3E,UAAI,oBAAoB,KAAK,eAAe;AACxC,yBAAiB,SAAS;AAAA,MAC9B;AAAA,IACJ;AACA,SAAK,cAAc,MAAM;AACrB,YAAM,EAAE,oBAAoB,qBAAqB,SAAS,IAAI;AAC9D,YAAM,cAAc,KAAK,eAAe;AACxC,iBAAW,cAAc,aAAa;AAClC,mBAAW,yBAAyB;AACpC,mBAAW,YAAY;AAAA,MAC3B;AAIA,YAAM,iBAAiB,aAAa,UAAa,YAAY,SAAS,YAAY,sBAAsB,sBAAsB;AAC9H,UAAI,gBAAgB;AAEhB,oBAAY,QAAQ,CAAC,YAAY,UAAU;AACvC,cAAI,UAAU,qBAAqB;AAC/B,uBAAW,yBAAyB;AAAA,UACxC;AAIA,cAAI,SAAS,uBAAuB,QAAQ,YAAY,SAAS,oBAAoB;AACjF,uBAAW,YAAY;AAAA,UAC3B;AAAA,QACJ,CAAC;AAAA,MACL;AAAA,IACJ;AACA,SAAK,yBAAyB,MAAM;AAChC,YAAM,EAAE,oBAAoB,qBAAqB,SAAS,IAAI;AAC9D,YAAM,cAAc,KAAK,eAAe;AAExC,YAAM,SAAS,YAAY,KAAK,CAAC,eAAe,WAAW,MAAM;AAEjE,iBAAW,cAAc,aAAa;AAIlC,cAAM,OAAO,aAAa,UAAa,uBAAuB,IACxD,eAAe,YAAY,mBAAmB,IAC9C,eAAe,YAAY,YAAY,SAAS,CAAC;AACvD,mBAAW,OAAO;AAIlB,cAAM,YAAY,WAAW,cAAc,SAAY,WAAW,YAAY,OAAO,SAAY;AACjG,mBAAW,YAAY;AAGvB,YAAI,CAAC,UAAU,MAAM;AACjB,qBAAW,SAAS;AACpB,eAAK,gBAAgB;AAAA,QACzB;AAAA,MACJ;AAAA,IACJ;AACA,SAAK,iBAAiB,MAAM;AACxB,aAAO,MAAM,KAAK,KAAK,GAAG,iBAAiB,gBAAgB,CAAC;AAAA,IAChE;AACA,SAAK,cAAc,MAAM;AACrB,WAAK,sBAAsB;AAC3B,WAAK,gBAAgB;AAAA,IACzB;AAAA,EACJ;AAAA,EACA,iBAAiB,IAAI;AACjB,UAAM,cAAc,KAAK,eAAe;AACxC,UAAM,uBAAuB,YAAY,OAAO,CAAC,eAAe,WAAW,SAAS;AACpF,SAAK,kBAAkB,KAAK,OAAO,OAAO,OAAO,OAAO,CAAC,GAAG,GAAG,MAAM,GAAG,EAAE,qBAAqB,CAAC,CAAC;AAAA,EACrG;AAAA,EACA,kBAAkB;AACd,SAAK,sBAAsB;AAC3B,SAAK,gBAAgB;AAAA,EACzB;AAAA,EACA,oBAAoB;AAChB,SAAK,gBAAgB;AAAA,EACzB;AAAA,EACA,SAAS;AACL,UAAM,EAAE,OAAO,UAAU,IAAI;AAC7B,UAAM,OAAO,WAAW,IAAI;AAC5B,WAAQ,EAAE,MAAM,EAAE,KAAK,4CAA4C,OAAO,mBAAmB,OAAO;AAAA,MAC5F,CAAC,IAAI,GAAG;AAAA,MACR,cAAc,YAAY,eAAe,KAAK,EAAE;AAAA,MAChD,oBAAoB,YAAY,sBAAsB,KAAK,EAAE;AAAA,MAC7D,yBAAyB;AAAA,IAC7B,CAAC,EAAE,GAAG,EAAE,QAAQ,EAAE,KAAK,4CAA4C,cAAc,KAAK,YAAY,CAAC,CAAC;AAAA,EAC5G;AAAA,EACA,IAAI,KAAK;AAAE,WAAO,WAAW,IAAI;AAAA,EAAG;AAAA,EACpC,WAAW,WAAW;AAAE,WAAO;AAAA,MAC3B,YAAY,CAAC,iBAAiB;AAAA,MAC9B,uBAAuB,CAAC,iBAAiB;AAAA,MACzC,sBAAsB,CAAC,iBAAiB;AAAA,IAC5C;AAAA,EAAG;AACP;AACA,YAAY,QAAQ;AAAA,EAChB,KAAK;AAAA,EACL,IAAI;AACR;", "names": []}