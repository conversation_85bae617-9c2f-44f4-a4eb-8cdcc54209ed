{"version": 3, "sources": ["../../../../../../node_modules/@ionic/core/dist/esm/keyboard-CUw4ekVy.js"], "sourcesContent": ["/*!\n * (C) Ionic http://ionicframework.com - MIT License\n */\nimport { g as getCapacitor } from './capacitor-CFERIeaU.js';\n\nvar ExceptionCode;\n(function (ExceptionCode) {\n    /**\n     * API is not implemented.\n     *\n     * This usually means the API can't be used because it is not implemented for\n     * the current platform.\n     */\n    ExceptionCode[\"Unimplemented\"] = \"UNIMPLEMENTED\";\n    /**\n     * API is not available.\n     *\n     * This means the API can't be used right now because:\n     *   - it is currently missing a prerequisite, such as network connectivity\n     *   - it requires a particular platform or browser version\n     */\n    ExceptionCode[\"Unavailable\"] = \"UNAVAILABLE\";\n})(ExceptionCode || (ExceptionCode = {}));\n\nvar KeyboardResize;\n(function (KeyboardResize) {\n    /**\n     * Only the `body` HTML element will be resized.\n     * Relative units are not affected, because the viewport does not change.\n     *\n     * @since 1.0.0\n     */\n    KeyboardResize[\"Body\"] = \"body\";\n    /**\n     * Only the `ion-app` HTML element will be resized.\n     * Use it only for Ionic Framework apps.\n     *\n     * @since 1.0.0\n     */\n    KeyboardResize[\"Ionic\"] = \"ionic\";\n    /**\n     * The whole native Web View will be resized when the keyboard shows/hides.\n     * This affects the `vh` relative unit.\n     *\n     * @since 1.0.0\n     */\n    KeyboardResize[\"Native\"] = \"native\";\n    /**\n     * Neither the app nor the Web View are resized.\n     *\n     * @since 1.0.0\n     */\n    KeyboardResize[\"None\"] = \"none\";\n})(KeyboardResize || (KeyboardResize = {}));\nconst Keyboard = {\n    getEngine() {\n        const capacitor = getCapacitor();\n        if (capacitor === null || capacitor === void 0 ? void 0 : capacitor.isPluginAvailable('Keyboard')) {\n            return capacitor.Plugins.Keyboard;\n        }\n        return undefined;\n    },\n    getResizeMode() {\n        const engine = this.getEngine();\n        if (!(engine === null || engine === void 0 ? void 0 : engine.getResizeMode)) {\n            return Promise.resolve(undefined);\n        }\n        return engine.getResizeMode().catch((e) => {\n            if (e.code === ExceptionCode.Unimplemented) {\n                // If the native implementation is not available\n                // we treat it the same as if the plugin is not available.\n                return undefined;\n            }\n            throw e;\n        });\n    },\n};\n\nexport { Keyboard as K, KeyboardResize as a };\n"], "mappings": ";;;;;AAKA,IAAI;AAAA,CACH,SAAUA,gBAAe;AAOtB,EAAAA,eAAc,eAAe,IAAI;AAQjC,EAAAA,eAAc,aAAa,IAAI;AACnC,GAAG,kBAAkB,gBAAgB,CAAC,EAAE;AAExC,IAAI;AAAA,CACH,SAAUC,iBAAgB;AAOvB,EAAAA,gBAAe,MAAM,IAAI;AAOzB,EAAAA,gBAAe,OAAO,IAAI;AAO1B,EAAAA,gBAAe,QAAQ,IAAI;AAM3B,EAAAA,gBAAe,MAAM,IAAI;AAC7B,GAAG,mBAAmB,iBAAiB,CAAC,EAAE;AAC1C,IAAM,WAAW;AAAA,EACb,YAAY;AACR,UAAM,YAAY,aAAa;AAC/B,QAAI,cAAc,QAAQ,cAAc,SAAS,SAAS,UAAU,kBAAkB,UAAU,GAAG;AAC/F,aAAO,UAAU,QAAQ;AAAA,IAC7B;AACA,WAAO;AAAA,EACX;AAAA,EACA,gBAAgB;AACZ,UAAM,SAAS,KAAK,UAAU;AAC9B,QAAI,EAAE,WAAW,QAAQ,WAAW,SAAS,SAAS,OAAO,gBAAgB;AACzE,aAAO,QAAQ,QAAQ,MAAS;AAAA,IACpC;AACA,WAAO,OAAO,cAAc,EAAE,MAAM,CAAC,MAAM;AACvC,UAAI,EAAE,SAAS,cAAc,eAAe;AAGxC,eAAO;AAAA,MACX;AACA,YAAM;AAAA,IACV,CAAC;AAAA,EACL;AACJ;", "names": ["ExceptionCode", "KeyboardResize"]}