{"logs": [{"outputFile": "io.ionic.starter.app-mergeDebugResources-31:/values/values.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\1851baad953a72983870260c24fb7d2a\\transformed\\activity-1.9.2\\res\\values\\values.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,97", "endColumns": "41,59", "endOffsets": "92,152"}, "to": {"startLines": "293,314", "startColumns": "4,4", "startOffsets": "18313,19400", "endColumns": "41,59", "endOffsets": "18350,19455"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\4ee185d8f5138a37e2052cba61bdfce2\\transformed\\savedstate-1.2.1\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "53", "endOffsets": "104"}, "to": {"startLines": "315", "startColumns": "4", "startOffsets": "19460", "endColumns": "53", "endOffsets": "19509"}}, {"source": "C:\\Users\\<USER>\\OneDrive\\Desktop\\FRONTEND DEVELOPER\\crud-sqlite\\android\\app\\src\\main\\res\\values\\styles.xml", "from": {"startLines": "4,11,18", "startColumns": "4,4,4", "startOffsets": "93,413,664", "endLines": "9,15,20", "endColumns": "12,12,12", "endOffsets": "407,657,810"}, "to": {"startLines": "383,389,394", "startColumns": "4,4,4", "startOffsets": "24428,24712,24961", "endLines": "388,393,396", "endColumns": "12,12,12", "endOffsets": "24707,24956,25107"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\99cf7c220a2a9f2a8cb7b1ae85acd02c\\transformed\\lifecycle-runtime-2.6.2\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "42", "endOffsets": "93"}, "to": {"startLines": "313", "startColumns": "4", "startOffsets": "19357", "endColumns": "42", "endOffsets": "19395"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\0d0aa8469f3a45927795e5e14c85ad05\\transformed\\appcompat-resources-1.7.0\\res\\values\\values.xml", "from": {"startLines": "2,18,24,34,50", "startColumns": "4,4,4,4,4", "startOffsets": "55,480,658,942,1353", "endLines": "17,23,33,49,53", "endColumns": "24,24,24,24,24", "endOffsets": "475,653,937,1348,1475"}, "to": {"startLines": "2057,2073,2079,3136,3152", "startColumns": "4,4,4,4,4", "startOffsets": "133377,133802,133980,171117,171528", "endLines": "2072,2078,2088,3151,3155", "endColumns": "24,24,24,24,24", "endOffsets": "133797,133975,134259,171523,171650"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\914c90e97af2aebbb6598590579e1b39\\transformed\\fragment-1.8.4\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,10", "startColumns": "4,4,4,4,4", "startOffsets": "55,112,177,241,411", "endLines": "2,3,4,9,13", "endColumns": "56,64,63,24,24", "endOffsets": "107,172,236,406,555"}, "to": {"startLines": "287,294,317,2824,2829", "startColumns": "4,4,4,4,4", "startOffsets": "18056,18355,19564,161483,161653", "endLines": "287,294,317,2828,2832", "endColumns": "56,64,63,24,24", "endOffsets": "18108,18415,19623,161648,161797"}}, {"source": "C:\\Users\\<USER>\\OneDrive\\Desktop\\FRONTEND DEVELOPER\\crud-sqlite\\android\\app\\src\\main\\res\\values\\strings.xml", "from": {"startLines": "2,5,4,3", "startColumns": "4,4,4,4", "startOffsets": "55,222,164,104", "endColumns": "48,62,57,59", "endOffsets": "99,280,217,159"}, "to": {"startLines": "352,361,374,377", "startColumns": "4,4,4,4", "startOffsets": "21958,22594,23744,23926", "endColumns": "48,62,57,59", "endOffsets": "22002,22652,23797,23981"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\eaaca94d50affd3c3a86a8212751e60b\\transformed\\biometric-1.1.0\\res\\values\\values.xml", "from": {"startLines": "2,6,8,11,15,34,35,36,37,38,39,40,41,42,43,44,45,46,47", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,194,277,388,523,1701,1757,1810,1886,1946,2035,2134,2242,2339,2427,2527,2597,2694,2804", "endLines": "5,7,10,14,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47", "endColumns": "19,19,19,19,19,55,52,75,59,88,98,107,96,87,99,69,96,109,88", "endOffsets": "189,272,383,518,1696,1752,1805,1881,1941,2030,2129,2237,2334,2422,2522,2592,2689,2799,2888"}, "to": {"startLines": "2,6,8,11,15,61,210,360,362,363,364,365,366,367,368,369,370,371,372", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,289,372,483,618,3002,13119,22518,22657,22717,22806,22905,23013,23110,23198,23298,23368,23465,23575", "endLines": "5,7,10,14,33,61,210,360,362,363,364,365,366,367,368,369,370,371,372", "endColumns": "19,19,19,19,19,55,52,75,59,88,98,107,96,87,99,69,96,109,88", "endOffsets": "284,367,478,613,1194,3053,13167,22589,22712,22801,22900,23008,23105,23193,23293,23363,23460,23570,23659"}}, {"source": "C:\\Users\\<USER>\\OneDrive\\Desktop\\FRONTEND DEVELOPER\\crud-sqlite\\android\\app\\src\\main\\res\\values\\ic_launcher_background.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "56", "endOffsets": "107"}, "to": {"startLines": "85", "startColumns": "4", "startOffsets": "4742", "endColumns": "56", "endOffsets": "4794"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\381b4363b5175c342310fb1bad1257c8\\transformed\\startup-runtime-1.1.1\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "82", "endOffsets": "133"}, "to": {"startLines": "351", "startColumns": "4", "startOffsets": "21875", "endColumns": "82", "endOffsets": "21953"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\53f40aa6f6d840a54b93ab2747ad6132\\transformed\\core-splashscreen-1.0.1\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,21,23,32,45", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,115,174,243,315,378,450,524,600,676,753,824,893,964,1032,1113,1205,1298,1407,1528,1988,2763", "endLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,20,22,31,44,48", "endColumns": "59,58,68,71,62,71,73,75,75,76,70,68,70,67,80,91,92,12,12,12,12,12", "endOffsets": "110,169,238,310,373,445,519,595,671,748,819,888,959,1027,1108,1200,1293,1402,1523,1983,2758,3031"}, "to": {"startLines": "39,40,42,43,44,45,233,234,235,236,237,238,239,322,651,652,653,1483,1485,1805,1814,1827", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1469,1529,1629,1698,1770,1833,14761,14835,14911,14987,15064,15135,15204,19871,41766,41847,41939,94376,94485,118886,119346,120121", "endLines": "39,40,42,43,44,45,233,234,235,236,237,238,239,322,651,652,653,1484,1486,1813,1826,1830", "endColumns": "59,58,68,71,62,71,73,75,75,76,70,68,70,67,80,91,92,12,12,12,12,12", "endOffsets": "1524,1583,1693,1765,1828,1900,14830,14906,14982,15059,15130,15199,15270,19934,41842,41934,42027,94480,94601,119341,120116,120389"}}, {"source": "C:\\Users\\<USER>\\OneDrive\\Desktop\\FRONTEND DEVELOPER\\crud-sqlite\\node_modules\\@capacitor\\android\\capacitor\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\values\\values.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "100,175,251,331", "endColumns": "74,75,79,79", "endOffsets": "170,246,326,406"}, "to": {"startLines": "72,73,74,373", "startColumns": "4,4,4,4", "startOffsets": "3830,3905,3981,23664", "endColumns": "74,75,79,79", "endOffsets": "3900,3976,4056,23739"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\e2542f07e03e83966d61d2ce34c14b7c\\transformed\\appcompat-1.7.0\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,213,221,222,226,230,234,239,245,252,256,260,265,269,273,277,281,285,289,295,299,305,309,315,319,324,328,331,335,341,345,351,355,361,364,368,372,376,380,384,385,386,387,390,393,396,399,403,404,405,406,407,410,412,414,416,421,422,426,432,436,437,439,451,452,456,462,466,467,468,472,499,503,504,508,536,708,734,905,931,962,970,976,992,1014,1019,1024,1034,1043,1052,1056,1063,1082,1089,1090,1099,1102,1105,1109,1113,1117,1120,1121,1126,1131,1141,1146,1153,1159,1160,1163,1167,1172,1174,1176,1179,1182,1184,1188,1191,1198,1201,1204,1208,1210,1214,1216,1218,1220,1224,1232,1240,1252,1258,1267,1270,1281,1284,1285,1290,1291,1296,1365,1435,1436,1446,1455,1456,1458,1462,1465,1468,1471,1474,1477,1480,1483,1487,1490,1493,1496,1500,1503,1507,1511,1512,1513,1514,1515,1516,1517,1518,1519,1520,1521,1522,1523,1524,1525,1526,1527,1528,1529,1530,1531,1533,1535,1536,1537,1538,1539,1540,1541,1542,1544,1545,1547,1548,1550,1552,1553,1555,1556,1557,1558,1559,1560,1562,1563,1564,1565,1566,1567,1569,1571,1573,1574,1575,1576,1577,1578,1579,1580,1581,1582,1583,1584,1585,1587,1588,1589,1590,1591,1592,1593,1595,1599,1603,1604,1605,1606,1607,1608,1612,1613,1614,1615,1617,1619,1621,1623,1625,1626,1627,1628,1630,1632,1634,1635,1636,1637,1638,1639,1640,1641,1642,1643,1644,1645,1648,1649,1650,1651,1653,1655,1656,1658,1659,1661,1663,1665,1666,1667,1668,1669,1670,1671,1672,1673,1674,1675,1676,1678,1679,1680,1681,1683,1684,1685,1686,1687,1689,1691,1693,1695,1696,1697,1698,1699,1700,1701,1702,1703,1704,1705,1706,1707,1708,1709,1710,1785,1788,1791,1794,1808,1814,1824,1827,1856,1883,1892,1956,2319,2323,2351,2379,2397,2421,2427,2433,2454,2578,2598,2604,2608,2614,2649,2661,2727,2747,2802,2814,2840", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,160,205,254,295,350,412,476,546,607,682,758,835,913,998,1080,1156,1232,1309,1387,1493,1599,1678,1758,1815,1873,1947,2022,2087,2153,2213,2274,2346,2419,2486,2554,2613,2672,2731,2790,2849,2903,2957,3010,3064,3118,3172,3226,3300,3379,3452,3526,3597,3669,3741,3814,3871,3929,4002,4076,4150,4225,4297,4370,4440,4511,4571,4632,4701,4770,4840,4914,4990,5054,5131,5207,5284,5349,5418,5495,5570,5639,5707,5784,5850,5911,6008,6073,6142,6241,6312,6371,6429,6486,6545,6609,6680,6752,6824,6896,6968,7035,7103,7171,7230,7293,7357,7447,7538,7598,7664,7731,7797,7867,7931,7984,8051,8112,8179,8292,8350,8413,8478,8543,8618,8691,8763,8807,8854,8900,8949,9010,9071,9132,9194,9258,9322,9386,9451,9514,9574,9635,9701,9760,9820,9882,9953,10013,10081,10167,10254,10344,10431,10519,10601,10684,10774,10865,10917,10975,11020,11086,11150,11207,11264,11318,11375,11423,11472,11523,11557,11604,11653,11699,11731,11795,11857,11917,11974,12048,12118,12196,12250,12320,12405,12453,12499,12560,12623,12689,12753,12824,12887,12952,13016,13077,13138,13190,13263,13337,13406,13481,13555,13629,13770,13840,13893,13971,14061,14149,14245,14335,14917,15006,15253,15534,15786,16071,16464,16941,17163,17385,17661,17888,18118,18348,18578,18808,19035,19454,19680,20105,20335,20763,20982,21265,21473,21604,21831,22257,22482,22909,23130,23555,23675,23951,24252,24576,24867,25181,25318,25449,25554,25796,25963,26167,26375,26646,26758,26870,26975,27092,27306,27452,27592,27678,28026,28114,28360,28778,29027,29109,29207,29864,29964,30216,30640,30895,30989,31078,31315,33339,33581,33683,33936,36092,46773,48289,58984,60512,62269,62895,63315,64576,65841,66097,66333,66880,67374,67979,68177,68757,70125,70500,70618,71156,71313,71509,71782,72038,72208,72349,72413,72778,73145,73821,74085,74423,74776,74870,75056,75362,75624,75749,75876,76115,76326,76445,76638,76815,77270,77451,77573,77832,77945,78132,78234,78341,78470,78745,79253,79749,80626,80920,81490,81639,82371,82543,82627,82963,83055,83333,88564,93935,93997,94575,95159,95250,95363,95592,95752,95904,96075,96241,96410,96577,96740,96983,97153,97326,97497,97771,97970,98175,98505,98589,98685,98781,98879,98979,99081,99183,99285,99387,99489,99589,99685,99797,99926,100049,100180,100311,100409,100523,100617,100757,100891,100987,101099,101199,101315,101411,101523,101623,101763,101899,102063,102193,102351,102501,102642,102786,102921,103033,103183,103311,103439,103575,103707,103837,103967,104079,104219,104365,104509,104647,104713,104803,104879,104983,105073,105175,105283,105391,105491,105571,105663,105761,105871,105923,106001,106107,106199,106303,106413,106535,106698,106855,106935,107035,107125,107235,107325,107566,107660,107766,107858,107958,108070,108184,108300,108416,108510,108624,108736,108838,108958,109080,109162,109266,109386,109512,109610,109704,109792,109904,110020,110142,110254,110429,110545,110631,110723,110835,110959,111026,111152,111220,111348,111492,111620,111689,111784,111899,112012,112111,112220,112331,112442,112543,112648,112748,112878,112969,113092,113186,113298,113384,113488,113584,113672,113790,113894,113998,114124,114212,114320,114420,114510,114620,114704,114806,114890,114944,115008,115114,115200,115310,115394,115514,118130,118248,118363,118443,118804,119037,119554,119632,120976,122337,122725,125568,135621,135756,137126,138483,139055,139806,140068,140268,140647,144925,145531,145760,145911,146126,147209,147521,150547,151291,153422,153762,155073", "endLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,220,221,225,229,233,238,244,251,255,259,264,268,272,276,280,284,288,294,298,304,308,314,318,323,327,330,334,340,344,350,354,360,363,367,371,375,379,383,384,385,386,389,392,395,398,402,403,404,405,406,409,411,413,415,420,421,425,431,435,436,438,450,451,455,461,465,466,467,471,498,502,503,507,535,707,733,904,930,961,969,975,991,1013,1018,1023,1033,1042,1051,1055,1062,1081,1088,1089,1098,1101,1104,1108,1112,1116,1119,1120,1125,1130,1140,1145,1152,1158,1159,1162,1166,1171,1173,1175,1178,1181,1183,1187,1190,1197,1200,1203,1207,1209,1213,1215,1217,1219,1223,1231,1239,1251,1257,1266,1269,1280,1283,1284,1289,1290,1295,1364,1434,1435,1445,1454,1455,1457,1461,1464,1467,1470,1473,1476,1479,1482,1486,1489,1492,1495,1499,1502,1506,1510,1511,1512,1513,1514,1515,1516,1517,1518,1519,1520,1521,1522,1523,1524,1525,1526,1527,1528,1529,1530,1532,1534,1535,1536,1537,1538,1539,1540,1541,1543,1544,1546,1547,1549,1551,1552,1554,1555,1556,1557,1558,1559,1561,1562,1563,1564,1565,1566,1568,1570,1572,1573,1574,1575,1576,1577,1578,1579,1580,1581,1582,1583,1584,1586,1587,1588,1589,1590,1591,1592,1594,1598,1602,1603,1604,1605,1606,1607,1611,1612,1613,1614,1616,1618,1620,1622,1624,1625,1626,1627,1629,1631,1633,1634,1635,1636,1637,1638,1639,1640,1641,1642,1643,1644,1647,1648,1649,1650,1652,1654,1655,1657,1658,1660,1662,1664,1665,1666,1667,1668,1669,1670,1671,1672,1673,1674,1675,1677,1678,1679,1680,1682,1683,1684,1685,1686,1688,1690,1692,1694,1695,1696,1697,1698,1699,1700,1701,1702,1703,1704,1705,1706,1707,1708,1709,1784,1787,1790,1793,1807,1813,1823,1826,1855,1882,1891,1955,2318,2322,2350,2378,2396,2420,2426,2432,2453,2577,2597,2603,2607,2613,2648,2660,2726,2746,2801,2813,2839,2846", "endColumns": "54,44,48,40,54,61,63,69,60,74,75,76,77,84,81,75,75,76,77,105,105,78,79,56,57,73,74,64,65,59,60,71,72,66,67,58,58,58,58,58,53,53,52,53,53,53,53,73,78,72,73,70,71,71,72,56,57,72,73,73,74,71,72,69,70,59,60,68,68,69,73,75,63,76,75,76,64,68,76,74,68,67,76,65,60,96,64,68,98,70,58,57,56,58,63,70,71,71,71,71,66,67,67,58,62,63,89,90,59,65,66,65,69,63,52,66,60,66,112,57,62,64,64,74,72,71,43,46,45,48,60,60,60,61,63,63,63,64,62,59,60,65,58,59,61,70,59,67,85,86,89,86,87,81,82,89,90,51,57,44,65,63,56,56,53,56,47,48,50,33,46,48,45,31,63,61,59,56,73,69,77,53,69,84,47,45,60,62,65,63,70,62,64,63,60,60,51,72,73,68,74,73,73,140,69,52,77,89,87,95,89,12,88,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,136,130,104,12,12,12,12,12,111,111,104,116,12,12,12,12,12,87,12,12,12,81,12,12,99,12,12,12,93,88,12,12,12,101,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,117,12,12,12,12,12,12,12,63,12,12,12,12,12,12,93,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,83,12,91,12,12,12,61,12,12,90,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,83,95,95,97,99,101,101,101,101,101,99,95,111,128,122,130,130,97,113,93,12,12,95,111,99,115,95,111,99,12,135,12,129,12,12,140,12,134,111,149,127,127,12,131,129,129,111,139,12,12,12,65,89,75,103,89,101,107,107,99,79,91,97,12,51,77,105,91,103,109,12,12,12,79,99,89,109,89,12,93,105,91,12,12,12,12,12,93,113,111,12,12,12,81,103,119,125,97,93,87,111,115,121,111,12,115,85,91,12,12,66,12,67,12,12,12,68,94,114,112,98,108,110,110,100,104,99,12,90,122,93,12,85,103,95,87,12,12,12,12,87,107,99,89,109,83,101,83,53,63,105,85,109,83,119,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24", "endOffsets": "155,200,249,290,345,407,471,541,602,677,753,830,908,993,1075,1151,1227,1304,1382,1488,1594,1673,1753,1810,1868,1942,2017,2082,2148,2208,2269,2341,2414,2481,2549,2608,2667,2726,2785,2844,2898,2952,3005,3059,3113,3167,3221,3295,3374,3447,3521,3592,3664,3736,3809,3866,3924,3997,4071,4145,4220,4292,4365,4435,4506,4566,4627,4696,4765,4835,4909,4985,5049,5126,5202,5279,5344,5413,5490,5565,5634,5702,5779,5845,5906,6003,6068,6137,6236,6307,6366,6424,6481,6540,6604,6675,6747,6819,6891,6963,7030,7098,7166,7225,7288,7352,7442,7533,7593,7659,7726,7792,7862,7926,7979,8046,8107,8174,8287,8345,8408,8473,8538,8613,8686,8758,8802,8849,8895,8944,9005,9066,9127,9189,9253,9317,9381,9446,9509,9569,9630,9696,9755,9815,9877,9948,10008,10076,10162,10249,10339,10426,10514,10596,10679,10769,10860,10912,10970,11015,11081,11145,11202,11259,11313,11370,11418,11467,11518,11552,11599,11648,11694,11726,11790,11852,11912,11969,12043,12113,12191,12245,12315,12400,12448,12494,12555,12618,12684,12748,12819,12882,12947,13011,13072,13133,13185,13258,13332,13401,13476,13550,13624,13765,13835,13888,13966,14056,14144,14240,14330,14912,15001,15248,15529,15781,16066,16459,16936,17158,17380,17656,17883,18113,18343,18573,18803,19030,19449,19675,20100,20330,20758,20977,21260,21468,21599,21826,22252,22477,22904,23125,23550,23670,23946,24247,24571,24862,25176,25313,25444,25549,25791,25958,26162,26370,26641,26753,26865,26970,27087,27301,27447,27587,27673,28021,28109,28355,28773,29022,29104,29202,29859,29959,30211,30635,30890,30984,31073,31310,33334,33576,33678,33931,36087,46768,48284,58979,60507,62264,62890,63310,64571,65836,66092,66328,66875,67369,67974,68172,68752,70120,70495,70613,71151,71308,71504,71777,72033,72203,72344,72408,72773,73140,73816,74080,74418,74771,74865,75051,75357,75619,75744,75871,76110,76321,76440,76633,76810,77265,77446,77568,77827,77940,78127,78229,78336,78465,78740,79248,79744,80621,80915,81485,81634,82366,82538,82622,82958,83050,83328,88559,93930,93992,94570,95154,95245,95358,95587,95747,95899,96070,96236,96405,96572,96735,96978,97148,97321,97492,97766,97965,98170,98500,98584,98680,98776,98874,98974,99076,99178,99280,99382,99484,99584,99680,99792,99921,100044,100175,100306,100404,100518,100612,100752,100886,100982,101094,101194,101310,101406,101518,101618,101758,101894,102058,102188,102346,102496,102637,102781,102916,103028,103178,103306,103434,103570,103702,103832,103962,104074,104214,104360,104504,104642,104708,104798,104874,104978,105068,105170,105278,105386,105486,105566,105658,105756,105866,105918,105996,106102,106194,106298,106408,106530,106693,106850,106930,107030,107120,107230,107320,107561,107655,107761,107853,107953,108065,108179,108295,108411,108505,108619,108731,108833,108953,109075,109157,109261,109381,109507,109605,109699,109787,109899,110015,110137,110249,110424,110540,110626,110718,110830,110954,111021,111147,111215,111343,111487,111615,111684,111779,111894,112007,112106,112215,112326,112437,112538,112643,112743,112873,112964,113087,113181,113293,113379,113483,113579,113667,113785,113889,113993,114119,114207,114315,114415,114505,114615,114699,114801,114885,114939,115003,115109,115195,115305,115389,115509,118125,118243,118358,118438,118799,119032,119549,119627,120971,122332,122720,125563,135616,135751,137121,138478,139050,139801,140063,140263,140642,144920,145526,145755,145906,146121,147204,147516,150542,151286,153417,153757,155068,155271"}, "to": {"startLines": "35,36,37,41,46,47,48,49,50,51,52,53,54,57,58,59,60,62,63,64,65,66,67,68,69,75,76,77,78,79,80,81,82,83,84,86,87,88,89,90,91,92,93,94,95,96,97,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,208,209,211,212,213,214,215,216,217,240,241,242,243,244,245,246,247,283,284,285,286,288,291,292,295,312,318,319,320,321,324,325,326,327,328,329,330,331,332,333,334,335,336,337,338,339,340,341,342,343,344,345,346,347,348,349,350,375,378,379,380,381,382,397,405,406,410,414,418,423,429,436,440,444,449,453,457,461,465,469,473,479,483,489,493,499,503,508,512,515,519,525,529,535,539,545,548,552,556,560,564,568,569,570,571,574,577,580,583,587,588,589,590,591,594,596,598,600,605,606,610,616,620,621,623,635,636,640,646,650,654,655,659,686,690,691,695,723,895,921,1092,1118,1149,1157,1163,1179,1201,1206,1211,1221,1230,1239,1243,1250,1269,1276,1277,1286,1289,1292,1296,1300,1304,1307,1308,1313,1318,1328,1333,1340,1346,1347,1350,1354,1359,1361,1363,1366,1369,1371,1375,1378,1385,1388,1391,1395,1397,1401,1403,1405,1407,1411,1419,1427,1439,1445,1454,1457,1468,1471,1472,1477,1478,1487,1556,1626,1627,1637,1646,1647,1649,1653,1656,1659,1662,1665,1668,1671,1674,1678,1681,1684,1687,1691,1694,1698,1702,1703,1704,1705,1706,1707,1708,1709,1710,1711,1712,1713,1714,1715,1716,1717,1718,1719,1720,1721,1722,1724,1726,1727,1728,1729,1730,1731,1732,1733,1735,1736,1738,1739,1741,1743,1744,1746,1747,1748,1749,1750,1751,1753,1754,1755,1756,1757,1769,1771,1773,1775,1776,1777,1778,1779,1780,1781,1782,1783,1784,1785,1786,1787,1789,1790,1791,1792,1793,1794,1795,1797,1801,1831,1832,1833,1834,1835,1836,1840,1841,1842,1843,1845,1847,1849,1851,1853,1854,1855,1856,1858,1860,1862,1863,1864,1865,1866,1867,1868,1869,1870,1871,1872,1873,1876,1877,1878,1879,1881,1883,1884,1886,1887,1889,1891,1893,1894,1895,1896,1897,1898,1899,1900,1901,1902,1903,1904,1906,1907,1908,1909,1911,1912,1913,1914,1915,1917,1919,1921,1923,1924,1925,1926,1927,1928,1929,1930,1931,1932,1933,1934,1935,1936,1937,1943,2018,2021,2024,2027,2041,2047,2089,2092,2121,2148,2157,2221,2584,2594,2632,2750,2872,2896,2902,2908,2929,3053,3073,3079,3083,3089,3124,3156,3222,3242,3297,3309,3335", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1260,1315,1360,1588,1905,1960,2022,2086,2156,2217,2292,2368,2445,2683,2768,2850,2926,3058,3135,3213,3319,3425,3504,3584,3641,4061,4135,4210,4275,4341,4401,4462,4534,4607,4674,4799,4858,4917,4976,5035,5094,5148,5202,5255,5309,5363,5417,5603,5677,5756,5829,5903,5974,6046,6118,6191,6248,6306,6379,6453,6527,6602,6674,6747,6817,6888,6948,7009,7078,7147,7217,7291,7367,7431,7508,7584,7661,7726,7795,7872,7947,8016,8084,8161,8227,8288,8385,8450,8519,8618,8689,8748,8806,8863,8922,8986,9057,9129,9201,9273,9345,9412,9480,9548,9607,9670,9734,9824,9915,9975,10041,10108,10174,10244,10308,10361,10428,10489,10556,10669,10727,10790,10855,10920,10995,11068,11140,11184,11231,11277,11326,11387,11448,11509,11571,11635,11699,11763,11828,11891,11951,12012,12078,12137,12197,12259,12330,12390,12946,13032,13172,13262,13349,13437,13519,13602,13692,15275,15327,15385,15430,15496,15560,15617,15674,17851,17908,17956,18005,18113,18217,18264,18420,19325,19628,19692,19754,19814,20009,20083,20153,20231,20285,20355,20440,20488,20534,20595,20658,20724,20788,20859,20922,20987,21051,21112,21173,21225,21298,21372,21441,21516,21590,21664,21805,23802,23986,24064,24154,24242,24338,25112,25694,25783,26030,26311,26563,26848,27241,27718,27940,28162,28438,28665,28895,29125,29355,29585,29812,30231,30457,30882,31112,31540,31759,32042,32250,32381,32608,33034,33259,33686,33907,34332,34452,34728,35029,35353,35644,35958,36095,36226,36331,36573,36740,36944,37152,37423,37535,37647,37752,37869,38083,38229,38369,38455,38803,38891,39137,39555,39804,39886,39984,40641,40741,40993,41417,41672,42032,42121,42358,44382,44624,44726,44979,47135,57816,59332,70027,71555,73312,73938,74358,75619,76884,77140,77376,77923,78417,79022,79220,79800,81168,81543,81661,82199,82356,82552,82825,83081,83251,83392,83456,83821,84188,84864,85128,85466,85819,85913,86099,86405,86667,86792,86919,87158,87369,87488,87681,87858,88313,88494,88616,88875,88988,89175,89277,89384,89513,89788,90296,90792,91669,91963,92533,92682,93414,93586,93670,94006,94098,94606,99837,105208,105270,105848,106432,106523,106636,106865,107025,107177,107348,107514,107683,107850,108013,108256,108426,108599,108770,109044,109243,109448,109778,109862,109958,110054,110152,110252,110354,110456,110558,110660,110762,110862,110958,111070,111199,111322,111453,111584,111682,111796,111890,112030,112164,112260,112372,112472,112588,112684,112796,112896,113036,113172,113336,113466,113624,113774,113915,114059,114194,114306,114456,114584,114712,114848,114980,115110,115240,115352,116250,116396,116540,116678,116744,116834,116910,117014,117104,117206,117314,117422,117522,117602,117694,117792,117902,117954,118032,118138,118230,118334,118444,118566,118729,120394,120474,120574,120664,120774,120864,121105,121199,121305,121397,121497,121609,121723,121839,121955,122049,122163,122275,122377,122497,122619,122701,122805,122925,123051,123149,123243,123331,123443,123559,123681,123793,123968,124084,124170,124262,124374,124498,124565,124691,124759,124887,125031,125159,125228,125323,125438,125551,125650,125759,125870,125981,126082,126187,126287,126417,126508,126631,126725,126837,126923,127027,127123,127211,127329,127433,127537,127663,127751,127859,127959,128049,128159,128243,128345,128429,128483,128547,128653,128739,128849,128933,129337,131953,132071,132186,132266,132627,132860,134264,134342,135686,137047,137435,140278,150331,150669,152340,158424,162651,163402,163664,163864,164243,168521,169127,169356,169507,169722,170805,171655,174681,175425,177556,177896,179207", "endLines": "35,36,37,41,46,47,48,49,50,51,52,53,54,57,58,59,60,62,63,64,65,66,67,68,69,75,76,77,78,79,80,81,82,83,84,86,87,88,89,90,91,92,93,94,95,96,97,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,208,209,211,212,213,214,215,216,217,240,241,242,243,244,245,246,247,283,284,285,286,288,291,292,295,312,318,319,320,321,324,325,326,327,328,329,330,331,332,333,334,335,336,337,338,339,340,341,342,343,344,345,346,347,348,349,350,375,378,379,380,381,382,404,405,409,413,417,422,428,435,439,443,448,452,456,460,464,468,472,478,482,488,492,498,502,507,511,514,518,524,528,534,538,544,547,551,555,559,563,567,568,569,570,573,576,579,582,586,587,588,589,590,593,595,597,599,604,605,609,615,619,620,622,634,635,639,645,649,650,654,658,685,689,690,694,722,894,920,1091,1117,1148,1156,1162,1178,1200,1205,1210,1220,1229,1238,1242,1249,1268,1275,1276,1285,1288,1291,1295,1299,1303,1306,1307,1312,1317,1327,1332,1339,1345,1346,1349,1353,1358,1360,1362,1365,1368,1370,1374,1377,1384,1387,1390,1394,1396,1400,1402,1404,1406,1410,1418,1426,1438,1444,1453,1456,1467,1470,1471,1476,1477,1482,1555,1625,1626,1636,1645,1646,1648,1652,1655,1658,1661,1664,1667,1670,1673,1677,1680,1683,1686,1690,1693,1697,1701,1702,1703,1704,1705,1706,1707,1708,1709,1710,1711,1712,1713,1714,1715,1716,1717,1718,1719,1720,1721,1723,1725,1726,1727,1728,1729,1730,1731,1732,1734,1735,1737,1738,1740,1742,1743,1745,1746,1747,1748,1749,1750,1752,1753,1754,1755,1756,1757,1770,1772,1774,1775,1776,1777,1778,1779,1780,1781,1782,1783,1784,1785,1786,1788,1789,1790,1791,1792,1793,1794,1796,1800,1804,1831,1832,1833,1834,1835,1839,1840,1841,1842,1844,1846,1848,1850,1852,1853,1854,1855,1857,1859,1861,1862,1863,1864,1865,1866,1867,1868,1869,1870,1871,1872,1875,1876,1877,1878,1880,1882,1883,1885,1886,1888,1890,1892,1893,1894,1895,1896,1897,1898,1899,1900,1901,1902,1903,1905,1906,1907,1908,1910,1911,1912,1913,1914,1916,1918,1920,1922,1923,1924,1925,1926,1927,1928,1929,1930,1931,1932,1933,1934,1935,1936,1937,2017,2020,2023,2026,2040,2046,2056,2091,2120,2147,2156,2220,2583,2587,2621,2659,2767,2895,2901,2907,2928,3052,3072,3078,3082,3088,3123,3135,3221,3241,3296,3308,3334,3341", "endColumns": "54,44,48,40,54,61,63,69,60,74,75,76,77,84,81,75,75,76,77,105,105,78,79,56,57,73,74,64,65,59,60,71,72,66,67,58,58,58,58,58,53,53,52,53,53,53,53,73,78,72,73,70,71,71,72,56,57,72,73,73,74,71,72,69,70,59,60,68,68,69,73,75,63,76,75,76,64,68,76,74,68,67,76,65,60,96,64,68,98,70,58,57,56,58,63,70,71,71,71,71,66,67,67,58,62,63,89,90,59,65,66,65,69,63,52,66,60,66,112,57,62,64,64,74,72,71,43,46,45,48,60,60,60,61,63,63,63,64,62,59,60,65,58,59,61,70,59,67,85,86,89,86,87,81,82,89,90,51,57,44,65,63,56,56,53,56,47,48,50,33,46,48,45,31,63,61,59,56,73,69,77,53,69,84,47,45,60,62,65,63,70,62,64,63,60,60,51,72,73,68,74,73,73,140,69,52,77,89,87,95,89,12,88,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,136,130,104,12,12,12,12,12,111,111,104,116,12,12,12,12,12,87,12,12,12,81,12,12,99,12,12,12,93,88,12,12,12,101,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,117,12,12,12,12,12,12,12,63,12,12,12,12,12,12,93,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,83,12,91,12,12,12,61,12,12,90,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,83,95,95,97,99,101,101,101,101,101,99,95,111,128,122,130,130,97,113,93,12,12,95,111,99,115,95,111,99,12,135,12,129,12,12,140,12,134,111,149,127,127,12,131,129,129,111,139,12,12,12,65,89,75,103,89,101,107,107,99,79,91,97,12,51,77,105,91,103,109,12,12,12,79,99,89,109,89,12,93,105,91,12,12,12,12,12,93,113,111,12,12,12,81,103,119,125,97,93,87,111,115,121,111,12,115,85,91,12,12,66,12,67,12,12,12,68,94,114,112,98,108,110,110,100,104,99,12,90,122,93,12,85,103,95,87,12,12,12,12,87,107,99,89,109,83,101,83,53,63,105,85,109,83,119,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24", "endOffsets": "1310,1355,1404,1624,1955,2017,2081,2151,2212,2287,2363,2440,2518,2763,2845,2921,2997,3130,3208,3314,3420,3499,3579,3636,3694,4130,4205,4270,4336,4396,4457,4529,4602,4669,4737,4853,4912,4971,5030,5089,5143,5197,5250,5304,5358,5412,5466,5672,5751,5824,5898,5969,6041,6113,6186,6243,6301,6374,6448,6522,6597,6669,6742,6812,6883,6943,7004,7073,7142,7212,7286,7362,7426,7503,7579,7656,7721,7790,7867,7942,8011,8079,8156,8222,8283,8380,8445,8514,8613,8684,8743,8801,8858,8917,8981,9052,9124,9196,9268,9340,9407,9475,9543,9602,9665,9729,9819,9910,9970,10036,10103,10169,10239,10303,10356,10423,10484,10551,10664,10722,10785,10850,10915,10990,11063,11135,11179,11226,11272,11321,11382,11443,11504,11566,11630,11694,11758,11823,11886,11946,12007,12073,12132,12192,12254,12325,12385,12453,13027,13114,13257,13344,13432,13514,13597,13687,13778,15322,15380,15425,15491,15555,15612,15669,15723,17903,17951,18000,18051,18142,18259,18308,18461,19352,19687,19749,19809,19866,20078,20148,20226,20280,20350,20435,20483,20529,20590,20653,20719,20783,20854,20917,20982,21046,21107,21168,21220,21293,21367,21436,21511,21585,21659,21800,21870,23850,24059,24149,24237,24333,24423,25689,25778,26025,26306,26558,26843,27236,27713,27935,28157,28433,28660,28890,29120,29350,29580,29807,30226,30452,30877,31107,31535,31754,32037,32245,32376,32603,33029,33254,33681,33902,34327,34447,34723,35024,35348,35639,35953,36090,36221,36326,36568,36735,36939,37147,37418,37530,37642,37747,37864,38078,38224,38364,38450,38798,38886,39132,39550,39799,39881,39979,40636,40736,40988,41412,41667,41761,42116,42353,44377,44619,44721,44974,47130,57811,59327,70022,71550,73307,73933,74353,75614,76879,77135,77371,77918,78412,79017,79215,79795,81163,81538,81656,82194,82351,82547,82820,83076,83246,83387,83451,83816,84183,84859,85123,85461,85814,85908,86094,86400,86662,86787,86914,87153,87364,87483,87676,87853,88308,88489,88611,88870,88983,89170,89272,89379,89508,89783,90291,90787,91664,91958,92528,92677,93409,93581,93665,94001,94093,94371,99832,105203,105265,105843,106427,106518,106631,106860,107020,107172,107343,107509,107678,107845,108008,108251,108421,108594,108765,109039,109238,109443,109773,109857,109953,110049,110147,110247,110349,110451,110553,110655,110757,110857,110953,111065,111194,111317,111448,111579,111677,111791,111885,112025,112159,112255,112367,112467,112583,112679,112791,112891,113031,113167,113331,113461,113619,113769,113910,114054,114189,114301,114451,114579,114707,114843,114975,115105,115235,115347,115487,116391,116535,116673,116739,116829,116905,117009,117099,117201,117309,117417,117517,117597,117689,117787,117897,117949,118027,118133,118225,118329,118439,118561,118724,118881,120469,120569,120659,120769,120859,121100,121194,121300,121392,121492,121604,121718,121834,121950,122044,122158,122270,122372,122492,122614,122696,122800,122920,123046,123144,123238,123326,123438,123554,123676,123788,123963,124079,124165,124257,124369,124493,124560,124686,124754,124882,125026,125154,125223,125318,125433,125546,125645,125754,125865,125976,126077,126182,126282,126412,126503,126626,126720,126832,126918,127022,127118,127206,127324,127428,127532,127658,127746,127854,127954,128044,128154,128238,128340,128424,128478,128542,128648,128734,128844,128928,129048,131948,132066,132181,132261,132622,132855,133372,134337,135681,137042,137430,140273,150326,150461,152034,153692,158991,163397,163659,163859,164238,168516,169122,169351,169502,169717,170800,171112,174676,175420,177551,177891,179202,179405"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\ea82f0e4740fea90ca38a885280cdee8\\transformed\\core-1.15.0\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,98,99,103,104,105,106,112,122,157,178,211", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,115,187,275,340,406,475,538,608,676,748,818,879,953,1026,1087,1148,1210,1274,1336,1397,1465,1565,1625,1691,1764,1833,1890,1942,2004,2076,2152,2217,2276,2335,2395,2455,2515,2575,2635,2695,2755,2815,2875,2935,2994,3054,3114,3174,3234,3294,3354,3414,3474,3534,3594,3653,3713,3773,3832,3891,3950,4009,4068,4127,4162,4197,4252,4315,4370,4428,4486,4547,4610,4667,4718,4768,4829,4886,4952,4986,5021,5056,5126,5193,5265,5334,5403,5477,5549,5637,5708,5825,6026,6136,6337,6466,6538,6605,6808,7109,8915,9596,10278", "endLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,97,98,102,103,104,105,111,121,156,177,210,216", "endColumns": "59,71,87,64,65,68,62,69,67,71,69,60,73,72,60,60,61,63,61,60,67,99,59,65,72,68,56,51,61,71,75,64,58,58,59,59,59,59,59,59,59,59,59,59,58,59,59,59,59,59,59,59,59,59,59,58,59,59,58,58,58,58,58,58,34,34,54,62,54,57,57,60,62,56,50,49,60,56,65,33,34,34,69,66,71,68,68,73,71,87,70,116,12,109,12,128,71,66,24,24,24,24,24,24", "endOffsets": "110,182,270,335,401,470,533,603,671,743,813,874,948,1021,1082,1143,1205,1269,1331,1392,1460,1560,1620,1686,1759,1828,1885,1937,1999,2071,2147,2212,2271,2330,2390,2450,2510,2570,2630,2690,2750,2810,2870,2930,2989,3049,3109,3169,3229,3289,3349,3409,3469,3529,3589,3648,3708,3768,3827,3886,3945,4004,4063,4122,4157,4192,4247,4310,4365,4423,4481,4542,4605,4662,4713,4763,4824,4881,4947,4981,5016,5051,5121,5188,5260,5329,5398,5472,5544,5632,5703,5820,6021,6131,6332,6461,6533,6600,6803,7104,8910,9591,10273,10440"}, "to": {"startLines": "38,55,56,70,71,98,99,201,202,203,204,205,206,207,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,248,249,250,251,252,253,254,255,256,257,258,259,260,261,262,263,264,265,266,267,268,269,270,271,272,273,274,275,276,277,278,279,280,281,282,289,290,296,297,298,299,300,301,302,303,304,305,306,307,308,309,310,311,323,353,354,355,356,357,358,359,376,1758,1759,1763,1764,1768,1938,1939,2588,2622,2768,2803,2833,2866", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1409,2523,2595,3699,3764,5471,5540,12458,12528,12596,12668,12738,12799,12873,13783,13844,13905,13967,14031,14093,14154,14222,14322,14382,14448,14521,14590,14647,14699,15728,15800,15876,15941,16000,16059,16119,16179,16239,16299,16359,16419,16479,16539,16599,16659,16718,16778,16838,16898,16958,17018,17078,17138,17198,17258,17318,17377,17437,17497,17556,17615,17674,17733,17792,18147,18182,18466,18521,18584,18639,18697,18755,18816,18879,18936,18987,19037,19098,19155,19221,19255,19290,19939,22007,22074,22146,22215,22284,22358,22430,23855,115492,115609,115810,115920,116121,129053,129125,150466,152039,158996,160802,161802,162484", "endLines": "38,55,56,70,71,98,99,201,202,203,204,205,206,207,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,248,249,250,251,252,253,254,255,256,257,258,259,260,261,262,263,264,265,266,267,268,269,270,271,272,273,274,275,276,277,278,279,280,281,282,289,290,296,297,298,299,300,301,302,303,304,305,306,307,308,309,310,311,323,353,354,355,356,357,358,359,376,1758,1762,1763,1767,1768,1938,1939,2593,2631,2802,2823,2865,2871", "endColumns": "59,71,87,64,65,68,62,69,67,71,69,60,73,72,60,60,61,63,61,60,67,99,59,65,72,68,56,51,61,71,75,64,58,58,59,59,59,59,59,59,59,59,59,59,58,59,59,59,59,59,59,59,59,59,59,58,59,59,58,58,58,58,58,58,34,34,54,62,54,57,57,60,62,56,50,49,60,56,65,33,34,34,69,66,71,68,68,73,71,87,70,116,12,109,12,128,71,66,24,24,24,24,24,24", "endOffsets": "1464,2590,2678,3759,3825,5535,5598,12523,12591,12663,12733,12794,12868,12941,13839,13900,13962,14026,14088,14149,14217,14317,14377,14443,14516,14585,14642,14694,14756,15795,15871,15936,15995,16054,16114,16174,16234,16294,16354,16414,16474,16534,16594,16654,16713,16773,16833,16893,16953,17013,17073,17133,17193,17253,17313,17372,17432,17492,17551,17610,17669,17728,17787,17846,18177,18212,18516,18579,18634,18692,18750,18811,18874,18931,18982,19032,19093,19150,19216,19250,19285,19320,20004,22069,22141,22210,22279,22353,22425,22513,23921,115604,115805,115915,116116,116245,129120,129187,150664,152335,160797,161478,162479,162646"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\7d963e06227f9c96672f16e0a743fb69\\transformed\\coordinatorlayout-1.2.0\\res\\values\\values.xml", "from": {"startLines": "2,3,6,16", "startColumns": "4,4,4,4", "startOffsets": "55,116,261,869", "endLines": "2,5,15,104", "endColumns": "60,12,24,24", "endOffsets": "111,256,864,6075"}, "to": {"startLines": "34,1940,2660,2666", "startColumns": "4,4,4,4", "startOffsets": "1199,129192,153697,153908", "endLines": "34,1942,2665,2749", "endColumns": "60,12,24,24", "endOffsets": "1255,129332,153903,158419"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\1a40dcf1dbe43aa4d3fcc9c9bef1a483\\transformed\\lifecycle-viewmodel-2.6.2\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "49", "endOffsets": "100"}, "to": {"startLines": "316", "startColumns": "4", "startOffsets": "19514", "endColumns": "49", "endOffsets": "19559"}}]}]}