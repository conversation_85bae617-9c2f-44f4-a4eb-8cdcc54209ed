{"version": 3, "sources": ["../../../../../../node_modules/@ionic/core/dist/esm/overlays-8Y2rA-ps.js"], "sourcesContent": ["/*!\n * (C) Ionic http://ionicframework.com - MIT License\n */\nimport { d as doc } from './index-ZjP4CjeZ.js';\nimport { n as focusVisibleElement, c as componentOnReady, f as addEventListener, m as removeEventListener, g as getElementRoot } from './helpers-1O4D2b7y.js';\nimport { OVERLAY_BACK_BUTTON_PRIORITY, shouldUseCloseWatcher } from './hardware-back-button-DcH0BbDp.js';\nimport { e as getIonMode, l as config, o as printIonError, m as printIonWarning, a as isPlatform } from './index-B_U9CtaY.js';\nimport { C as CoreDelegate } from './framework-delegate-DxcnWic_.js';\nimport { B as BACKDROP_NO_SCROLL } from './gesture-controller-BTEOs1at.js';\n\n/**\n * This query string selects elements that\n * are eligible to receive focus. We select\n * interactive elements that meet the following\n * criteria:\n * 1. <PERSON><PERSON> does not have a negative tabindex\n * 2. <PERSON><PERSON> does not have `hidden`\n * 3. <PERSON><PERSON> does not have `disabled` for non-Ionic components.\n * 4. <PERSON><PERSON> does not have `disabled` or `disabled=\"true\"` for Ionic components.\n * Note: We need this distinction because `disabled=\"false\"` is\n * valid usage for the disabled property on ion-button.\n */\nconst focusableQueryString = '[tabindex]:not([tabindex^=\"-\"]):not([hidden]):not([disabled]), input:not([type=hidden]):not([tabindex^=\"-\"]):not([hidden]):not([disabled]), textarea:not([tabindex^=\"-\"]):not([hidden]):not([disabled]), button:not([tabindex^=\"-\"]):not([hidden]):not([disabled]), select:not([tabindex^=\"-\"]):not([hidden]):not([disabled]), ion-checkbox:not([tabindex^=\"-\"]):not([hidden]):not([disabled]), ion-radio:not([tabindex^=\"-\"]):not([hidden]):not([disabled]), .ion-focusable:not([tabindex^=\"-\"]):not([hidden]):not([disabled]), .ion-focusable[disabled=\"false\"]:not([tabindex^=\"-\"]):not([hidden])';\n/**\n * Focuses the first descendant in a context\n * that can receive focus. If none exists,\n * a fallback element will be focused.\n * This fallback is typically an ancestor\n * container such as a menu or overlay so focus does not\n * leave the container we are trying to trap focus in.\n *\n * If no fallback is specified then we focus the container itself.\n */\nconst focusFirstDescendant = (ref, fallbackElement) => {\n    const firstInput = ref.querySelector(focusableQueryString);\n    focusElementInContext(firstInput, fallbackElement !== null && fallbackElement !== void 0 ? fallbackElement : ref);\n};\n/**\n * Focuses the last descendant in a context\n * that can receive focus. If none exists,\n * a fallback element will be focused.\n * This fallback is typically an ancestor\n * container such as a menu or overlay so focus does not\n * leave the container we are trying to trap focus in.\n *\n * If no fallback is specified then we focus the container itself.\n */\nconst focusLastDescendant = (ref, fallbackElement) => {\n    const inputs = Array.from(ref.querySelectorAll(focusableQueryString));\n    const lastInput = inputs.length > 0 ? inputs[inputs.length - 1] : null;\n    focusElementInContext(lastInput, fallbackElement !== null && fallbackElement !== void 0 ? fallbackElement : ref);\n};\n/**\n * Focuses a particular element in a context. If the element\n * doesn't have anything focusable associated with it then\n * a fallback element will be focused.\n *\n * This fallback is typically an ancestor\n * container such as a menu or overlay so focus does not\n * leave the container we are trying to trap focus in.\n * This should be used instead of the focus() method\n * on most elements because the focusable element\n * may not be the host element.\n *\n * For example, if an ion-button should be focused\n * then we should actually focus the native <button>\n * element inside of ion-button's shadow root, not\n * the host element itself.\n */\nconst focusElementInContext = (hostToFocus, fallbackElement) => {\n    let elementToFocus = hostToFocus;\n    const shadowRoot = hostToFocus === null || hostToFocus === void 0 ? void 0 : hostToFocus.shadowRoot;\n    if (shadowRoot) {\n        // If there are no inner focusable elements, just focus the host element.\n        elementToFocus = shadowRoot.querySelector(focusableQueryString) || hostToFocus;\n    }\n    if (elementToFocus) {\n        const radioGroup = elementToFocus.closest('ion-radio-group');\n        if (radioGroup) {\n            radioGroup.setFocus();\n        }\n        else {\n            focusVisibleElement(elementToFocus);\n        }\n    }\n    else {\n        // Focus fallback element instead of letting focus escape\n        fallbackElement.focus();\n    }\n};\n\nlet lastOverlayIndex = 0;\nlet lastId = 0;\nconst activeAnimations = new WeakMap();\nconst createController = (tagName) => {\n    return {\n        create(options) {\n            return createOverlay(tagName, options);\n        },\n        dismiss(data, role, id) {\n            return dismissOverlay(document, data, role, tagName, id);\n        },\n        async getTop() {\n            return getPresentedOverlay(document, tagName);\n        },\n    };\n};\nconst alertController = /*@__PURE__*/ createController('ion-alert');\nconst actionSheetController = /*@__PURE__*/ createController('ion-action-sheet');\nconst loadingController = /*@__PURE__*/ createController('ion-loading');\nconst modalController = /*@__PURE__*/ createController('ion-modal');\n/**\n * @deprecated Use the inline ion-picker component instead.\n */\nconst pickerController = /*@__PURE__*/ createController('ion-picker-legacy');\nconst popoverController = /*@__PURE__*/ createController('ion-popover');\nconst toastController = /*@__PURE__*/ createController('ion-toast');\n/**\n * Prepares the overlay element to be presented.\n */\nconst prepareOverlay = (el) => {\n    if (typeof document !== 'undefined') {\n        /**\n         * Adds a single instance of event listeners for application behaviors:\n         *\n         * - Escape Key behavior to dismiss an overlay\n         * - Trapping focus within an overlay\n         * - Back button behavior to dismiss an overlay\n         *\n         * This only occurs when the first overlay is created.\n         */\n        connectListeners(document);\n    }\n    const overlayIndex = lastOverlayIndex++;\n    /**\n     * overlayIndex is used in the overlay components to set a zIndex.\n     * This ensures that the most recently presented overlay will be\n     * on top.\n     */\n    el.overlayIndex = overlayIndex;\n};\n/**\n * Assigns an incrementing id to an overlay element, that does not\n * already have an id assigned to it.\n *\n * Used to track unique instances of an overlay element.\n */\nconst setOverlayId = (el) => {\n    if (!el.hasAttribute('id')) {\n        el.id = `ion-overlay-${++lastId}`;\n    }\n    return el.id;\n};\nconst createOverlay = (tagName, opts) => {\n    // eslint-disable-next-line @typescript-eslint/prefer-optional-chain\n    if (typeof window !== 'undefined' && typeof window.customElements !== 'undefined') {\n        return window.customElements.whenDefined(tagName).then(() => {\n            const element = document.createElement(tagName);\n            element.classList.add('overlay-hidden');\n            /**\n             * Convert the passed in overlay options into props\n             * that get passed down into the new overlay.\n             */\n            Object.assign(element, Object.assign(Object.assign({}, opts), { hasController: true }));\n            // append the overlay element to the document body\n            getAppRoot(document).appendChild(element);\n            return new Promise((resolve) => componentOnReady(element, resolve));\n        });\n    }\n    return Promise.resolve();\n};\nconst isOverlayHidden = (overlay) => overlay.classList.contains('overlay-hidden');\n/**\n * Focuses a particular element in an overlay. If the element\n * doesn't have anything focusable associated with it then\n * the overlay itself will be focused.\n * This should be used instead of the focus() method\n * on most elements because the focusable element\n * may not be the host element.\n *\n * For example, if an ion-button should be focused\n * then we should actually focus the native <button>\n * element inside of ion-button's shadow root, not\n * the host element itself.\n */\nconst focusElementInOverlay = (hostToFocus, overlay) => {\n    let elementToFocus = hostToFocus;\n    const shadowRoot = hostToFocus === null || hostToFocus === void 0 ? void 0 : hostToFocus.shadowRoot;\n    if (shadowRoot) {\n        // If there are no inner focusable elements, just focus the host element.\n        elementToFocus = shadowRoot.querySelector(focusableQueryString) || hostToFocus;\n    }\n    if (elementToFocus) {\n        focusVisibleElement(elementToFocus);\n    }\n    else {\n        // Focus overlay instead of letting focus escape\n        overlay.focus();\n    }\n};\n/**\n * Traps keyboard focus inside of overlay components.\n * Based on https://w3c.github.io/aria-practices/examples/dialog-modal/alertdialog.html\n * This includes the following components: Action Sheet, Alert, Loading, Modal,\n * Picker, and Popover.\n * Should NOT include: Toast\n */\nconst trapKeyboardFocus = (ev, doc) => {\n    const lastOverlay = getPresentedOverlay(doc, 'ion-alert,ion-action-sheet,ion-loading,ion-modal,ion-picker-legacy,ion-popover');\n    const target = ev.target;\n    /**\n     * If no active overlay, ignore this event.\n     *\n     * If this component uses the shadow dom,\n     * this global listener is pointless\n     * since it will not catch the focus\n     * traps as they are inside the shadow root.\n     * We need to add a listener to the shadow root\n     * itself to ensure the focus trap works.\n     */\n    if (!lastOverlay || !target) {\n        return;\n    }\n    /**\n     * If the ion-disable-focus-trap class\n     * is present on an overlay, then this component\n     * instance has opted out of focus trapping.\n     * An example of this is when the sheet modal\n     * has a backdrop that is disabled. The content\n     * behind the sheet should be focusable until\n     * the backdrop is enabled.\n     */\n    if (lastOverlay.classList.contains(FOCUS_TRAP_DISABLE_CLASS)) {\n        return;\n    }\n    const trapScopedFocus = () => {\n        /**\n         * If we are focusing the overlay, clear\n         * the last focused element so that hitting\n         * tab activates the first focusable element\n         * in the overlay wrapper.\n         */\n        if (lastOverlay === target) {\n            lastOverlay.lastFocus = undefined;\n            /**\n             * Toasts can be presented from an overlay.\n             * However, focus should still be returned to\n             * the overlay when clicking a toast. Normally,\n             * focus would be returned to the last focusable\n             * descendant in the overlay which may not always be\n             * the button that the toast was presented from. In this case,\n             * the focus may be returned to an unexpected element.\n             * To account for this, we make sure to return focus to the\n             * last focused element in the overlay if focus is\n             * moved to the toast.\n             */\n        }\n        else if (target.tagName === 'ION-TOAST') {\n            focusElementInOverlay(lastOverlay.lastFocus, lastOverlay);\n            /**\n             * Otherwise, we must be focusing an element\n             * inside of the overlay. The two possible options\n             * here are an input/button/etc or the ion-focus-trap\n             * element. The focus trap element is used to prevent\n             * the keyboard focus from leaving the overlay when\n             * using Tab or screen assistants.\n             */\n        }\n        else {\n            /**\n             * We do not want to focus the traps, so get the overlay\n             * wrapper element as the traps live outside of the wrapper.\n             */\n            const overlayRoot = getElementRoot(lastOverlay);\n            if (!overlayRoot.contains(target)) {\n                return;\n            }\n            const overlayWrapper = overlayRoot.querySelector('.ion-overlay-wrapper');\n            if (!overlayWrapper) {\n                return;\n            }\n            /**\n             * If the target is inside the wrapper, let the browser\n             * focus as normal and keep a log of the last focused element.\n             * Additionally, if the backdrop was tapped we should not\n             * move focus back inside the wrapper as that could cause\n             * an interactive elements focus state to activate.\n             */\n            if (overlayWrapper.contains(target) || target === overlayRoot.querySelector('ion-backdrop')) {\n                lastOverlay.lastFocus = target;\n            }\n            else {\n                /**\n                 * Otherwise, we must have focused one of the focus traps.\n                 * We need to wrap the focus to either the first element\n                 * or the last element.\n                 */\n                /**\n                 * Once we call `focusFirstDescendant` and focus the first\n                 * descendant, another focus event will fire which will\n                 * cause `lastOverlay.lastFocus` to be updated before\n                 * we can run the code after that. We will cache the value\n                 * here to avoid that.\n                 */\n                const lastFocus = lastOverlay.lastFocus;\n                // Focus the first element in the overlay wrapper\n                focusFirstDescendant(overlayWrapper, lastOverlay);\n                /**\n                 * If the cached last focused element is the\n                 * same as the active element, then we need\n                 * to wrap focus to the last descendant. This happens\n                 * when the first descendant is focused, and the user\n                 * presses Shift + Tab. The previous line will focus\n                 * the same descendant again (the first one), causing\n                 * last focus to equal the active element.\n                 */\n                if (lastFocus === doc.activeElement) {\n                    focusLastDescendant(overlayWrapper, lastOverlay);\n                }\n                lastOverlay.lastFocus = doc.activeElement;\n            }\n        }\n    };\n    const trapShadowFocus = () => {\n        /**\n         * If the target is inside the wrapper, let the browser\n         * focus as normal and keep a log of the last focused element.\n         */\n        if (lastOverlay.contains(target)) {\n            lastOverlay.lastFocus = target;\n            /**\n             * Toasts can be presented from an overlay.\n             * However, focus should still be returned to\n             * the overlay when clicking a toast. Normally,\n             * focus would be returned to the last focusable\n             * descendant in the overlay which may not always be\n             * the button that the toast was presented from. In this case,\n             * the focus may be returned to an unexpected element.\n             * To account for this, we make sure to return focus to the\n             * last focused element in the overlay if focus is\n             * moved to the toast.\n             */\n        }\n        else if (target.tagName === 'ION-TOAST') {\n            focusElementInOverlay(lastOverlay.lastFocus, lastOverlay);\n        }\n        else {\n            /**\n             * Otherwise, we are about to have focus\n             * go out of the overlay. We need to wrap\n             * the focus to either the first element\n             * or the last element.\n             */\n            /**\n             * Once we call `focusFirstDescendant` and focus the first\n             * descendant, another focus event will fire which will\n             * cause `lastOverlay.lastFocus` to be updated before\n             * we can run the code after that. We will cache the value\n             * here to avoid that.\n             */\n            const lastFocus = lastOverlay.lastFocus;\n            // Focus the first element in the overlay wrapper\n            focusFirstDescendant(lastOverlay);\n            /**\n             * If the cached last focused element is the\n             * same as the active element, then we need\n             * to wrap focus to the last descendant. This happens\n             * when the first descendant is focused, and the user\n             * presses Shift + Tab. The previous line will focus\n             * the same descendant again (the first one), causing\n             * last focus to equal the active element.\n             */\n            if (lastFocus === doc.activeElement) {\n                focusLastDescendant(lastOverlay);\n            }\n            lastOverlay.lastFocus = doc.activeElement;\n        }\n    };\n    if (lastOverlay.shadowRoot) {\n        trapShadowFocus();\n    }\n    else {\n        trapScopedFocus();\n    }\n};\nconst connectListeners = (doc) => {\n    if (lastOverlayIndex === 0) {\n        lastOverlayIndex = 1;\n        doc.addEventListener('focus', (ev) => {\n            trapKeyboardFocus(ev, doc);\n        }, true);\n        // handle back-button click\n        doc.addEventListener('ionBackButton', (ev) => {\n            const lastOverlay = getPresentedOverlay(doc);\n            if (lastOverlay === null || lastOverlay === void 0 ? void 0 : lastOverlay.backdropDismiss) {\n                ev.detail.register(OVERLAY_BACK_BUTTON_PRIORITY, () => {\n                    /**\n                     * Do not return this promise otherwise\n                     * the hardware back button utility will\n                     * be blocked until the overlay dismisses.\n                     * This is important for a modal with canDismiss.\n                     * If the application presents a confirmation alert\n                     * in the \"canDismiss\" callback, then it will be impossible\n                     * to use the hardware back button to dismiss the alert\n                     * dialog because the hardware back button utility\n                     * is blocked on waiting for the modal to dismiss.\n                     */\n                    lastOverlay.dismiss(undefined, BACKDROP);\n                });\n            }\n        });\n        /**\n         * Handle ESC to close overlay.\n         * CloseWatcher also handles pressing the Esc\n         * key, so if a browser supports CloseWatcher then\n         * this behavior will be handled via the ionBackButton\n         * event.\n         */\n        if (!shouldUseCloseWatcher()) {\n            doc.addEventListener('keydown', (ev) => {\n                if (ev.key === 'Escape') {\n                    const lastOverlay = getPresentedOverlay(doc);\n                    if (lastOverlay === null || lastOverlay === void 0 ? void 0 : lastOverlay.backdropDismiss) {\n                        lastOverlay.dismiss(undefined, BACKDROP);\n                    }\n                }\n            });\n        }\n    }\n};\nconst dismissOverlay = (doc, data, role, overlayTag, id) => {\n    const overlay = getPresentedOverlay(doc, overlayTag, id);\n    if (!overlay) {\n        return Promise.reject('overlay does not exist');\n    }\n    return overlay.dismiss(data, role);\n};\n/**\n * Returns a list of all overlays in the DOM even if they are not presented.\n */\nconst getOverlays = (doc, selector) => {\n    if (selector === undefined) {\n        selector = 'ion-alert,ion-action-sheet,ion-loading,ion-modal,ion-picker-legacy,ion-popover,ion-toast';\n    }\n    return Array.from(doc.querySelectorAll(selector)).filter((c) => c.overlayIndex > 0);\n};\n/**\n * Returns a list of all presented overlays.\n * Inline overlays can exist in the DOM but not be presented,\n * so there are times when we want to exclude those.\n * @param doc The document to find the element within.\n * @param overlayTag The selector for the overlay, defaults to Ionic overlay components.\n */\nconst getPresentedOverlays = (doc, overlayTag) => {\n    return getOverlays(doc, overlayTag).filter((o) => !isOverlayHidden(o));\n};\n/**\n * Returns a presented overlay element.\n * @param doc The document to find the element within.\n * @param overlayTag The selector for the overlay, defaults to Ionic overlay components.\n * @param id The unique identifier for the overlay instance.\n * @returns The overlay element or `undefined` if no overlay element is found.\n */\nconst getPresentedOverlay = (doc, overlayTag, id) => {\n    const overlays = getPresentedOverlays(doc, overlayTag);\n    return id === undefined ? overlays[overlays.length - 1] : overlays.find((o) => o.id === id);\n};\n/**\n * When an overlay is presented, the main\n * focus is the overlay not the page content.\n * We need to remove the page content from the\n * accessibility tree otherwise when\n * users use \"read screen from top\" gestures with\n * TalkBack and VoiceOver, the screen reader will begin\n * to read the content underneath the overlay.\n *\n * We need a container where all page components\n * exist that is separate from where the overlays\n * are added in the DOM. For most apps, this element\n * is the top most ion-router-outlet. In the event\n * that devs are not using a router,\n * they will need to add the \"ion-view-container-root\"\n * id to the element that contains all of their views.\n *\n * TODO: If Framework supports having multiple top\n * level router outlets we would need to update this.\n * Example: One outlet for side menu and one outlet\n * for main content.\n */\nconst setRootAriaHidden = (hidden = false) => {\n    const root = getAppRoot(document);\n    const viewContainer = root.querySelector('ion-router-outlet, ion-nav, #ion-view-container-root');\n    if (!viewContainer) {\n        return;\n    }\n    if (hidden) {\n        viewContainer.setAttribute('aria-hidden', 'true');\n    }\n    else {\n        viewContainer.removeAttribute('aria-hidden');\n    }\n};\nconst present = async (overlay, name, iosEnterAnimation, mdEnterAnimation, opts) => {\n    var _a, _b;\n    if (overlay.presented) {\n        return;\n    }\n    /**\n     * Due to accessibility guidelines, toasts do not have\n     * focus traps.\n     *\n     * All other overlays should have focus traps to prevent\n     * the keyboard focus from leaving the overlay.\n     */\n    if (overlay.el.tagName !== 'ION-TOAST') {\n        setRootAriaHidden(true);\n        document.body.classList.add(BACKDROP_NO_SCROLL);\n    }\n    hideUnderlyingOverlaysFromScreenReaders(overlay.el);\n    hideAnimatingOverlayFromScreenReaders(overlay.el);\n    overlay.presented = true;\n    overlay.willPresent.emit();\n    (_a = overlay.willPresentShorthand) === null || _a === void 0 ? void 0 : _a.emit();\n    const mode = getIonMode(overlay);\n    // get the user's animation fn if one was provided\n    const animationBuilder = overlay.enterAnimation\n        ? overlay.enterAnimation\n        : config.get(name, mode === 'ios' ? iosEnterAnimation : mdEnterAnimation);\n    const completed = await overlayAnimation(overlay, animationBuilder, overlay.el, opts);\n    if (completed) {\n        overlay.didPresent.emit();\n        (_b = overlay.didPresentShorthand) === null || _b === void 0 ? void 0 : _b.emit();\n    }\n    /**\n     * When an overlay that steals focus\n     * is dismissed, focus should be returned\n     * to the element that was focused\n     * prior to the overlay opening. Toast\n     * does not steal focus and is excluded\n     * from returning focus as a result.\n     */\n    if (overlay.el.tagName !== 'ION-TOAST') {\n        restoreElementFocus(overlay.el);\n    }\n    /**\n     * If the focused element is already\n     * inside the overlay component then\n     * focus should not be moved from that\n     * to the overlay container.\n     */\n    if (overlay.keyboardClose && (document.activeElement === null || !overlay.el.contains(document.activeElement))) {\n        overlay.el.focus();\n    }\n    /**\n     * If this overlay was previously dismissed without being\n     * the topmost one (such as by manually calling dismiss()),\n     * it would still have aria-hidden on being presented again.\n     * Removing it here ensures the overlay is visible to screen\n     * readers.\n     *\n     * If this overlay was being presented, then it was hidden\n     * from screen readers during the animation. Now that the\n     * animation is complete, we can reveal the overlay to\n     * screen readers.\n     */\n    overlay.el.removeAttribute('aria-hidden');\n};\n/**\n * When an overlay component is dismissed,\n * focus should be returned to the element\n * that presented the overlay. Otherwise\n * focus will be set on the body which\n * means that people using screen readers\n * or tabbing will need to re-navigate\n * to where they were before they\n * opened the overlay.\n */\nconst restoreElementFocus = async (overlayEl) => {\n    let previousElement = document.activeElement;\n    if (!previousElement) {\n        return;\n    }\n    const shadowRoot = previousElement === null || previousElement === void 0 ? void 0 : previousElement.shadowRoot;\n    if (shadowRoot) {\n        // If there are no inner focusable elements, just focus the host element.\n        previousElement = shadowRoot.querySelector(focusableQueryString) || previousElement;\n    }\n    await overlayEl.onDidDismiss();\n    /**\n     * After onDidDismiss, the overlay loses focus\n     * because it is removed from the document\n     *\n     * > An element will also lose focus [...]\n     * > if the element is removed from the document)\n     *\n     * https://developer.mozilla.org/en-US/docs/Web/API/Element/blur_event\n     *\n     * Additionally, `document.activeElement` returns:\n     *\n     * > The Element which currently has focus,\n     * > `<body>` or null if there is\n     * > no focused element.\n     *\n     * https://developer.mozilla.org/en-US/docs/Web/API/Document/activeElement#value\n     *\n     * However, if the user has already focused\n     * an element sometime between onWillDismiss\n     * and onDidDismiss (for example, focusing a\n     * text box after tapping a button in an\n     * action sheet) then don't restore focus to\n     * previous element\n     */\n    if (document.activeElement === null || document.activeElement === document.body) {\n        previousElement.focus();\n    }\n};\nconst dismiss = async (overlay, data, role, name, iosLeaveAnimation, mdLeaveAnimation, opts) => {\n    var _a, _b;\n    if (!overlay.presented) {\n        return false;\n    }\n    const presentedOverlays = doc !== undefined ? getPresentedOverlays(doc) : [];\n    /**\n     * For accessibility, toasts lack focus traps and don't receive\n     * `aria-hidden` on the root element when presented.\n     *\n     * All other overlays use focus traps to keep keyboard focus\n     * within the overlay, setting `aria-hidden` on the root element\n     * to enhance accessibility.\n     *\n     * Therefore, we must remove `aria-hidden` from the root element\n     * when the last non-toast overlay is dismissed.\n     */\n    const overlaysNotToast = presentedOverlays.filter((o) => o.tagName !== 'ION-TOAST');\n    const lastOverlayNotToast = overlaysNotToast.length === 1 && overlaysNotToast[0].id === overlay.el.id;\n    /**\n     * If this is the last visible overlay that is not a toast\n     * then we want to re-add the root to the accessibility tree.\n     */\n    if (lastOverlayNotToast) {\n        setRootAriaHidden(false);\n        document.body.classList.remove(BACKDROP_NO_SCROLL);\n    }\n    overlay.presented = false;\n    try {\n        /**\n         * There is no need to show the overlay to screen readers during\n         * the dismiss animation. This is because the overlay will be removed\n         * from the DOM after the animation is complete.\n         */\n        hideAnimatingOverlayFromScreenReaders(overlay.el);\n        // Overlay contents should not be clickable during dismiss\n        overlay.el.style.setProperty('pointer-events', 'none');\n        overlay.willDismiss.emit({ data, role });\n        (_a = overlay.willDismissShorthand) === null || _a === void 0 ? void 0 : _a.emit({ data, role });\n        const mode = getIonMode(overlay);\n        const animationBuilder = overlay.leaveAnimation\n            ? overlay.leaveAnimation\n            : config.get(name, mode === 'ios' ? iosLeaveAnimation : mdLeaveAnimation);\n        // If dismissed via gesture, no need to play leaving animation again\n        if (role !== GESTURE) {\n            await overlayAnimation(overlay, animationBuilder, overlay.el, opts);\n        }\n        overlay.didDismiss.emit({ data, role });\n        (_b = overlay.didDismissShorthand) === null || _b === void 0 ? void 0 : _b.emit({ data, role });\n        // Get a reference to all animations currently assigned to this overlay\n        // Then tear them down to return the overlay to its initial visual state\n        const animations = activeAnimations.get(overlay) || [];\n        animations.forEach((ani) => ani.destroy());\n        activeAnimations.delete(overlay);\n        /**\n         * Make overlay hidden again in case it is being reused.\n         * We can safely remove pointer-events: none as\n         * overlay-hidden will set display: none.\n         */\n        overlay.el.classList.add('overlay-hidden');\n        overlay.el.style.removeProperty('pointer-events');\n        /**\n         * Clear any focus trapping references\n         * when the overlay is dismissed.\n         */\n        if (overlay.el.lastFocus !== undefined) {\n            overlay.el.lastFocus = undefined;\n        }\n    }\n    catch (err) {\n        printIonError(`[${overlay.el.tagName.toLowerCase()}] - `, err);\n    }\n    overlay.el.remove();\n    revealOverlaysToScreenReaders();\n    return true;\n};\nconst getAppRoot = (doc) => {\n    return doc.querySelector('ion-app') || doc.body;\n};\nconst overlayAnimation = async (overlay, animationBuilder, baseEl, opts) => {\n    // Make overlay visible in case it's hidden\n    baseEl.classList.remove('overlay-hidden');\n    const aniRoot = overlay.el;\n    const animation = animationBuilder(aniRoot, opts);\n    if (!overlay.animated || !config.getBoolean('animated', true)) {\n        animation.duration(0);\n    }\n    if (overlay.keyboardClose) {\n        animation.beforeAddWrite(() => {\n            const activeElement = baseEl.ownerDocument.activeElement;\n            if (activeElement === null || activeElement === void 0 ? void 0 : activeElement.matches('input,ion-input, ion-textarea')) {\n                activeElement.blur();\n            }\n        });\n    }\n    const activeAni = activeAnimations.get(overlay) || [];\n    activeAnimations.set(overlay, [...activeAni, animation]);\n    await animation.play();\n    return true;\n};\nconst eventMethod = (element, eventName) => {\n    let resolve;\n    const promise = new Promise((r) => (resolve = r));\n    onceEvent(element, eventName, (event) => {\n        resolve(event.detail);\n    });\n    return promise;\n};\nconst onceEvent = (element, eventName, callback) => {\n    const handler = (ev) => {\n        removeEventListener(element, eventName, handler);\n        callback(ev);\n    };\n    addEventListener(element, eventName, handler);\n};\nconst isCancel = (role) => {\n    return role === 'cancel' || role === BACKDROP;\n};\nconst defaultGate = (h) => h();\n/**\n * Calls a developer provided method while avoiding\n * Angular Zones. Since the handler is provided by\n * the developer, we should throw any errors\n * received so that developer-provided bug\n * tracking software can log it.\n */\nconst safeCall = (handler, arg) => {\n    if (typeof handler === 'function') {\n        const jmp = config.get('_zoneGate', defaultGate);\n        return jmp(() => {\n            try {\n                return handler(arg);\n            }\n            catch (e) {\n                throw e;\n            }\n        });\n    }\n    return undefined;\n};\nconst BACKDROP = 'backdrop';\nconst GESTURE = 'gesture';\nconst OVERLAY_GESTURE_PRIORITY = 39;\n/**\n * Creates a delegate controller.\n *\n * Requires that the component has the following properties:\n * - `el: HTMLElement`\n * - `hasController: boolean`\n * - `delegate?: FrameworkDelegate`\n *\n * @param ref The component class instance.\n */\nconst createDelegateController = (ref) => {\n    let inline = false;\n    let workingDelegate;\n    const coreDelegate = CoreDelegate();\n    /**\n     * Determines whether or not an overlay is being used\n     * inline or via a controller/JS and returns the correct delegate.\n     * By default, subsequent calls to getDelegate will use\n     * a cached version of the delegate.\n     * This is useful for calling dismiss after present,\n     * so that the correct delegate is given.\n     * @param force `true` to force the non-cached version of the delegate.\n     * @returns The delegate to use and whether or not the overlay is inline.\n     */\n    const getDelegate = (force = false) => {\n        if (workingDelegate && !force) {\n            return {\n                delegate: workingDelegate,\n                inline,\n            };\n        }\n        const { el, hasController, delegate } = ref;\n        /**\n         * If using overlay inline\n         * we potentially need to use the coreDelegate\n         * so that this works in vanilla JS apps.\n         * If a developer has presented this component\n         * via a controller, then we can assume\n         * the component is already in the\n         * correct place.\n         */\n        const parentEl = el.parentNode;\n        inline = parentEl !== null && !hasController;\n        workingDelegate = inline ? delegate || coreDelegate : delegate;\n        return { inline, delegate: workingDelegate };\n    };\n    /**\n     * Attaches a component in the DOM. Teleports the component\n     * to the root of the app.\n     * @param component The component to optionally construct and append to the element.\n     */\n    const attachViewToDom = async (component) => {\n        const { delegate } = getDelegate(true);\n        if (delegate) {\n            return await delegate.attachViewToDom(ref.el, component);\n        }\n        const { hasController } = ref;\n        if (hasController && component !== undefined) {\n            throw new Error('framework delegate is missing');\n        }\n        return null;\n    };\n    /**\n     * Moves a component back to its original location in the DOM.\n     */\n    const removeViewFromDom = () => {\n        const { delegate } = getDelegate();\n        if (delegate && ref.el !== undefined) {\n            delegate.removeViewFromDom(ref.el.parentElement, ref.el);\n        }\n    };\n    return {\n        attachViewToDom,\n        removeViewFromDom,\n    };\n};\n/**\n * Constructs a trigger interaction for an overlay.\n * Presents an overlay when the trigger is clicked.\n *\n * Usage:\n * ```ts\n * triggerController = createTriggerController();\n * triggerController.addClickListener(el, trigger);\n * ```\n */\nconst createTriggerController = () => {\n    let destroyTriggerInteraction;\n    /**\n     * Removes the click listener from the trigger element.\n     */\n    const removeClickListener = () => {\n        if (destroyTriggerInteraction) {\n            destroyTriggerInteraction();\n            destroyTriggerInteraction = undefined;\n        }\n    };\n    /**\n     * Adds a click listener to the trigger element.\n     * Presents the overlay when the trigger is clicked.\n     * @param el The overlay element.\n     * @param trigger The ID of the element to add a click listener to.\n     */\n    const addClickListener = (el, trigger) => {\n        removeClickListener();\n        const triggerEl = trigger !== undefined ? document.getElementById(trigger) : null;\n        if (!triggerEl) {\n            printIonWarning(`[${el.tagName.toLowerCase()}] - A trigger element with the ID \"${trigger}\" was not found in the DOM. The trigger element must be in the DOM when the \"trigger\" property is set on an overlay component.`, el);\n            return;\n        }\n        const configureTriggerInteraction = (targetEl, overlayEl) => {\n            const openOverlay = () => {\n                overlayEl.present();\n            };\n            targetEl.addEventListener('click', openOverlay);\n            return () => {\n                targetEl.removeEventListener('click', openOverlay);\n            };\n        };\n        destroyTriggerInteraction = configureTriggerInteraction(triggerEl, el);\n    };\n    return {\n        addClickListener,\n        removeClickListener,\n    };\n};\n/**\n * The overlay that is being animated also needs to hide from screen\n * readers during its animation. This ensures that assistive technologies\n * like TalkBack do not announce or interact with the content until the\n * animation is complete, avoiding confusion for users.\n *\n * When the overlay is presented on an Android device, TalkBack's focus rings\n * may appear in the wrong position due to the transition (specifically\n * `transform` styles). This occurs because the focus rings are initially\n * displayed at the starting position of the elements before the transition\n * begins. This workaround ensures the focus rings do not appear in the\n * incorrect location.\n *\n * If this solution is applied to iOS devices, then it leads to a bug where\n * the overlays cannot be accessed by screen readers. This is due to\n * VoiceOver not being able to update the accessibility tree when the\n * `aria-hidden` is removed.\n *\n * @param overlay - The overlay that is being animated.\n */\nconst hideAnimatingOverlayFromScreenReaders = (overlay) => {\n    if (doc === undefined)\n        return;\n    if (isPlatform('android')) {\n        /**\n         * Once the animation is complete, this attribute will be removed.\n         * This is done at the end of the `present` method.\n         */\n        overlay.setAttribute('aria-hidden', 'true');\n    }\n};\n/**\n * Ensure that underlying overlays have aria-hidden if necessary so that screen readers\n * cannot move focus to these elements. Note that we cannot rely on focus/focusin/focusout\n * events here because those events do not fire when the screen readers moves to a non-focusable\n * element such as text.\n * Without this logic screen readers would be able to move focus outside of the top focus-trapped overlay.\n *\n * @param newTopMostOverlay - The overlay that is being presented. Since the overlay has not been\n * fully presented yet at the time this function is called it will not be included in the getPresentedOverlays result.\n */\nconst hideUnderlyingOverlaysFromScreenReaders = (newTopMostOverlay) => {\n    var _a;\n    if (doc === undefined)\n        return;\n    const overlays = getPresentedOverlays(doc);\n    for (let i = overlays.length - 1; i >= 0; i--) {\n        const presentedOverlay = overlays[i];\n        const nextPresentedOverlay = (_a = overlays[i + 1]) !== null && _a !== void 0 ? _a : newTopMostOverlay;\n        /**\n         * If next overlay has aria-hidden then all remaining overlays will have it too.\n         * Or, if the next overlay is a Toast that does not have aria-hidden then current overlay\n         * should not have aria-hidden either so focus can remain in the current overlay.\n         */\n        if (nextPresentedOverlay.hasAttribute('aria-hidden') || nextPresentedOverlay.tagName !== 'ION-TOAST') {\n            presentedOverlay.setAttribute('aria-hidden', 'true');\n        }\n    }\n};\n/**\n * When dismissing an overlay we need to reveal the new top-most overlay to screen readers.\n * If the top-most overlay is a Toast we potentially need to reveal more overlays since\n * focus is never automatically moved to the Toast.\n */\nconst revealOverlaysToScreenReaders = () => {\n    if (doc === undefined)\n        return;\n    const overlays = getPresentedOverlays(doc);\n    for (let i = overlays.length - 1; i >= 0; i--) {\n        const currentOverlay = overlays[i];\n        /**\n         * If the current we are looking at is a Toast then we can remove aria-hidden.\n         * However, we potentially need to keep looking at the overlay stack because there\n         * could be more Toasts underneath. Additionally, we need to unhide the closest non-Toast\n         * overlay too so focus can move there since focus is never automatically moved to the Toast.\n         */\n        currentOverlay.removeAttribute('aria-hidden');\n        /**\n         * If we found a non-Toast element then we can just remove aria-hidden and stop searching entirely\n         * since this overlay should always receive focus. As a result, all underlying overlays should still\n         * be hidden from screen readers.\n         */\n        if (currentOverlay.tagName !== 'ION-TOAST') {\n            break;\n        }\n    }\n};\nconst FOCUS_TRAP_DISABLE_CLASS = 'ion-disable-focus-trap';\n\nexport { BACKDROP as B, FOCUS_TRAP_DISABLE_CLASS as F, GESTURE as G, OVERLAY_GESTURE_PRIORITY as O, alertController as a, actionSheetController as b, popoverController as c, createDelegateController as d, createTriggerController as e, present as f, dismiss as g, eventMethod as h, isCancel as i, prepareOverlay as j, setOverlayId as k, loadingController as l, modalController as m, focusFirstDescendant as n, getPresentedOverlay as o, pickerController as p, focusLastDescendant as q, safeCall as s, toastController as t };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAsBA,IAAM,uBAAuB;AAW7B,IAAM,uBAAuB,CAAC,KAAK,oBAAoB;AACnD,QAAM,aAAa,IAAI,cAAc,oBAAoB;AACzD,wBAAsB,YAAY,oBAAoB,QAAQ,oBAAoB,SAAS,kBAAkB,GAAG;AACpH;AAWA,IAAM,sBAAsB,CAAC,KAAK,oBAAoB;AAClD,QAAM,SAAS,MAAM,KAAK,IAAI,iBAAiB,oBAAoB,CAAC;AACpE,QAAM,YAAY,OAAO,SAAS,IAAI,OAAO,OAAO,SAAS,CAAC,IAAI;AAClE,wBAAsB,WAAW,oBAAoB,QAAQ,oBAAoB,SAAS,kBAAkB,GAAG;AACnH;AAkBA,IAAM,wBAAwB,CAAC,aAAa,oBAAoB;AAC5D,MAAI,iBAAiB;AACrB,QAAM,aAAa,gBAAgB,QAAQ,gBAAgB,SAAS,SAAS,YAAY;AACzF,MAAI,YAAY;AAEZ,qBAAiB,WAAW,cAAc,oBAAoB,KAAK;AAAA,EACvE;AACA,MAAI,gBAAgB;AAChB,UAAM,aAAa,eAAe,QAAQ,iBAAiB;AAC3D,QAAI,YAAY;AACZ,iBAAW,SAAS;AAAA,IACxB,OACK;AACD,0BAAoB,cAAc;AAAA,IACtC;AAAA,EACJ,OACK;AAED,oBAAgB,MAAM;AAAA,EAC1B;AACJ;AAEA,IAAI,mBAAmB;AACvB,IAAI,SAAS;AACb,IAAM,mBAAmB,oBAAI,QAAQ;AACrC,IAAM,mBAAmB,CAAC,YAAY;AAClC,SAAO;AAAA,IACH,OAAO,SAAS;AACZ,aAAO,cAAc,SAAS,OAAO;AAAA,IACzC;AAAA,IACA,QAAQ,MAAM,MAAM,IAAI;AACpB,aAAO,eAAe,UAAU,MAAM,MAAM,SAAS,EAAE;AAAA,IAC3D;AAAA,IACM,SAAS;AAAA;AACX,eAAO,oBAAoB,UAAU,OAAO;AAAA,MAChD;AAAA;AAAA,EACJ;AACJ;AACA,IAAM,kBAAgC,iBAAiB,WAAW;AAClE,IAAM,wBAAsC,iBAAiB,kBAAkB;AAC/E,IAAM,oBAAkC,iBAAiB,aAAa;AACtE,IAAM,kBAAgC,iBAAiB,WAAW;AAIlE,IAAM,mBAAiC,iBAAiB,mBAAmB;AAC3E,IAAM,oBAAkC,iBAAiB,aAAa;AACtE,IAAM,kBAAgC,iBAAiB,WAAW;AAIlE,IAAM,iBAAiB,CAAC,OAAO;AAC3B,MAAI,OAAO,aAAa,aAAa;AAUjC,qBAAiB,QAAQ;AAAA,EAC7B;AACA,QAAM,eAAe;AAMrB,KAAG,eAAe;AACtB;AAOA,IAAM,eAAe,CAAC,OAAO;AACzB,MAAI,CAAC,GAAG,aAAa,IAAI,GAAG;AACxB,OAAG,KAAK,eAAe,EAAE,MAAM;AAAA,EACnC;AACA,SAAO,GAAG;AACd;AACA,IAAM,gBAAgB,CAAC,SAAS,SAAS;AAErC,MAAI,OAAO,WAAW,eAAe,OAAO,OAAO,mBAAmB,aAAa;AAC/E,WAAO,OAAO,eAAe,YAAY,OAAO,EAAE,KAAK,MAAM;AACzD,YAAM,UAAU,SAAS,cAAc,OAAO;AAC9C,cAAQ,UAAU,IAAI,gBAAgB;AAKtC,aAAO,OAAO,SAAS,OAAO,OAAO,OAAO,OAAO,CAAC,GAAG,IAAI,GAAG,EAAE,eAAe,KAAK,CAAC,CAAC;AAEtF,iBAAW,QAAQ,EAAE,YAAY,OAAO;AACxC,aAAO,IAAI,QAAQ,CAAC,YAAY,iBAAiB,SAAS,OAAO,CAAC;AAAA,IACtE,CAAC;AAAA,EACL;AACA,SAAO,QAAQ,QAAQ;AAC3B;AACA,IAAM,kBAAkB,CAAC,YAAY,QAAQ,UAAU,SAAS,gBAAgB;AAchF,IAAM,wBAAwB,CAAC,aAAa,YAAY;AACpD,MAAI,iBAAiB;AACrB,QAAM,aAAa,gBAAgB,QAAQ,gBAAgB,SAAS,SAAS,YAAY;AACzF,MAAI,YAAY;AAEZ,qBAAiB,WAAW,cAAc,oBAAoB,KAAK;AAAA,EACvE;AACA,MAAI,gBAAgB;AAChB,wBAAoB,cAAc;AAAA,EACtC,OACK;AAED,YAAQ,MAAM;AAAA,EAClB;AACJ;AAQA,IAAM,oBAAoB,CAAC,IAAIA,SAAQ;AACnC,QAAM,cAAc,oBAAoBA,MAAK,gFAAgF;AAC7H,QAAM,SAAS,GAAG;AAWlB,MAAI,CAAC,eAAe,CAAC,QAAQ;AACzB;AAAA,EACJ;AAUA,MAAI,YAAY,UAAU,SAAS,wBAAwB,GAAG;AAC1D;AAAA,EACJ;AACA,QAAM,kBAAkB,MAAM;AAO1B,QAAI,gBAAgB,QAAQ;AACxB,kBAAY,YAAY;AAAA,IAa5B,WACS,OAAO,YAAY,aAAa;AACrC,4BAAsB,YAAY,WAAW,WAAW;AAAA,IAS5D,OACK;AAKD,YAAM,cAAc,eAAe,WAAW;AAC9C,UAAI,CAAC,YAAY,SAAS,MAAM,GAAG;AAC/B;AAAA,MACJ;AACA,YAAM,iBAAiB,YAAY,cAAc,sBAAsB;AACvE,UAAI,CAAC,gBAAgB;AACjB;AAAA,MACJ;AAQA,UAAI,eAAe,SAAS,MAAM,KAAK,WAAW,YAAY,cAAc,cAAc,GAAG;AACzF,oBAAY,YAAY;AAAA,MAC5B,OACK;AAaD,cAAM,YAAY,YAAY;AAE9B,6BAAqB,gBAAgB,WAAW;AAUhD,YAAI,cAAcA,KAAI,eAAe;AACjC,8BAAoB,gBAAgB,WAAW;AAAA,QACnD;AACA,oBAAY,YAAYA,KAAI;AAAA,MAChC;AAAA,IACJ;AAAA,EACJ;AACA,QAAM,kBAAkB,MAAM;AAK1B,QAAI,YAAY,SAAS,MAAM,GAAG;AAC9B,kBAAY,YAAY;AAAA,IAa5B,WACS,OAAO,YAAY,aAAa;AACrC,4BAAsB,YAAY,WAAW,WAAW;AAAA,IAC5D,OACK;AAcD,YAAM,YAAY,YAAY;AAE9B,2BAAqB,WAAW;AAUhC,UAAI,cAAcA,KAAI,eAAe;AACjC,4BAAoB,WAAW;AAAA,MACnC;AACA,kBAAY,YAAYA,KAAI;AAAA,IAChC;AAAA,EACJ;AACA,MAAI,YAAY,YAAY;AACxB,oBAAgB;AAAA,EACpB,OACK;AACD,oBAAgB;AAAA,EACpB;AACJ;AACA,IAAM,mBAAmB,CAACA,SAAQ;AAC9B,MAAI,qBAAqB,GAAG;AACxB,uBAAmB;AACnB,IAAAA,KAAI,iBAAiB,SAAS,CAAC,OAAO;AAClC,wBAAkB,IAAIA,IAAG;AAAA,IAC7B,GAAG,IAAI;AAEP,IAAAA,KAAI,iBAAiB,iBAAiB,CAAC,OAAO;AAC1C,YAAM,cAAc,oBAAoBA,IAAG;AAC3C,UAAI,gBAAgB,QAAQ,gBAAgB,SAAS,SAAS,YAAY,iBAAiB;AACvF,WAAG,OAAO,SAAS,8BAA8B,MAAM;AAYnD,sBAAY,QAAQ,QAAW,QAAQ;AAAA,QAC3C,CAAC;AAAA,MACL;AAAA,IACJ,CAAC;AAQD,QAAI,CAAC,sBAAsB,GAAG;AAC1B,MAAAA,KAAI,iBAAiB,WAAW,CAAC,OAAO;AACpC,YAAI,GAAG,QAAQ,UAAU;AACrB,gBAAM,cAAc,oBAAoBA,IAAG;AAC3C,cAAI,gBAAgB,QAAQ,gBAAgB,SAAS,SAAS,YAAY,iBAAiB;AACvF,wBAAY,QAAQ,QAAW,QAAQ;AAAA,UAC3C;AAAA,QACJ;AAAA,MACJ,CAAC;AAAA,IACL;AAAA,EACJ;AACJ;AACA,IAAM,iBAAiB,CAACA,MAAK,MAAM,MAAM,YAAY,OAAO;AACxD,QAAM,UAAU,oBAAoBA,MAAK,YAAY,EAAE;AACvD,MAAI,CAAC,SAAS;AACV,WAAO,QAAQ,OAAO,wBAAwB;AAAA,EAClD;AACA,SAAO,QAAQ,QAAQ,MAAM,IAAI;AACrC;AAIA,IAAM,cAAc,CAACA,MAAK,aAAa;AACnC,MAAI,aAAa,QAAW;AACxB,eAAW;AAAA,EACf;AACA,SAAO,MAAM,KAAKA,KAAI,iBAAiB,QAAQ,CAAC,EAAE,OAAO,CAAC,MAAM,EAAE,eAAe,CAAC;AACtF;AAQA,IAAM,uBAAuB,CAACA,MAAK,eAAe;AAC9C,SAAO,YAAYA,MAAK,UAAU,EAAE,OAAO,CAAC,MAAM,CAAC,gBAAgB,CAAC,CAAC;AACzE;AAQA,IAAM,sBAAsB,CAACA,MAAK,YAAY,OAAO;AACjD,QAAM,WAAW,qBAAqBA,MAAK,UAAU;AACrD,SAAO,OAAO,SAAY,SAAS,SAAS,SAAS,CAAC,IAAI,SAAS,KAAK,CAAC,MAAM,EAAE,OAAO,EAAE;AAC9F;AAuBA,IAAM,oBAAoB,CAAC,SAAS,UAAU;AAC1C,QAAM,OAAO,WAAW,QAAQ;AAChC,QAAM,gBAAgB,KAAK,cAAc,sDAAsD;AAC/F,MAAI,CAAC,eAAe;AAChB;AAAA,EACJ;AACA,MAAI,QAAQ;AACR,kBAAc,aAAa,eAAe,MAAM;AAAA,EACpD,OACK;AACD,kBAAc,gBAAgB,aAAa;AAAA,EAC/C;AACJ;AACA,IAAM,UAAU,CAAO,SAAS,MAAM,mBAAmB,kBAAkB,SAAS;AAChF,MAAI,IAAI;AACR,MAAI,QAAQ,WAAW;AACnB;AAAA,EACJ;AAQA,MAAI,QAAQ,GAAG,YAAY,aAAa;AACpC,sBAAkB,IAAI;AACtB,aAAS,KAAK,UAAU,IAAI,kBAAkB;AAAA,EAClD;AACA,0CAAwC,QAAQ,EAAE;AAClD,wCAAsC,QAAQ,EAAE;AAChD,UAAQ,YAAY;AACpB,UAAQ,YAAY,KAAK;AACzB,GAAC,KAAK,QAAQ,0BAA0B,QAAQ,OAAO,SAAS,SAAS,GAAG,KAAK;AACjF,QAAM,OAAO,WAAW,OAAO;AAE/B,QAAM,mBAAmB,QAAQ,iBAC3B,QAAQ,iBACR,OAAO,IAAI,MAAM,SAAS,QAAQ,oBAAoB,gBAAgB;AAC5E,QAAM,YAAY,MAAM,iBAAiB,SAAS,kBAAkB,QAAQ,IAAI,IAAI;AACpF,MAAI,WAAW;AACX,YAAQ,WAAW,KAAK;AACxB,KAAC,KAAK,QAAQ,yBAAyB,QAAQ,OAAO,SAAS,SAAS,GAAG,KAAK;AAAA,EACpF;AASA,MAAI,QAAQ,GAAG,YAAY,aAAa;AACpC,wBAAoB,QAAQ,EAAE;AAAA,EAClC;AAOA,MAAI,QAAQ,kBAAkB,SAAS,kBAAkB,QAAQ,CAAC,QAAQ,GAAG,SAAS,SAAS,aAAa,IAAI;AAC5G,YAAQ,GAAG,MAAM;AAAA,EACrB;AAaA,UAAQ,GAAG,gBAAgB,aAAa;AAC5C;AAWA,IAAM,sBAAsB,CAAO,cAAc;AAC7C,MAAI,kBAAkB,SAAS;AAC/B,MAAI,CAAC,iBAAiB;AAClB;AAAA,EACJ;AACA,QAAM,aAAa,oBAAoB,QAAQ,oBAAoB,SAAS,SAAS,gBAAgB;AACrG,MAAI,YAAY;AAEZ,sBAAkB,WAAW,cAAc,oBAAoB,KAAK;AAAA,EACxE;AACA,QAAM,UAAU,aAAa;AAyB7B,MAAI,SAAS,kBAAkB,QAAQ,SAAS,kBAAkB,SAAS,MAAM;AAC7E,oBAAgB,MAAM;AAAA,EAC1B;AACJ;AACA,IAAM,UAAU,CAAO,SAAS,MAAM,MAAM,MAAM,mBAAmB,kBAAkB,SAAS;AAC5F,MAAI,IAAI;AACR,MAAI,CAAC,QAAQ,WAAW;AACpB,WAAO;AAAA,EACX;AACA,QAAM,oBAAoB,QAAQ,SAAY,qBAAqB,GAAG,IAAI,CAAC;AAY3E,QAAM,mBAAmB,kBAAkB,OAAO,CAAC,MAAM,EAAE,YAAY,WAAW;AAClF,QAAM,sBAAsB,iBAAiB,WAAW,KAAK,iBAAiB,CAAC,EAAE,OAAO,QAAQ,GAAG;AAKnG,MAAI,qBAAqB;AACrB,sBAAkB,KAAK;AACvB,aAAS,KAAK,UAAU,OAAO,kBAAkB;AAAA,EACrD;AACA,UAAQ,YAAY;AACpB,MAAI;AAMA,0CAAsC,QAAQ,EAAE;AAEhD,YAAQ,GAAG,MAAM,YAAY,kBAAkB,MAAM;AACrD,YAAQ,YAAY,KAAK,EAAE,MAAM,KAAK,CAAC;AACvC,KAAC,KAAK,QAAQ,0BAA0B,QAAQ,OAAO,SAAS,SAAS,GAAG,KAAK,EAAE,MAAM,KAAK,CAAC;AAC/F,UAAM,OAAO,WAAW,OAAO;AAC/B,UAAM,mBAAmB,QAAQ,iBAC3B,QAAQ,iBACR,OAAO,IAAI,MAAM,SAAS,QAAQ,oBAAoB,gBAAgB;AAE5E,QAAI,SAAS,SAAS;AAClB,YAAM,iBAAiB,SAAS,kBAAkB,QAAQ,IAAI,IAAI;AAAA,IACtE;AACA,YAAQ,WAAW,KAAK,EAAE,MAAM,KAAK,CAAC;AACtC,KAAC,KAAK,QAAQ,yBAAyB,QAAQ,OAAO,SAAS,SAAS,GAAG,KAAK,EAAE,MAAM,KAAK,CAAC;AAG9F,UAAM,aAAa,iBAAiB,IAAI,OAAO,KAAK,CAAC;AACrD,eAAW,QAAQ,CAAC,QAAQ,IAAI,QAAQ,CAAC;AACzC,qBAAiB,OAAO,OAAO;AAM/B,YAAQ,GAAG,UAAU,IAAI,gBAAgB;AACzC,YAAQ,GAAG,MAAM,eAAe,gBAAgB;AAKhD,QAAI,QAAQ,GAAG,cAAc,QAAW;AACpC,cAAQ,GAAG,YAAY;AAAA,IAC3B;AAAA,EACJ,SACO,KAAK;AACR,kBAAc,IAAI,QAAQ,GAAG,QAAQ,YAAY,CAAC,QAAQ,GAAG;AAAA,EACjE;AACA,UAAQ,GAAG,OAAO;AAClB,gCAA8B;AAC9B,SAAO;AACX;AACA,IAAM,aAAa,CAACA,SAAQ;AACxB,SAAOA,KAAI,cAAc,SAAS,KAAKA,KAAI;AAC/C;AACA,IAAM,mBAAmB,CAAO,SAAS,kBAAkB,QAAQ,SAAS;AAExE,SAAO,UAAU,OAAO,gBAAgB;AACxC,QAAM,UAAU,QAAQ;AACxB,QAAM,YAAY,iBAAiB,SAAS,IAAI;AAChD,MAAI,CAAC,QAAQ,YAAY,CAAC,OAAO,WAAW,YAAY,IAAI,GAAG;AAC3D,cAAU,SAAS,CAAC;AAAA,EACxB;AACA,MAAI,QAAQ,eAAe;AACvB,cAAU,eAAe,MAAM;AAC3B,YAAM,gBAAgB,OAAO,cAAc;AAC3C,UAAI,kBAAkB,QAAQ,kBAAkB,SAAS,SAAS,cAAc,QAAQ,+BAA+B,GAAG;AACtH,sBAAc,KAAK;AAAA,MACvB;AAAA,IACJ,CAAC;AAAA,EACL;AACA,QAAM,YAAY,iBAAiB,IAAI,OAAO,KAAK,CAAC;AACpD,mBAAiB,IAAI,SAAS,CAAC,GAAG,WAAW,SAAS,CAAC;AACvD,QAAM,UAAU,KAAK;AACrB,SAAO;AACX;AACA,IAAM,cAAc,CAAC,SAAS,cAAc;AACxC,MAAI;AACJ,QAAM,UAAU,IAAI,QAAQ,CAAC,MAAO,UAAU,CAAE;AAChD,YAAU,SAAS,WAAW,CAAC,UAAU;AACrC,YAAQ,MAAM,MAAM;AAAA,EACxB,CAAC;AACD,SAAO;AACX;AACA,IAAM,YAAY,CAAC,SAAS,WAAW,aAAa;AAChD,QAAM,UAAU,CAAC,OAAO;AACpB,wBAAoB,SAAS,WAAW,OAAO;AAC/C,aAAS,EAAE;AAAA,EACf;AACA,mBAAiB,SAAS,WAAW,OAAO;AAChD;AACA,IAAM,WAAW,CAAC,SAAS;AACvB,SAAO,SAAS,YAAY,SAAS;AACzC;AACA,IAAM,cAAc,CAAC,MAAM,EAAE;AAQ7B,IAAM,WAAW,CAAC,SAAS,QAAQ;AAC/B,MAAI,OAAO,YAAY,YAAY;AAC/B,UAAM,MAAM,OAAO,IAAI,aAAa,WAAW;AAC/C,WAAO,IAAI,MAAM;AACb,UAAI;AACA,eAAO,QAAQ,GAAG;AAAA,MACtB,SACO,GAAG;AACN,cAAM;AAAA,MACV;AAAA,IACJ,CAAC;AAAA,EACL;AACA,SAAO;AACX;AACA,IAAM,WAAW;AACjB,IAAM,UAAU;AAChB,IAAM,2BAA2B;AAWjC,IAAM,2BAA2B,CAAC,QAAQ;AACtC,MAAI,SAAS;AACb,MAAI;AACJ,QAAM,eAAe,aAAa;AAWlC,QAAM,cAAc,CAAC,QAAQ,UAAU;AACnC,QAAI,mBAAmB,CAAC,OAAO;AAC3B,aAAO;AAAA,QACH,UAAU;AAAA,QACV;AAAA,MACJ;AAAA,IACJ;AACA,UAAM,EAAE,IAAI,eAAe,SAAS,IAAI;AAUxC,UAAM,WAAW,GAAG;AACpB,aAAS,aAAa,QAAQ,CAAC;AAC/B,sBAAkB,SAAS,YAAY,eAAe;AACtD,WAAO,EAAE,QAAQ,UAAU,gBAAgB;AAAA,EAC/C;AAMA,QAAM,kBAAkB,CAAO,cAAc;AACzC,UAAM,EAAE,SAAS,IAAI,YAAY,IAAI;AACrC,QAAI,UAAU;AACV,aAAO,MAAM,SAAS,gBAAgB,IAAI,IAAI,SAAS;AAAA,IAC3D;AACA,UAAM,EAAE,cAAc,IAAI;AAC1B,QAAI,iBAAiB,cAAc,QAAW;AAC1C,YAAM,IAAI,MAAM,+BAA+B;AAAA,IACnD;AACA,WAAO;AAAA,EACX;AAIA,QAAM,oBAAoB,MAAM;AAC5B,UAAM,EAAE,SAAS,IAAI,YAAY;AACjC,QAAI,YAAY,IAAI,OAAO,QAAW;AAClC,eAAS,kBAAkB,IAAI,GAAG,eAAe,IAAI,EAAE;AAAA,IAC3D;AAAA,EACJ;AACA,SAAO;AAAA,IACH;AAAA,IACA;AAAA,EACJ;AACJ;AAWA,IAAM,0BAA0B,MAAM;AAClC,MAAI;AAIJ,QAAM,sBAAsB,MAAM;AAC9B,QAAI,2BAA2B;AAC3B,gCAA0B;AAC1B,kCAA4B;AAAA,IAChC;AAAA,EACJ;AAOA,QAAM,mBAAmB,CAAC,IAAI,YAAY;AACtC,wBAAoB;AACpB,UAAM,YAAY,YAAY,SAAY,SAAS,eAAe,OAAO,IAAI;AAC7E,QAAI,CAAC,WAAW;AACZ,sBAAgB,IAAI,GAAG,QAAQ,YAAY,CAAC,sCAAsC,OAAO,kIAAkI,EAAE;AAC7N;AAAA,IACJ;AACA,UAAM,8BAA8B,CAAC,UAAU,cAAc;AACzD,YAAM,cAAc,MAAM;AACtB,kBAAU,QAAQ;AAAA,MACtB;AACA,eAAS,iBAAiB,SAAS,WAAW;AAC9C,aAAO,MAAM;AACT,iBAAS,oBAAoB,SAAS,WAAW;AAAA,MACrD;AAAA,IACJ;AACA,gCAA4B,4BAA4B,WAAW,EAAE;AAAA,EACzE;AACA,SAAO;AAAA,IACH;AAAA,IACA;AAAA,EACJ;AACJ;AAqBA,IAAM,wCAAwC,CAAC,YAAY;AACvD,MAAI,QAAQ;AACR;AACJ,MAAI,WAAW,SAAS,GAAG;AAKvB,YAAQ,aAAa,eAAe,MAAM;AAAA,EAC9C;AACJ;AAWA,IAAM,0CAA0C,CAAC,sBAAsB;AACnE,MAAI;AACJ,MAAI,QAAQ;AACR;AACJ,QAAM,WAAW,qBAAqB,GAAG;AACzC,WAAS,IAAI,SAAS,SAAS,GAAG,KAAK,GAAG,KAAK;AAC3C,UAAM,mBAAmB,SAAS,CAAC;AACnC,UAAM,wBAAwB,KAAK,SAAS,IAAI,CAAC,OAAO,QAAQ,OAAO,SAAS,KAAK;AAMrF,QAAI,qBAAqB,aAAa,aAAa,KAAK,qBAAqB,YAAY,aAAa;AAClG,uBAAiB,aAAa,eAAe,MAAM;AAAA,IACvD;AAAA,EACJ;AACJ;AAMA,IAAM,gCAAgC,MAAM;AACxC,MAAI,QAAQ;AACR;AACJ,QAAM,WAAW,qBAAqB,GAAG;AACzC,WAAS,IAAI,SAAS,SAAS,GAAG,KAAK,GAAG,KAAK;AAC3C,UAAM,iBAAiB,SAAS,CAAC;AAOjC,mBAAe,gBAAgB,aAAa;AAM5C,QAAI,eAAe,YAAY,aAAa;AACxC;AAAA,IACJ;AAAA,EACJ;AACJ;AACA,IAAM,2BAA2B;", "names": ["doc"]}