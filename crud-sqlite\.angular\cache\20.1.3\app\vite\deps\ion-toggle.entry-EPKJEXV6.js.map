{"version": 3, "sources": ["../../../../../../node_modules/@ionic/core/dist/esm/ion-toggle.entry.js"], "sourcesContent": ["/*!\n * (C) Ionic http://ionicframework.com - MIT License\n */\nimport { r as registerInstance, d as createEvent, l as config, a as isPlatform, h, e as getIonMode, j as Host, k as getElement } from './index-B_U9CtaY.js';\nimport { i as inheritAriaAttributes, a as renderHiddenInput } from './helpers-1O4D2b7y.js';\nimport { c as hapticSelection } from './haptic-DzAMWJuk.js';\nimport { i as isRTL } from './dir-C53feagD.js';\nimport { c as createColorClasses, h as hostContext } from './theme-DiVJyqlX.js';\nimport { f as checkmarkOutline, r as removeOutline, g as ellipseOutline } from './index-BLV6ykCk.js';\nimport './capacitor-CFERIeaU.js';\nimport './index-ZjP4CjeZ.js';\n\nconst toggleIosCss = \":host{-webkit-box-sizing:content-box !important;box-sizing:content-box !important;display:inline-block;position:relative;max-width:100%;cursor:pointer;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none;z-index:2}:host(.in-item){-ms-flex:1 1 0px;flex:1 1 0;width:100%;height:100%}:host([slot=start]),:host([slot=end]){-ms-flex:initial;flex:initial;width:auto}:host(.ion-focused) input{border:2px solid #5e9ed6}:host(.toggle-disabled){pointer-events:none}input{display:none}.toggle-wrapper{display:-ms-flexbox;display:flex;position:relative;-ms-flex-positive:1;flex-grow:1;-ms-flex-align:center;align-items:center;-ms-flex-pack:justify;justify-content:space-between;height:inherit;-webkit-transition:background-color 15ms linear;transition:background-color 15ms linear;cursor:inherit}.label-text-wrapper{text-overflow:ellipsis;white-space:nowrap;overflow:hidden}:host(.in-item) .label-text-wrapper{margin-top:10px;margin-bottom:10px}:host(.in-item.toggle-label-placement-stacked) .label-text-wrapper{margin-top:10px;margin-bottom:16px}:host(.in-item.toggle-label-placement-stacked) .native-wrapper{margin-bottom:10px}.label-text-wrapper-hidden{display:none}.native-wrapper{display:-ms-flexbox;display:flex;-ms-flex-align:center;align-items:center}.toggle-bottom{padding-top:4px;display:-ms-flexbox;display:flex;-ms-flex-pack:justify;justify-content:space-between;font-size:0.75rem;white-space:normal}:host(.toggle-label-placement-stacked) .toggle-bottom{font-size:1rem}.toggle-bottom .error-text{display:none;color:var(--ion-color-danger, #c5000f)}.toggle-bottom .helper-text{display:block;color:var(--ion-color-step-700, var(--ion-text-color-step-300, #4d4d4d))}:host(.ion-touched.ion-invalid) .toggle-bottom .error-text{display:block}:host(.ion-touched.ion-invalid) .toggle-bottom .helper-text{display:none}:host(.toggle-label-placement-start) .toggle-wrapper{-ms-flex-direction:row;flex-direction:row}:host(.toggle-label-placement-start) .label-text-wrapper{-webkit-margin-start:0;margin-inline-start:0;-webkit-margin-end:16px;margin-inline-end:16px}:host(.toggle-label-placement-end) .toggle-wrapper{-ms-flex-direction:row-reverse;flex-direction:row-reverse;-ms-flex-pack:start;justify-content:start}:host(.toggle-label-placement-end) .label-text-wrapper{-webkit-margin-start:16px;margin-inline-start:16px;-webkit-margin-end:0;margin-inline-end:0}:host(.toggle-label-placement-fixed) .label-text-wrapper{-webkit-margin-start:0;margin-inline-start:0;-webkit-margin-end:16px;margin-inline-end:16px}:host(.toggle-label-placement-fixed) .label-text-wrapper{-ms-flex:0 0 100px;flex:0 0 100px;width:100px;min-width:100px;max-width:200px}:host(.toggle-label-placement-stacked) .toggle-wrapper{-ms-flex-direction:column;flex-direction:column;text-align:center}:host(.toggle-label-placement-stacked) .label-text-wrapper{-webkit-transform:scale(0.75);transform:scale(0.75);margin-left:0;margin-right:0;margin-bottom:16px;max-width:calc(100% / 0.75)}:host(.toggle-label-placement-stacked.toggle-alignment-start) .label-text-wrapper{-webkit-transform-origin:left top;transform-origin:left top}:host-context([dir=rtl]):host(.toggle-label-placement-stacked.toggle-alignment-start) .label-text-wrapper,:host-context([dir=rtl]).toggle-label-placement-stacked.toggle-alignment-start .label-text-wrapper{-webkit-transform-origin:right top;transform-origin:right top}@supports selector(:dir(rtl)){:host(.toggle-label-placement-stacked.toggle-alignment-start:dir(rtl)) .label-text-wrapper{-webkit-transform-origin:right top;transform-origin:right top}}:host(.toggle-label-placement-stacked.toggle-alignment-center) .label-text-wrapper{-webkit-transform-origin:center top;transform-origin:center top}:host-context([dir=rtl]):host(.toggle-label-placement-stacked.toggle-alignment-center) .label-text-wrapper,:host-context([dir=rtl]).toggle-label-placement-stacked.toggle-alignment-center .label-text-wrapper{-webkit-transform-origin:calc(100% - center) top;transform-origin:calc(100% - center) top}@supports selector(:dir(rtl)){:host(.toggle-label-placement-stacked.toggle-alignment-center:dir(rtl)) .label-text-wrapper{-webkit-transform-origin:calc(100% - center) top;transform-origin:calc(100% - center) top}}:host(.toggle-justify-space-between) .toggle-wrapper{-ms-flex-pack:justify;justify-content:space-between}:host(.toggle-justify-start) .toggle-wrapper{-ms-flex-pack:start;justify-content:start}:host(.toggle-justify-end) .toggle-wrapper{-ms-flex-pack:end;justify-content:end}:host(.toggle-alignment-start) .toggle-wrapper{-ms-flex-align:start;align-items:start}:host(.toggle-alignment-center) .toggle-wrapper{-ms-flex-align:center;align-items:center}:host(.toggle-justify-space-between),:host(.toggle-justify-start),:host(.toggle-justify-end),:host(.toggle-alignment-start),:host(.toggle-alignment-center){display:block}.toggle-icon-wrapper{display:-ms-flexbox;display:flex;position:relative;-ms-flex-align:center;align-items:center;width:100%;height:100%;-webkit-transition:var(--handle-transition);transition:var(--handle-transition);will-change:transform}.toggle-icon{border-radius:var(--border-radius);display:block;position:relative;width:100%;height:100%;background:var(--track-background);overflow:inherit}:host(.toggle-checked) .toggle-icon{background:var(--track-background-checked)}.toggle-inner{border-radius:var(--handle-border-radius);position:absolute;left:var(--handle-spacing);width:var(--handle-width);height:var(--handle-height);max-height:var(--handle-max-height);-webkit-transition:var(--handle-transition);transition:var(--handle-transition);background:var(--handle-background);-webkit-box-shadow:var(--handle-box-shadow);box-shadow:var(--handle-box-shadow);contain:strict}:host(.toggle-ltr) .toggle-inner{left:var(--handle-spacing)}:host(.toggle-rtl) .toggle-inner{right:var(--handle-spacing)}:host(.toggle-ltr.toggle-checked) .toggle-icon-wrapper{-webkit-transform:translate3d(calc(100% - var(--handle-width)), 0, 0);transform:translate3d(calc(100% - var(--handle-width)), 0, 0)}:host(.toggle-rtl.toggle-checked) .toggle-icon-wrapper{-webkit-transform:translate3d(calc(-100% + var(--handle-width)), 0, 0);transform:translate3d(calc(-100% + var(--handle-width)), 0, 0)}:host(.toggle-checked) .toggle-inner{background:var(--handle-background-checked)}:host(.toggle-ltr.toggle-checked) .toggle-inner{-webkit-transform:translate3d(calc(var(--handle-spacing) * -2), 0, 0);transform:translate3d(calc(var(--handle-spacing) * -2), 0, 0)}:host(.toggle-rtl.toggle-checked) .toggle-inner{-webkit-transform:translate3d(calc(var(--handle-spacing) * 2), 0, 0);transform:translate3d(calc(var(--handle-spacing) * 2), 0, 0)}:host{--track-background:rgba(var(--ion-text-color-rgb, 0, 0, 0), 0.088);--track-background-checked:var(--ion-color-primary, #0054e9);--border-radius:15.5px;--handle-background:#ffffff;--handle-background-checked:#ffffff;--handle-border-radius:25.5px;--handle-box-shadow:0 3px 4px rgba(0, 0, 0, 0.06), 0 3px 8px rgba(0, 0, 0, 0.06);--handle-height:calc(31px - (2px * 2));--handle-max-height:calc(100% - var(--handle-spacing) * 2);--handle-width:calc(31px - (2px * 2));--handle-spacing:2px;--handle-transition:transform 300ms, width 120ms ease-in-out 80ms, left 110ms ease-in-out 80ms, right 110ms ease-in-out 80ms}.native-wrapper .toggle-icon{width:51px;height:31px;overflow:hidden}:host(.ion-color.toggle-checked) .toggle-icon{background:var(--ion-color-base)}:host(.toggle-activated) .toggle-switch-icon{opacity:0}.toggle-icon{-webkit-transform:translate3d(0, 0, 0);transform:translate3d(0, 0, 0);-webkit-transition:background-color 300ms;transition:background-color 300ms}.toggle-inner{will-change:transform}.toggle-switch-icon{position:absolute;top:50%;width:11px;height:11px;-webkit-transform:translateY(-50%);transform:translateY(-50%);-webkit-transition:opacity 300ms, color 300ms;transition:opacity 300ms, color 300ms}.toggle-switch-icon{position:absolute;color:var(--ion-color-dark, #222428)}:host(.toggle-ltr) .toggle-switch-icon{right:6px}:host(.toggle-rtl) .toggle-switch-icon{right:initial;left:6px;}:host(.toggle-checked) .toggle-switch-icon.toggle-switch-icon-checked{color:var(--ion-color-contrast, #fff)}:host(.toggle-checked) .toggle-switch-icon:not(.toggle-switch-icon-checked){opacity:0}.toggle-switch-icon-checked{position:absolute;width:15px;height:15px;-webkit-transform:translateY(-50%) rotate(90deg);transform:translateY(-50%) rotate(90deg)}:host(.toggle-ltr) .toggle-switch-icon-checked{right:initial;left:4px;}:host(.toggle-rtl) .toggle-switch-icon-checked{right:4px}:host(.toggle-activated) .toggle-icon::before,:host(.toggle-checked) .toggle-icon::before{-webkit-transform:scale3d(0, 0, 0);transform:scale3d(0, 0, 0)}:host(.toggle-activated.toggle-checked) .toggle-inner::before{-webkit-transform:scale3d(0, 0, 0);transform:scale3d(0, 0, 0)}:host(.toggle-activated) .toggle-inner{width:calc(var(--handle-width) + 6px)}:host(.toggle-ltr.toggle-activated.toggle-checked) .toggle-icon-wrapper{-webkit-transform:translate3d(calc(100% - var(--handle-width) - 6px), 0, 0);transform:translate3d(calc(100% - var(--handle-width) - 6px), 0, 0)}:host(.toggle-rtl.toggle-activated.toggle-checked) .toggle-icon-wrapper{-webkit-transform:translate3d(calc(-100% + var(--handle-width) + 6px), 0, 0);transform:translate3d(calc(-100% + var(--handle-width) + 6px), 0, 0)}:host(.toggle-disabled){opacity:0.3}\";\n\nconst toggleMdCss = \":host{-webkit-box-sizing:content-box !important;box-sizing:content-box !important;display:inline-block;position:relative;max-width:100%;cursor:pointer;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none;z-index:2}:host(.in-item){-ms-flex:1 1 0px;flex:1 1 0;width:100%;height:100%}:host([slot=start]),:host([slot=end]){-ms-flex:initial;flex:initial;width:auto}:host(.ion-focused) input{border:2px solid #5e9ed6}:host(.toggle-disabled){pointer-events:none}input{display:none}.toggle-wrapper{display:-ms-flexbox;display:flex;position:relative;-ms-flex-positive:1;flex-grow:1;-ms-flex-align:center;align-items:center;-ms-flex-pack:justify;justify-content:space-between;height:inherit;-webkit-transition:background-color 15ms linear;transition:background-color 15ms linear;cursor:inherit}.label-text-wrapper{text-overflow:ellipsis;white-space:nowrap;overflow:hidden}:host(.in-item) .label-text-wrapper{margin-top:10px;margin-bottom:10px}:host(.in-item.toggle-label-placement-stacked) .label-text-wrapper{margin-top:10px;margin-bottom:16px}:host(.in-item.toggle-label-placement-stacked) .native-wrapper{margin-bottom:10px}.label-text-wrapper-hidden{display:none}.native-wrapper{display:-ms-flexbox;display:flex;-ms-flex-align:center;align-items:center}.toggle-bottom{padding-top:4px;display:-ms-flexbox;display:flex;-ms-flex-pack:justify;justify-content:space-between;font-size:0.75rem;white-space:normal}:host(.toggle-label-placement-stacked) .toggle-bottom{font-size:1rem}.toggle-bottom .error-text{display:none;color:var(--ion-color-danger, #c5000f)}.toggle-bottom .helper-text{display:block;color:var(--ion-color-step-700, var(--ion-text-color-step-300, #4d4d4d))}:host(.ion-touched.ion-invalid) .toggle-bottom .error-text{display:block}:host(.ion-touched.ion-invalid) .toggle-bottom .helper-text{display:none}:host(.toggle-label-placement-start) .toggle-wrapper{-ms-flex-direction:row;flex-direction:row}:host(.toggle-label-placement-start) .label-text-wrapper{-webkit-margin-start:0;margin-inline-start:0;-webkit-margin-end:16px;margin-inline-end:16px}:host(.toggle-label-placement-end) .toggle-wrapper{-ms-flex-direction:row-reverse;flex-direction:row-reverse;-ms-flex-pack:start;justify-content:start}:host(.toggle-label-placement-end) .label-text-wrapper{-webkit-margin-start:16px;margin-inline-start:16px;-webkit-margin-end:0;margin-inline-end:0}:host(.toggle-label-placement-fixed) .label-text-wrapper{-webkit-margin-start:0;margin-inline-start:0;-webkit-margin-end:16px;margin-inline-end:16px}:host(.toggle-label-placement-fixed) .label-text-wrapper{-ms-flex:0 0 100px;flex:0 0 100px;width:100px;min-width:100px;max-width:200px}:host(.toggle-label-placement-stacked) .toggle-wrapper{-ms-flex-direction:column;flex-direction:column;text-align:center}:host(.toggle-label-placement-stacked) .label-text-wrapper{-webkit-transform:scale(0.75);transform:scale(0.75);margin-left:0;margin-right:0;margin-bottom:16px;max-width:calc(100% / 0.75)}:host(.toggle-label-placement-stacked.toggle-alignment-start) .label-text-wrapper{-webkit-transform-origin:left top;transform-origin:left top}:host-context([dir=rtl]):host(.toggle-label-placement-stacked.toggle-alignment-start) .label-text-wrapper,:host-context([dir=rtl]).toggle-label-placement-stacked.toggle-alignment-start .label-text-wrapper{-webkit-transform-origin:right top;transform-origin:right top}@supports selector(:dir(rtl)){:host(.toggle-label-placement-stacked.toggle-alignment-start:dir(rtl)) .label-text-wrapper{-webkit-transform-origin:right top;transform-origin:right top}}:host(.toggle-label-placement-stacked.toggle-alignment-center) .label-text-wrapper{-webkit-transform-origin:center top;transform-origin:center top}:host-context([dir=rtl]):host(.toggle-label-placement-stacked.toggle-alignment-center) .label-text-wrapper,:host-context([dir=rtl]).toggle-label-placement-stacked.toggle-alignment-center .label-text-wrapper{-webkit-transform-origin:calc(100% - center) top;transform-origin:calc(100% - center) top}@supports selector(:dir(rtl)){:host(.toggle-label-placement-stacked.toggle-alignment-center:dir(rtl)) .label-text-wrapper{-webkit-transform-origin:calc(100% - center) top;transform-origin:calc(100% - center) top}}:host(.toggle-justify-space-between) .toggle-wrapper{-ms-flex-pack:justify;justify-content:space-between}:host(.toggle-justify-start) .toggle-wrapper{-ms-flex-pack:start;justify-content:start}:host(.toggle-justify-end) .toggle-wrapper{-ms-flex-pack:end;justify-content:end}:host(.toggle-alignment-start) .toggle-wrapper{-ms-flex-align:start;align-items:start}:host(.toggle-alignment-center) .toggle-wrapper{-ms-flex-align:center;align-items:center}:host(.toggle-justify-space-between),:host(.toggle-justify-start),:host(.toggle-justify-end),:host(.toggle-alignment-start),:host(.toggle-alignment-center){display:block}.toggle-icon-wrapper{display:-ms-flexbox;display:flex;position:relative;-ms-flex-align:center;align-items:center;width:100%;height:100%;-webkit-transition:var(--handle-transition);transition:var(--handle-transition);will-change:transform}.toggle-icon{border-radius:var(--border-radius);display:block;position:relative;width:100%;height:100%;background:var(--track-background);overflow:inherit}:host(.toggle-checked) .toggle-icon{background:var(--track-background-checked)}.toggle-inner{border-radius:var(--handle-border-radius);position:absolute;left:var(--handle-spacing);width:var(--handle-width);height:var(--handle-height);max-height:var(--handle-max-height);-webkit-transition:var(--handle-transition);transition:var(--handle-transition);background:var(--handle-background);-webkit-box-shadow:var(--handle-box-shadow);box-shadow:var(--handle-box-shadow);contain:strict}:host(.toggle-ltr) .toggle-inner{left:var(--handle-spacing)}:host(.toggle-rtl) .toggle-inner{right:var(--handle-spacing)}:host(.toggle-ltr.toggle-checked) .toggle-icon-wrapper{-webkit-transform:translate3d(calc(100% - var(--handle-width)), 0, 0);transform:translate3d(calc(100% - var(--handle-width)), 0, 0)}:host(.toggle-rtl.toggle-checked) .toggle-icon-wrapper{-webkit-transform:translate3d(calc(-100% + var(--handle-width)), 0, 0);transform:translate3d(calc(-100% + var(--handle-width)), 0, 0)}:host(.toggle-checked) .toggle-inner{background:var(--handle-background-checked)}:host(.toggle-ltr.toggle-checked) .toggle-inner{-webkit-transform:translate3d(calc(var(--handle-spacing) * -2), 0, 0);transform:translate3d(calc(var(--handle-spacing) * -2), 0, 0)}:host(.toggle-rtl.toggle-checked) .toggle-inner{-webkit-transform:translate3d(calc(var(--handle-spacing) * 2), 0, 0);transform:translate3d(calc(var(--handle-spacing) * 2), 0, 0)}:host{--track-background:rgba(var(--ion-text-color-rgb, 0, 0, 0), 0.39);--track-background-checked:rgba(var(--ion-color-primary-rgb, 0, 84, 233), 0.5);--border-radius:14px;--handle-background:#ffffff;--handle-background-checked:var(--ion-color-primary, #0054e9);--handle-border-radius:50%;--handle-box-shadow:0 3px 1px -2px rgba(0, 0, 0, 0.2), 0 2px 2px 0 rgba(0, 0, 0, 0.14), 0 1px 5px 0 rgba(0, 0, 0, 0.12);--handle-width:20px;--handle-height:20px;--handle-max-height:calc(100% + 6px);--handle-spacing:0;--handle-transition:transform 160ms cubic-bezier(0.4, 0, 0.2, 1), background-color 160ms cubic-bezier(0.4, 0, 0.2, 1)}.native-wrapper .toggle-icon{width:36px;height:14px}:host(.ion-color.toggle-checked) .toggle-icon{background:rgba(var(--ion-color-base-rgb), 0.5)}:host(.ion-color.toggle-checked) .toggle-inner{background:var(--ion-color-base)}:host(.toggle-checked) .toggle-inner{color:var(--ion-color-contrast, #fff)}.toggle-icon{-webkit-transition:background-color 160ms;transition:background-color 160ms}.toggle-inner{will-change:background-color, transform;display:-ms-flexbox;display:flex;-ms-flex-align:center;align-items:center;-ms-flex-pack:center;justify-content:center;color:#000}.toggle-inner .toggle-switch-icon{-webkit-padding-start:1px;padding-inline-start:1px;-webkit-padding-end:1px;padding-inline-end:1px;padding-top:1px;padding-bottom:1px;width:100%;height:100%}:host(.toggle-disabled){opacity:0.38}\";\n\nconst Toggle = class {\n    constructor(hostRef) {\n        registerInstance(this, hostRef);\n        this.ionChange = createEvent(this, \"ionChange\", 7);\n        this.ionFocus = createEvent(this, \"ionFocus\", 7);\n        this.ionBlur = createEvent(this, \"ionBlur\", 7);\n        this.inputId = `ion-tg-${toggleIds++}`;\n        this.inputLabelId = `${this.inputId}-lbl`;\n        this.helperTextId = `${this.inputId}-helper-text`;\n        this.errorTextId = `${this.inputId}-error-text`;\n        this.lastDrag = 0;\n        this.inheritedAttributes = {};\n        this.didLoad = false;\n        this.activated = false;\n        /**\n         * The name of the control, which is submitted with the form data.\n         */\n        this.name = this.inputId;\n        /**\n         * If `true`, the toggle is selected.\n         */\n        this.checked = false;\n        /**\n         * If `true`, the user cannot interact with the toggle.\n         */\n        this.disabled = false;\n        /**\n         * The value of the toggle does not mean if it's checked or not, use the `checked`\n         * property for that.\n         *\n         * The value of a toggle is analogous to the value of a `<input type=\"checkbox\">`,\n         * it's only used when the toggle participates in a native `<form>`.\n         */\n        this.value = 'on';\n        /**\n         * Enables the on/off accessibility switch labels within the toggle.\n         */\n        this.enableOnOffLabels = config.get('toggleOnOffLabels');\n        /**\n         * Where to place the label relative to the input.\n         * `\"start\"`: The label will appear to the left of the toggle in LTR and to the right in RTL.\n         * `\"end\"`: The label will appear to the right of the toggle in LTR and to the left in RTL.\n         * `\"fixed\"`: The label has the same behavior as `\"start\"` except it also has a fixed width. Long text will be truncated with ellipses (\"...\").\n         * `\"stacked\"`: The label will appear above the toggle regardless of the direction. The alignment of the label can be controlled with the `alignment` property.\n         */\n        this.labelPlacement = 'start';\n        /**\n         * If true, screen readers will announce it as a required field. This property\n         * works only for accessibility purposes, it will not prevent the form from\n         * submitting if the value is invalid.\n         */\n        this.required = false;\n        this.setupGesture = async () => {\n            const { toggleTrack } = this;\n            if (toggleTrack) {\n                this.gesture = (await import('./index-CfgBF1SE.js')).createGesture({\n                    el: toggleTrack,\n                    gestureName: 'toggle',\n                    gesturePriority: 100,\n                    threshold: 5,\n                    passive: false,\n                    onStart: () => this.onStart(),\n                    onMove: (ev) => this.onMove(ev),\n                    onEnd: (ev) => this.onEnd(ev),\n                });\n                this.disabledChanged();\n            }\n        };\n        this.onKeyDown = (ev) => {\n            if (ev.key === ' ') {\n                ev.preventDefault();\n                if (!this.disabled) {\n                    this.toggleChecked();\n                }\n            }\n        };\n        this.onClick = (ev) => {\n            /**\n             * The haptics for the toggle on tap is\n             * an iOS-only feature. As such, it should\n             * only trigger on iOS.\n             */\n            const enableHaptics = isPlatform('ios');\n            if (this.disabled) {\n                return;\n            }\n            ev.preventDefault();\n            if (this.lastDrag + 300 < Date.now()) {\n                this.toggleChecked();\n                enableHaptics && hapticSelection();\n            }\n        };\n        /**\n         * Stops propagation when the display label is clicked,\n         * otherwise, two clicks will be triggered.\n         */\n        this.onDivLabelClick = (ev) => {\n            ev.stopPropagation();\n        };\n        this.onFocus = () => {\n            this.ionFocus.emit();\n        };\n        this.onBlur = () => {\n            this.ionBlur.emit();\n        };\n        this.getSwitchLabelIcon = (mode, checked) => {\n            if (mode === 'md') {\n                return checked ? checkmarkOutline : removeOutline;\n            }\n            return checked ? removeOutline : ellipseOutline;\n        };\n    }\n    disabledChanged() {\n        if (this.gesture) {\n            this.gesture.enable(!this.disabled);\n        }\n    }\n    toggleChecked() {\n        const { checked, value } = this;\n        const isNowChecked = !checked;\n        this.checked = isNowChecked;\n        this.setFocus();\n        this.ionChange.emit({\n            checked: isNowChecked,\n            value,\n        });\n    }\n    async connectedCallback() {\n        /**\n         * If we have not yet rendered\n         * ion-toggle, then toggleTrack is not defined.\n         * But if we are moving ion-toggle via appendChild,\n         * then toggleTrack will be defined.\n         */\n        if (this.didLoad) {\n            this.setupGesture();\n        }\n    }\n    componentDidLoad() {\n        this.setupGesture();\n        this.didLoad = true;\n    }\n    disconnectedCallback() {\n        if (this.gesture) {\n            this.gesture.destroy();\n            this.gesture = undefined;\n        }\n    }\n    componentWillLoad() {\n        this.inheritedAttributes = Object.assign({}, inheritAriaAttributes(this.el));\n    }\n    onStart() {\n        this.activated = true;\n        // touch-action does not work in iOS\n        this.setFocus();\n    }\n    onMove(detail) {\n        if (shouldToggle(isRTL(this.el), this.checked, detail.deltaX, -10)) {\n            this.toggleChecked();\n            hapticSelection();\n        }\n    }\n    onEnd(ev) {\n        this.activated = false;\n        this.lastDrag = Date.now();\n        ev.event.preventDefault();\n        ev.event.stopImmediatePropagation();\n    }\n    getValue() {\n        return this.value || '';\n    }\n    setFocus() {\n        if (this.focusEl) {\n            this.focusEl.focus();\n        }\n    }\n    renderOnOffSwitchLabels(mode, checked) {\n        const icon = this.getSwitchLabelIcon(mode, checked);\n        return (h(\"ion-icon\", { class: {\n                'toggle-switch-icon': true,\n                'toggle-switch-icon-checked': checked,\n            }, icon: icon, \"aria-hidden\": \"true\" }));\n    }\n    renderToggleControl() {\n        const mode = getIonMode(this);\n        const { enableOnOffLabels, checked } = this;\n        return (h(\"div\", { class: \"toggle-icon\", part: \"track\", ref: (el) => (this.toggleTrack = el) }, enableOnOffLabels &&\n            mode === 'ios' && [this.renderOnOffSwitchLabels(mode, true), this.renderOnOffSwitchLabels(mode, false)], h(\"div\", { class: \"toggle-icon-wrapper\" }, h(\"div\", { class: \"toggle-inner\", part: \"handle\" }, enableOnOffLabels && mode === 'md' && this.renderOnOffSwitchLabels(mode, checked)))));\n    }\n    get hasLabel() {\n        return this.el.textContent !== '';\n    }\n    getHintTextID() {\n        const { el, helperText, errorText, helperTextId, errorTextId } = this;\n        if (el.classList.contains('ion-touched') && el.classList.contains('ion-invalid') && errorText) {\n            return errorTextId;\n        }\n        if (helperText) {\n            return helperTextId;\n        }\n        return undefined;\n    }\n    /**\n     * Responsible for rendering helper text and error text.\n     * This element should only be rendered if hint text is set.\n     */\n    renderHintText() {\n        const { helperText, errorText, helperTextId, errorTextId } = this;\n        /**\n         * undefined and empty string values should\n         * be treated as not having helper/error text.\n         */\n        const hasHintText = !!helperText || !!errorText;\n        if (!hasHintText) {\n            return;\n        }\n        return (h(\"div\", { class: \"toggle-bottom\" }, h(\"div\", { id: helperTextId, class: \"helper-text\", part: \"supporting-text helper-text\" }, helperText), h(\"div\", { id: errorTextId, class: \"error-text\", part: \"supporting-text error-text\" }, errorText)));\n    }\n    render() {\n        const { activated, alignment, checked, color, disabled, el, errorTextId, hasLabel, inheritedAttributes, inputId, inputLabelId, justify, labelPlacement, name, required, } = this;\n        const mode = getIonMode(this);\n        const value = this.getValue();\n        const rtl = isRTL(el) ? 'rtl' : 'ltr';\n        renderHiddenInput(true, el, name, checked ? value : '', disabled);\n        return (h(Host, { key: '21037ea2e8326f58c84becadde475f007f931924', role: \"switch\", \"aria-checked\": `${checked}`, \"aria-describedby\": this.getHintTextID(), \"aria-invalid\": this.getHintTextID() === errorTextId, onClick: this.onClick, \"aria-labelledby\": hasLabel ? inputLabelId : null, \"aria-label\": inheritedAttributes['aria-label'] || null, \"aria-disabled\": disabled ? 'true' : null, tabindex: disabled ? undefined : 0, onKeyDown: this.onKeyDown, class: createColorClasses(color, {\n                [mode]: true,\n                'in-item': hostContext('ion-item', el),\n                'toggle-activated': activated,\n                'toggle-checked': checked,\n                'toggle-disabled': disabled,\n                [`toggle-justify-${justify}`]: justify !== undefined,\n                [`toggle-alignment-${alignment}`]: alignment !== undefined,\n                [`toggle-label-placement-${labelPlacement}`]: true,\n                [`toggle-${rtl}`]: true,\n            }) }, h(\"label\", { key: '4d153679d118d01286f6633d1c19558a97745ff6', class: \"toggle-wrapper\", htmlFor: inputId }, h(\"input\", Object.assign({ key: '0dfcd4df15b8d41bec5ff5f8912503afbb7bec53', type: \"checkbox\", role: \"switch\", \"aria-checked\": `${checked}`, checked: checked, disabled: disabled, id: inputId, onFocus: () => this.onFocus(), onBlur: () => this.onBlur(), ref: (focusEl) => (this.focusEl = focusEl), required: required }, inheritedAttributes)), h(\"div\", { key: 'ffed3a07ba2ab70e5b232e6041bc3b6b34be8331', class: {\n                'label-text-wrapper': true,\n                'label-text-wrapper-hidden': !hasLabel,\n            }, part: \"label\", id: inputLabelId, onClick: this.onDivLabelClick }, h(\"slot\", { key: 'd88e1e3dcdd8293f6b61f237cd7a0511dcbce300' }), this.renderHintText()), h(\"div\", { key: '0e924225f5f0caf3c88738acb6c557bd8c1b68f6', class: \"native-wrapper\" }, this.renderToggleControl()))));\n    }\n    get el() { return getElement(this); }\n    static get watchers() { return {\n        \"disabled\": [\"disabledChanged\"]\n    }; }\n};\nconst shouldToggle = (rtl, checked, deltaX, margin) => {\n    if (checked) {\n        return (!rtl && margin > deltaX) || (rtl && 10 < deltaX);\n    }\n    else {\n        return (!rtl && 10 < deltaX) || (rtl && margin > deltaX);\n    }\n};\nlet toggleIds = 0;\nToggle.style = {\n    ios: toggleIosCss,\n    md: toggleMdCss\n};\n\nexport { Toggle as ion_toggle };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAYA,IAAM,eAAe;AAErB,IAAM,cAAc;AAEpB,IAAM,SAAS,MAAM;AAAA,EACjB,YAAY,SAAS;AACjB,qBAAiB,MAAM,OAAO;AAC9B,SAAK,YAAY,YAAY,MAAM,aAAa,CAAC;AACjD,SAAK,WAAW,YAAY,MAAM,YAAY,CAAC;AAC/C,SAAK,UAAU,YAAY,MAAM,WAAW,CAAC;AAC7C,SAAK,UAAU,UAAU,WAAW;AACpC,SAAK,eAAe,GAAG,KAAK,OAAO;AACnC,SAAK,eAAe,GAAG,KAAK,OAAO;AACnC,SAAK,cAAc,GAAG,KAAK,OAAO;AAClC,SAAK,WAAW;AAChB,SAAK,sBAAsB,CAAC;AAC5B,SAAK,UAAU;AACf,SAAK,YAAY;AAIjB,SAAK,OAAO,KAAK;AAIjB,SAAK,UAAU;AAIf,SAAK,WAAW;AAQhB,SAAK,QAAQ;AAIb,SAAK,oBAAoB,OAAO,IAAI,mBAAmB;AAQvD,SAAK,iBAAiB;AAMtB,SAAK,WAAW;AAChB,SAAK,eAAe,MAAY;AAC5B,YAAM,EAAE,YAAY,IAAI;AACxB,UAAI,aAAa;AACb,aAAK,WAAW,MAAM,OAAO,8BAAqB,GAAG,cAAc;AAAA,UAC/D,IAAI;AAAA,UACJ,aAAa;AAAA,UACb,iBAAiB;AAAA,UACjB,WAAW;AAAA,UACX,SAAS;AAAA,UACT,SAAS,MAAM,KAAK,QAAQ;AAAA,UAC5B,QAAQ,CAAC,OAAO,KAAK,OAAO,EAAE;AAAA,UAC9B,OAAO,CAAC,OAAO,KAAK,MAAM,EAAE;AAAA,QAChC,CAAC;AACD,aAAK,gBAAgB;AAAA,MACzB;AAAA,IACJ;AACA,SAAK,YAAY,CAAC,OAAO;AACrB,UAAI,GAAG,QAAQ,KAAK;AAChB,WAAG,eAAe;AAClB,YAAI,CAAC,KAAK,UAAU;AAChB,eAAK,cAAc;AAAA,QACvB;AAAA,MACJ;AAAA,IACJ;AACA,SAAK,UAAU,CAAC,OAAO;AAMnB,YAAM,gBAAgB,WAAW,KAAK;AACtC,UAAI,KAAK,UAAU;AACf;AAAA,MACJ;AACA,SAAG,eAAe;AAClB,UAAI,KAAK,WAAW,MAAM,KAAK,IAAI,GAAG;AAClC,aAAK,cAAc;AACnB,yBAAiB,gBAAgB;AAAA,MACrC;AAAA,IACJ;AAKA,SAAK,kBAAkB,CAAC,OAAO;AAC3B,SAAG,gBAAgB;AAAA,IACvB;AACA,SAAK,UAAU,MAAM;AACjB,WAAK,SAAS,KAAK;AAAA,IACvB;AACA,SAAK,SAAS,MAAM;AAChB,WAAK,QAAQ,KAAK;AAAA,IACtB;AACA,SAAK,qBAAqB,CAAC,MAAM,YAAY;AACzC,UAAI,SAAS,MAAM;AACf,eAAO,UAAU,mBAAmB;AAAA,MACxC;AACA,aAAO,UAAU,gBAAgB;AAAA,IACrC;AAAA,EACJ;AAAA,EACA,kBAAkB;AACd,QAAI,KAAK,SAAS;AACd,WAAK,QAAQ,OAAO,CAAC,KAAK,QAAQ;AAAA,IACtC;AAAA,EACJ;AAAA,EACA,gBAAgB;AACZ,UAAM,EAAE,SAAS,MAAM,IAAI;AAC3B,UAAM,eAAe,CAAC;AACtB,SAAK,UAAU;AACf,SAAK,SAAS;AACd,SAAK,UAAU,KAAK;AAAA,MAChB,SAAS;AAAA,MACT;AAAA,IACJ,CAAC;AAAA,EACL;AAAA,EACM,oBAAoB;AAAA;AAOtB,UAAI,KAAK,SAAS;AACd,aAAK,aAAa;AAAA,MACtB;AAAA,IACJ;AAAA;AAAA,EACA,mBAAmB;AACf,SAAK,aAAa;AAClB,SAAK,UAAU;AAAA,EACnB;AAAA,EACA,uBAAuB;AACnB,QAAI,KAAK,SAAS;AACd,WAAK,QAAQ,QAAQ;AACrB,WAAK,UAAU;AAAA,IACnB;AAAA,EACJ;AAAA,EACA,oBAAoB;AAChB,SAAK,sBAAsB,OAAO,OAAO,CAAC,GAAG,sBAAsB,KAAK,EAAE,CAAC;AAAA,EAC/E;AAAA,EACA,UAAU;AACN,SAAK,YAAY;AAEjB,SAAK,SAAS;AAAA,EAClB;AAAA,EACA,OAAO,QAAQ;AACX,QAAI,aAAa,MAAM,KAAK,EAAE,GAAG,KAAK,SAAS,OAAO,QAAQ,GAAG,GAAG;AAChE,WAAK,cAAc;AACnB,sBAAgB;AAAA,IACpB;AAAA,EACJ;AAAA,EACA,MAAM,IAAI;AACN,SAAK,YAAY;AACjB,SAAK,WAAW,KAAK,IAAI;AACzB,OAAG,MAAM,eAAe;AACxB,OAAG,MAAM,yBAAyB;AAAA,EACtC;AAAA,EACA,WAAW;AACP,WAAO,KAAK,SAAS;AAAA,EACzB;AAAA,EACA,WAAW;AACP,QAAI,KAAK,SAAS;AACd,WAAK,QAAQ,MAAM;AAAA,IACvB;AAAA,EACJ;AAAA,EACA,wBAAwB,MAAM,SAAS;AACnC,UAAM,OAAO,KAAK,mBAAmB,MAAM,OAAO;AAClD,WAAQ,EAAE,YAAY,EAAE,OAAO;AAAA,MACvB,sBAAsB;AAAA,MACtB,8BAA8B;AAAA,IAClC,GAAG,MAAY,eAAe,OAAO,CAAC;AAAA,EAC9C;AAAA,EACA,sBAAsB;AAClB,UAAM,OAAO,WAAW,IAAI;AAC5B,UAAM,EAAE,mBAAmB,QAAQ,IAAI;AACvC,WAAQ,EAAE,OAAO,EAAE,OAAO,eAAe,MAAM,SAAS,KAAK,CAAC,OAAQ,KAAK,cAAc,GAAI,GAAG,qBAC5F,SAAS,SAAS,CAAC,KAAK,wBAAwB,MAAM,IAAI,GAAG,KAAK,wBAAwB,MAAM,KAAK,CAAC,GAAG,EAAE,OAAO,EAAE,OAAO,sBAAsB,GAAG,EAAE,OAAO,EAAE,OAAO,gBAAgB,MAAM,SAAS,GAAG,qBAAqB,SAAS,QAAQ,KAAK,wBAAwB,MAAM,OAAO,CAAC,CAAC,CAAC;AAAA,EACnS;AAAA,EACA,IAAI,WAAW;AACX,WAAO,KAAK,GAAG,gBAAgB;AAAA,EACnC;AAAA,EACA,gBAAgB;AACZ,UAAM,EAAE,IAAI,YAAY,WAAW,cAAc,YAAY,IAAI;AACjE,QAAI,GAAG,UAAU,SAAS,aAAa,KAAK,GAAG,UAAU,SAAS,aAAa,KAAK,WAAW;AAC3F,aAAO;AAAA,IACX;AACA,QAAI,YAAY;AACZ,aAAO;AAAA,IACX;AACA,WAAO;AAAA,EACX;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,iBAAiB;AACb,UAAM,EAAE,YAAY,WAAW,cAAc,YAAY,IAAI;AAK7D,UAAM,cAAc,CAAC,CAAC,cAAc,CAAC,CAAC;AACtC,QAAI,CAAC,aAAa;AACd;AAAA,IACJ;AACA,WAAQ,EAAE,OAAO,EAAE,OAAO,gBAAgB,GAAG,EAAE,OAAO,EAAE,IAAI,cAAc,OAAO,eAAe,MAAM,8BAA8B,GAAG,UAAU,GAAG,EAAE,OAAO,EAAE,IAAI,aAAa,OAAO,cAAc,MAAM,6BAA6B,GAAG,SAAS,CAAC;AAAA,EACzP;AAAA,EACA,SAAS;AACL,UAAM,EAAE,WAAW,WAAW,SAAS,OAAO,UAAU,IAAI,aAAa,UAAU,qBAAqB,SAAS,cAAc,SAAS,gBAAgB,MAAM,SAAU,IAAI;AAC5K,UAAM,OAAO,WAAW,IAAI;AAC5B,UAAM,QAAQ,KAAK,SAAS;AAC5B,UAAM,MAAM,MAAM,EAAE,IAAI,QAAQ;AAChC,sBAAkB,MAAM,IAAI,MAAM,UAAU,QAAQ,IAAI,QAAQ;AAChE,WAAQ,EAAE,MAAM,EAAE,KAAK,4CAA4C,MAAM,UAAU,gBAAgB,GAAG,OAAO,IAAI,oBAAoB,KAAK,cAAc,GAAG,gBAAgB,KAAK,cAAc,MAAM,aAAa,SAAS,KAAK,SAAS,mBAAmB,WAAW,eAAe,MAAM,cAAc,oBAAoB,YAAY,KAAK,MAAM,iBAAiB,WAAW,SAAS,MAAM,UAAU,WAAW,SAAY,GAAG,WAAW,KAAK,WAAW,OAAO,mBAAmB,OAAO;AAAA,MACvd,CAAC,IAAI,GAAG;AAAA,MACR,WAAW,YAAY,YAAY,EAAE;AAAA,MACrC,oBAAoB;AAAA,MACpB,kBAAkB;AAAA,MAClB,mBAAmB;AAAA,MACnB,CAAC,kBAAkB,OAAO,EAAE,GAAG,YAAY;AAAA,MAC3C,CAAC,oBAAoB,SAAS,EAAE,GAAG,cAAc;AAAA,MACjD,CAAC,0BAA0B,cAAc,EAAE,GAAG;AAAA,MAC9C,CAAC,UAAU,GAAG,EAAE,GAAG;AAAA,IACvB,CAAC,EAAE,GAAG,EAAE,SAAS,EAAE,KAAK,4CAA4C,OAAO,kBAAkB,SAAS,QAAQ,GAAG,EAAE,SAAS,OAAO,OAAO,EAAE,KAAK,4CAA4C,MAAM,YAAY,MAAM,UAAU,gBAAgB,GAAG,OAAO,IAAI,SAAkB,UAAoB,IAAI,SAAS,SAAS,MAAM,KAAK,QAAQ,GAAG,QAAQ,MAAM,KAAK,OAAO,GAAG,KAAK,CAAC,YAAa,KAAK,UAAU,SAAU,SAAmB,GAAG,mBAAmB,CAAC,GAAG,EAAE,OAAO,EAAE,KAAK,4CAA4C,OAAO;AAAA,MACpgB,sBAAsB;AAAA,MACtB,6BAA6B,CAAC;AAAA,IAClC,GAAG,MAAM,SAAS,IAAI,cAAc,SAAS,KAAK,gBAAgB,GAAG,EAAE,QAAQ,EAAE,KAAK,2CAA2C,CAAC,GAAG,KAAK,eAAe,CAAC,GAAG,EAAE,OAAO,EAAE,KAAK,4CAA4C,OAAO,iBAAiB,GAAG,KAAK,oBAAoB,CAAC,CAAC,CAAC;AAAA,EACxR;AAAA,EACA,IAAI,KAAK;AAAE,WAAO,WAAW,IAAI;AAAA,EAAG;AAAA,EACpC,WAAW,WAAW;AAAE,WAAO;AAAA,MAC3B,YAAY,CAAC,iBAAiB;AAAA,IAClC;AAAA,EAAG;AACP;AACA,IAAM,eAAe,CAAC,KAAK,SAAS,QAAQ,WAAW;AACnD,MAAI,SAAS;AACT,WAAQ,CAAC,OAAO,SAAS,UAAY,OAAO,KAAK;AAAA,EACrD,OACK;AACD,WAAQ,CAAC,OAAO,KAAK,UAAY,OAAO,SAAS;AAAA,EACrD;AACJ;AACA,IAAI,YAAY;AAChB,OAAO,QAAQ;AAAA,EACX,KAAK;AAAA,EACL,IAAI;AACR;", "names": []}