<ion-header>
  <ion-toolbar>
    <ion-title>SQLite CRUD</ion-title>
  </ion-toolbar>
</ion-header>

<ion-content class="ion-padding">
  <ion-item>
    <ion-input [(ngModel)]="name" placeholder="Name"></ion-input>
  </ion-item>
  <ion-item>
    <ion-input [(ngModel)]="email" placeholder="Email"></ion-input>
  </ion-item>
  <ion-button expand="full" (click)="saveUser()">
    {{ editMode ? 'Update' : 'Add' }} User
  </ion-button>

  <ion-list>
    <ion-item *ngFor="let user of users">
      <ion-label>
        <h2>{{ user.name }}</h2>
        <p>{{ user.email }}</p>
      </ion-label>
      <ion-button size="small" (click)="editUser(user)">Edit</ion-button>
      <ion-button size="small" color="danger" (click)="deleteUser(user.id)">Delete</ion-button>
    </ion-item>
  </ion-list>
</ion-content>
