{"version": 3, "sources": ["../../../../../../node_modules/@ionic/core/dist/esm/ion-accordion_2.entry.js"], "sourcesContent": ["/*!\n * (C) Ionic http://ionicframework.com - MIT License\n */\nimport { r as registerInstance, l as config, e as getIonMode, h, j as Host, k as getElement, d as createEvent, m as printIonWarning } from './index-B_U9CtaY.js';\nimport { g as getElementRoot, r as raf, f as addEventListener, m as removeEventListener, t as transitionEndAsync } from './helpers-1O4D2b7y.js';\nimport { l as chevronDown } from './index-BLV6ykCk.js';\n\nconst accordionIosCss = \":host{display:block;position:relative;width:100%;background-color:var(--ion-background-color, #ffffff);overflow:hidden;z-index:0}:host(.accordion-expanding) ::slotted(ion-item[slot=header]),:host(.accordion-expanded) ::slotted(ion-item[slot=header]){--border-width:0px}:host(.accordion-animated){-webkit-transition:all 300ms cubic-bezier(0.25, 0.8, 0.5, 1);transition:all 300ms cubic-bezier(0.25, 0.8, 0.5, 1)}:host(.accordion-animated) #content{-webkit-transition:max-height 300ms cubic-bezier(0.25, 0.8, 0.5, 1);transition:max-height 300ms cubic-bezier(0.25, 0.8, 0.5, 1)}#content{overflow:hidden;will-change:max-height}:host(.accordion-collapsing) #content{max-height:0 !important}:host(.accordion-collapsed) #content{display:none}:host(.accordion-expanding) #content{max-height:0}:host(.accordion-expanding) #content-wrapper{overflow:auto}:host(.accordion-disabled) #header,:host(.accordion-readonly) #header,:host(.accordion-disabled) #content,:host(.accordion-readonly) #content{pointer-events:none}:host(.accordion-disabled) #header,:host(.accordion-disabled) #content{opacity:0.4}@media (prefers-reduced-motion: reduce){:host,#content{-webkit-transition:none !important;transition:none !important}}:host(.accordion-next) ::slotted(ion-item[slot=header]){--border-width:0.55px 0px 0.55px 0px}\";\n\nconst accordionMdCss = \":host{display:block;position:relative;width:100%;background-color:var(--ion-background-color, #ffffff);overflow:hidden;z-index:0}:host(.accordion-expanding) ::slotted(ion-item[slot=header]),:host(.accordion-expanded) ::slotted(ion-item[slot=header]){--border-width:0px}:host(.accordion-animated){-webkit-transition:all 300ms cubic-bezier(0.25, 0.8, 0.5, 1);transition:all 300ms cubic-bezier(0.25, 0.8, 0.5, 1)}:host(.accordion-animated) #content{-webkit-transition:max-height 300ms cubic-bezier(0.25, 0.8, 0.5, 1);transition:max-height 300ms cubic-bezier(0.25, 0.8, 0.5, 1)}#content{overflow:hidden;will-change:max-height}:host(.accordion-collapsing) #content{max-height:0 !important}:host(.accordion-collapsed) #content{display:none}:host(.accordion-expanding) #content{max-height:0}:host(.accordion-expanding) #content-wrapper{overflow:auto}:host(.accordion-disabled) #header,:host(.accordion-readonly) #header,:host(.accordion-disabled) #content,:host(.accordion-readonly) #content{pointer-events:none}:host(.accordion-disabled) #header,:host(.accordion-disabled) #content{opacity:0.4}@media (prefers-reduced-motion: reduce){:host,#content{-webkit-transition:none !important;transition:none !important}}\";\n\nconst Accordion = class {\n    constructor(hostRef) {\n        registerInstance(this, hostRef);\n        this.updateListener = () => this.updateState(false);\n        this.state = 1 /* AccordionState.Collapsed */;\n        this.isNext = false;\n        this.isPrevious = false;\n        /**\n         * The value of the accordion. Defaults to an autogenerated\n         * value.\n         */\n        this.value = `ion-accordion-${accordionIds++}`;\n        /**\n         * If `true`, the accordion cannot be interacted with.\n         */\n        this.disabled = false;\n        /**\n         * If `true`, the accordion cannot be interacted with,\n         * but does not alter the opacity.\n         */\n        this.readonly = false;\n        /**\n         * The toggle icon to use. This icon will be\n         * rotated when the accordion is expanded\n         * or collapsed.\n         */\n        this.toggleIcon = chevronDown;\n        /**\n         * The slot inside of `ion-item` to\n         * place the toggle icon. Defaults to `\"end\"`.\n         */\n        this.toggleIconSlot = 'end';\n        this.setItemDefaults = () => {\n            const ionItem = this.getSlottedHeaderIonItem();\n            if (!ionItem) {\n                return;\n            }\n            /**\n             * For a11y purposes, we make\n             * the ion-item a button so users\n             * can tab to it and use keyboard\n             * navigation to get around.\n             */\n            ionItem.button = true;\n            ionItem.detail = false;\n            /**\n             * By default, the lines in an\n             * item should be full here, but\n             * only do that if a user has\n             * not explicitly overridden them\n             */\n            if (ionItem.lines === undefined) {\n                ionItem.lines = 'full';\n            }\n        };\n        this.getSlottedHeaderIonItem = () => {\n            const { headerEl } = this;\n            if (!headerEl) {\n                return;\n            }\n            /**\n             * Get the first ion-item\n             * slotted in the header slot\n             */\n            const slot = headerEl.querySelector('slot');\n            if (!slot) {\n                return;\n            }\n            // This is not defined in unit tests\n            if (slot.assignedElements === undefined)\n                return;\n            return slot.assignedElements().find((el) => el.tagName === 'ION-ITEM');\n        };\n        this.setAria = (expanded = false) => {\n            const ionItem = this.getSlottedHeaderIonItem();\n            if (!ionItem) {\n                return;\n            }\n            /**\n             * Get the native <button> element inside of\n             * ion-item because that is what will be focused\n             */\n            const root = getElementRoot(ionItem);\n            const button = root.querySelector('button');\n            if (!button) {\n                return;\n            }\n            button.setAttribute('aria-expanded', `${expanded}`);\n        };\n        this.slotToggleIcon = () => {\n            const ionItem = this.getSlottedHeaderIonItem();\n            if (!ionItem) {\n                return;\n            }\n            const { toggleIconSlot, toggleIcon } = this;\n            /**\n             * Check if there already is a toggle icon.\n             * If so, do not add another one.\n             */\n            const existingToggleIcon = ionItem.querySelector('.ion-accordion-toggle-icon');\n            if (existingToggleIcon) {\n                return;\n            }\n            const iconEl = document.createElement('ion-icon');\n            iconEl.slot = toggleIconSlot;\n            iconEl.lazy = false;\n            iconEl.classList.add('ion-accordion-toggle-icon');\n            iconEl.icon = toggleIcon;\n            iconEl.setAttribute('aria-hidden', 'true');\n            ionItem.appendChild(iconEl);\n        };\n        this.expandAccordion = (initialUpdate = false) => {\n            const { contentEl, contentElWrapper } = this;\n            if (initialUpdate || contentEl === undefined || contentElWrapper === undefined) {\n                this.state = 4 /* AccordionState.Expanded */;\n                return;\n            }\n            if (this.state === 4 /* AccordionState.Expanded */) {\n                return;\n            }\n            if (this.currentRaf !== undefined) {\n                cancelAnimationFrame(this.currentRaf);\n            }\n            if (this.shouldAnimate()) {\n                raf(() => {\n                    this.state = 8 /* AccordionState.Expanding */;\n                    this.currentRaf = raf(async () => {\n                        const contentHeight = contentElWrapper.offsetHeight;\n                        const waitForTransition = transitionEndAsync(contentEl, 2000);\n                        contentEl.style.setProperty('max-height', `${contentHeight}px`);\n                        await waitForTransition;\n                        this.state = 4 /* AccordionState.Expanded */;\n                        contentEl.style.removeProperty('max-height');\n                    });\n                });\n            }\n            else {\n                this.state = 4 /* AccordionState.Expanded */;\n            }\n        };\n        this.collapseAccordion = (initialUpdate = false) => {\n            const { contentEl } = this;\n            if (initialUpdate || contentEl === undefined) {\n                this.state = 1 /* AccordionState.Collapsed */;\n                return;\n            }\n            if (this.state === 1 /* AccordionState.Collapsed */) {\n                return;\n            }\n            if (this.currentRaf !== undefined) {\n                cancelAnimationFrame(this.currentRaf);\n            }\n            if (this.shouldAnimate()) {\n                this.currentRaf = raf(async () => {\n                    const contentHeight = contentEl.offsetHeight;\n                    contentEl.style.setProperty('max-height', `${contentHeight}px`);\n                    raf(async () => {\n                        const waitForTransition = transitionEndAsync(contentEl, 2000);\n                        this.state = 2 /* AccordionState.Collapsing */;\n                        await waitForTransition;\n                        this.state = 1 /* AccordionState.Collapsed */;\n                        contentEl.style.removeProperty('max-height');\n                    });\n                });\n            }\n            else {\n                this.state = 1 /* AccordionState.Collapsed */;\n            }\n        };\n        /**\n         * Helper function to determine if\n         * something should animate.\n         * If prefers-reduced-motion is set\n         * then we should not animate, regardless\n         * of what is set in the config.\n         */\n        this.shouldAnimate = () => {\n            if (typeof window === 'undefined') {\n                return false;\n            }\n            const prefersReducedMotion = matchMedia('(prefers-reduced-motion: reduce)').matches;\n            if (prefersReducedMotion) {\n                return false;\n            }\n            const animated = config.get('animated', true);\n            if (!animated) {\n                return false;\n            }\n            if (this.accordionGroupEl && !this.accordionGroupEl.animated) {\n                return false;\n            }\n            return true;\n        };\n        this.updateState = async (initialUpdate = false) => {\n            const accordionGroup = this.accordionGroupEl;\n            const accordionValue = this.value;\n            if (!accordionGroup) {\n                return;\n            }\n            const value = accordionGroup.value;\n            const shouldExpand = Array.isArray(value) ? value.includes(accordionValue) : value === accordionValue;\n            if (shouldExpand) {\n                this.expandAccordion(initialUpdate);\n                this.isNext = this.isPrevious = false;\n            }\n            else {\n                this.collapseAccordion(initialUpdate);\n                /**\n                 * When using popout or inset,\n                 * the collapsed accordion items\n                 * may need additional border radius\n                 * applied. Check to see if the\n                 * next or previous accordion is selected.\n                 */\n                const nextAccordion = this.getNextSibling();\n                const nextAccordionValue = nextAccordion === null || nextAccordion === void 0 ? void 0 : nextAccordion.value;\n                if (nextAccordionValue !== undefined) {\n                    this.isPrevious = Array.isArray(value) ? value.includes(nextAccordionValue) : value === nextAccordionValue;\n                }\n                const previousAccordion = this.getPreviousSibling();\n                const previousAccordionValue = previousAccordion === null || previousAccordion === void 0 ? void 0 : previousAccordion.value;\n                if (previousAccordionValue !== undefined) {\n                    this.isNext = Array.isArray(value) ? value.includes(previousAccordionValue) : value === previousAccordionValue;\n                }\n            }\n        };\n        this.getNextSibling = () => {\n            if (!this.el) {\n                return;\n            }\n            const nextSibling = this.el.nextElementSibling;\n            if ((nextSibling === null || nextSibling === void 0 ? void 0 : nextSibling.tagName) !== 'ION-ACCORDION') {\n                return;\n            }\n            return nextSibling;\n        };\n        this.getPreviousSibling = () => {\n            if (!this.el) {\n                return;\n            }\n            const previousSibling = this.el.previousElementSibling;\n            if ((previousSibling === null || previousSibling === void 0 ? void 0 : previousSibling.tagName) !== 'ION-ACCORDION') {\n                return;\n            }\n            return previousSibling;\n        };\n    }\n    valueChanged() {\n        this.updateState();\n    }\n    connectedCallback() {\n        var _a;\n        const accordionGroupEl = (this.accordionGroupEl = (_a = this.el) === null || _a === void 0 ? void 0 : _a.closest('ion-accordion-group'));\n        if (accordionGroupEl) {\n            this.updateState(true);\n            addEventListener(accordionGroupEl, 'ionValueChange', this.updateListener);\n        }\n    }\n    disconnectedCallback() {\n        const accordionGroupEl = this.accordionGroupEl;\n        if (accordionGroupEl) {\n            removeEventListener(accordionGroupEl, 'ionValueChange', this.updateListener);\n        }\n    }\n    componentDidLoad() {\n        this.setItemDefaults();\n        this.slotToggleIcon();\n        /**\n         * We need to wait a tick because we\n         * just set ionItem.button = true and\n         * the button has not have been rendered yet.\n         */\n        raf(() => {\n            /**\n             * Set aria label on button inside of ion-item\n             * once the inner content has been rendered.\n             */\n            const expanded = this.state === 4 /* AccordionState.Expanded */ || this.state === 8 /* AccordionState.Expanding */;\n            this.setAria(expanded);\n        });\n    }\n    toggleExpanded() {\n        const { accordionGroupEl, disabled, readonly, value, state } = this;\n        if (disabled || readonly)\n            return;\n        if (accordionGroupEl) {\n            /**\n             * Because the accordion group may or may\n             * not allow multiple accordions open, we\n             * need to request the toggling of this\n             * accordion and the accordion group will\n             * make the decision on whether or not\n             * to allow it.\n             */\n            const expand = state === 1 /* AccordionState.Collapsed */ || state === 2 /* AccordionState.Collapsing */;\n            accordionGroupEl.requestAccordionToggle(value, expand);\n        }\n    }\n    render() {\n        const { disabled, readonly } = this;\n        const mode = getIonMode(this);\n        const expanded = this.state === 4 /* AccordionState.Expanded */ || this.state === 8 /* AccordionState.Expanding */;\n        const headerPart = expanded ? 'header expanded' : 'header';\n        const contentPart = expanded ? 'content expanded' : 'content';\n        this.setAria(expanded);\n        return (h(Host, { key: '073e1d02c18dcbc20c68648426e87c14750c031d', class: {\n                [mode]: true,\n                'accordion-expanding': this.state === 8 /* AccordionState.Expanding */,\n                'accordion-expanded': this.state === 4 /* AccordionState.Expanded */,\n                'accordion-collapsing': this.state === 2 /* AccordionState.Collapsing */,\n                'accordion-collapsed': this.state === 1 /* AccordionState.Collapsed */,\n                'accordion-next': this.isNext,\n                'accordion-previous': this.isPrevious,\n                'accordion-disabled': disabled,\n                'accordion-readonly': readonly,\n                'accordion-animated': this.shouldAnimate(),\n            } }, h(\"div\", { key: '9b4cf326de8bb6b4033992903c0c1bfd7eea9bcc', onClick: () => this.toggleExpanded(), id: \"header\", part: headerPart, \"aria-controls\": \"content\", ref: (headerEl) => (this.headerEl = headerEl) }, h(\"slot\", { key: '464c32a37f64655eacf4218284214f5f30b14a1e', name: \"header\" })), h(\"div\", { key: '8bb52e6a62d7de0106b253201a89a32e79d9a594', id: \"content\", part: contentPart, role: \"region\", \"aria-labelledby\": \"header\", ref: (contentEl) => (this.contentEl = contentEl) }, h(\"div\", { key: '1d9dfd952ad493754aaeea7a8f625b33c2dd90a0', id: \"content-wrapper\", ref: (contentElWrapper) => (this.contentElWrapper = contentElWrapper) }, h(\"slot\", { key: '970dfbc55a612d739d0ca3b7b1a08e5c96d0c479', name: \"content\" })))));\n    }\n    static get delegatesFocus() { return true; }\n    get el() { return getElement(this); }\n    static get watchers() { return {\n        \"value\": [\"valueChanged\"]\n    }; }\n};\nlet accordionIds = 0;\nAccordion.style = {\n    ios: accordionIosCss,\n    md: accordionMdCss\n};\n\nconst accordionGroupIosCss = \":host{display:block}:host(.accordion-group-expand-inset){-webkit-margin-start:16px;margin-inline-start:16px;-webkit-margin-end:16px;margin-inline-end:16px;margin-top:16px;margin-bottom:16px}:host(.accordion-group-expand-inset) ::slotted(ion-accordion.accordion-expanding),:host(.accordion-group-expand-inset) ::slotted(ion-accordion.accordion-expanded){border-bottom:none}\";\n\nconst accordionGroupMdCss = \":host{display:block}:host(.accordion-group-expand-inset){-webkit-margin-start:16px;margin-inline-start:16px;-webkit-margin-end:16px;margin-inline-end:16px;margin-top:16px;margin-bottom:16px}:host(.accordion-group-expand-inset) ::slotted(ion-accordion){-webkit-box-shadow:0px 3px 1px -2px rgba(0, 0, 0, 0.2), 0px 2px 2px 0px rgba(0, 0, 0, 0.14), 0px 1px 5px 0px rgba(0, 0, 0, 0.12);box-shadow:0px 3px 1px -2px rgba(0, 0, 0, 0.2), 0px 2px 2px 0px rgba(0, 0, 0, 0.14), 0px 1px 5px 0px rgba(0, 0, 0, 0.12)}:host(.accordion-group-expand-inset) ::slotted(ion-accordion.accordion-expanding),:host(.accordion-group-expand-inset) ::slotted(ion-accordion.accordion-expanded){margin-left:0;margin-right:0;margin-top:16px;margin-bottom:16px;border-radius:6px}:host(.accordion-group-expand-inset) ::slotted(ion-accordion.accordion-previous){border-end-end-radius:6px;border-end-start-radius:6px}:host(.accordion-group-expand-inset) ::slotted(ion-accordion.accordion-next){border-start-start-radius:6px;border-start-end-radius:6px}:host(.accordion-group-expand-inset) ::slotted(ion-accordion):first-of-type{margin-left:0;margin-right:0;margin-top:0;margin-bottom:0}\";\n\nconst AccordionGroup = class {\n    constructor(hostRef) {\n        registerInstance(this, hostRef);\n        this.ionChange = createEvent(this, \"ionChange\", 7);\n        this.ionValueChange = createEvent(this, \"ionValueChange\", 7);\n        /**\n         * If `true`, all accordions inside of the\n         * accordion group will animate when expanding\n         * or collapsing.\n         */\n        this.animated = true;\n        /**\n         * If `true`, the accordion group cannot be interacted with.\n         */\n        this.disabled = false;\n        /**\n         * If `true`, the accordion group cannot be interacted with,\n         * but does not alter the opacity.\n         */\n        this.readonly = false;\n        /**\n         * Describes the expansion behavior for each accordion.\n         * Possible values are `\"compact\"` and `\"inset\"`.\n         * Defaults to `\"compact\"`.\n         */\n        this.expand = 'compact';\n    }\n    valueChanged() {\n        const { value, multiple } = this;\n        if (!multiple && Array.isArray(value)) {\n            /**\n             * We do some processing on the `value` array so\n             * that it looks more like an array when logged to\n             * the console.\n             * Example given ['a', 'b']\n             * Default toString() behavior: a,b\n             * Custom behavior: ['a', 'b']\n             */\n            printIonWarning(`[ion-accordion-group] - An array of values was passed, but multiple is \"false\". This is incorrect usage and may result in unexpected behaviors. To dismiss this warning, pass a string to the \"value\" property when multiple=\"false\".\n\n  Value Passed: [${value.map((v) => `'${v}'`).join(', ')}]\n`, this.el);\n        }\n        /**\n         * Do not use `value` here as that will be\n         * not account for the adjustment we make above.\n         */\n        this.ionValueChange.emit({ value: this.value });\n    }\n    async disabledChanged() {\n        const { disabled } = this;\n        const accordions = await this.getAccordions();\n        for (const accordion of accordions) {\n            accordion.disabled = disabled;\n        }\n    }\n    async readonlyChanged() {\n        const { readonly } = this;\n        const accordions = await this.getAccordions();\n        for (const accordion of accordions) {\n            accordion.readonly = readonly;\n        }\n    }\n    async onKeydown(ev) {\n        const activeElement = document.activeElement;\n        if (!activeElement) {\n            return;\n        }\n        /**\n         * Make sure focus is in the header, not the body, of the accordion. This ensures\n         * that if there are any interactable elements in the body, their keyboard\n         * interaction doesn't get stolen by the accordion. Example: using up/down keys\n         * in ion-textarea.\n         */\n        const activeAccordionHeader = activeElement.closest('ion-accordion [slot=\"header\"]');\n        if (!activeAccordionHeader) {\n            return;\n        }\n        const accordionEl = activeElement.tagName === 'ION-ACCORDION' ? activeElement : activeElement.closest('ion-accordion');\n        if (!accordionEl) {\n            return;\n        }\n        const closestGroup = accordionEl.closest('ion-accordion-group');\n        if (closestGroup !== this.el) {\n            return;\n        }\n        // If the active accordion is not in the current array of accordions, do not do anything\n        const accordions = await this.getAccordions();\n        const startingIndex = accordions.findIndex((a) => a === accordionEl);\n        if (startingIndex === -1) {\n            return;\n        }\n        let accordion;\n        if (ev.key === 'ArrowDown') {\n            accordion = this.findNextAccordion(accordions, startingIndex);\n        }\n        else if (ev.key === 'ArrowUp') {\n            accordion = this.findPreviousAccordion(accordions, startingIndex);\n        }\n        else if (ev.key === 'Home') {\n            accordion = accordions[0];\n        }\n        else if (ev.key === 'End') {\n            accordion = accordions[accordions.length - 1];\n        }\n        if (accordion !== undefined && accordion !== activeElement) {\n            accordion.focus();\n        }\n    }\n    async componentDidLoad() {\n        if (this.disabled) {\n            this.disabledChanged();\n        }\n        if (this.readonly) {\n            this.readonlyChanged();\n        }\n        /**\n         * When binding values in frameworks such as Angular\n         * it is possible for the value to be set after the Web Component\n         * initializes but before the value watcher is set up in Stencil.\n         * As a result, the watcher callback may not be fired.\n         * We work around this by manually calling the watcher\n         * callback when the component has loaded and the watcher\n         * is configured.\n         */\n        this.valueChanged();\n    }\n    /**\n     * Sets the value property and emits ionChange.\n     * This should only be called when the user interacts\n     * with the accordion and not for any update\n     * to the value property. The exception is when\n     * the app sets the value of a single-select\n     * accordion group to an array.\n     */\n    setValue(accordionValue) {\n        const value = (this.value = accordionValue);\n        this.ionChange.emit({ value });\n    }\n    /**\n     * This method is used to ensure that the value\n     * of ion-accordion-group is being set in a valid\n     * way. This method should only be called in\n     * response to a user generated action.\n     * @internal\n     */\n    async requestAccordionToggle(accordionValue, accordionExpand) {\n        const { multiple, value, readonly, disabled } = this;\n        if (readonly || disabled) {\n            return;\n        }\n        if (accordionExpand) {\n            /**\n             * If group accepts multiple values\n             * check to see if value is already in\n             * in values array. If not, add it\n             * to the array.\n             */\n            if (multiple) {\n                const groupValue = value !== null && value !== void 0 ? value : [];\n                const processedValue = Array.isArray(groupValue) ? groupValue : [groupValue];\n                const valueExists = processedValue.find((v) => v === accordionValue);\n                if (valueExists === undefined && accordionValue !== undefined) {\n                    this.setValue([...processedValue, accordionValue]);\n                }\n            }\n            else {\n                this.setValue(accordionValue);\n            }\n        }\n        else {\n            /**\n             * If collapsing accordion, either filter the value\n             * out of the values array or unset the value.\n             */\n            if (multiple) {\n                const groupValue = value !== null && value !== void 0 ? value : [];\n                const processedValue = Array.isArray(groupValue) ? groupValue : [groupValue];\n                this.setValue(processedValue.filter((v) => v !== accordionValue));\n            }\n            else {\n                this.setValue(undefined);\n            }\n        }\n    }\n    findNextAccordion(accordions, startingIndex) {\n        const nextAccordion = accordions[startingIndex + 1];\n        if (nextAccordion === undefined) {\n            return accordions[0];\n        }\n        return nextAccordion;\n    }\n    findPreviousAccordion(accordions, startingIndex) {\n        const prevAccordion = accordions[startingIndex - 1];\n        if (prevAccordion === undefined) {\n            return accordions[accordions.length - 1];\n        }\n        return prevAccordion;\n    }\n    /**\n     * @internal\n     */\n    async getAccordions() {\n        return Array.from(this.el.querySelectorAll(':scope > ion-accordion'));\n    }\n    render() {\n        const { disabled, readonly, expand } = this;\n        const mode = getIonMode(this);\n        return (h(Host, { key: 'd1a79a93179474fbba66fcf11a92f4871dacc975', class: {\n                [mode]: true,\n                'accordion-group-disabled': disabled,\n                'accordion-group-readonly': readonly,\n                [`accordion-group-expand-${expand}`]: true,\n            }, role: \"presentation\" }, h(\"slot\", { key: 'e6b8954b686d1fbb4fc92adb07fddc97a24b0a31' })));\n    }\n    get el() { return getElement(this); }\n    static get watchers() { return {\n        \"value\": [\"valueChanged\"],\n        \"disabled\": [\"disabledChanged\"],\n        \"readonly\": [\"readonlyChanged\"]\n    }; }\n};\nAccordionGroup.style = {\n    ios: accordionGroupIosCss,\n    md: accordionGroupMdCss\n};\n\nexport { Accordion as ion_accordion, AccordionGroup as ion_accordion_group };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;AAOA,IAAM,kBAAkB;AAExB,IAAM,iBAAiB;AAEvB,IAAM,YAAY,MAAM;AAAA,EACpB,YAAY,SAAS;AACjB,qBAAiB,MAAM,OAAO;AAC9B,SAAK,iBAAiB,MAAM,KAAK,YAAY,KAAK;AAClD,SAAK,QAAQ;AACb,SAAK,SAAS;AACd,SAAK,aAAa;AAKlB,SAAK,QAAQ,iBAAiB,cAAc;AAI5C,SAAK,WAAW;AAKhB,SAAK,WAAW;AAMhB,SAAK,aAAa;AAKlB,SAAK,iBAAiB;AACtB,SAAK,kBAAkB,MAAM;AACzB,YAAM,UAAU,KAAK,wBAAwB;AAC7C,UAAI,CAAC,SAAS;AACV;AAAA,MACJ;AAOA,cAAQ,SAAS;AACjB,cAAQ,SAAS;AAOjB,UAAI,QAAQ,UAAU,QAAW;AAC7B,gBAAQ,QAAQ;AAAA,MACpB;AAAA,IACJ;AACA,SAAK,0BAA0B,MAAM;AACjC,YAAM,EAAE,SAAS,IAAI;AACrB,UAAI,CAAC,UAAU;AACX;AAAA,MACJ;AAKA,YAAM,OAAO,SAAS,cAAc,MAAM;AAC1C,UAAI,CAAC,MAAM;AACP;AAAA,MACJ;AAEA,UAAI,KAAK,qBAAqB;AAC1B;AACJ,aAAO,KAAK,iBAAiB,EAAE,KAAK,CAAC,OAAO,GAAG,YAAY,UAAU;AAAA,IACzE;AACA,SAAK,UAAU,CAAC,WAAW,UAAU;AACjC,YAAM,UAAU,KAAK,wBAAwB;AAC7C,UAAI,CAAC,SAAS;AACV;AAAA,MACJ;AAKA,YAAM,OAAO,eAAe,OAAO;AACnC,YAAM,SAAS,KAAK,cAAc,QAAQ;AAC1C,UAAI,CAAC,QAAQ;AACT;AAAA,MACJ;AACA,aAAO,aAAa,iBAAiB,GAAG,QAAQ,EAAE;AAAA,IACtD;AACA,SAAK,iBAAiB,MAAM;AACxB,YAAM,UAAU,KAAK,wBAAwB;AAC7C,UAAI,CAAC,SAAS;AACV;AAAA,MACJ;AACA,YAAM,EAAE,gBAAgB,WAAW,IAAI;AAKvC,YAAM,qBAAqB,QAAQ,cAAc,4BAA4B;AAC7E,UAAI,oBAAoB;AACpB;AAAA,MACJ;AACA,YAAM,SAAS,SAAS,cAAc,UAAU;AAChD,aAAO,OAAO;AACd,aAAO,OAAO;AACd,aAAO,UAAU,IAAI,2BAA2B;AAChD,aAAO,OAAO;AACd,aAAO,aAAa,eAAe,MAAM;AACzC,cAAQ,YAAY,MAAM;AAAA,IAC9B;AACA,SAAK,kBAAkB,CAAC,gBAAgB,UAAU;AAC9C,YAAM,EAAE,WAAW,iBAAiB,IAAI;AACxC,UAAI,iBAAiB,cAAc,UAAa,qBAAqB,QAAW;AAC5E,aAAK,QAAQ;AACb;AAAA,MACJ;AACA,UAAI,KAAK,UAAU,GAAiC;AAChD;AAAA,MACJ;AACA,UAAI,KAAK,eAAe,QAAW;AAC/B,6BAAqB,KAAK,UAAU;AAAA,MACxC;AACA,UAAI,KAAK,cAAc,GAAG;AACtB,YAAI,MAAM;AACN,eAAK,QAAQ;AACb,eAAK,aAAa,IAAI,MAAY;AAC9B,kBAAM,gBAAgB,iBAAiB;AACvC,kBAAM,oBAAoB,mBAAmB,WAAW,GAAI;AAC5D,sBAAU,MAAM,YAAY,cAAc,GAAG,aAAa,IAAI;AAC9D,kBAAM;AACN,iBAAK,QAAQ;AACb,sBAAU,MAAM,eAAe,YAAY;AAAA,UAC/C,EAAC;AAAA,QACL,CAAC;AAAA,MACL,OACK;AACD,aAAK,QAAQ;AAAA,MACjB;AAAA,IACJ;AACA,SAAK,oBAAoB,CAAC,gBAAgB,UAAU;AAChD,YAAM,EAAE,UAAU,IAAI;AACtB,UAAI,iBAAiB,cAAc,QAAW;AAC1C,aAAK,QAAQ;AACb;AAAA,MACJ;AACA,UAAI,KAAK,UAAU,GAAkC;AACjD;AAAA,MACJ;AACA,UAAI,KAAK,eAAe,QAAW;AAC/B,6BAAqB,KAAK,UAAU;AAAA,MACxC;AACA,UAAI,KAAK,cAAc,GAAG;AACtB,aAAK,aAAa,IAAI,MAAY;AAC9B,gBAAM,gBAAgB,UAAU;AAChC,oBAAU,MAAM,YAAY,cAAc,GAAG,aAAa,IAAI;AAC9D,cAAI,MAAY;AACZ,kBAAM,oBAAoB,mBAAmB,WAAW,GAAI;AAC5D,iBAAK,QAAQ;AACb,kBAAM;AACN,iBAAK,QAAQ;AACb,sBAAU,MAAM,eAAe,YAAY;AAAA,UAC/C,EAAC;AAAA,QACL,EAAC;AAAA,MACL,OACK;AACD,aAAK,QAAQ;AAAA,MACjB;AAAA,IACJ;AAQA,SAAK,gBAAgB,MAAM;AACvB,UAAI,OAAO,WAAW,aAAa;AAC/B,eAAO;AAAA,MACX;AACA,YAAM,uBAAuB,WAAW,kCAAkC,EAAE;AAC5E,UAAI,sBAAsB;AACtB,eAAO;AAAA,MACX;AACA,YAAM,WAAW,OAAO,IAAI,YAAY,IAAI;AAC5C,UAAI,CAAC,UAAU;AACX,eAAO;AAAA,MACX;AACA,UAAI,KAAK,oBAAoB,CAAC,KAAK,iBAAiB,UAAU;AAC1D,eAAO;AAAA,MACX;AACA,aAAO;AAAA,IACX;AACA,SAAK,cAAc,CAAO,gBAAgB,UAAU;AAChD,YAAM,iBAAiB,KAAK;AAC5B,YAAM,iBAAiB,KAAK;AAC5B,UAAI,CAAC,gBAAgB;AACjB;AAAA,MACJ;AACA,YAAM,QAAQ,eAAe;AAC7B,YAAM,eAAe,MAAM,QAAQ,KAAK,IAAI,MAAM,SAAS,cAAc,IAAI,UAAU;AACvF,UAAI,cAAc;AACd,aAAK,gBAAgB,aAAa;AAClC,aAAK,SAAS,KAAK,aAAa;AAAA,MACpC,OACK;AACD,aAAK,kBAAkB,aAAa;AAQpC,cAAM,gBAAgB,KAAK,eAAe;AAC1C,cAAM,qBAAqB,kBAAkB,QAAQ,kBAAkB,SAAS,SAAS,cAAc;AACvG,YAAI,uBAAuB,QAAW;AAClC,eAAK,aAAa,MAAM,QAAQ,KAAK,IAAI,MAAM,SAAS,kBAAkB,IAAI,UAAU;AAAA,QAC5F;AACA,cAAM,oBAAoB,KAAK,mBAAmB;AAClD,cAAM,yBAAyB,sBAAsB,QAAQ,sBAAsB,SAAS,SAAS,kBAAkB;AACvH,YAAI,2BAA2B,QAAW;AACtC,eAAK,SAAS,MAAM,QAAQ,KAAK,IAAI,MAAM,SAAS,sBAAsB,IAAI,UAAU;AAAA,QAC5F;AAAA,MACJ;AAAA,IACJ;AACA,SAAK,iBAAiB,MAAM;AACxB,UAAI,CAAC,KAAK,IAAI;AACV;AAAA,MACJ;AACA,YAAM,cAAc,KAAK,GAAG;AAC5B,WAAK,gBAAgB,QAAQ,gBAAgB,SAAS,SAAS,YAAY,aAAa,iBAAiB;AACrG;AAAA,MACJ;AACA,aAAO;AAAA,IACX;AACA,SAAK,qBAAqB,MAAM;AAC5B,UAAI,CAAC,KAAK,IAAI;AACV;AAAA,MACJ;AACA,YAAM,kBAAkB,KAAK,GAAG;AAChC,WAAK,oBAAoB,QAAQ,oBAAoB,SAAS,SAAS,gBAAgB,aAAa,iBAAiB;AACjH;AAAA,MACJ;AACA,aAAO;AAAA,IACX;AAAA,EACJ;AAAA,EACA,eAAe;AACX,SAAK,YAAY;AAAA,EACrB;AAAA,EACA,oBAAoB;AAChB,QAAI;AACJ,UAAM,mBAAoB,KAAK,oBAAoB,KAAK,KAAK,QAAQ,QAAQ,OAAO,SAAS,SAAS,GAAG,QAAQ,qBAAqB;AACtI,QAAI,kBAAkB;AAClB,WAAK,YAAY,IAAI;AACrB,uBAAiB,kBAAkB,kBAAkB,KAAK,cAAc;AAAA,IAC5E;AAAA,EACJ;AAAA,EACA,uBAAuB;AACnB,UAAM,mBAAmB,KAAK;AAC9B,QAAI,kBAAkB;AAClB,0BAAoB,kBAAkB,kBAAkB,KAAK,cAAc;AAAA,IAC/E;AAAA,EACJ;AAAA,EACA,mBAAmB;AACf,SAAK,gBAAgB;AACrB,SAAK,eAAe;AAMpB,QAAI,MAAM;AAKN,YAAM,WAAW,KAAK,UAAU,KAAmC,KAAK,UAAU;AAClF,WAAK,QAAQ,QAAQ;AAAA,IACzB,CAAC;AAAA,EACL;AAAA,EACA,iBAAiB;AACb,UAAM,EAAE,kBAAkB,UAAU,UAAU,OAAO,MAAM,IAAI;AAC/D,QAAI,YAAY;AACZ;AACJ,QAAI,kBAAkB;AASlB,YAAM,SAAS,UAAU,KAAoC,UAAU;AACvE,uBAAiB,uBAAuB,OAAO,MAAM;AAAA,IACzD;AAAA,EACJ;AAAA,EACA,SAAS;AACL,UAAM,EAAE,UAAU,SAAS,IAAI;AAC/B,UAAM,OAAO,WAAW,IAAI;AAC5B,UAAM,WAAW,KAAK,UAAU,KAAmC,KAAK,UAAU;AAClF,UAAM,aAAa,WAAW,oBAAoB;AAClD,UAAM,cAAc,WAAW,qBAAqB;AACpD,SAAK,QAAQ,QAAQ;AACrB,WAAQ,EAAE,MAAM,EAAE,KAAK,4CAA4C,OAAO;AAAA,MAClE,CAAC,IAAI,GAAG;AAAA,MACR,uBAAuB,KAAK,UAAU;AAAA,MACtC,sBAAsB,KAAK,UAAU;AAAA,MACrC,wBAAwB,KAAK,UAAU;AAAA,MACvC,uBAAuB,KAAK,UAAU;AAAA,MACtC,kBAAkB,KAAK;AAAA,MACvB,sBAAsB,KAAK;AAAA,MAC3B,sBAAsB;AAAA,MACtB,sBAAsB;AAAA,MACtB,sBAAsB,KAAK,cAAc;AAAA,IAC7C,EAAE,GAAG,EAAE,OAAO,EAAE,KAAK,4CAA4C,SAAS,MAAM,KAAK,eAAe,GAAG,IAAI,UAAU,MAAM,YAAY,iBAAiB,WAAW,KAAK,CAAC,aAAc,KAAK,WAAW,SAAU,GAAG,EAAE,QAAQ,EAAE,KAAK,4CAA4C,MAAM,SAAS,CAAC,CAAC,GAAG,EAAE,OAAO,EAAE,KAAK,4CAA4C,IAAI,WAAW,MAAM,aAAa,MAAM,UAAU,mBAAmB,UAAU,KAAK,CAAC,cAAe,KAAK,YAAY,UAAW,GAAG,EAAE,OAAO,EAAE,KAAK,4CAA4C,IAAI,mBAAmB,KAAK,CAAC,qBAAsB,KAAK,mBAAmB,iBAAkB,GAAG,EAAE,QAAQ,EAAE,KAAK,4CAA4C,MAAM,UAAU,CAAC,CAAC,CAAC,CAAC;AAAA,EACztB;AAAA,EACA,WAAW,iBAAiB;AAAE,WAAO;AAAA,EAAM;AAAA,EAC3C,IAAI,KAAK;AAAE,WAAO,WAAW,IAAI;AAAA,EAAG;AAAA,EACpC,WAAW,WAAW;AAAE,WAAO;AAAA,MAC3B,SAAS,CAAC,cAAc;AAAA,IAC5B;AAAA,EAAG;AACP;AACA,IAAI,eAAe;AACnB,UAAU,QAAQ;AAAA,EACd,KAAK;AAAA,EACL,IAAI;AACR;AAEA,IAAM,uBAAuB;AAE7B,IAAM,sBAAsB;AAE5B,IAAM,iBAAiB,MAAM;AAAA,EACzB,YAAY,SAAS;AACjB,qBAAiB,MAAM,OAAO;AAC9B,SAAK,YAAY,YAAY,MAAM,aAAa,CAAC;AACjD,SAAK,iBAAiB,YAAY,MAAM,kBAAkB,CAAC;AAM3D,SAAK,WAAW;AAIhB,SAAK,WAAW;AAKhB,SAAK,WAAW;AAMhB,SAAK,SAAS;AAAA,EAClB;AAAA,EACA,eAAe;AACX,UAAM,EAAE,OAAO,SAAS,IAAI;AAC5B,QAAI,CAAC,YAAY,MAAM,QAAQ,KAAK,GAAG;AASnC,sBAAgB;AAAA;AAAA,mBAET,MAAM,IAAI,CAAC,MAAM,IAAI,CAAC,GAAG,EAAE,KAAK,IAAI,CAAC;AAAA,GACrD,KAAK,EAAE;AAAA,IACF;AAKA,SAAK,eAAe,KAAK,EAAE,OAAO,KAAK,MAAM,CAAC;AAAA,EAClD;AAAA,EACM,kBAAkB;AAAA;AACpB,YAAM,EAAE,SAAS,IAAI;AACrB,YAAM,aAAa,MAAM,KAAK,cAAc;AAC5C,iBAAW,aAAa,YAAY;AAChC,kBAAU,WAAW;AAAA,MACzB;AAAA,IACJ;AAAA;AAAA,EACM,kBAAkB;AAAA;AACpB,YAAM,EAAE,SAAS,IAAI;AACrB,YAAM,aAAa,MAAM,KAAK,cAAc;AAC5C,iBAAW,aAAa,YAAY;AAChC,kBAAU,WAAW;AAAA,MACzB;AAAA,IACJ;AAAA;AAAA,EACM,UAAU,IAAI;AAAA;AAChB,YAAM,gBAAgB,SAAS;AAC/B,UAAI,CAAC,eAAe;AAChB;AAAA,MACJ;AAOA,YAAM,wBAAwB,cAAc,QAAQ,+BAA+B;AACnF,UAAI,CAAC,uBAAuB;AACxB;AAAA,MACJ;AACA,YAAM,cAAc,cAAc,YAAY,kBAAkB,gBAAgB,cAAc,QAAQ,eAAe;AACrH,UAAI,CAAC,aAAa;AACd;AAAA,MACJ;AACA,YAAM,eAAe,YAAY,QAAQ,qBAAqB;AAC9D,UAAI,iBAAiB,KAAK,IAAI;AAC1B;AAAA,MACJ;AAEA,YAAM,aAAa,MAAM,KAAK,cAAc;AAC5C,YAAM,gBAAgB,WAAW,UAAU,CAAC,MAAM,MAAM,WAAW;AACnE,UAAI,kBAAkB,IAAI;AACtB;AAAA,MACJ;AACA,UAAI;AACJ,UAAI,GAAG,QAAQ,aAAa;AACxB,oBAAY,KAAK,kBAAkB,YAAY,aAAa;AAAA,MAChE,WACS,GAAG,QAAQ,WAAW;AAC3B,oBAAY,KAAK,sBAAsB,YAAY,aAAa;AAAA,MACpE,WACS,GAAG,QAAQ,QAAQ;AACxB,oBAAY,WAAW,CAAC;AAAA,MAC5B,WACS,GAAG,QAAQ,OAAO;AACvB,oBAAY,WAAW,WAAW,SAAS,CAAC;AAAA,MAChD;AACA,UAAI,cAAc,UAAa,cAAc,eAAe;AACxD,kBAAU,MAAM;AAAA,MACpB;AAAA,IACJ;AAAA;AAAA,EACM,mBAAmB;AAAA;AACrB,UAAI,KAAK,UAAU;AACf,aAAK,gBAAgB;AAAA,MACzB;AACA,UAAI,KAAK,UAAU;AACf,aAAK,gBAAgB;AAAA,MACzB;AAUA,WAAK,aAAa;AAAA,IACtB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASA,SAAS,gBAAgB;AACrB,UAAM,QAAS,KAAK,QAAQ;AAC5B,SAAK,UAAU,KAAK,EAAE,MAAM,CAAC;AAAA,EACjC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQM,uBAAuB,gBAAgB,iBAAiB;AAAA;AAC1D,YAAM,EAAE,UAAU,OAAO,UAAU,SAAS,IAAI;AAChD,UAAI,YAAY,UAAU;AACtB;AAAA,MACJ;AACA,UAAI,iBAAiB;AAOjB,YAAI,UAAU;AACV,gBAAM,aAAa,UAAU,QAAQ,UAAU,SAAS,QAAQ,CAAC;AACjE,gBAAM,iBAAiB,MAAM,QAAQ,UAAU,IAAI,aAAa,CAAC,UAAU;AAC3E,gBAAM,cAAc,eAAe,KAAK,CAAC,MAAM,MAAM,cAAc;AACnE,cAAI,gBAAgB,UAAa,mBAAmB,QAAW;AAC3D,iBAAK,SAAS,CAAC,GAAG,gBAAgB,cAAc,CAAC;AAAA,UACrD;AAAA,QACJ,OACK;AACD,eAAK,SAAS,cAAc;AAAA,QAChC;AAAA,MACJ,OACK;AAKD,YAAI,UAAU;AACV,gBAAM,aAAa,UAAU,QAAQ,UAAU,SAAS,QAAQ,CAAC;AACjE,gBAAM,iBAAiB,MAAM,QAAQ,UAAU,IAAI,aAAa,CAAC,UAAU;AAC3E,eAAK,SAAS,eAAe,OAAO,CAAC,MAAM,MAAM,cAAc,CAAC;AAAA,QACpE,OACK;AACD,eAAK,SAAS,MAAS;AAAA,QAC3B;AAAA,MACJ;AAAA,IACJ;AAAA;AAAA,EACA,kBAAkB,YAAY,eAAe;AACzC,UAAM,gBAAgB,WAAW,gBAAgB,CAAC;AAClD,QAAI,kBAAkB,QAAW;AAC7B,aAAO,WAAW,CAAC;AAAA,IACvB;AACA,WAAO;AAAA,EACX;AAAA,EACA,sBAAsB,YAAY,eAAe;AAC7C,UAAM,gBAAgB,WAAW,gBAAgB,CAAC;AAClD,QAAI,kBAAkB,QAAW;AAC7B,aAAO,WAAW,WAAW,SAAS,CAAC;AAAA,IAC3C;AACA,WAAO;AAAA,EACX;AAAA;AAAA;AAAA;AAAA,EAIM,gBAAgB;AAAA;AAClB,aAAO,MAAM,KAAK,KAAK,GAAG,iBAAiB,wBAAwB,CAAC;AAAA,IACxE;AAAA;AAAA,EACA,SAAS;AACL,UAAM,EAAE,UAAU,UAAU,OAAO,IAAI;AACvC,UAAM,OAAO,WAAW,IAAI;AAC5B,WAAQ,EAAE,MAAM,EAAE,KAAK,4CAA4C,OAAO;AAAA,MAClE,CAAC,IAAI,GAAG;AAAA,MACR,4BAA4B;AAAA,MAC5B,4BAA4B;AAAA,MAC5B,CAAC,0BAA0B,MAAM,EAAE,GAAG;AAAA,IAC1C,GAAG,MAAM,eAAe,GAAG,EAAE,QAAQ,EAAE,KAAK,2CAA2C,CAAC,CAAC;AAAA,EACjG;AAAA,EACA,IAAI,KAAK;AAAE,WAAO,WAAW,IAAI;AAAA,EAAG;AAAA,EACpC,WAAW,WAAW;AAAE,WAAO;AAAA,MAC3B,SAAS,CAAC,cAAc;AAAA,MACxB,YAAY,CAAC,iBAAiB;AAAA,MAC9B,YAAY,CAAC,iBAAiB;AAAA,IAClC;AAAA,EAAG;AACP;AACA,eAAe,QAAQ;AAAA,EACnB,KAAK;AAAA,EACL,IAAI;AACR;", "names": []}