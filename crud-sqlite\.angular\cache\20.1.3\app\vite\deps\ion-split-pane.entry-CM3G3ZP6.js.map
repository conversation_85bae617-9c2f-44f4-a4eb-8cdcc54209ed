{"version": 3, "sources": ["../../../../../../node_modules/@ionic/core/dist/esm/ion-split-pane.entry.js"], "sourcesContent": ["/*!\n * (C) Ionic http://ionicframework.com - MIT License\n */\nimport { r as registerInstance, d as createEvent, m as printIonWarning, e as getIonMode, h, j as Host, k as getElement } from './index-B_U9CtaY.js';\n\nconst splitPaneIosCss = \":host{--side-width:100%;left:0;right:0;top:0;bottom:0;display:-ms-flexbox;display:flex;position:absolute;-ms-flex-direction:row;flex-direction:row;-ms-flex-wrap:nowrap;flex-wrap:nowrap;contain:strict}:host(.split-pane-visible) ::slotted(.split-pane-main){left:0;right:0;top:0;bottom:0;position:relative;-ms-flex:1;flex:1;-webkit-box-shadow:none;box-shadow:none;overflow:hidden;z-index:0}::slotted(.split-pane-side:not(ion-menu)){display:none}:host{--border:0.55px solid var(--ion-item-border-color, var(--ion-border-color, var(--ion-color-step-250, var(--ion-background-color-step-250, #c8c7cc))));--side-min-width:270px;--side-max-width:28%}\";\n\nconst splitPaneMdCss = \":host{--side-width:100%;left:0;right:0;top:0;bottom:0;display:-ms-flexbox;display:flex;position:absolute;-ms-flex-direction:row;flex-direction:row;-ms-flex-wrap:nowrap;flex-wrap:nowrap;contain:strict}:host(.split-pane-visible) ::slotted(.split-pane-main){left:0;right:0;top:0;bottom:0;position:relative;-ms-flex:1;flex:1;-webkit-box-shadow:none;box-shadow:none;overflow:hidden;z-index:0}::slotted(.split-pane-side:not(ion-menu)){display:none}:host{--border:1px solid var(--ion-item-border-color, var(--ion-border-color, var(--ion-color-step-150, var(--ion-background-color-step-150, rgba(0, 0, 0, 0.13)))));--side-min-width:270px;--side-max-width:28%}\";\n\n// TODO(FW-2832): types\nconst SPLIT_PANE_MAIN = 'split-pane-main';\nconst SPLIT_PANE_SIDE = 'split-pane-side';\nconst QUERY = {\n    xs: '(min-width: 0px)',\n    sm: '(min-width: 576px)',\n    md: '(min-width: 768px)',\n    lg: '(min-width: 992px)',\n    xl: '(min-width: 1200px)',\n    never: '',\n};\nconst SplitPane = class {\n    constructor(hostRef) {\n        registerInstance(this, hostRef);\n        this.ionSplitPaneVisible = createEvent(this, \"ionSplitPaneVisible\", 7);\n        this.visible = false;\n        /**\n         * If `true`, the split pane will be hidden.\n         */\n        this.disabled = false;\n        /**\n         * When the split-pane should be shown.\n         * Can be a CSS media query expression, or a shortcut expression.\n         * Can also be a boolean expression.\n         */\n        this.when = QUERY['lg'];\n    }\n    visibleChanged(visible) {\n        this.ionSplitPaneVisible.emit({ visible });\n    }\n    /**\n     * @internal\n     */\n    async isVisible() {\n        return Promise.resolve(this.visible);\n    }\n    async connectedCallback() {\n        // TODO: connectedCallback is fired in CE build\n        // before WC is defined. This needs to be fixed in Stencil.\n        if (typeof customElements !== 'undefined' && customElements != null) {\n            await customElements.whenDefined('ion-split-pane');\n        }\n        this.styleMainElement();\n        this.updateState();\n    }\n    disconnectedCallback() {\n        if (this.rmL) {\n            this.rmL();\n            this.rmL = undefined;\n        }\n    }\n    updateState() {\n        if (this.rmL) {\n            this.rmL();\n            this.rmL = undefined;\n        }\n        // Check if the split-pane is disabled\n        if (this.disabled) {\n            this.visible = false;\n            return;\n        }\n        // When query is a boolean\n        const query = this.when;\n        if (typeof query === 'boolean') {\n            this.visible = query;\n            return;\n        }\n        // When query is a string, let's find first if it is a shortcut\n        const mediaQuery = QUERY[query] || query;\n        // Media query is empty or null, we hide it\n        if (mediaQuery.length === 0) {\n            this.visible = false;\n            return;\n        }\n        // Listen on media query\n        const callback = (q) => {\n            this.visible = q.matches;\n        };\n        const mediaList = window.matchMedia(mediaQuery);\n        // TODO FW-5869\n        mediaList.addListener(callback);\n        this.rmL = () => mediaList.removeListener(callback);\n        this.visible = mediaList.matches;\n    }\n    /**\n     * Attempt to find the main content\n     * element inside of the split pane.\n     * If found, set it as the main node.\n     *\n     * We assume that the main node\n     * is available in the DOM on split\n     * pane load.\n     */\n    styleMainElement() {\n        const contentId = this.contentId;\n        const children = this.el.children;\n        const nu = this.el.childElementCount;\n        let foundMain = false;\n        for (let i = 0; i < nu; i++) {\n            const child = children[i];\n            const isMain = contentId !== undefined && child.id === contentId;\n            if (isMain) {\n                if (foundMain) {\n                    printIonWarning('[ion-split-pane] - Cannot have more than one main node.');\n                    return;\n                }\n                else {\n                    setPaneClass(child, isMain);\n                    foundMain = true;\n                }\n            }\n        }\n        if (!foundMain) {\n            printIonWarning('[ion-split-pane] - Does not have a specified main node.');\n        }\n    }\n    render() {\n        const mode = getIonMode(this);\n        return (h(Host, { key: 'd5e30df12f1f1f855da4c66f98076b9dce762c59', class: {\n                [mode]: true,\n                // Used internally for styling\n                [`split-pane-${mode}`]: true,\n                'split-pane-visible': this.visible,\n            } }, h(\"slot\", { key: '3e30d7cf3bc1cf434e16876a0cb2a36377b8e00f' })));\n    }\n    get el() { return getElement(this); }\n    static get watchers() { return {\n        \"visible\": [\"visibleChanged\"],\n        \"disabled\": [\"updateState\"],\n        \"when\": [\"updateState\"]\n    }; }\n};\nconst setPaneClass = (el, isMain) => {\n    let toAdd;\n    let toRemove;\n    if (isMain) {\n        toAdd = SPLIT_PANE_MAIN;\n        toRemove = SPLIT_PANE_SIDE;\n    }\n    else {\n        toAdd = SPLIT_PANE_SIDE;\n        toRemove = SPLIT_PANE_MAIN;\n    }\n    const classList = el.classList;\n    classList.add(toAdd);\n    classList.remove(toRemove);\n};\nSplitPane.style = {\n    ios: splitPaneIosCss,\n    md: splitPaneMdCss\n};\n\nexport { SplitPane as ion_split_pane };\n"], "mappings": ";;;;;;;;;;;;;;AAKA,IAAM,kBAAkB;AAExB,IAAM,iBAAiB;AAGvB,IAAM,kBAAkB;AACxB,IAAM,kBAAkB;AACxB,IAAM,QAAQ;AAAA,EACV,IAAI;AAAA,EACJ,IAAI;AAAA,EACJ,IAAI;AAAA,EACJ,IAAI;AAAA,EACJ,IAAI;AAAA,EACJ,OAAO;AACX;AACA,IAAM,YAAY,MAAM;AAAA,EACpB,YAAY,SAAS;AACjB,qBAAiB,MAAM,OAAO;AAC9B,SAAK,sBAAsB,YAAY,MAAM,uBAAuB,CAAC;AACrE,SAAK,UAAU;AAIf,SAAK,WAAW;AAMhB,SAAK,OAAO,MAAM,IAAI;AAAA,EAC1B;AAAA,EACA,eAAe,SAAS;AACpB,SAAK,oBAAoB,KAAK,EAAE,QAAQ,CAAC;AAAA,EAC7C;AAAA;AAAA;AAAA;AAAA,EAIM,YAAY;AAAA;AACd,aAAO,QAAQ,QAAQ,KAAK,OAAO;AAAA,IACvC;AAAA;AAAA,EACM,oBAAoB;AAAA;AAGtB,UAAI,OAAO,mBAAmB,eAAe,kBAAkB,MAAM;AACjE,cAAM,eAAe,YAAY,gBAAgB;AAAA,MACrD;AACA,WAAK,iBAAiB;AACtB,WAAK,YAAY;AAAA,IACrB;AAAA;AAAA,EACA,uBAAuB;AACnB,QAAI,KAAK,KAAK;AACV,WAAK,IAAI;AACT,WAAK,MAAM;AAAA,IACf;AAAA,EACJ;AAAA,EACA,cAAc;AACV,QAAI,KAAK,KAAK;AACV,WAAK,IAAI;AACT,WAAK,MAAM;AAAA,IACf;AAEA,QAAI,KAAK,UAAU;AACf,WAAK,UAAU;AACf;AAAA,IACJ;AAEA,UAAM,QAAQ,KAAK;AACnB,QAAI,OAAO,UAAU,WAAW;AAC5B,WAAK,UAAU;AACf;AAAA,IACJ;AAEA,UAAM,aAAa,MAAM,KAAK,KAAK;AAEnC,QAAI,WAAW,WAAW,GAAG;AACzB,WAAK,UAAU;AACf;AAAA,IACJ;AAEA,UAAM,WAAW,CAAC,MAAM;AACpB,WAAK,UAAU,EAAE;AAAA,IACrB;AACA,UAAM,YAAY,OAAO,WAAW,UAAU;AAE9C,cAAU,YAAY,QAAQ;AAC9B,SAAK,MAAM,MAAM,UAAU,eAAe,QAAQ;AAClD,SAAK,UAAU,UAAU;AAAA,EAC7B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAUA,mBAAmB;AACf,UAAM,YAAY,KAAK;AACvB,UAAM,WAAW,KAAK,GAAG;AACzB,UAAM,KAAK,KAAK,GAAG;AACnB,QAAI,YAAY;AAChB,aAAS,IAAI,GAAG,IAAI,IAAI,KAAK;AACzB,YAAM,QAAQ,SAAS,CAAC;AACxB,YAAM,SAAS,cAAc,UAAa,MAAM,OAAO;AACvD,UAAI,QAAQ;AACR,YAAI,WAAW;AACX,0BAAgB,yDAAyD;AACzE;AAAA,QACJ,OACK;AACD,uBAAa,OAAO,MAAM;AAC1B,sBAAY;AAAA,QAChB;AAAA,MACJ;AAAA,IACJ;AACA,QAAI,CAAC,WAAW;AACZ,sBAAgB,yDAAyD;AAAA,IAC7E;AAAA,EACJ;AAAA,EACA,SAAS;AACL,UAAM,OAAO,WAAW,IAAI;AAC5B,WAAQ,EAAE,MAAM,EAAE,KAAK,4CAA4C,OAAO;AAAA,MAClE,CAAC,IAAI,GAAG;AAAA;AAAA,MAER,CAAC,cAAc,IAAI,EAAE,GAAG;AAAA,MACxB,sBAAsB,KAAK;AAAA,IAC/B,EAAE,GAAG,EAAE,QAAQ,EAAE,KAAK,2CAA2C,CAAC,CAAC;AAAA,EAC3E;AAAA,EACA,IAAI,KAAK;AAAE,WAAO,WAAW,IAAI;AAAA,EAAG;AAAA,EACpC,WAAW,WAAW;AAAE,WAAO;AAAA,MAC3B,WAAW,CAAC,gBAAgB;AAAA,MAC5B,YAAY,CAAC,aAAa;AAAA,MAC1B,QAAQ,CAAC,aAAa;AAAA,IAC1B;AAAA,EAAG;AACP;AACA,IAAM,eAAe,CAAC,IAAI,WAAW;AACjC,MAAI;AACJ,MAAI;AACJ,MAAI,QAAQ;AACR,YAAQ;AACR,eAAW;AAAA,EACf,OACK;AACD,YAAQ;AACR,eAAW;AAAA,EACf;AACA,QAAM,YAAY,GAAG;AACrB,YAAU,IAAI,KAAK;AACnB,YAAU,OAAO,QAAQ;AAC7B;AACA,UAAU,QAAQ;AAAA,EACd,KAAK;AAAA,EACL,IAAI;AACR;", "names": []}