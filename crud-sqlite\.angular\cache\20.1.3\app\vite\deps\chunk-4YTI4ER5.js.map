{"version": 3, "sources": ["../../../../../../node_modules/@ionic/core/dist/esm/compare-with-utils-sObYyvOy.js"], "sourcesContent": ["/*!\n * (C) Ionic http://ionicframework.com - MIT License\n */\n/**\n * Uses the compareWith param to compare two values to determine if they are equal.\n *\n * @param currentValue The current value of the control.\n * @param compareValue The value to compare against.\n * @param compareWith The function or property name to use to compare values.\n */\nconst compareOptions = (currentValue, compareValue, compareWith) => {\n    if (typeof compareWith === 'function') {\n        return compareWith(currentValue, compareValue);\n    }\n    else if (typeof compareWith === 'string') {\n        return currentValue[compareWith] === compareValue[compareWith];\n    }\n    else {\n        return Array.isArray(compareValue) ? compareValue.includes(currentValue) : currentValue === compareValue;\n    }\n};\n/**\n * Compares a value against the current value(s) to determine if it is selected.\n *\n * @param currentValue The current value of the control.\n * @param compareValue The value to compare against.\n * @param compareWith The function or property name to use to compare values.\n */\nconst isOptionSelected = (currentValue, compareValue, compareWith) => {\n    if (currentValue === undefined) {\n        return false;\n    }\n    if (Array.isArray(currentValue)) {\n        return currentValue.some((val) => compareOptions(val, compareValue, compareWith));\n    }\n    else {\n        return compareOptions(currentValue, compareValue, compareWith);\n    }\n};\n\nexport { compareOptions as c, isOptionSelected as i };\n"], "mappings": ";AAUA,IAAM,iBAAiB,CAAC,cAAc,cAAc,gBAAgB;AAChE,MAAI,OAAO,gBAAgB,YAAY;AACnC,WAAO,YAAY,cAAc,YAAY;AAAA,EACjD,WACS,OAAO,gBAAgB,UAAU;AACtC,WAAO,aAAa,WAAW,MAAM,aAAa,WAAW;AAAA,EACjE,OACK;AACD,WAAO,MAAM,QAAQ,YAAY,IAAI,aAAa,SAAS,YAAY,IAAI,iBAAiB;AAAA,EAChG;AACJ;AAQA,IAAM,mBAAmB,CAAC,cAAc,cAAc,gBAAgB;AAClE,MAAI,iBAAiB,QAAW;AAC5B,WAAO;AAAA,EACX;AACA,MAAI,MAAM,QAAQ,YAAY,GAAG;AAC7B,WAAO,aAAa,KAAK,CAAC,QAAQ,eAAe,KAAK,cAAc,WAAW,CAAC;AAAA,EACpF,OACK;AACD,WAAO,eAAe,cAAc,cAAc,WAAW;AAAA,EACjE;AACJ;", "names": []}