1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="io.ionic.starter"
4    android:versionCode="1"
5    android:versionName="1.0" >
6
7    <uses-sdk
8        android:minSdkVersion="23"
9        android:targetSdkVersion="35" />
10
11    <!-- Permissions -->
12
13    <uses-permission android:name="android.permission.INTERNET" />
13-->C:\Users\<USER>\OneDrive\Desktop\FRONTEND DEVELOPER\crud-sqlite\android\app\src\main\AndroidManifest.xml:40:5-67
13-->C:\Users\<USER>\OneDrive\Desktop\FRONTEND DEVELOPER\crud-sqlite\android\app\src\main\AndroidManifest.xml:40:22-64
14    <uses-permission android:name="android.permission.VIBRATE" />
14-->[:capacitor-haptics] C:\Users\<USER>\OneDrive\Desktop\FRONTEND DEVELOPER\crud-sqlite\node_modules\@capacitor\haptics\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-66
14-->[:capacitor-haptics] C:\Users\<USER>\OneDrive\Desktop\FRONTEND DEVELOPER\crud-sqlite\node_modules\@capacitor\haptics\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:22-63
15    <uses-permission android:name="android.permission.USE_BIOMETRIC" /> <!-- suppress DeprecatedClassUsageInspection -->
15-->[androidx.biometric:biometric:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\eaaca94d50affd3c3a86a8212751e60b\transformed\biometric-1.1.0\AndroidManifest.xml:24:5-72
15-->[androidx.biometric:biometric:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\eaaca94d50affd3c3a86a8212751e60b\transformed\biometric-1.1.0\AndroidManifest.xml:24:22-69
16    <uses-permission android:name="android.permission.USE_FINGERPRINT" />
16-->[androidx.biometric:biometric:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\eaaca94d50affd3c3a86a8212751e60b\transformed\biometric-1.1.0\AndroidManifest.xml:27:5-74
16-->[androidx.biometric:biometric:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\eaaca94d50affd3c3a86a8212751e60b\transformed\biometric-1.1.0\AndroidManifest.xml:27:22-71
17
18    <permission
18-->[androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ea82f0e4740fea90ca38a885280cdee8\transformed\core-1.15.0\AndroidManifest.xml:22:5-24:47
19        android:name="io.ionic.starter.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
19-->[androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ea82f0e4740fea90ca38a885280cdee8\transformed\core-1.15.0\AndroidManifest.xml:23:9-81
20        android:protectionLevel="signature" />
20-->[androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ea82f0e4740fea90ca38a885280cdee8\transformed\core-1.15.0\AndroidManifest.xml:24:9-44
21
22    <uses-permission android:name="io.ionic.starter.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
22-->[androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ea82f0e4740fea90ca38a885280cdee8\transformed\core-1.15.0\AndroidManifest.xml:26:5-97
22-->[androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ea82f0e4740fea90ca38a885280cdee8\transformed\core-1.15.0\AndroidManifest.xml:26:22-94
23
24    <application
24-->C:\Users\<USER>\OneDrive\Desktop\FRONTEND DEVELOPER\crud-sqlite\android\app\src\main\AndroidManifest.xml:4:5-36:19
25        android:allowBackup="true"
25-->C:\Users\<USER>\OneDrive\Desktop\FRONTEND DEVELOPER\crud-sqlite\android\app\src\main\AndroidManifest.xml:5:9-35
26        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
26-->[androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ea82f0e4740fea90ca38a885280cdee8\transformed\core-1.15.0\AndroidManifest.xml:28:18-86
27        android:debuggable="true"
28        android:extractNativeLibs="false"
29        android:icon="@mipmap/ic_launcher"
29-->C:\Users\<USER>\OneDrive\Desktop\FRONTEND DEVELOPER\crud-sqlite\android\app\src\main\AndroidManifest.xml:6:9-43
30        android:label="@string/app_name"
30-->C:\Users\<USER>\OneDrive\Desktop\FRONTEND DEVELOPER\crud-sqlite\android\app\src\main\AndroidManifest.xml:7:9-41
31        android:roundIcon="@mipmap/ic_launcher_round"
31-->C:\Users\<USER>\OneDrive\Desktop\FRONTEND DEVELOPER\crud-sqlite\android\app\src\main\AndroidManifest.xml:8:9-54
32        android:supportsRtl="true"
32-->C:\Users\<USER>\OneDrive\Desktop\FRONTEND DEVELOPER\crud-sqlite\android\app\src\main\AndroidManifest.xml:9:9-35
33        android:testOnly="true"
34        android:theme="@style/AppTheme" >
34-->C:\Users\<USER>\OneDrive\Desktop\FRONTEND DEVELOPER\crud-sqlite\android\app\src\main\AndroidManifest.xml:10:9-40
35        <activity
35-->C:\Users\<USER>\OneDrive\Desktop\FRONTEND DEVELOPER\crud-sqlite\android\app\src\main\AndroidManifest.xml:12:9-25:20
36            android:name="io.ionic.starter.MainActivity"
36-->C:\Users\<USER>\OneDrive\Desktop\FRONTEND DEVELOPER\crud-sqlite\android\app\src\main\AndroidManifest.xml:14:13-41
37            android:configChanges="orientation|keyboardHidden|keyboard|screenSize|locale|smallestScreenSize|screenLayout|uiMode|navigation"
37-->C:\Users\<USER>\OneDrive\Desktop\FRONTEND DEVELOPER\crud-sqlite\android\app\src\main\AndroidManifest.xml:13:13-140
38            android:exported="true"
38-->C:\Users\<USER>\OneDrive\Desktop\FRONTEND DEVELOPER\crud-sqlite\android\app\src\main\AndroidManifest.xml:18:13-36
39            android:label="@string/title_activity_main"
39-->C:\Users\<USER>\OneDrive\Desktop\FRONTEND DEVELOPER\crud-sqlite\android\app\src\main\AndroidManifest.xml:15:13-56
40            android:launchMode="singleTask"
40-->C:\Users\<USER>\OneDrive\Desktop\FRONTEND DEVELOPER\crud-sqlite\android\app\src\main\AndroidManifest.xml:17:13-44
41            android:theme="@style/AppTheme.NoActionBarLaunch" >
41-->C:\Users\<USER>\OneDrive\Desktop\FRONTEND DEVELOPER\crud-sqlite\android\app\src\main\AndroidManifest.xml:16:13-62
42            <intent-filter>
42-->C:\Users\<USER>\OneDrive\Desktop\FRONTEND DEVELOPER\crud-sqlite\android\app\src\main\AndroidManifest.xml:20:13-23:29
43                <action android:name="android.intent.action.MAIN" />
43-->C:\Users\<USER>\OneDrive\Desktop\FRONTEND DEVELOPER\crud-sqlite\android\app\src\main\AndroidManifest.xml:21:17-69
43-->C:\Users\<USER>\OneDrive\Desktop\FRONTEND DEVELOPER\crud-sqlite\android\app\src\main\AndroidManifest.xml:21:25-66
44
45                <category android:name="android.intent.category.LAUNCHER" />
45-->C:\Users\<USER>\OneDrive\Desktop\FRONTEND DEVELOPER\crud-sqlite\android\app\src\main\AndroidManifest.xml:22:17-77
45-->C:\Users\<USER>\OneDrive\Desktop\FRONTEND DEVELOPER\crud-sqlite\android\app\src\main\AndroidManifest.xml:22:27-74
46            </intent-filter>
47        </activity>
48
49        <provider
50            android:name="androidx.core.content.FileProvider"
50-->C:\Users\<USER>\OneDrive\Desktop\FRONTEND DEVELOPER\crud-sqlite\android\app\src\main\AndroidManifest.xml:28:13-62
51            android:authorities="io.ionic.starter.fileprovider"
51-->C:\Users\<USER>\OneDrive\Desktop\FRONTEND DEVELOPER\crud-sqlite\android\app\src\main\AndroidManifest.xml:29:13-64
52            android:exported="false"
52-->C:\Users\<USER>\OneDrive\Desktop\FRONTEND DEVELOPER\crud-sqlite\android\app\src\main\AndroidManifest.xml:30:13-37
53            android:grantUriPermissions="true" >
53-->C:\Users\<USER>\OneDrive\Desktop\FRONTEND DEVELOPER\crud-sqlite\android\app\src\main\AndroidManifest.xml:31:13-47
54            <meta-data
54-->C:\Users\<USER>\OneDrive\Desktop\FRONTEND DEVELOPER\crud-sqlite\android\app\src\main\AndroidManifest.xml:32:13-34:64
55                android:name="android.support.FILE_PROVIDER_PATHS"
55-->C:\Users\<USER>\OneDrive\Desktop\FRONTEND DEVELOPER\crud-sqlite\android\app\src\main\AndroidManifest.xml:33:17-67
56                android:resource="@xml/file_paths" />
56-->C:\Users\<USER>\OneDrive\Desktop\FRONTEND DEVELOPER\crud-sqlite\android\app\src\main\AndroidManifest.xml:34:17-51
57        </provider>
58        <provider
58-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\e9683f504198d3d554b1474ed6ab66f7\transformed\emoji2-1.3.0\AndroidManifest.xml:24:9-32:20
59            android:name="androidx.startup.InitializationProvider"
59-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\e9683f504198d3d554b1474ed6ab66f7\transformed\emoji2-1.3.0\AndroidManifest.xml:25:13-67
60            android:authorities="io.ionic.starter.androidx-startup"
60-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\e9683f504198d3d554b1474ed6ab66f7\transformed\emoji2-1.3.0\AndroidManifest.xml:26:13-68
61            android:exported="false" >
61-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\e9683f504198d3d554b1474ed6ab66f7\transformed\emoji2-1.3.0\AndroidManifest.xml:27:13-37
62            <meta-data
62-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\e9683f504198d3d554b1474ed6ab66f7\transformed\emoji2-1.3.0\AndroidManifest.xml:29:13-31:52
63                android:name="androidx.emoji2.text.EmojiCompatInitializer"
63-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\e9683f504198d3d554b1474ed6ab66f7\transformed\emoji2-1.3.0\AndroidManifest.xml:30:17-75
64                android:value="androidx.startup" />
64-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\e9683f504198d3d554b1474ed6ab66f7\transformed\emoji2-1.3.0\AndroidManifest.xml:31:17-49
65            <meta-data
65-->[androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\7fcaa2d94568f1111aa0d45f264971fb\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:29:13-31:52
66                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
66-->[androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\7fcaa2d94568f1111aa0d45f264971fb\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:30:17-78
67                android:value="androidx.startup" />
67-->[androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\7fcaa2d94568f1111aa0d45f264971fb\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:31:17-49
68            <meta-data
68-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\9004adbda7bfb1b3286dec38d922dd38\transformed\profileinstaller-1.3.1\AndroidManifest.xml:29:13-31:52
69                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
69-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\9004adbda7bfb1b3286dec38d922dd38\transformed\profileinstaller-1.3.1\AndroidManifest.xml:30:17-85
70                android:value="androidx.startup" />
70-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\9004adbda7bfb1b3286dec38d922dd38\transformed\profileinstaller-1.3.1\AndroidManifest.xml:31:17-49
71        </provider>
72
73        <receiver
73-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\9004adbda7bfb1b3286dec38d922dd38\transformed\profileinstaller-1.3.1\AndroidManifest.xml:34:9-52:20
74            android:name="androidx.profileinstaller.ProfileInstallReceiver"
74-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\9004adbda7bfb1b3286dec38d922dd38\transformed\profileinstaller-1.3.1\AndroidManifest.xml:35:13-76
75            android:directBootAware="false"
75-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\9004adbda7bfb1b3286dec38d922dd38\transformed\profileinstaller-1.3.1\AndroidManifest.xml:36:13-44
76            android:enabled="true"
76-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\9004adbda7bfb1b3286dec38d922dd38\transformed\profileinstaller-1.3.1\AndroidManifest.xml:37:13-35
77            android:exported="true"
77-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\9004adbda7bfb1b3286dec38d922dd38\transformed\profileinstaller-1.3.1\AndroidManifest.xml:38:13-36
78            android:permission="android.permission.DUMP" >
78-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\9004adbda7bfb1b3286dec38d922dd38\transformed\profileinstaller-1.3.1\AndroidManifest.xml:39:13-57
79            <intent-filter>
79-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\9004adbda7bfb1b3286dec38d922dd38\transformed\profileinstaller-1.3.1\AndroidManifest.xml:40:13-42:29
80                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
80-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\9004adbda7bfb1b3286dec38d922dd38\transformed\profileinstaller-1.3.1\AndroidManifest.xml:41:17-91
80-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\9004adbda7bfb1b3286dec38d922dd38\transformed\profileinstaller-1.3.1\AndroidManifest.xml:41:25-88
81            </intent-filter>
82            <intent-filter>
82-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\9004adbda7bfb1b3286dec38d922dd38\transformed\profileinstaller-1.3.1\AndroidManifest.xml:43:13-45:29
83                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
83-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\9004adbda7bfb1b3286dec38d922dd38\transformed\profileinstaller-1.3.1\AndroidManifest.xml:44:17-85
83-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\9004adbda7bfb1b3286dec38d922dd38\transformed\profileinstaller-1.3.1\AndroidManifest.xml:44:25-82
84            </intent-filter>
85            <intent-filter>
85-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\9004adbda7bfb1b3286dec38d922dd38\transformed\profileinstaller-1.3.1\AndroidManifest.xml:46:13-48:29
86                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
86-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\9004adbda7bfb1b3286dec38d922dd38\transformed\profileinstaller-1.3.1\AndroidManifest.xml:47:17-88
86-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\9004adbda7bfb1b3286dec38d922dd38\transformed\profileinstaller-1.3.1\AndroidManifest.xml:47:25-85
87            </intent-filter>
88            <intent-filter>
88-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\9004adbda7bfb1b3286dec38d922dd38\transformed\profileinstaller-1.3.1\AndroidManifest.xml:49:13-51:29
89                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
89-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\9004adbda7bfb1b3286dec38d922dd38\transformed\profileinstaller-1.3.1\AndroidManifest.xml:50:17-95
89-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\9004adbda7bfb1b3286dec38d922dd38\transformed\profileinstaller-1.3.1\AndroidManifest.xml:50:25-92
90            </intent-filter>
91        </receiver>
92
93        <service
93-->[androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\bc6d32dbdad3a4b2c0ef6b4bde666a61\transformed\room-runtime-2.6.1\AndroidManifest.xml:24:9-28:63
94            android:name="androidx.room.MultiInstanceInvalidationService"
94-->[androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\bc6d32dbdad3a4b2c0ef6b4bde666a61\transformed\room-runtime-2.6.1\AndroidManifest.xml:25:13-74
95            android:directBootAware="true"
95-->[androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\bc6d32dbdad3a4b2c0ef6b4bde666a61\transformed\room-runtime-2.6.1\AndroidManifest.xml:26:13-43
96            android:exported="false" />
96-->[androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\bc6d32dbdad3a4b2c0ef6b4bde666a61\transformed\room-runtime-2.6.1\AndroidManifest.xml:27:13-37
97    </application>
98
99</manifest>
