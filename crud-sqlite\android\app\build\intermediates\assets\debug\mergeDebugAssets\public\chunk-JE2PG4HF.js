import{a as So,b as vc,c as Hi,d as ds,g as $i,h as yc,i as lh}from"./chunk-QUM6RZVN.js";import{a as uh}from"./chunk-6WAW2KHA.js";import{a as T,b as dc,c as fc,d as pc,e as hc,f as mc,g as rh,h as ls,i as cs}from"./chunk-ICSGBKZQ.js";import{a as ct,b as Z,c as vn,d as wt,e as Zp,f as m,g as J,h as Qp,i as Kp,j as N,k as ut,l as Q}from"./chunk-YSN7XVTD.js";import{a as Bi,d as sh}from"./chunk-R5HL6L5F.js";import{a as ah}from"./chunk-4WFVMWDK.js";import{a as gc,c as us}from"./chunk-M2X7KQLB.js";import{a as bc}from"./chunk-2YSZFPCQ.js";import{a as ch}from"./chunk-57YRIO75.js";import{a as tt,b as Zt,c as xo,d as bn,e as as,f as th,g as ce,h as je,i as nh,j as uc,k as wn,m as oh,n as ih}from"./chunk-REYR55MP.js";import{a as ie,b as Xp,c as Jp,d as eh,e as Le,f as Eo}from"./chunk-C5RQ2IC2.js";import{a as Ct,b as yn}from"./chunk-42C7ZIID.js";import{a as b,b as L,d as Yp,e as C}from"./chunk-JHI3MBHO.js";var wc;function fs(){return wc}function At(e){let t=wc;return wc=e,t}var dh=Symbol("NotFound");function _o(e){return e===dh||e?.name==="\u0275NotFound"}function vs(e,t){return Object.is(e,t)}var xe=null,ps=!1,Cc=1,xw=null,Ve=Symbol("SIGNAL");function j(e){let t=xe;return xe=e,t}function ys(){return xe}var Hn={version:0,lastCleanEpoch:0,dirty:!1,producerNode:void 0,producerLastReadVersion:void 0,producerIndexOfThis:void 0,nextProducerIndex:0,liveConsumerNode:void 0,liveConsumerIndexOfThis:void 0,consumerAllowSignalWrites:!1,consumerIsAlwaysLive:!1,kind:"unknown",producerMustRecompute:()=>!1,producerRecomputeValue:()=>{},consumerMarkedDirty:()=>{},consumerOnSignalRead:()=>{}};function To(e){if(ps)throw new Error("");if(xe===null)return;xe.consumerOnSignalRead(e);let t=xe.nextProducerIndex++;if(Ds(xe),t<xe.producerNode.length&&xe.producerNode[t]!==e&&Ui(xe)){let n=xe.producerNode[t];Cs(n,xe.producerIndexOfThis[t])}xe.producerNode[t]!==e&&(xe.producerNode[t]=e,xe.producerIndexOfThis[t]=Ui(xe)?ph(e,xe,t):0),xe.producerLastReadVersion[t]=e.version}function fh(){Cc++}function bs(e){if(!(Ui(e)&&!e.dirty)&&!(!e.dirty&&e.lastCleanEpoch===Cc)){if(!e.producerMustRecompute(e)&&!qi(e)){gs(e);return}e.producerRecomputeValue(e),gs(e)}}function Dc(e){if(e.liveConsumerNode===void 0)return;let t=ps;ps=!0;try{for(let n of e.liveConsumerNode)n.dirty||Sw(n)}finally{ps=t}}function Ic(){return xe?.consumerAllowSignalWrites!==!1}function Sw(e){e.dirty=!0,Dc(e),e.consumerMarkedDirty?.(e)}function gs(e){e.dirty=!1,e.lastCleanEpoch=Cc}function $n(e){return e&&(e.nextProducerIndex=0),j(e)}function Mo(e,t){if(j(t),!(!e||e.producerNode===void 0||e.producerIndexOfThis===void 0||e.producerLastReadVersion===void 0)){if(Ui(e))for(let n=e.nextProducerIndex;n<e.producerNode.length;n++)Cs(e.producerNode[n],e.producerIndexOfThis[n]);for(;e.producerNode.length>e.nextProducerIndex;)e.producerNode.pop(),e.producerLastReadVersion.pop(),e.producerIndexOfThis.pop()}}function qi(e){Ds(e);for(let t=0;t<e.producerNode.length;t++){let n=e.producerNode[t],o=e.producerLastReadVersion[t];if(o!==n.version||(bs(n),o!==n.version))return!0}return!1}function ws(e){if(Ds(e),Ui(e))for(let t=0;t<e.producerNode.length;t++)Cs(e.producerNode[t],e.producerIndexOfThis[t]);e.producerNode.length=e.producerLastReadVersion.length=e.producerIndexOfThis.length=0,e.liveConsumerNode&&(e.liveConsumerNode.length=e.liveConsumerIndexOfThis.length=0)}function ph(e,t,n){if(hh(e),e.liveConsumerNode.length===0&&mh(e))for(let o=0;o<e.producerNode.length;o++)e.producerIndexOfThis[o]=ph(e.producerNode[o],e,o);return e.liveConsumerIndexOfThis.push(n),e.liveConsumerNode.push(t)-1}function Cs(e,t){if(hh(e),e.liveConsumerNode.length===1&&mh(e))for(let o=0;o<e.producerNode.length;o++)Cs(e.producerNode[o],e.producerIndexOfThis[o]);let n=e.liveConsumerNode.length-1;if(e.liveConsumerNode[t]=e.liveConsumerNode[n],e.liveConsumerIndexOfThis[t]=e.liveConsumerIndexOfThis[n],e.liveConsumerNode.length--,e.liveConsumerIndexOfThis.length--,t<e.liveConsumerNode.length){let o=e.liveConsumerIndexOfThis[t],i=e.liveConsumerNode[t];Ds(i),i.producerIndexOfThis[o]=t}}function Ui(e){return e.consumerIsAlwaysLive||(e?.liveConsumerNode?.length??0)>0}function Ds(e){e.producerNode??=[],e.producerIndexOfThis??=[],e.producerLastReadVersion??=[]}function hh(e){e.liveConsumerNode??=[],e.liveConsumerIndexOfThis??=[]}function mh(e){return e.producerNode!==void 0}function Is(e){xw?.(e)}function Es(e,t){let n=Object.create(_w);n.computation=e,t!==void 0&&(n.equal=t);let o=()=>{if(bs(n),To(n),n.value===zi)throw n.error;return n.value};return o[Ve]=n,Is(n),o}var hs=Symbol("UNSET"),ms=Symbol("COMPUTING"),zi=Symbol("ERRORED"),_w=L(b({},Hn),{value:hs,dirty:!0,error:null,equal:vs,kind:"computed",producerMustRecompute(e){return e.value===hs||e.value===ms},producerRecomputeValue(e){if(e.value===ms)throw new Error("");let t=e.value;e.value=ms;let n=$n(e),o,i=!1;try{o=e.computation(),j(null),i=t!==hs&&t!==zi&&o!==zi&&e.equal(t,o)}catch(r){o=zi,e.error=r}finally{Mo(e,n)}if(i){e.value=t;return}e.value=o,e.version++}});function Tw(){throw new Error}var gh=Tw;function vh(e){gh(e)}function Ec(e){gh=e}var Mw=null;function xc(e,t){let n=Object.create(xs);n.value=e,t!==void 0&&(n.equal=t);let o=()=>yh(n);return o[Ve]=n,Is(n),[o,s=>ko(n,s),s=>Sc(n,s)]}function yh(e){return To(e),e.value}function ko(e,t){Ic()||vh(e),e.equal(e.value,t)||(e.value=t,kw(e))}function Sc(e,t){Ic()||vh(e),ko(e,t(e.value))}var xs=L(b({},Hn),{equal:vs,value:void 0,kind:"signal"});function kw(e){e.version++,fh(),Dc(e),Mw?.(e)}function _(e){return typeof e=="function"}function Ao(e){let n=e(o=>{Error.call(o),o.stack=new Error().stack});return n.prototype=Object.create(Error.prototype),n.prototype.constructor=n,n}var Ss=Ao(e=>function(n){e(this),this.message=n?`${n.length} errors occurred during unsubscription:
${n.map((o,i)=>`${i+1}) ${o.toString()}`).join(`
  `)}`:"",this.name="UnsubscriptionError",this.errors=n});function Gi(e,t){if(e){let n=e.indexOf(t);0<=n&&e.splice(n,1)}}var De=class e{constructor(t){this.initialTeardown=t,this.closed=!1,this._parentage=null,this._finalizers=null}unsubscribe(){let t;if(!this.closed){this.closed=!0;let{_parentage:n}=this;if(n)if(this._parentage=null,Array.isArray(n))for(let r of n)r.remove(this);else n.remove(this);let{initialTeardown:o}=this;if(_(o))try{o()}catch(r){t=r instanceof Ss?r.errors:[r]}let{_finalizers:i}=this;if(i){this._finalizers=null;for(let r of i)try{bh(r)}catch(s){t=t??[],s instanceof Ss?t=[...t,...s.errors]:t.push(s)}}if(t)throw new Ss(t)}}add(t){var n;if(t&&t!==this)if(this.closed)bh(t);else{if(t instanceof e){if(t.closed||t._hasParent(this))return;t._addParent(this)}(this._finalizers=(n=this._finalizers)!==null&&n!==void 0?n:[]).push(t)}}_hasParent(t){let{_parentage:n}=this;return n===t||Array.isArray(n)&&n.includes(t)}_addParent(t){let{_parentage:n}=this;this._parentage=Array.isArray(n)?(n.push(t),n):n?[n,t]:t}_removeParent(t){let{_parentage:n}=this;n===t?this._parentage=null:Array.isArray(n)&&Gi(n,t)}remove(t){let{_finalizers:n}=this;n&&Gi(n,t),t instanceof e&&t._removeParent(this)}};De.EMPTY=(()=>{let e=new De;return e.closed=!0,e})();var _c=De.EMPTY;function _s(e){return e instanceof De||e&&"closed"in e&&_(e.remove)&&_(e.add)&&_(e.unsubscribe)}function bh(e){_(e)?e():e.unsubscribe()}var Dt={onUnhandledError:null,onStoppedNotification:null,Promise:void 0,useDeprecatedSynchronousErrorHandling:!1,useDeprecatedNextContext:!1};var Ro={setTimeout(e,t,...n){let{delegate:o}=Ro;return o?.setTimeout?o.setTimeout(e,t,...n):setTimeout(e,t,...n)},clearTimeout(e){let{delegate:t}=Ro;return(t?.clearTimeout||clearTimeout)(e)},delegate:void 0};function Ts(e){Ro.setTimeout(()=>{let{onUnhandledError:t}=Dt;if(t)t(e);else throw e})}function Wi(){}var wh=Tc("C",void 0,void 0);function Ch(e){return Tc("E",void 0,e)}function Dh(e){return Tc("N",e,void 0)}function Tc(e,t,n){return{kind:e,value:t,error:n}}var zn=null;function No(e){if(Dt.useDeprecatedSynchronousErrorHandling){let t=!zn;if(t&&(zn={errorThrown:!1,error:null}),e(),t){let{errorThrown:n,error:o}=zn;if(zn=null,n)throw o}}else e()}function Ih(e){Dt.useDeprecatedSynchronousErrorHandling&&zn&&(zn.errorThrown=!0,zn.error=e)}var Un=class extends De{constructor(t){super(),this.isStopped=!1,t?(this.destination=t,_s(t)&&t.add(this)):this.destination=Nw}static create(t,n,o){return new Oo(t,n,o)}next(t){this.isStopped?kc(Dh(t),this):this._next(t)}error(t){this.isStopped?kc(Ch(t),this):(this.isStopped=!0,this._error(t))}complete(){this.isStopped?kc(wh,this):(this.isStopped=!0,this._complete())}unsubscribe(){this.closed||(this.isStopped=!0,super.unsubscribe(),this.destination=null)}_next(t){this.destination.next(t)}_error(t){try{this.destination.error(t)}finally{this.unsubscribe()}}_complete(){try{this.destination.complete()}finally{this.unsubscribe()}}},Aw=Function.prototype.bind;function Mc(e,t){return Aw.call(e,t)}var Ac=class{constructor(t){this.partialObserver=t}next(t){let{partialObserver:n}=this;if(n.next)try{n.next(t)}catch(o){Ms(o)}}error(t){let{partialObserver:n}=this;if(n.error)try{n.error(t)}catch(o){Ms(o)}else Ms(t)}complete(){let{partialObserver:t}=this;if(t.complete)try{t.complete()}catch(n){Ms(n)}}},Oo=class extends Un{constructor(t,n,o){super();let i;if(_(t)||!t)i={next:t??void 0,error:n??void 0,complete:o??void 0};else{let r;this&&Dt.useDeprecatedNextContext?(r=Object.create(t),r.unsubscribe=()=>this.unsubscribe(),i={next:t.next&&Mc(t.next,r),error:t.error&&Mc(t.error,r),complete:t.complete&&Mc(t.complete,r)}):i=t}this.destination=new Ac(i)}};function Ms(e){Dt.useDeprecatedSynchronousErrorHandling?Ih(e):Ts(e)}function Rw(e){throw e}function kc(e,t){let{onStoppedNotification:n}=Dt;n&&Ro.setTimeout(()=>n(e,t))}var Nw={closed:!0,next:Wi,error:Rw,complete:Wi};var Po=typeof Symbol=="function"&&Symbol.observable||"@@observable";function Ue(e){return e}function Rc(...e){return Nc(e)}function Nc(e){return e.length===0?Ue:e.length===1?e[0]:function(n){return e.reduce((o,i)=>i(o),n)}}var z=(()=>{class e{constructor(n){n&&(this._subscribe=n)}lift(n){let o=new e;return o.source=this,o.operator=n,o}subscribe(n,o,i){let r=Pw(n)?n:new Oo(n,o,i);return No(()=>{let{operator:s,source:a}=this;r.add(s?s.call(r,a):a?this._subscribe(r):this._trySubscribe(r))}),r}_trySubscribe(n){try{return this._subscribe(n)}catch(o){n.error(o)}}forEach(n,o){return o=Eh(o),new o((i,r)=>{let s=new Oo({next:a=>{try{n(a)}catch(l){r(l),s.unsubscribe()}},error:r,complete:i});this.subscribe(s)})}_subscribe(n){var o;return(o=this.source)===null||o===void 0?void 0:o.subscribe(n)}[Po](){return this}pipe(...n){return Nc(n)(this)}toPromise(n){return n=Eh(n),new n((o,i)=>{let r;this.subscribe(s=>r=s,s=>i(s),()=>o(r))})}}return e.create=t=>new e(t),e})();function Eh(e){var t;return(t=e??Dt.Promise)!==null&&t!==void 0?t:Promise}function Ow(e){return e&&_(e.next)&&_(e.error)&&_(e.complete)}function Pw(e){return e&&e instanceof Un||Ow(e)&&_s(e)}function Oc(e){return _(e?.lift)}function q(e){return t=>{if(Oc(t))return t.lift(function(n){try{return e(n,this)}catch(o){this.error(o)}});throw new TypeError("Unable to lift unknown Observable type")}}function B(e,t,n,o,i){return new Pc(e,t,n,o,i)}var Pc=class extends Un{constructor(t,n,o,i,r,s){super(t),this.onFinalize=r,this.shouldUnsubscribe=s,this._next=n?function(a){try{n(a)}catch(l){t.error(l)}}:super._next,this._error=i?function(a){try{i(a)}catch(l){t.error(l)}finally{this.unsubscribe()}}:super._error,this._complete=o?function(){try{o()}catch(a){t.error(a)}finally{this.unsubscribe()}}:super._complete}unsubscribe(){var t;if(!this.shouldUnsubscribe||this.shouldUnsubscribe()){let{closed:n}=this;super.unsubscribe(),!n&&((t=this.onFinalize)===null||t===void 0||t.call(this))}}};function Fo(){return q((e,t)=>{let n=null;e._refCount++;let o=B(t,void 0,void 0,void 0,()=>{if(!e||e._refCount<=0||0<--e._refCount){n=null;return}let i=e._connection,r=n;n=null,i&&(!r||i===r)&&i.unsubscribe(),t.unsubscribe()});e.subscribe(o),o.closed||(n=e.connect())})}var Lo=class extends z{constructor(t,n){super(),this.source=t,this.subjectFactory=n,this._subject=null,this._refCount=0,this._connection=null,Oc(t)&&(this.lift=t.lift)}_subscribe(t){return this.getSubject().subscribe(t)}getSubject(){let t=this._subject;return(!t||t.isStopped)&&(this._subject=this.subjectFactory()),this._subject}_teardown(){this._refCount=0;let{_connection:t}=this;this._subject=this._connection=null,t?.unsubscribe()}connect(){let t=this._connection;if(!t){t=this._connection=new De;let n=this.getSubject();t.add(this.source.subscribe(B(n,void 0,()=>{this._teardown(),n.complete()},o=>{this._teardown(),n.error(o)},()=>this._teardown()))),t.closed&&(this._connection=null,t=De.EMPTY)}return t}refCount(){return Fo()(this)}};var xh=Ao(e=>function(){e(this),this.name="ObjectUnsubscribedError",this.message="object unsubscribed"});var re=(()=>{class e extends z{constructor(){super(),this.closed=!1,this.currentObservers=null,this.observers=[],this.isStopped=!1,this.hasError=!1,this.thrownError=null}lift(n){let o=new ks(this,this);return o.operator=n,o}_throwIfClosed(){if(this.closed)throw new xh}next(n){No(()=>{if(this._throwIfClosed(),!this.isStopped){this.currentObservers||(this.currentObservers=Array.from(this.observers));for(let o of this.currentObservers)o.next(n)}})}error(n){No(()=>{if(this._throwIfClosed(),!this.isStopped){this.hasError=this.isStopped=!0,this.thrownError=n;let{observers:o}=this;for(;o.length;)o.shift().error(n)}})}complete(){No(()=>{if(this._throwIfClosed(),!this.isStopped){this.isStopped=!0;let{observers:n}=this;for(;n.length;)n.shift().complete()}})}unsubscribe(){this.isStopped=this.closed=!0,this.observers=this.currentObservers=null}get observed(){var n;return((n=this.observers)===null||n===void 0?void 0:n.length)>0}_trySubscribe(n){return this._throwIfClosed(),super._trySubscribe(n)}_subscribe(n){return this._throwIfClosed(),this._checkFinalizedStatuses(n),this._innerSubscribe(n)}_innerSubscribe(n){let{hasError:o,isStopped:i,observers:r}=this;return o||i?_c:(this.currentObservers=null,r.push(n),new De(()=>{this.currentObservers=null,Gi(r,n)}))}_checkFinalizedStatuses(n){let{hasError:o,thrownError:i,isStopped:r}=this;o?n.error(i):r&&n.complete()}asObservable(){let n=new z;return n.source=this,n}}return e.create=(t,n)=>new ks(t,n),e})(),ks=class extends re{constructor(t,n){super(),this.destination=t,this.source=n}next(t){var n,o;(o=(n=this.destination)===null||n===void 0?void 0:n.next)===null||o===void 0||o.call(n,t)}error(t){var n,o;(o=(n=this.destination)===null||n===void 0?void 0:n.error)===null||o===void 0||o.call(n,t)}complete(){var t,n;(n=(t=this.destination)===null||t===void 0?void 0:t.complete)===null||n===void 0||n.call(t)}_subscribe(t){var n,o;return(o=(n=this.source)===null||n===void 0?void 0:n.subscribe(t))!==null&&o!==void 0?o:_c}};var Ie=class extends re{constructor(t){super(),this._value=t}get value(){return this.getValue()}_subscribe(t){let n=super._subscribe(t);return!n.closed&&t.next(this._value),n}getValue(){let{hasError:t,thrownError:n,_value:o}=this;if(t)throw n;return this._throwIfClosed(),o}next(t){super.next(this._value=t)}};var Ke=new z(e=>e.complete());function Sh(e){return e&&_(e.schedule)}function _h(e){return e[e.length-1]}function As(e){return _(_h(e))?e.pop():void 0}function Cn(e){return Sh(_h(e))?e.pop():void 0}function dt(e,t,n,o){var i=arguments.length,r=i<3?t:o===null?o=Object.getOwnPropertyDescriptor(t,n):o,s;if(typeof Reflect=="object"&&typeof Reflect.decorate=="function")r=Reflect.decorate(e,t,n,o);else for(var a=e.length-1;a>=0;a--)(s=e[a])&&(r=(i<3?s(r):i>3?s(t,n,r):s(t,n))||r);return i>3&&r&&Object.defineProperty(t,n,r),r}function Mh(e,t,n,o){function i(r){return r instanceof n?r:new n(function(s){s(r)})}return new(n||(n=Promise))(function(r,s){function a(u){try{c(o.next(u))}catch(d){s(d)}}function l(u){try{c(o.throw(u))}catch(d){s(d)}}function c(u){u.done?r(u.value):i(u.value).then(a,l)}c((o=o.apply(e,t||[])).next())})}function Th(e){var t=typeof Symbol=="function"&&Symbol.iterator,n=t&&e[t],o=0;if(n)return n.call(e);if(e&&typeof e.length=="number")return{next:function(){return e&&o>=e.length&&(e=void 0),{value:e&&e[o++],done:!e}}};throw new TypeError(t?"Object is not iterable.":"Symbol.iterator is not defined.")}function qn(e){return this instanceof qn?(this.v=e,this):new qn(e)}function kh(e,t,n){if(!Symbol.asyncIterator)throw new TypeError("Symbol.asyncIterator is not defined.");var o=n.apply(e,t||[]),i,r=[];return i=Object.create((typeof AsyncIterator=="function"?AsyncIterator:Object).prototype),a("next"),a("throw"),a("return",s),i[Symbol.asyncIterator]=function(){return this},i;function s(f){return function(h){return Promise.resolve(h).then(f,d)}}function a(f,h){o[f]&&(i[f]=function(y){return new Promise(function(g,w){r.push([f,y,g,w])>1||l(f,y)})},h&&(i[f]=h(i[f])))}function l(f,h){try{c(o[f](h))}catch(y){p(r[0][3],y)}}function c(f){f.value instanceof qn?Promise.resolve(f.value.v).then(u,d):p(r[0][2],f)}function u(f){l("next",f)}function d(f){l("throw",f)}function p(f,h){f(h),r.shift(),r.length&&l(r[0][0],r[0][1])}}function Ah(e){if(!Symbol.asyncIterator)throw new TypeError("Symbol.asyncIterator is not defined.");var t=e[Symbol.asyncIterator],n;return t?t.call(e):(e=typeof Th=="function"?Th(e):e[Symbol.iterator](),n={},o("next"),o("throw"),o("return"),n[Symbol.asyncIterator]=function(){return this},n);function o(r){n[r]=e[r]&&function(s){return new Promise(function(a,l){s=e[r](s),i(a,l,s.done,s.value)})}}function i(r,s,a,l){Promise.resolve(l).then(function(c){r({value:c,done:a})},s)}}var jo=e=>e&&typeof e.length=="number"&&typeof e!="function";function Rs(e){return _(e?.then)}function Ns(e){return _(e[Po])}function Os(e){return Symbol.asyncIterator&&_(e?.[Symbol.asyncIterator])}function Ps(e){return new TypeError(`You provided ${e!==null&&typeof e=="object"?"an invalid object":`'${e}'`} where a stream was expected. You can provide an Observable, Promise, ReadableStream, Array, AsyncIterable, or Iterable.`)}function Fw(){return typeof Symbol!="function"||!Symbol.iterator?"@@iterator":Symbol.iterator}var Fs=Fw();function Ls(e){return _(e?.[Fs])}function js(e){return kh(this,arguments,function*(){let n=e.getReader();try{for(;;){let{value:o,done:i}=yield qn(n.read());if(i)return yield qn(void 0);yield yield qn(o)}}finally{n.releaseLock()}})}function Vs(e){return _(e?.getReader)}function ge(e){if(e instanceof z)return e;if(e!=null){if(Ns(e))return Lw(e);if(jo(e))return jw(e);if(Rs(e))return Vw(e);if(Os(e))return Rh(e);if(Ls(e))return Bw(e);if(Vs(e))return Hw(e)}throw Ps(e)}function Lw(e){return new z(t=>{let n=e[Po]();if(_(n.subscribe))return n.subscribe(t);throw new TypeError("Provided object does not correctly implement Symbol.observable")})}function jw(e){return new z(t=>{for(let n=0;n<e.length&&!t.closed;n++)t.next(e[n]);t.complete()})}function Vw(e){return new z(t=>{e.then(n=>{t.closed||(t.next(n),t.complete())},n=>t.error(n)).then(null,Ts)})}function Bw(e){return new z(t=>{for(let n of e)if(t.next(n),t.closed)return;t.complete()})}function Rh(e){return new z(t=>{$w(e,t).catch(n=>t.error(n))})}function Hw(e){return Rh(js(e))}function $w(e,t){var n,o,i,r;return Mh(this,void 0,void 0,function*(){try{for(n=Ah(e);o=yield n.next(),!o.done;){let s=o.value;if(t.next(s),t.closed)return}}catch(s){i={error:s}}finally{try{o&&!o.done&&(r=n.return)&&(yield r.call(n))}finally{if(i)throw i.error}}t.complete()})}function Xe(e,t,n,o=0,i=!1){let r=t.schedule(function(){n(),i?e.add(this.schedule(null,o)):this.unsubscribe()},o);if(e.add(r),!i)return r}function Bs(e,t=0){return q((n,o)=>{n.subscribe(B(o,i=>Xe(o,e,()=>o.next(i),t),()=>Xe(o,e,()=>o.complete(),t),i=>Xe(o,e,()=>o.error(i),t)))})}function Hs(e,t=0){return q((n,o)=>{o.add(e.schedule(()=>n.subscribe(o),t))})}function Nh(e,t){return ge(e).pipe(Hs(t),Bs(t))}function Oh(e,t){return ge(e).pipe(Hs(t),Bs(t))}function Ph(e,t){return new z(n=>{let o=0;return t.schedule(function(){o===e.length?n.complete():(n.next(e[o++]),n.closed||this.schedule())})})}function Fh(e,t){return new z(n=>{let o;return Xe(n,t,()=>{o=e[Fs](),Xe(n,t,()=>{let i,r;try{({value:i,done:r}=o.next())}catch(s){n.error(s);return}r?n.complete():n.next(i)},0,!0)}),()=>_(o?.return)&&o.return()})}function $s(e,t){if(!e)throw new Error("Iterable cannot be null");return new z(n=>{Xe(n,t,()=>{let o=e[Symbol.asyncIterator]();Xe(n,t,()=>{o.next().then(i=>{i.done?n.complete():n.next(i.value)})},0,!0)})})}function Lh(e,t){return $s(js(e),t)}function jh(e,t){if(e!=null){if(Ns(e))return Nh(e,t);if(jo(e))return Ph(e,t);if(Rs(e))return Oh(e,t);if(Os(e))return $s(e,t);if(Ls(e))return Fh(e,t);if(Vs(e))return Lh(e,t)}throw Ps(e)}function ue(e,t){return t?jh(e,t):ge(e)}function k(...e){let t=Cn(e);return ue(e,t)}function Vo(e,t){let n=_(e)?e:()=>e,o=i=>i.error(n());return new z(t?i=>t.schedule(o,0,i):o)}function Fc(e){return!!e&&(e instanceof z||_(e.lift)&&_(e.subscribe))}var Qt=Ao(e=>function(){e(this),this.name="EmptyError",this.message="no elements in sequence"});function G(e,t){return q((n,o)=>{let i=0;n.subscribe(B(o,r=>{o.next(e.call(t,r,i++))}))})}var{isArray:zw}=Array;function Uw(e,t){return zw(t)?e(...t):e(t)}function Bo(e){return G(t=>Uw(e,t))}var{isArray:qw}=Array,{getPrototypeOf:Gw,prototype:Ww,keys:Yw}=Object;function zs(e){if(e.length===1){let t=e[0];if(qw(t))return{args:t,keys:null};if(Zw(t)){let n=Yw(t);return{args:n.map(o=>t[o]),keys:n}}}return{args:e,keys:null}}function Zw(e){return e&&typeof e=="object"&&Gw(e)===Ww}function Us(e,t){return e.reduce((n,o,i)=>(n[o]=t[i],n),{})}function Ho(...e){let t=Cn(e),n=As(e),{args:o,keys:i}=zs(e);if(o.length===0)return ue([],t);let r=new z(Qw(o,t,i?s=>Us(i,s):Ue));return n?r.pipe(Bo(n)):r}function Qw(e,t,n=Ue){return o=>{Vh(t,()=>{let{length:i}=e,r=new Array(i),s=i,a=i;for(let l=0;l<i;l++)Vh(t,()=>{let c=ue(e[l],t),u=!1;c.subscribe(B(o,d=>{r[l]=d,u||(u=!0,a--),a||o.next(n(r.slice()))},()=>{--s||o.complete()}))},o)},o)}}function Vh(e,t,n){e?Xe(n,e,t):t()}function Bh(e,t,n,o,i,r,s,a){let l=[],c=0,u=0,d=!1,p=()=>{d&&!l.length&&!c&&t.complete()},f=y=>c<o?h(y):l.push(y),h=y=>{r&&t.next(y),c++;let g=!1;ge(n(y,u++)).subscribe(B(t,w=>{i?.(w),r?f(w):t.next(w)},()=>{g=!0},void 0,()=>{if(g)try{for(c--;l.length&&c<o;){let w=l.shift();s?Xe(t,s,()=>h(w)):h(w)}p()}catch(w){t.error(w)}}))};return e.subscribe(B(t,f,()=>{d=!0,p()})),()=>{a?.()}}function fe(e,t,n=1/0){return _(t)?fe((o,i)=>G((r,s)=>t(o,r,i,s))(ge(e(o,i))),n):(typeof t=="number"&&(n=t),q((o,i)=>Bh(o,i,e,n)))}function $o(e=1/0){return fe(Ue,e)}function Hh(){return $o(1)}function zo(...e){return Hh()(ue(e,Cn(e)))}function Yi(e){return new z(t=>{ge(e()).subscribe(t)})}function Lc(...e){let t=As(e),{args:n,keys:o}=zs(e),i=new z(r=>{let{length:s}=n;if(!s){r.complete();return}let a=new Array(s),l=s,c=s;for(let u=0;u<s;u++){let d=!1;ge(n[u]).subscribe(B(r,p=>{d||(d=!0,c--),a[u]=p},()=>l--,void 0,()=>{(!l||!d)&&(c||r.next(o?Us(o,a):a),r.complete())}))}});return t?i.pipe(Bo(t)):i}var Kw=["addListener","removeListener"],Xw=["addEventListener","removeEventListener"],Jw=["on","off"];function Uo(e,t,n,o){if(_(n)&&(o=n,n=void 0),o)return Uo(e,t,n).pipe(Bo(o));let[i,r]=nC(e)?Xw.map(s=>a=>e[s](t,a,n)):eC(e)?Kw.map($h(e,t)):tC(e)?Jw.map($h(e,t)):[];if(!i&&jo(e))return fe(s=>Uo(s,t,n))(ge(e));if(!i)throw new TypeError("Invalid event target");return new z(s=>{let a=(...l)=>s.next(1<l.length?l:l[0]);return i(a),()=>r(a)})}function $h(e,t){return n=>o=>e[n](t,o)}function eC(e){return _(e.addListener)&&_(e.removeListener)}function tC(e){return _(e.on)&&_(e.off)}function nC(e){return _(e.addEventListener)&&_(e.removeEventListener)}function Ae(e,t){return q((n,o)=>{let i=0;n.subscribe(B(o,r=>e.call(t,r,i++)&&o.next(r)))})}function Rt(e){return q((t,n)=>{let o=null,i=!1,r;o=t.subscribe(B(n,void 0,void 0,s=>{r=ge(e(s,Rt(e)(t))),o?(o.unsubscribe(),o=null,r.subscribe(n)):i=!0})),i&&(o.unsubscribe(),o=null,r.subscribe(n))})}function zh(e,t,n,o,i){return(r,s)=>{let a=n,l=t,c=0;r.subscribe(B(s,u=>{let d=c++;l=a?e(l,u,d):(a=!0,u),o&&s.next(l)},i&&(()=>{a&&s.next(l),s.complete()})))}}function Dn(e,t){return _(t)?fe(e,t,1):fe(e,1)}function In(e){return q((t,n)=>{let o=!1;t.subscribe(B(n,i=>{o=!0,n.next(i)},()=>{o||n.next(e),n.complete()}))})}function Kt(e){return e<=0?()=>Ke:q((t,n)=>{let o=0;t.subscribe(B(n,i=>{++o<=e&&(n.next(i),e<=o&&n.complete())}))})}function jc(e,t=Ue){return e=e??oC,q((n,o)=>{let i,r=!0;n.subscribe(B(o,s=>{let a=t(s);(r||!e(i,a))&&(r=!1,i=a,o.next(s))}))})}function oC(e,t){return e===t}function qs(e=iC){return q((t,n)=>{let o=!1;t.subscribe(B(n,i=>{o=!0,n.next(i)},()=>o?n.complete():n.error(e())))})}function iC(){return new Qt}function Zi(e){return q((t,n)=>{try{t.subscribe(n)}finally{n.add(e)}})}function Xt(e,t){let n=arguments.length>=2;return o=>o.pipe(e?Ae((i,r)=>e(i,r,o)):Ue,Kt(1),n?In(t):qs(()=>new Qt))}function qo(e){return e<=0?()=>Ke:q((t,n)=>{let o=[];t.subscribe(B(n,i=>{o.push(i),e<o.length&&o.shift()},()=>{for(let i of o)n.next(i);n.complete()},void 0,()=>{o=null}))})}function Vc(e,t){let n=arguments.length>=2;return o=>o.pipe(e?Ae((i,r)=>e(i,r,o)):Ue,qo(1),n?In(t):qs(()=>new Qt))}function Bc(e,t){return q(zh(e,t,arguments.length>=2,!0))}function Hc(...e){let t=Cn(e);return q((n,o)=>{(t?zo(e,n,t):zo(e,n)).subscribe(o)})}function Be(e,t){return q((n,o)=>{let i=null,r=0,s=!1,a=()=>s&&!i&&o.complete();n.subscribe(B(o,l=>{i?.unsubscribe();let c=0,u=r++;ge(e(l,u)).subscribe(i=B(o,d=>o.next(t?t(l,d,u,c++):d),()=>{i=null,a()}))},()=>{s=!0,a()}))})}function Gs(e){return q((t,n)=>{ge(e).subscribe(B(n,()=>n.complete(),Wi)),!n.closed&&t.subscribe(n)})}function Se(e,t,n){let o=_(e)||t||n?{next:e,error:t,complete:n}:e;return o?q((i,r)=>{var s;(s=o.subscribe)===null||s===void 0||s.call(o);let a=!0;i.subscribe(B(r,l=>{var c;(c=o.next)===null||c===void 0||c.call(o,l),r.next(l)},()=>{var l;a=!1,(l=o.complete)===null||l===void 0||l.call(o),r.complete()},l=>{var c;a=!1,(c=o.error)===null||c===void 0||c.call(o,l),r.error(l)},()=>{var l,c;a&&((l=o.unsubscribe)===null||l===void 0||l.call(o)),(c=o.finalize)===null||c===void 0||c.call(o)}))}):Ue}function Uh(e){let t=j(null);try{return e()}finally{j(t)}}var eu="https://angular.dev/best-practices/security#preventing-cross-site-scripting-xss",E=class extends Error{code;constructor(t,n){super(Ks(t,n)),this.code=t}};function rC(e){return`NG0${Math.abs(e)}`}function Ks(e,t){return`${rC(e)}${t?": "+t:""}`}function ee(e){for(let t in e)if(e[t]===ee)return t;throw Error("")}function Wh(e,t){for(let n in t)t.hasOwnProperty(n)&&!e.hasOwnProperty(n)&&(e[n]=t[n])}function en(e){if(typeof e=="string")return e;if(Array.isArray(e))return`[${e.map(en).join(", ")}]`;if(e==null)return""+e;let t=e.overriddenName||e.name;if(t)return`${t}`;let n=e.toString();if(n==null)return""+n;let o=n.indexOf(`
`);return o>=0?n.slice(0,o):n}function tu(e,t){return e?t?`${e} ${t}`:e:t||""}var sC=ee({__forward_ref__:ee});function tn(e){return e.__forward_ref__=tn,e.toString=function(){return en(this())},e}function Re(e){return nu(e)?e():e}function nu(e){return typeof e=="function"&&e.hasOwnProperty(sC)&&e.__forward_ref__===tn}function Yh(e,t){e==null&&ou(t,e,null,"!=")}function ou(e,t,n,o){throw new Error(`ASSERTION ERROR: ${e}`+(o==null?"":` [Expected=> ${n} ${o} ${t} <=Actual]`))}function I(e){return{token:e.token,providedIn:e.providedIn||null,factory:e.factory,value:void 0}}function Ot(e){return{providers:e.providers||[],imports:e.imports||[]}}function er(e){return aC(e,Xs)}function iu(e){return er(e)!==null}function aC(e,t){return e.hasOwnProperty(t)&&e[t]||null}function lC(e){let t=e?.[Xs]??null;return t||null}function zc(e){return e&&e.hasOwnProperty(Ys)?e[Ys]:null}var Xs=ee({\u0275prov:ee}),Ys=ee({\u0275inj:ee}),x=class{_desc;ngMetadataName="InjectionToken";\u0275prov;constructor(t,n){this._desc=t,this.\u0275prov=void 0,typeof n=="number"?this.__NG_ELEMENT_ID__=n:n!==void 0&&(this.\u0275prov=I({token:this,providedIn:n.providedIn||"root",factory:n.factory}))}get multi(){return this}toString(){return`InjectionToken ${this._desc}`}};function ru(e){return e&&!!e.\u0275providers}var su=ee({\u0275cmp:ee}),au=ee({\u0275dir:ee}),lu=ee({\u0275pipe:ee}),cu=ee({\u0275mod:ee}),Xi=ee({\u0275fac:ee}),Xn=ee({__NG_ELEMENT_ID__:ee}),qh=ee({__NG_ENV_ID__:ee});function uu(e){return typeof e=="string"?e:e==null?"":String(e)}function Zs(e){return typeof e=="function"?e.name||e.toString():typeof e=="object"&&e!=null&&typeof e.type=="function"?e.type.name||e.type.toString():uu(e)}var du=ee({ngErrorCode:ee}),Zh=ee({ngErrorMessage:ee}),Ki=ee({ngTokenPath:ee});function fu(e,t){return Qh("",-200,t)}function Js(e,t){throw new E(-201,!1)}function cC(e,t){e[Ki]??=[];let n=e[Ki],o;typeof t=="object"&&"multi"in t&&t?.multi===!0?(Yh(t.provide,"Token with multi: true should have a provide property"),o=Zs(t.provide)):o=Zs(t),n[0]!==o&&e[Ki].unshift(o)}function uC(e,t){let n=e[Ki],o=e[du],i=e[Zh]||e.message;return e.message=fC(i,o,n,t),e}function Qh(e,t,n){let o=new E(t,e);return o[du]=t,o[Zh]=e,n&&(o[Ki]=n),o}function dC(e){return e[du]}function fC(e,t,n=[],o=null){let i="";n&&n.length>1&&(i=` Path: ${n.join(" -> ")}.`);let r=o?` Source: ${o}.`:"";return Ks(t,`${e}${r}${i}`)}var Uc;function Kh(){return Uc}function nt(e){let t=Uc;return Uc=e,t}function pu(e,t,n){let o=er(e);if(o&&o.providedIn=="root")return o.value===void 0?o.value=o.factory():o.value;if(n&8)return null;if(t!==void 0)return t;Js(e,"Injector")}var pC={},Gn=pC,qc="__NG_DI_FLAG__",Gc=class{injector;constructor(t){this.injector=t}retrieve(t,n){let o=Wn(n)||0;try{return this.injector.get(t,o&8?null:Gn,o)}catch(i){if(_o(i))return i;throw i}}};function hC(e,t=0){let n=fs();if(n===void 0)throw new E(-203,!1);if(n===null)return pu(e,void 0,t);{let o=mC(t),i=n.retrieve(e,o);if(_o(i)){if(o.optional)return null;throw i}return i}}function S(e,t=0){return(Kh()||hC)(Re(e),t)}function v(e,t){return S(e,Wn(t))}function Wn(e){return typeof e>"u"||typeof e=="number"?e:0|(e.optional&&8)|(e.host&&1)|(e.self&&2)|(e.skipSelf&&4)}function mC(e){return{optional:!!(e&8),host:!!(e&1),self:!!(e&2),skipSelf:!!(e&4)}}function Wc(e){let t=[];for(let n=0;n<e.length;n++){let o=Re(e[n]);if(Array.isArray(o)){if(o.length===0)throw new E(900,!1);let i,r=0;for(let s=0;s<o.length;s++){let a=o[s],l=gC(a);typeof l=="number"?l===-1?i=a.token:r|=l:i=a}t.push(S(i,r))}else t.push(S(o))}return t}function hu(e,t){return e[qc]=t,e.prototype[qc]=t,e}function gC(e){return e[qc]}function Yn(e,t){let n=e.hasOwnProperty(Xi);return n?e[Xi]:null}function Xh(e,t,n){if(e.length!==t.length)return!1;for(let o=0;o<e.length;o++){let i=e[o],r=t[o];if(n&&(i=n(i),r=n(r)),r!==i)return!1}return!0}function Jh(e){return e.flat(Number.POSITIVE_INFINITY)}function ea(e,t){e.forEach(n=>Array.isArray(n)?ea(n,t):t(n))}function mu(e,t,n){t>=e.length?e.push(n):e.splice(t,0,n)}function tr(e,t){return t>=e.length-1?e.pop():e.splice(t,1)[0]}function em(e,t){let n=[];for(let o=0;o<e;o++)n.push(t);return n}function tm(e,t,n,o){let i=e.length;if(i==t)e.push(n,o);else if(i===1)e.push(o,e[0]),e[0]=n;else{for(i--,e.push(e[i-1],e[i]);i>t;){let r=i-2;e[i]=e[r],i--}e[t]=n,e[t+1]=o}}function nm(e,t,n){let o=Wo(e,t);return o>=0?e[o|1]=n:(o=~o,tm(e,o,t,n)),o}function ta(e,t){let n=Wo(e,t);if(n>=0)return e[n|1]}function Wo(e,t){return vC(e,t,1)}function vC(e,t,n){let o=0,i=e.length>>n;for(;i!==o;){let r=o+(i-o>>1),s=e[r<<n];if(t===s)return r<<n;s>t?i=r:o=r+1}return~(i<<n)}var xn={},ot=[],Sn=new x(""),gu=new x("",-1),vu=new x(""),Ji=class{get(t,n=Gn){if(n===Gn){let i=Qh("",-201);throw i.name="\u0275NotFound",i}return n}};function yu(e){return e[cu]||null}function Pt(e){return e[su]||null}function bu(e){return e[au]||null}function om(e){return e[lu]||null}function Yo(e){return{\u0275providers:e}}function im(...e){return{\u0275providers:wu(!0,e),\u0275fromNgModule:!0}}function wu(e,...t){let n=[],o=new Set,i,r=s=>{n.push(s)};return ea(t,s=>{let a=s;Qs(a,r,[],o)&&(i||=[],i.push(a))}),i!==void 0&&rm(i,r),n}function rm(e,t){for(let n=0;n<e.length;n++){let{ngModule:o,providers:i}=e[n];Cu(i,r=>{t(r,o)})}}function Qs(e,t,n,o){if(e=Re(e),!e)return!1;let i=null,r=zc(e),s=!r&&Pt(e);if(!r&&!s){let l=e.ngModule;if(r=zc(l),r)i=l;else return!1}else{if(s&&!s.standalone)return!1;i=e}let a=o.has(i);if(s){if(a)return!1;if(o.add(i),s.dependencies){let l=typeof s.dependencies=="function"?s.dependencies():s.dependencies;for(let c of l)Qs(c,t,n,o)}}else if(r){if(r.imports!=null&&!a){o.add(i);let c;try{ea(r.imports,u=>{Qs(u,t,n,o)&&(c||=[],c.push(u))})}finally{}c!==void 0&&rm(c,t)}if(!a){let c=Yn(i)||(()=>new i);t({provide:i,useFactory:c,deps:ot},i),t({provide:vu,useValue:i,multi:!0},i),t({provide:Sn,useValue:()=>S(i),multi:!0},i)}let l=r.providers;if(l!=null&&!a){let c=e;Cu(l,u=>{t(u,c)})}}else return!1;return i!==e&&e.providers!==void 0}function Cu(e,t){for(let n of e)ru(n)&&(n=n.\u0275providers),Array.isArray(n)?Cu(n,t):t(n)}var yC=ee({provide:String,useValue:ee});function sm(e){return e!==null&&typeof e=="object"&&yC in e}function bC(e){return!!(e&&e.useExisting)}function wC(e){return!!(e&&e.useFactory)}function Zn(e){return typeof e=="function"}function am(e){return!!e.useClass}var nr=new x(""),Ws={},Gh={},$c;function Zo(){return $c===void 0&&($c=new Ji),$c}var ae=class{},Qn=class extends ae{parent;source;scopes;records=new Map;_ngOnDestroyHooks=new Set;_onDestroyHooks=[];get destroyed(){return this._destroyed}_destroyed=!1;injectorDefTypes;constructor(t,n,o,i){super(),this.parent=n,this.source=o,this.scopes=i,Zc(t,s=>this.processProvider(s)),this.records.set(gu,Go(void 0,this)),i.has("environment")&&this.records.set(ae,Go(void 0,this));let r=this.records.get(nr);r!=null&&typeof r.value=="string"&&this.scopes.add(r.value),this.injectorDefTypes=new Set(this.get(vu,ot,{self:!0}))}retrieve(t,n){let o=Wn(n)||0;try{return this.get(t,Gn,o)}catch(i){if(_o(i))return i;throw i}}destroy(){Qi(this),this._destroyed=!0;let t=j(null);try{for(let o of this._ngOnDestroyHooks)o.ngOnDestroy();let n=this._onDestroyHooks;this._onDestroyHooks=[];for(let o of n)o()}finally{this.records.clear(),this._ngOnDestroyHooks.clear(),this.injectorDefTypes.clear(),j(t)}}onDestroy(t){return Qi(this),this._onDestroyHooks.push(t),()=>this.removeOnDestroy(t)}runInContext(t){Qi(this);let n=At(this),o=nt(void 0),i;try{return t()}finally{At(n),nt(o)}}get(t,n=Gn,o){if(Qi(this),t.hasOwnProperty(qh))return t[qh](this);let i=Wn(o),r,s=At(this),a=nt(void 0);try{if(!(i&4)){let c=this.records.get(t);if(c===void 0){let u=xC(t)&&er(t);u&&this.injectableDefInScope(u)?c=Go(Yc(t),Ws):c=null,this.records.set(t,c)}if(c!=null)return this.hydrate(t,c,i)}let l=i&2?Zo():this.parent;return n=i&8&&n===Gn?null:n,l.get(t,n)}catch(l){let c=dC(l);throw c===-200||c===-201?new E(c,null):l}finally{nt(a),At(s)}}resolveInjectorInitializers(){let t=j(null),n=At(this),o=nt(void 0),i;try{let r=this.get(Sn,ot,{self:!0});for(let s of r)s()}finally{At(n),nt(o),j(t)}}toString(){let t=[],n=this.records;for(let o of n.keys())t.push(en(o));return`R3Injector[${t.join(", ")}]`}processProvider(t){t=Re(t);let n=Zn(t)?t:Re(t&&t.provide),o=DC(t);if(!Zn(t)&&t.multi===!0){let i=this.records.get(n);i||(i=Go(void 0,Ws,!0),i.factory=()=>Wc(i.multi),this.records.set(n,i)),n=t,i.multi.push(t)}this.records.set(n,o)}hydrate(t,n,o){let i=j(null);try{if(n.value===Gh)throw fu(en(t));return n.value===Ws&&(n.value=Gh,n.value=n.factory(void 0,o)),typeof n.value=="object"&&n.value&&EC(n.value)&&this._ngOnDestroyHooks.add(n.value),n.value}finally{j(i)}}injectableDefInScope(t){if(!t.providedIn)return!1;let n=Re(t.providedIn);return typeof n=="string"?n==="any"||this.scopes.has(n):this.injectorDefTypes.has(n)}removeOnDestroy(t){let n=this._onDestroyHooks.indexOf(t);n!==-1&&this._onDestroyHooks.splice(n,1)}};function Yc(e){let t=er(e),n=t!==null?t.factory:Yn(e);if(n!==null)return n;if(e instanceof x)throw new E(204,!1);if(e instanceof Function)return CC(e);throw new E(204,!1)}function CC(e){if(e.length>0)throw new E(204,!1);let n=lC(e);return n!==null?()=>n.factory(e):()=>new e}function DC(e){if(sm(e))return Go(void 0,e.useValue);{let t=Du(e);return Go(t,Ws)}}function Du(e,t,n){let o;if(Zn(e)){let i=Re(e);return Yn(i)||Yc(i)}else if(sm(e))o=()=>Re(e.useValue);else if(wC(e))o=()=>e.useFactory(...Wc(e.deps||[]));else if(bC(e))o=(i,r)=>S(Re(e.useExisting),r!==void 0&&r&8?8:void 0);else{let i=Re(e&&(e.useClass||e.provide));if(IC(e))o=()=>new i(...Wc(e.deps));else return Yn(i)||Yc(i)}return o}function Qi(e){if(e.destroyed)throw new E(205,!1)}function Go(e,t,n=!1){return{factory:e,value:t,multi:n?[]:void 0}}function IC(e){return!!e.deps}function EC(e){return e!==null&&typeof e=="object"&&typeof e.ngOnDestroy=="function"}function xC(e){return typeof e=="function"||typeof e=="object"&&e.ngMetadataName==="InjectionToken"}function Zc(e,t){for(let n of e)Array.isArray(n)?Zc(n,t):n&&ru(n)?Zc(n.\u0275providers,t):t(n)}function qe(e,t){let n;e instanceof Qn?(Qi(e),n=e):n=new Gc(e);let o,i=At(n),r=nt(void 0);try{return t()}finally{At(i),nt(r)}}function lm(){return Kh()!==void 0||fs()!=null}var It=0,A=1,O=2,_e=3,ft=4,Ge=5,Qo=6,Ko=7,He=8,Jn=9,nn=10,pe=11,Xo=12,Iu=13,eo=14,Je=15,_n=16,to=17,Ft=18,or=19,Eu=20,Jt=21,na=22,ir=23,it=24,oa=25,Ne=26,cm=1;var Tn=7,rr=8,no=9,We=10;function Lt(e){return Array.isArray(e)&&typeof e[cm]=="object"}function Et(e){return Array.isArray(e)&&e[cm]===!0}function xu(e){return(e.flags&4)!==0}function Mn(e){return e.componentOffset>-1}function sr(e){return(e.flags&1)===1}function jt(e){return!!e.template}function Jo(e){return(e[O]&512)!==0}function oo(e){return(e[O]&256)===256}var um="svg",dm="math";function pt(e){for(;Array.isArray(e);)e=e[It];return e}function Su(e,t){return pt(t[e])}function Vt(e,t){return pt(t[e.index])}function ia(e,t){return e.data[t]}function ht(e,t){let n=t[e];return Lt(n)?n:n[It]}function fm(e){return(e[O]&4)===4}function ra(e){return(e[O]&128)===128}function pm(e){return Et(e[_e])}function kn(e,t){return t==null?null:e[t]}function _u(e){e[to]=0}function Tu(e){e[O]&1024||(e[O]|=1024,ra(e)&&lr(e))}function hm(e,t){for(;e>0;)t=t[eo],e--;return t}function ar(e){return!!(e[O]&9216||e[it]?.dirty)}function sa(e){e[nn].changeDetectionScheduler?.notify(8),e[O]&64&&(e[O]|=1024),ar(e)&&lr(e)}function lr(e){e[nn].changeDetectionScheduler?.notify(0);let t=En(e);for(;t!==null&&!(t[O]&8192||(t[O]|=8192,!ra(t)));)t=En(t)}function Mu(e,t){if(oo(e))throw new E(911,!1);e[Jt]===null&&(e[Jt]=[]),e[Jt].push(t)}function mm(e,t){if(e[Jt]===null)return;let n=e[Jt].indexOf(t);n!==-1&&e[Jt].splice(n,1)}function En(e){let t=e[_e];return Et(t)?t[_e]:t}function ku(e){return e[Ko]??=[]}function Au(e){return e.cleanup??=[]}function gm(e,t,n,o){let i=ku(t);i.push(n),e.firstCreatePass&&Au(e).push(o,i.length-1)}var U={lFrame:km(null),bindingsEnabled:!0,skipHydrationRootTNode:null},cr=function(e){return e[e.Off=0]="Off",e[e.Exhaustive=1]="Exhaustive",e[e.OnlyDirtyViews=2]="OnlyDirtyViews",e}(cr||{}),SC=0,Qc=!1;function vm(){return U.lFrame.elementDepthCount}function ym(){U.lFrame.elementDepthCount++}function bm(){U.lFrame.elementDepthCount--}function Ru(){return U.bindingsEnabled}function Nu(){return U.skipHydrationRootTNode!==null}function wm(e){return U.skipHydrationRootTNode===e}function Cm(){U.skipHydrationRootTNode=null}function te(){return U.lFrame.lView}function $e(){return U.lFrame.tView}function Ou(e){return U.lFrame.contextLView=e,e[He]}function Pu(e){return U.lFrame.contextLView=null,e}function Oe(){let e=Fu();for(;e!==null&&e.type===64;)e=e.parent;return e}function Fu(){return U.lFrame.currentTNode}function Dm(){let e=U.lFrame,t=e.currentTNode;return e.isParent?t:t.parent}function ei(e,t){let n=U.lFrame;n.currentTNode=e,n.isParent=t}function Lu(){return U.lFrame.isParent}function ju(){U.lFrame.isParent=!1}function Vu(e){ou("Must never be called in production mode"),SC=e}function Bu(){return Qc}function Hu(e){let t=Qc;return Qc=e,t}function Im(e){return U.lFrame.bindingIndex=e}function aa(){return U.lFrame.bindingIndex++}function Em(e){let t=U.lFrame,n=t.bindingIndex;return t.bindingIndex=t.bindingIndex+e,n}function xm(){return U.lFrame.inI18n}function Sm(e,t){let n=U.lFrame;n.bindingIndex=n.bindingRootIndex=e,la(t)}function _m(){return U.lFrame.currentDirectiveIndex}function la(e){U.lFrame.currentDirectiveIndex=e}function Tm(e){let t=U.lFrame.currentDirectiveIndex;return t===-1?null:e[t]}function $u(){return U.lFrame.currentQueryIndex}function ca(e){U.lFrame.currentQueryIndex=e}function _C(e){let t=e[A];return t.type===2?t.declTNode:t.type===1?e[Ge]:null}function zu(e,t,n){if(n&4){let i=t,r=e;for(;i=i.parent,i===null&&!(n&1);)if(i=_C(r),i===null||(r=r[eo],i.type&10))break;if(i===null)return!1;t=i,e=r}let o=U.lFrame=Mm();return o.currentTNode=t,o.lView=e,!0}function ua(e){let t=Mm(),n=e[A];U.lFrame=t,t.currentTNode=n.firstChild,t.lView=e,t.tView=n,t.contextLView=e,t.bindingIndex=n.bindingStartIndex,t.inI18n=!1}function Mm(){let e=U.lFrame,t=e===null?null:e.child;return t===null?km(e):t}function km(e){let t={currentTNode:null,isParent:!0,lView:null,tView:null,selectedIndex:-1,contextLView:null,elementDepthCount:0,currentNamespace:null,currentDirectiveIndex:-1,bindingRootIndex:-1,bindingIndex:-1,currentQueryIndex:0,parent:e,child:null,inI18n:!1};return e!==null&&(e.child=t),t}function Am(){let e=U.lFrame;return U.lFrame=e.parent,e.currentTNode=null,e.lView=null,e}var Uu=Am;function da(){let e=Am();e.isParent=!0,e.tView=null,e.selectedIndex=-1,e.contextLView=null,e.elementDepthCount=0,e.currentDirectiveIndex=-1,e.currentNamespace=null,e.bindingRootIndex=-1,e.bindingIndex=-1,e.currentQueryIndex=0}function Rm(e){return(U.lFrame.contextLView=hm(e,U.lFrame.contextLView))[He]}function io(){return U.lFrame.selectedIndex}function An(e){U.lFrame.selectedIndex=e}function qu(){let e=U.lFrame;return ia(e.tView,e.selectedIndex)}function Nm(){return U.lFrame.currentNamespace}var Om=!0;function fa(){return Om}function ur(e){Om=e}function Kc(e,t=null,n=null,o){let i=Gu(e,t,n,o);return i.resolveInjectorInitializers(),i}function Gu(e,t=null,n=null,o,i=new Set){let r=[n||ot,im(e)];return o=o||(typeof e=="object"?void 0:en(e)),new Qn(r,t||Zo(),o||null,i)}var Ee=class e{static THROW_IF_NOT_FOUND=Gn;static NULL=new Ji;static create(t,n){if(Array.isArray(t))return Kc({name:""},n,t,"");{let o=t.name??"";return Kc({name:o},t.parent,t.providers,o)}}static \u0275prov=I({token:e,providedIn:"any",factory:()=>S(gu)});static __NG_ELEMENT_ID__=-1},Ce=new x(""),ro=(()=>{class e{static __NG_ELEMENT_ID__=TC;static __NG_ENV_ID__=n=>n}return e})(),Xc=class extends ro{_lView;constructor(t){super(),this._lView=t}get destroyed(){return oo(this._lView)}onDestroy(t){let n=this._lView;return Mu(n,t),()=>mm(n,t)}};function TC(){return new Xc(te())}var Nt=class{_console=console;handleError(t){this._console.error("ERROR",t)}},xt=new x("",{providedIn:"root",factory:()=>{let e=v(ae),t;return n=>{e.destroyed&&!t?setTimeout(()=>{throw n}):(t??=e.get(Nt),t.handleError(n))}}}),Pm={provide:Sn,useValue:()=>void v(Nt),multi:!0};function Wu(e){return typeof e=="function"&&e[Ve]!==void 0}function so(e,t){let[n,o,i]=xc(e,t?.equal),r=n,s=r[Ve];return r.set=o,r.update=i,r.asReadonly=Fm.bind(r),r}function Fm(){let e=this[Ve];if(e.readonlyFn===void 0){let t=()=>this();t[Ve]=e,e.readonlyFn=t}return e.readonlyFn}function Yu(e){return Wu(e)&&typeof e.set=="function"}var Kn=class{},pa=new x("",{providedIn:"root",factory:()=>!1});var Zu=new x(""),Qu=new x("");var on=(()=>{class e{taskId=0;pendingTasks=new Set;destroyed=!1;pendingTask=new Ie(!1);get hasPendingTasks(){return this.destroyed?!1:this.pendingTask.value}get hasPendingTasksObservable(){return this.destroyed?new z(n=>{n.next(!1),n.complete()}):this.pendingTask}add(){!this.hasPendingTasks&&!this.destroyed&&this.pendingTask.next(!0);let n=this.taskId++;return this.pendingTasks.add(n),n}has(n){return this.pendingTasks.has(n)}remove(n){this.pendingTasks.delete(n),this.pendingTasks.size===0&&this.hasPendingTasks&&this.pendingTask.next(!1)}ngOnDestroy(){this.pendingTasks.clear(),this.hasPendingTasks&&this.pendingTask.next(!1),this.destroyed=!0,this.pendingTask.unsubscribe()}static \u0275prov=I({token:e,providedIn:"root",factory:()=>new e})}return e})();function dr(...e){}var Ku=(()=>{class e{static \u0275prov=I({token:e,providedIn:"root",factory:()=>new Jc})}return e})(),Jc=class{dirtyEffectCount=0;queues=new Map;add(t){this.enqueue(t),this.schedule(t)}schedule(t){t.dirty&&this.dirtyEffectCount++}remove(t){let n=t.zone,o=this.queues.get(n);o.has(t)&&(o.delete(t),t.dirty&&this.dirtyEffectCount--)}enqueue(t){let n=t.zone;this.queues.has(n)||this.queues.set(n,new Set);let o=this.queues.get(n);o.has(t)||o.add(t)}flush(){for(;this.dirtyEffectCount>0;){let t=!1;for(let[n,o]of this.queues)n===null?t||=this.flushQueue(o):t||=n.run(()=>this.flushQueue(o));t||(this.dirtyEffectCount=0)}}flushQueue(t){let n=!1;for(let o of t)o.dirty&&(this.dirtyEffectCount--,n=!0,o.run());return n}};function ai(e){return{toString:e}.toString()}var ha="__parameters__";function VC(e){return function(...n){if(e){let o=e(...n);for(let i in o)this[i]=o[i]}}}function dg(e,t,n){return ai(()=>{let o=VC(t);function i(...r){if(this instanceof i)return o.apply(this,r),this;let s=new i(...r);return a.annotation=s,a;function a(l,c,u){let d=l.hasOwnProperty(ha)?l[ha]:Object.defineProperty(l,ha,{value:[]})[ha];for(;d.length<=u;)d.push(null);return(d[u]=d[u]||[]).push(s),l}}return i.prototype.ngMetadataName=e,i.annotationCls=i,i})}var fg=hu(dg("Optional"),8);var pg=hu(dg("SkipSelf"),4);function BC(e){return typeof e=="function"}var wa=class{previousValue;currentValue;firstChange;constructor(t,n,o){this.previousValue=t,this.currentValue=n,this.firstChange=o}isFirstChange(){return this.firstChange}};function hg(e,t,n,o){t!==null?t.applyValueToInputSignal(t,o):e[n]=o}var Pn=(()=>{let e=()=>mg;return e.ngInherit=!0,e})();function mg(e){return e.type.prototype.ngOnChanges&&(e.setInput=$C),HC}function HC(){let e=vg(this),t=e?.current;if(t){let n=e.previous;if(n===xn)e.previous=t;else for(let o in t)n[o]=t[o];e.current=null,this.ngOnChanges(t)}}function $C(e,t,n,o,i){let r=this.declaredInputs[o],s=vg(e)||zC(e,{previous:xn,current:null}),a=s.current||(s.current={}),l=s.previous,c=l[r];a[r]=new wa(c&&c.currentValue,n,l===xn),hg(e,t,i,n)}var gg="__ngSimpleChanges__";function vg(e){return e[gg]||null}function zC(e,t){return e[gg]=t}var Lm=[];var se=function(e,t=null,n){for(let o=0;o<Lm.length;o++){let i=Lm[o];i(e,t,n)}};function UC(e,t,n){let{ngOnChanges:o,ngOnInit:i,ngDoCheck:r}=t.type.prototype;if(o){let s=mg(t);(n.preOrderHooks??=[]).push(e,s),(n.preOrderCheckHooks??=[]).push(e,s)}i&&(n.preOrderHooks??=[]).push(0-e,i),r&&((n.preOrderHooks??=[]).push(e,r),(n.preOrderCheckHooks??=[]).push(e,r))}function yg(e,t){for(let n=t.directiveStart,o=t.directiveEnd;n<o;n++){let r=e.data[n].type.prototype,{ngAfterContentInit:s,ngAfterContentChecked:a,ngAfterViewInit:l,ngAfterViewChecked:c,ngOnDestroy:u}=r;s&&(e.contentHooks??=[]).push(-n,s),a&&((e.contentHooks??=[]).push(n,a),(e.contentCheckHooks??=[]).push(n,a)),l&&(e.viewHooks??=[]).push(-n,l),c&&((e.viewHooks??=[]).push(n,c),(e.viewCheckHooks??=[]).push(n,c)),u!=null&&(e.destroyHooks??=[]).push(n,u)}}function ga(e,t,n){bg(e,t,3,n)}function va(e,t,n,o){(e[O]&3)===n&&bg(e,t,n,o)}function Xu(e,t){let n=e[O];(n&3)===t&&(n&=16383,n+=1,e[O]=n)}function bg(e,t,n,o){let i=o!==void 0?e[to]&65535:0,r=o??-1,s=t.length-1,a=0;for(let l=i;l<s;l++)if(typeof t[l+1]=="number"){if(a=t[l],o!=null&&a>=o)break}else t[l]<0&&(e[to]+=65536),(a<r||r==-1)&&(qC(e,n,t,l),e[to]=(e[to]&**********)+l+2),l++}function jm(e,t){se(4,e,t);let n=j(null);try{t.call(e)}finally{j(n),se(5,e,t)}}function qC(e,t,n,o){let i=n[o]<0,r=n[o+1],s=i?-n[o]:n[o],a=e[s];i?e[O]>>14<e[to]>>16&&(e[O]&3)===t&&(e[O]+=16384,jm(a,r)):jm(a,r)}var ni=-1,lo=class{factory;name;injectImpl;resolving=!1;canSeeViewProviders;multi;componentProviders;index;providerFactory;constructor(t,n,o,i){this.factory=t,this.name=i,this.canSeeViewProviders=n,this.injectImpl=o}};function GC(e){return(e.flags&8)!==0}function WC(e){return(e.flags&16)!==0}function YC(e,t,n){let o=0;for(;o<n.length;){let i=n[o];if(typeof i=="number"){if(i!==0)break;o++;let r=n[o++],s=n[o++],a=n[o++];e.setAttribute(t,s,a,r)}else{let r=i,s=n[++o];ZC(r)?e.setProperty(t,r,s):e.setAttribute(t,r,s),o++}}return o}function wg(e){return e===3||e===4||e===6}function ZC(e){return e.charCodeAt(0)===64}function oi(e,t){if(!(t===null||t.length===0))if(e===null||e.length===0)e=t.slice();else{let n=-1;for(let o=0;o<t.length;o++){let i=t[o];typeof i=="number"?n=i:n===0||(n===-1||n===2?Vm(e,n,i,null,t[++o]):Vm(e,n,i,null,null))}}return e}function Vm(e,t,n,o,i){let r=0,s=e.length;if(t===-1)s=-1;else for(;r<e.length;){let a=e[r++];if(typeof a=="number"){if(a===t){s=-1;break}else if(a>t){s=r-1;break}}}for(;r<e.length;){let a=e[r];if(typeof a=="number")break;if(a===n){i!==null&&(e[r+1]=i);return}r++,i!==null&&r++}s!==-1&&(e.splice(s,0,t),r=s+1),e.splice(r++,0,n),i!==null&&e.splice(r++,0,i)}function Cg(e){return e!==ni}function Ca(e){return e&32767}function QC(e){return e>>16}function Da(e,t){let n=QC(e),o=t;for(;n>0;)o=o[eo],n--;return o}var ld=!0;function Bm(e){let t=ld;return ld=e,t}var KC=256,Dg=KC-1,Ig=5,XC=0,Bt={};function JC(e,t,n){let o;typeof n=="string"?o=n.charCodeAt(0)||0:n.hasOwnProperty(Xn)&&(o=n[Xn]),o==null&&(o=n[Xn]=XC++);let i=o&Dg,r=1<<i;t.data[e+(i>>Ig)]|=r}function Ia(e,t){let n=Eg(e,t);if(n!==-1)return n;let o=t[A];o.firstCreatePass&&(e.injectorIndex=t.length,Ju(o.data,e),Ju(t,null),Ju(o.blueprint,null));let i=Fd(e,t),r=e.injectorIndex;if(Cg(i)){let s=Ca(i),a=Da(i,t),l=a[A].data;for(let c=0;c<8;c++)t[r+c]=a[s+c]|l[s+c]}return t[r+8]=i,r}function Ju(e,t){e.push(0,0,0,0,0,0,0,0,t)}function Eg(e,t){return e.injectorIndex===-1||e.parent&&e.parent.injectorIndex===e.injectorIndex||t[e.injectorIndex+8]===null?-1:e.injectorIndex}function Fd(e,t){if(e.parent&&e.parent.injectorIndex!==-1)return e.parent.injectorIndex;let n=0,o=null,i=t;for(;i!==null;){if(o=Mg(i),o===null)return ni;if(n++,i=i[eo],o.injectorIndex!==-1)return o.injectorIndex|n<<16}return ni}function cd(e,t,n){JC(e,t,n)}function eD(e,t){if(t==="class")return e.classes;if(t==="style")return e.styles;let n=e.attrs;if(n){let o=n.length,i=0;for(;i<o;){let r=n[i];if(wg(r))break;if(r===0)i=i+2;else if(typeof r=="number")for(i++;i<o&&typeof n[i]=="string";)i++;else{if(r===t)return n[i+1];i=i+2}}}return null}function xg(e,t,n){if(n&8||e!==void 0)return e;Js(t,"NodeInjector")}function Sg(e,t,n,o){if(n&8&&o===void 0&&(o=null),(n&3)===0){let i=e[Jn],r=nt(void 0);try{return i?i.get(t,o,n&8):pu(t,o,n&8)}finally{nt(r)}}return xg(o,t,n)}function _g(e,t,n,o=0,i){if(e!==null){if(t[O]&2048&&!(o&2)){let s=iD(e,t,n,o,Bt);if(s!==Bt)return s}let r=Tg(e,t,n,o,Bt);if(r!==Bt)return r}return Sg(t,n,o,i)}function Tg(e,t,n,o,i){let r=nD(n);if(typeof r=="function"){if(!zu(t,e,o))return o&1?xg(i,n,o):Sg(t,n,o,i);try{let s;if(s=r(o),s==null&&!(o&8))Js(n);else return s}finally{Uu()}}else if(typeof r=="number"){let s=null,a=Eg(e,t),l=ni,c=o&1?t[Je][Ge]:null;for((a===-1||o&4)&&(l=a===-1?Fd(e,t):t[a+8],l===ni||!$m(o,!1)?a=-1:(s=t[A],a=Ca(l),t=Da(l,t)));a!==-1;){let u=t[A];if(Hm(r,a,u.data)){let d=tD(a,t,n,s,o,c);if(d!==Bt)return d}l=t[a+8],l!==ni&&$m(o,t[A].data[a+8]===c)&&Hm(r,a,t)?(s=u,a=Ca(l),t=Da(l,t)):a=-1}}return i}function tD(e,t,n,o,i,r){let s=t[A],a=s.data[e+8],l=o==null?Mn(a)&&ld:o!=s&&(a.type&3)!==0,c=i&1&&r===a,u=ya(a,s,n,l,c);return u!==null?hr(t,s,u,a,i):Bt}function ya(e,t,n,o,i){let r=e.providerIndexes,s=t.data,a=r&1048575,l=e.directiveStart,c=e.directiveEnd,u=r>>20,d=o?a:a+u,p=i?a+u:c;for(let f=d;f<p;f++){let h=s[f];if(f<l&&n===h||f>=l&&h.type===n)return f}if(i){let f=s[l];if(f&&jt(f)&&f.type===n)return l}return null}function hr(e,t,n,o,i){let r=e[n],s=t.data;if(r instanceof lo){let a=r;if(a.resolving){let f=Zs(s[n]);throw fu(f)}let l=Bm(a.canSeeViewProviders);a.resolving=!0;let c=s[n].type||s[n],u,d=a.injectImpl?nt(a.injectImpl):null,p=zu(e,o,0);try{r=e[n]=a.factory(void 0,i,s,e,o),t.firstCreatePass&&n>=o.directiveStart&&UC(n,s[n],t)}finally{d!==null&&nt(d),Bm(l),a.resolving=!1,Uu()}}return r}function nD(e){if(typeof e=="string")return e.charCodeAt(0)||0;let t=e.hasOwnProperty(Xn)?e[Xn]:void 0;return typeof t=="number"?t>=0?t&Dg:oD:t}function Hm(e,t,n){let o=1<<e;return!!(n[t+(e>>Ig)]&o)}function $m(e,t){return!(e&2)&&!(e&1&&t)}var ao=class{_tNode;_lView;constructor(t,n){this._tNode=t,this._lView=n}get(t,n,o){return _g(this._tNode,this._lView,t,Wn(o),n)}};function oD(){return new ao(Oe(),te())}function fo(e){return ai(()=>{let t=e.prototype.constructor,n=t[Xi]||ud(t),o=Object.prototype,i=Object.getPrototypeOf(e.prototype).constructor;for(;i&&i!==o;){let r=i[Xi]||ud(i);if(r&&r!==n)return r;i=Object.getPrototypeOf(i)}return r=>new r})}function ud(e){return nu(e)?()=>{let t=ud(Re(e));return t&&t()}:Yn(e)}function iD(e,t,n,o,i){let r=e,s=t;for(;r!==null&&s!==null&&s[O]&2048&&!Jo(s);){let a=Tg(r,s,n,o|2,Bt);if(a!==Bt)return a;let l=r.parent;if(!l){let c=s[Eu];if(c){let u=c.get(n,Bt,o);if(u!==Bt)return u}l=Mg(s),s=s[eo]}r=l}return i}function Mg(e){let t=e[A],n=t.type;return n===2?t.declTNode:n===1?e[Ge]:null}function an(e){return eD(Oe(),e)}function rD(){return li(Oe(),te())}function li(e,t){return new le(Vt(e,t))}var le=(()=>{class e{nativeElement;constructor(n){this.nativeElement=n}static __NG_ELEMENT_ID__=rD}return e})();function sD(e){return e instanceof le?e.nativeElement:e}function aD(){return this._results[Symbol.iterator]()}var Ea=class{_emitDistinctChangesOnly;dirty=!0;_onDirty=void 0;_results=[];_changesDetected=!1;_changes=void 0;length=0;first=void 0;last=void 0;get changes(){return this._changes??=new re}constructor(t=!1){this._emitDistinctChangesOnly=t}get(t){return this._results[t]}map(t){return this._results.map(t)}filter(t){return this._results.filter(t)}find(t){return this._results.find(t)}reduce(t,n){return this._results.reduce(t,n)}forEach(t){this._results.forEach(t)}some(t){return this._results.some(t)}toArray(){return this._results.slice()}toString(){return this._results.toString()}reset(t,n){this.dirty=!1;let o=Jh(t);(this._changesDetected=!Xh(this._results,o,n))&&(this._results=o,this.length=o.length,this.last=o[this.length-1],this.first=o[0])}notifyOnChanges(){this._changes!==void 0&&(this._changesDetected||!this._emitDistinctChangesOnly)&&this._changes.next(this)}onDirty(t){this._onDirty=t}setDirty(){this.dirty=!0,this._onDirty?.()}destroy(){this._changes!==void 0&&(this._changes.complete(),this._changes.unsubscribe())}[Symbol.iterator]=aD};function kg(e){return(e.flags&128)===128}var Ld=function(e){return e[e.OnPush=0]="OnPush",e[e.Default=1]="Default",e}(Ld||{}),Ag=new Map,lD=0;function cD(){return lD++}function uD(e){Ag.set(e[or],e)}function dd(e){Ag.delete(e[or])}var zm="__ngContext__";function ii(e,t){Lt(t)?(e[zm]=t[or],uD(t)):e[zm]=t}function Rg(e){return Og(e[Xo])}function Ng(e){return Og(e[ft])}function Og(e){for(;e!==null&&!Et(e);)e=e[ft];return e}var fd;function jd(e){fd=e}function Pg(){if(fd!==void 0)return fd;if(typeof document<"u")return document;throw new E(210,!1)}var Pa=new x("",{providedIn:"root",factory:()=>dD}),dD="ng",Fa=new x(""),ci=new x("",{providedIn:"platform",factory:()=>"unknown"});var La=new x("",{providedIn:"root",factory:()=>Pg().body?.querySelector("[ngCspNonce]")?.getAttribute("ngCspNonce")||null});var fD="h",pD="b";var Fg=!1,Lg=new x("",{providedIn:"root",factory:()=>Fg});var hD=(e,t,n,o)=>{};function mD(e,t,n,o){hD(e,t,n,o)}function ja(e){return(e.flags&32)===32}var gD=()=>null;function jg(e,t,n=!1){return gD(e,t,n)}function Vg(e,t){let n=e.contentQueries;if(n!==null){let o=j(null);try{for(let i=0;i<n.length;i+=2){let r=n[i],s=n[i+1];if(s!==-1){let a=e.data[s];ca(r),a.contentQueries(2,t[s],s)}}}finally{j(o)}}}function pd(e,t,n){ca(0);let o=j(null);try{t(e,n)}finally{j(o)}}function Bg(e,t,n){if(xu(t)){let o=j(null);try{let i=t.directiveStart,r=t.directiveEnd;for(let s=i;s<r;s++){let a=e.data[s];if(a.contentQueries){let l=n[s];a.contentQueries(1,l,s)}}}finally{j(o)}}}var rn=function(e){return e[e.Emulated=0]="Emulated",e[e.None=2]="None",e[e.ShadowDom=3]="ShadowDom",e}(rn||{});var hd=class{changingThisBreaksApplicationSecurity;constructor(t){this.changingThisBreaksApplicationSecurity=t}toString(){return`SafeValue must use [property]=binding: ${this.changingThisBreaksApplicationSecurity} (see ${eu})`}};function Hg(e){return e instanceof hd?e.changingThisBreaksApplicationSecurity:e}var vD=/^>|^->|<!--|-->|--!>|<!-$/g,yD=/(<|>)/g,bD="\u200B$1\u200B";function wD(e){return e.replace(vD,t=>t.replace(yD,bD))}function $g(e){return e instanceof Function?e():e}function CD(e,t,n){let o=e.length;for(;;){let i=e.indexOf(t,n);if(i===-1)return i;if(i===0||e.charCodeAt(i-1)<=32){let r=t.length;if(i+r===o||e.charCodeAt(i+r)<=32)return i}n=i+1}}var zg="ng-template";function DD(e,t,n,o){let i=0;if(o){for(;i<t.length&&typeof t[i]=="string";i+=2)if(t[i]==="class"&&CD(t[i+1].toLowerCase(),n,0)!==-1)return!0}else if(Vd(e))return!1;if(i=t.indexOf(1,i),i>-1){let r;for(;++i<t.length&&typeof(r=t[i])=="string";)if(r.toLowerCase()===n)return!0}return!1}function Vd(e){return e.type===4&&e.value!==zg}function ID(e,t,n){let o=e.type===4&&!n?zg:e.value;return t===o}function ED(e,t,n){let o=4,i=e.attrs,r=i!==null?_D(i):0,s=!1;for(let a=0;a<t.length;a++){let l=t[a];if(typeof l=="number"){if(!s&&!St(o)&&!St(l))return!1;if(s&&St(l))continue;s=!1,o=l|o&1;continue}if(!s)if(o&4){if(o=2|o&1,l!==""&&!ID(e,l,n)||l===""&&t.length===1){if(St(o))return!1;s=!0}}else if(o&8){if(i===null||!DD(e,i,l,n)){if(St(o))return!1;s=!0}}else{let c=t[++a],u=xD(l,i,Vd(e),n);if(u===-1){if(St(o))return!1;s=!0;continue}if(c!==""){let d;if(u>r?d="":d=i[u+1].toLowerCase(),o&2&&c!==d){if(St(o))return!1;s=!0}}}}return St(o)||s}function St(e){return(e&1)===0}function xD(e,t,n,o){if(t===null)return-1;let i=0;if(o||!n){let r=!1;for(;i<t.length;){let s=t[i];if(s===e)return i;if(s===3||s===6)r=!0;else if(s===1||s===2){let a=t[++i];for(;typeof a=="string";)a=t[++i];continue}else{if(s===4)break;if(s===0){i+=4;continue}}i+=r?1:2}return-1}else return TD(t,e)}function Ug(e,t,n=!1){for(let o=0;o<t.length;o++)if(ED(e,t[o],n))return!0;return!1}function SD(e){let t=e.attrs;if(t!=null){let n=t.indexOf(5);if((n&1)===0)return t[n+1]}return null}function _D(e){for(let t=0;t<e.length;t++){let n=e[t];if(wg(n))return t}return e.length}function TD(e,t){let n=e.indexOf(4);if(n>-1)for(n++;n<e.length;){let o=e[n];if(typeof o=="number")return-1;if(o===t)return n;n++}return-1}function MD(e,t){e:for(let n=0;n<t.length;n++){let o=t[n];if(e.length===o.length){for(let i=0;i<e.length;i++)if(e[i]!==o[i])continue e;return!0}}return!1}function Um(e,t){return e?":not("+t.trim()+")":t}function kD(e){let t=e[0],n=1,o=2,i="",r=!1;for(;n<e.length;){let s=e[n];if(typeof s=="string")if(o&2){let a=e[++n];i+="["+s+(a.length>0?'="'+a+'"':"")+"]"}else o&8?i+="."+s:o&4&&(i+=" "+s);else i!==""&&!St(s)&&(t+=Um(r,i),i=""),o=s,r=r||!St(o);n++}return i!==""&&(t+=Um(r,i)),t}function AD(e){return e.map(kD).join(",")}function RD(e){let t=[],n=[],o=1,i=2;for(;o<e.length;){let r=e[o];if(typeof r=="string")i===2?r!==""&&t.push(r,e[++o]):i===8&&n.push(r);else{if(!St(i))break;i=r}o++}return n.length&&t.push(1,...n),t}var Fn={};function ND(e,t){return e.createText(t)}function OD(e,t,n){e.setValue(t,n)}function PD(e,t){return e.createComment(wD(t))}function qg(e,t,n){return e.createElement(t,n)}function xa(e,t,n,o,i){e.insertBefore(t,n,o,i)}function Gg(e,t,n){e.appendChild(t,n)}function qm(e,t,n,o,i){o!==null?xa(e,t,n,o,i):Gg(e,t,n)}function FD(e,t,n){e.removeChild(null,t,n)}function LD(e,t,n){e.setAttribute(t,"style",n)}function jD(e,t,n){n===""?e.removeAttribute(t,"class"):e.setAttribute(t,"class",n)}function Wg(e,t,n){let{mergedAttrs:o,classes:i,styles:r}=n;o!==null&&YC(e,t,o),i!==null&&jD(e,t,i),r!==null&&LD(e,t,r)}function Bd(e,t,n,o,i,r,s,a,l,c,u){let d=Ne+o,p=d+i,f=VD(d,p),h=typeof c=="function"?c():c;return f[A]={type:e,blueprint:f,template:n,queries:null,viewQuery:a,declTNode:t,data:f.slice().fill(null,d),bindingStartIndex:d,expandoStartIndex:p,hostBindingOpCodes:null,firstCreatePass:!0,firstUpdatePass:!0,staticViewQueries:!1,staticContentQueries:!1,preOrderHooks:null,preOrderCheckHooks:null,contentHooks:null,contentCheckHooks:null,viewHooks:null,viewCheckHooks:null,destroyHooks:null,cleanup:null,contentQueries:null,components:null,directiveRegistry:typeof r=="function"?r():r,pipeRegistry:typeof s=="function"?s():s,firstChild:null,schemas:l,consts:h,incompleteFirstPass:!1,ssrId:u}}function VD(e,t){let n=[];for(let o=0;o<t;o++)n.push(o<e?null:Fn);return n}function BD(e){let t=e.tView;return t===null||t.incompleteFirstPass?e.tView=Bd(1,null,e.template,e.decls,e.vars,e.directiveDefs,e.pipeDefs,e.viewQuery,e.schemas,e.consts,e.id):t}function Hd(e,t,n,o,i,r,s,a,l,c,u){let d=t.blueprint.slice();return d[It]=i,d[O]=o|4|128|8|64|1024,(c!==null||e&&e[O]&2048)&&(d[O]|=2048),_u(d),d[_e]=d[eo]=e,d[He]=n,d[nn]=s||e&&e[nn],d[pe]=a||e&&e[pe],d[Jn]=l||e&&e[Jn]||null,d[Ge]=r,d[or]=cD(),d[Qo]=u,d[Eu]=c,d[Je]=t.type==2?e[Je]:d,d}function HD(e,t,n){let o=Vt(t,e),i=BD(n),r=e[nn].rendererFactory,s=$d(e,Hd(e,i,null,Yg(n),o,t,null,r.createRenderer(o,n),null,null,null));return e[t.index]=s}function Yg(e){let t=16;return e.signals?t=4096:e.onPush&&(t=64),t}function Zg(e,t,n,o){if(n===0)return-1;let i=t.length;for(let r=0;r<n;r++)t.push(o),e.blueprint.push(o),e.data.push(null);return i}function $d(e,t){return e[Xo]?e[Iu][ft]=t:e[Xo]=t,e[Iu]=t,t}function Qg(e=1){Kg($e(),te(),io()+e,!1)}function Kg(e,t,n,o){if(!o)if((t[O]&3)===3){let r=e.preOrderCheckHooks;r!==null&&ga(t,r,n)}else{let r=e.preOrderHooks;r!==null&&va(t,r,0,n)}An(n)}var Va=function(e){return e[e.None=0]="None",e[e.SignalBased=1]="SignalBased",e[e.HasDecoratorInputTransform=2]="HasDecoratorInputTransform",e}(Va||{});function md(e,t,n,o){let i=j(null);try{let[r,s,a]=e.inputs[n],l=null;(s&Va.SignalBased)!==0&&(l=t[r][Ve]),l!==null&&l.transformFn!==void 0?o=l.transformFn(o):a!==null&&(o=a.call(t,o)),e.setInput!==null?e.setInput(t,l,o,n,r):hg(t,l,r,o)}finally{j(i)}}var sn=function(e){return e[e.Important=1]="Important",e[e.DashCase=2]="DashCase",e}(sn||{}),$D;function zd(e,t){return $D(e,t)}function ti(e,t,n,o,i){if(o!=null){let r,s=!1;Et(o)?r=o:Lt(o)&&(s=!0,o=o[It]);let a=pt(o);e===0&&n!==null?i==null?Gg(t,n,a):xa(t,n,a,i||null,!0):e===1&&n!==null?xa(t,n,a,i||null,!0):e===2?FD(t,a,s):e===3&&t.destroyNode(a),r!=null&&XD(t,e,r,n,i)}}function zD(e,t){Xg(e,t),t[It]=null,t[Ge]=null}function UD(e,t,n,o,i,r){o[It]=i,o[Ge]=t,Ba(e,o,n,1,i,r)}function Xg(e,t){t[nn].changeDetectionScheduler?.notify(9),Ba(e,t,t[pe],2,null,null)}function qD(e){let t=e[Xo];if(!t)return ed(e[A],e);for(;t;){let n=null;if(Lt(t))n=t[Xo];else{let o=t[We];o&&(n=o)}if(!n){for(;t&&!t[ft]&&t!==e;)Lt(t)&&ed(t[A],t),t=t[_e];t===null&&(t=e),Lt(t)&&ed(t[A],t),n=t&&t[ft]}t=n}}function Ud(e,t){let n=e[no],o=n.indexOf(t);n.splice(o,1)}function Jg(e,t){if(oo(t))return;let n=t[pe];n.destroyNode&&Ba(e,t,n,3,null,null),qD(t)}function ed(e,t){if(oo(t))return;let n=j(null);try{t[O]&=-129,t[O]|=256,t[it]&&ws(t[it]),WD(e,t),GD(e,t),t[A].type===1&&t[pe].destroy();let o=t[_n];if(o!==null&&Et(t[_e])){o!==t[_e]&&Ud(o,t);let i=t[Ft];i!==null&&i.detachView(e)}dd(t)}finally{j(n)}}function GD(e,t){let n=e.cleanup,o=t[Ko];if(n!==null)for(let s=0;s<n.length-1;s+=2)if(typeof n[s]=="string"){let a=n[s+3];a>=0?o[a]():o[-a].unsubscribe(),s+=2}else{let a=o[n[s+1]];n[s].call(a)}o!==null&&(t[Ko]=null);let i=t[Jt];if(i!==null){t[Jt]=null;for(let s=0;s<i.length;s++){let a=i[s];a()}}let r=t[ir];if(r!==null){t[ir]=null;for(let s of r)s.destroy()}}function WD(e,t){let n;if(e!=null&&(n=e.destroyHooks)!=null)for(let o=0;o<n.length;o+=2){let i=t[n[o]];if(!(i instanceof lo)){let r=n[o+1];if(Array.isArray(r))for(let s=0;s<r.length;s+=2){let a=i[r[s]],l=r[s+1];se(4,a,l);try{l.call(a)}finally{se(5,a,l)}}else{se(4,i,r);try{r.call(i)}finally{se(5,i,r)}}}}}function ev(e,t,n){return YD(e,t.parent,n)}function YD(e,t,n){let o=t;for(;o!==null&&o.type&168;)t=o,o=t.parent;if(o===null)return n[It];if(Mn(o)){let{encapsulation:i}=e.data[o.directiveStart+o.componentOffset];if(i===rn.None||i===rn.Emulated)return null}return Vt(o,n)}function tv(e,t,n){return QD(e,t,n)}function ZD(e,t,n){return e.type&40?Vt(e,n):null}var QD=ZD,Gm;function qd(e,t,n,o){let i=ev(e,o,t),r=t[pe],s=o.parent||t[Ge],a=tv(s,o,t);if(i!=null)if(Array.isArray(n))for(let l=0;l<n.length;l++)qm(r,i,n[l],a,!1);else qm(r,i,n,a,!1);Gm!==void 0&&Gm(r,o,t,n,i)}function fr(e,t){if(t!==null){let n=t.type;if(n&3)return Vt(t,e);if(n&4)return gd(-1,e[t.index]);if(n&8){let o=t.child;if(o!==null)return fr(e,o);{let i=e[t.index];return Et(i)?gd(-1,i):pt(i)}}else{if(n&128)return fr(e,t.next);if(n&32)return zd(t,e)()||pt(e[t.index]);{let o=nv(e,t);if(o!==null){if(Array.isArray(o))return o[0];let i=En(e[Je]);return fr(i,o)}else return fr(e,t.next)}}}return null}function nv(e,t){if(t!==null){let o=e[Je][Ge],i=t.projection;return o.projection[i]}return null}function gd(e,t){let n=We+e+1;if(n<t.length){let o=t[n],i=o[A].firstChild;if(i!==null)return fr(o,i)}return t[Tn]}function Gd(e,t,n,o,i,r,s){for(;n!=null;){if(n.type===128){n=n.next;continue}let a=o[n.index],l=n.type;if(s&&t===0&&(a&&ii(pt(a),o),n.flags|=2),!ja(n))if(l&8)Gd(e,t,n.child,o,i,r,!1),ti(t,e,i,a,r);else if(l&32){let c=zd(n,o),u;for(;u=c();)ti(t,e,i,u,r);ti(t,e,i,a,r)}else l&16?ov(e,t,o,n,i,r):ti(t,e,i,a,r);n=s?n.projectionNext:n.next}}function Ba(e,t,n,o,i,r){Gd(n,o,e.firstChild,t,i,r,!1)}function KD(e,t,n){let o=t[pe],i=ev(e,n,t),r=n.parent||t[Ge],s=tv(r,n,t);ov(o,0,t,n,i,s)}function ov(e,t,n,o,i,r){let s=n[Je],l=s[Ge].projection[o.projection];if(Array.isArray(l))for(let c=0;c<l.length;c++){let u=l[c];ti(t,e,i,u,r)}else{let c=l,u=s[_e];kg(o)&&(c.flags|=128),Gd(e,t,c,u,i,r,!0)}}function XD(e,t,n,o,i){let r=n[Tn],s=pt(n);r!==s&&ti(t,e,o,r,i);for(let a=We;a<n.length;a++){let l=n[a];Ba(l[A],l,e,t,o,r)}}function JD(e,t,n,o,i){if(t)i?e.addClass(n,o):e.removeClass(n,o);else{let r=o.indexOf("-")===-1?void 0:sn.DashCase;i==null?e.removeStyle(n,o,r):(typeof i=="string"&&i.endsWith("!important")&&(i=i.slice(0,-10),r|=sn.Important),e.setStyle(n,o,i,r))}}function iv(e,t,n,o,i){let r=io(),s=o&2;try{An(-1),s&&t.length>Ne&&Kg(e,t,Ne,!1),se(s?2:0,i,n),n(o,i)}finally{An(r),se(s?3:1,i,n)}}function Wd(e,t,n){sI(e,t,n),(n.flags&64)===64&&aI(e,t,n)}function Ha(e,t,n=Vt){let o=t.localNames;if(o!==null){let i=t.index+1;for(let r=0;r<o.length;r+=2){let s=o[r+1],a=s===-1?n(t,e):e[s];e[i++]=a}}}function eI(e,t,n,o){let r=o.get(Lg,Fg)||n===rn.ShadowDom,s=e.selectRootElement(t,r);return tI(s),s}function tI(e){nI(e)}var nI=()=>null;function oI(e){return e==="class"?"className":e==="for"?"htmlFor":e==="formaction"?"formAction":e==="innerHtml"?"innerHTML":e==="readonly"?"readOnly":e==="tabindex"?"tabIndex":e}function rv(e,t,n,o,i,r){let s=t[A];if(Zd(e,s,t,n,o)){Mn(e)&&rI(t,e.index);return}e.type&3&&(n=oI(n)),iI(e,t,n,o,i,r)}function iI(e,t,n,o,i,r){if(e.type&3){let s=Vt(e,t);o=r!=null?r(o,e.value||"",n):o,i.setProperty(s,n,o)}else e.type&12}function rI(e,t){let n=ht(t,e);n[O]&16||(n[O]|=64)}function sI(e,t,n){let o=n.directiveStart,i=n.directiveEnd;Mn(n)&&HD(t,n,e.data[o+n.componentOffset]),e.firstCreatePass||Ia(n,t);let r=n.initialInputs;for(let s=o;s<i;s++){let a=e.data[s],l=hr(t,e,s,n);if(ii(l,t),r!==null&&cI(t,s-o,l,a,n,r),jt(a)){let c=ht(n.index,t);c[He]=hr(t,e,s,n)}}}function aI(e,t,n){let o=n.directiveStart,i=n.directiveEnd,r=n.index,s=_m();try{An(r);for(let a=o;a<i;a++){let l=e.data[a],c=t[a];la(a),(l.hostBindings!==null||l.hostVars!==0||l.hostAttrs!==null)&&lI(l,c)}}finally{An(-1),la(s)}}function lI(e,t){e.hostBindings!==null&&e.hostBindings(1,t)}function sv(e,t){let n=e.directiveRegistry,o=null;if(n)for(let i=0;i<n.length;i++){let r=n[i];Ug(t,r.selectors,!1)&&(o??=[],jt(r)?o.unshift(r):o.push(r))}return o}function cI(e,t,n,o,i,r){let s=r[t];if(s!==null)for(let a=0;a<s.length;a+=2){let l=s[a],c=s[a+1];md(o,n,l,c)}}function av(e,t,n,o,i){let r=Ne+n,s=t[A],a=i(s,t,e,o,n);t[r]=a,ei(e,!0);let l=e.type===2;return l?(Wg(t[pe],a,e),(vm()===0||sr(e))&&ii(a,t),ym()):ii(a,t),fa()&&(!l||!ja(e))&&qd(s,t,a,e),e}function Yd(e){let t=e;return Lu()?ju():(t=t.parent,ei(t,!1)),t}function uI(e,t){let n=e[Jn];if(!n)return;n.get(xt,null)?.(t)}function Zd(e,t,n,o,i){let r=e.inputs?.[o],s=e.hostDirectiveInputs?.[o],a=!1;if(s)for(let l=0;l<s.length;l+=2){let c=s[l],u=s[l+1],d=t.data[c];md(d,n[c],u,i),a=!0}if(r)for(let l of r){let c=n[l],u=t.data[l];md(u,c,o,i),a=!0}return a}function dI(e,t){let n=ht(t,e),o=n[A];fI(o,n);let i=n[It];i!==null&&n[Qo]===null&&(n[Qo]=jg(i,n[Jn])),se(18),Qd(o,n,n[He]),se(19,n[He])}function fI(e,t){for(let n=t.length;n<e.blueprint.length;n++)t.push(e.blueprint[n])}function Qd(e,t,n){ua(t);try{let o=e.viewQuery;o!==null&&pd(1,o,n);let i=e.template;i!==null&&iv(e,t,i,1,n),e.firstCreatePass&&(e.firstCreatePass=!1),t[Ft]?.finishViewCreation(e),e.staticContentQueries&&Vg(e,t),e.staticViewQueries&&pd(2,e.viewQuery,n);let r=e.components;r!==null&&pI(t,r)}catch(o){throw e.firstCreatePass&&(e.incompleteFirstPass=!0,e.firstCreatePass=!1),o}finally{t[O]&=-5,da()}}function pI(e,t){for(let n=0;n<t.length;n++)dI(e,t[n])}function lv(e,t,n,o){let i=j(null);try{let r=t.tView,a=e[O]&4096?4096:16,l=Hd(e,r,n,a,null,t,null,null,o?.injector??null,o?.embeddedViewInjector??null,o?.dehydratedView??null),c=e[t.index];l[_n]=c;let u=e[Ft];return u!==null&&(l[Ft]=u.createEmbeddedView(r)),Qd(r,l,n),l}finally{j(i)}}function vd(e,t){return!t||t.firstChild===null||kg(e)}var Wm=!1,hI=new x("");function mr(e,t,n,o,i=!1){for(;n!==null;){if(n.type===128){n=i?n.projectionNext:n.next;continue}let r=t[n.index];r!==null&&o.push(pt(r)),Et(r)&&cv(r,o);let s=n.type;if(s&8)mr(e,t,n.child,o);else if(s&32){let a=zd(n,t),l;for(;l=a();)o.push(l)}else if(s&16){let a=nv(t,n);if(Array.isArray(a))o.push(...a);else{let l=En(t[Je]);mr(l[A],l,a,o,!0)}}n=i?n.projectionNext:n.next}return o}function cv(e,t){for(let n=We;n<e.length;n++){let o=e[n],i=o[A].firstChild;i!==null&&mr(o[A],o,i,t)}e[Tn]!==e[It]&&t.push(e[Tn])}function uv(e){if(e[oa]!==null){for(let t of e[oa])t.impl.addSequence(t);e[oa].length=0}}var dv=[];function mI(e){return e[it]??gI(e)}function gI(e){let t=dv.pop()??Object.create(yI);return t.lView=e,t}function vI(e){e.lView[it]!==e&&(e.lView=null,dv.push(e))}var yI=L(b({},Hn),{consumerIsAlwaysLive:!0,kind:"template",consumerMarkedDirty:e=>{lr(e.lView)},consumerOnSignalRead(){this.lView[it]=this}});function bI(e){let t=e[it]??Object.create(wI);return t.lView=e,t}var wI=L(b({},Hn),{consumerIsAlwaysLive:!0,kind:"template",consumerMarkedDirty:e=>{let t=En(e.lView);for(;t&&!fv(t[A]);)t=En(t);t&&Tu(t)},consumerOnSignalRead(){this.lView[it]=this}});function fv(e){return e.type!==2}function pv(e){if(e[ir]===null)return;let t=!0;for(;t;){let n=!1;for(let o of e[ir])o.dirty&&(n=!0,o.zone===null||Zone.current===o.zone?o.run():o.zone.run(()=>o.run()));t=n&&!!(e[O]&8192)}}var CI=100;function Kd(e,t=0){let o=e[nn].rendererFactory,i=!1;i||o.begin?.();try{DI(e,t)}finally{i||o.end?.()}}function DI(e,t){let n=Bu();try{Hu(!0),yd(e,t);let o=0;for(;ar(e);){if(o===CI)throw new E(103,!1);o++,yd(e,1)}}finally{Hu(n)}}function hv(e,t){Vu(t?cr.Exhaustive:cr.OnlyDirtyViews);try{Kd(e)}finally{Vu(cr.Off)}}function II(e,t,n,o){if(oo(t))return;let i=t[O],r=!1,s=!1;ua(t);let a=!0,l=null,c=null;r||(fv(e)?(c=mI(t),l=$n(c)):ys()===null?(a=!1,c=bI(t),l=$n(c)):t[it]&&(ws(t[it]),t[it]=null));try{_u(t),Im(e.bindingStartIndex),n!==null&&iv(e,t,n,2,o);let u=(i&3)===3;if(!r)if(u){let f=e.preOrderCheckHooks;f!==null&&ga(t,f,null)}else{let f=e.preOrderHooks;f!==null&&va(t,f,0,null),Xu(t,0)}if(s||EI(t),pv(t),mv(t,0),e.contentQueries!==null&&Vg(e,t),!r)if(u){let f=e.contentCheckHooks;f!==null&&ga(t,f)}else{let f=e.contentHooks;f!==null&&va(t,f,1),Xu(t,1)}SI(e,t);let d=e.components;d!==null&&vv(t,d,0);let p=e.viewQuery;if(p!==null&&pd(2,p,o),!r)if(u){let f=e.viewCheckHooks;f!==null&&ga(t,f)}else{let f=e.viewHooks;f!==null&&va(t,f,2),Xu(t,2)}if(e.firstUpdatePass===!0&&(e.firstUpdatePass=!1),t[na]){for(let f of t[na])f();t[na]=null}r||(uv(t),t[O]&=-73)}catch(u){throw r||lr(t),u}finally{c!==null&&(Mo(c,l),a&&vI(c)),da()}}function mv(e,t){for(let n=Rg(e);n!==null;n=Ng(n))for(let o=We;o<n.length;o++){let i=n[o];gv(i,t)}}function EI(e){for(let t=Rg(e);t!==null;t=Ng(t)){if(!(t[O]&2))continue;let n=t[no];for(let o=0;o<n.length;o++){let i=n[o];Tu(i)}}}function xI(e,t,n){se(18);let o=ht(t,e);gv(o,n),se(19,o[He])}function gv(e,t){ra(e)&&yd(e,t)}function yd(e,t){let o=e[A],i=e[O],r=e[it],s=!!(t===0&&i&16);if(s||=!!(i&64&&t===0),s||=!!(i&1024),s||=!!(r?.dirty&&qi(r)),s||=!1,r&&(r.dirty=!1),e[O]&=-9217,s)II(o,e,o.template,e[He]);else if(i&8192){let a=j(null);try{pv(e),mv(e,1);let l=o.components;l!==null&&vv(e,l,1),uv(e)}finally{j(a)}}}function vv(e,t,n){for(let o=0;o<t.length;o++)xI(e,t[o],n)}function SI(e,t){let n=e.hostBindingOpCodes;if(n!==null)try{for(let o=0;o<n.length;o++){let i=n[o];if(i<0)An(~i);else{let r=i,s=n[++o],a=n[++o];Sm(s,r);let l=t[r];se(24,l),a(2,l),se(25,l)}}}finally{An(-1)}}function Xd(e,t){let n=Bu()?64:1088;for(e[nn].changeDetectionScheduler?.notify(t);e;){e[O]|=n;let o=En(e);if(Jo(e)&&!o)return e;e=o}return null}function yv(e,t,n,o){return[e,!0,0,t,null,o,null,n,null,null]}function bv(e,t,n,o=!0){let i=t[A];if(_I(i,t,e,n),o){let s=gd(n,e),a=t[pe],l=a.parentNode(e[Tn]);l!==null&&UD(i,e[Ge],a,t,l,s)}let r=t[Qo];r!==null&&r.firstChild!==null&&(r.firstChild=null)}function bd(e,t){if(e.length<=We)return;let n=We+t,o=e[n];if(o){let i=o[_n];i!==null&&i!==e&&Ud(i,o),t>0&&(e[n-1][ft]=o[ft]);let r=tr(e,We+t);zD(o[A],o);let s=r[Ft];s!==null&&s.detachView(r[A]),o[_e]=null,o[ft]=null,o[O]&=-129}return o}function _I(e,t,n,o){let i=We+o,r=n.length;o>0&&(n[i-1][ft]=t),o<r-We?(t[ft]=n[i],mu(n,We+o,t)):(n.push(t),t[ft]=null),t[_e]=n;let s=t[_n];s!==null&&n!==s&&wv(s,t);let a=t[Ft];a!==null&&a.insertView(e),sa(t),t[O]|=128}function wv(e,t){let n=e[no],o=t[_e];if(Lt(o))e[O]|=2;else{let i=o[_e][Je];t[Je]!==i&&(e[O]|=2)}n===null?e[no]=[t]:n.push(t)}var Rn=class{_lView;_cdRefInjectingView;_appRef=null;_attachedToViewContainer=!1;exhaustive;get rootNodes(){let t=this._lView,n=t[A];return mr(n,t,n.firstChild,[])}constructor(t,n){this._lView=t,this._cdRefInjectingView=n}get context(){return this._lView[He]}set context(t){this._lView[He]=t}get destroyed(){return oo(this._lView)}destroy(){if(this._appRef)this._appRef.detachView(this);else if(this._attachedToViewContainer){let t=this._lView[_e];if(Et(t)){let n=t[rr],o=n?n.indexOf(this):-1;o>-1&&(bd(t,o),tr(n,o))}this._attachedToViewContainer=!1}Jg(this._lView[A],this._lView)}onDestroy(t){Mu(this._lView,t)}markForCheck(){Xd(this._cdRefInjectingView||this._lView,4)}detach(){this._lView[O]&=-129}reattach(){sa(this._lView),this._lView[O]|=128}detectChanges(){this._lView[O]|=1024,Kd(this._lView)}checkNoChanges(){return;try{this.exhaustive??=this._lView[Jn].get(hI,Wm)}catch{this.exhaustive=Wm}}attachToViewContainerRef(){if(this._appRef)throw new E(902,!1);this._attachedToViewContainer=!0}detachFromAppRef(){this._appRef=null;let t=Jo(this._lView),n=this._lView[_n];n!==null&&!t&&Ud(n,this._lView),Xg(this._lView[A],this._lView)}attachToAppRef(t){if(this._attachedToViewContainer)throw new E(902,!1);this._appRef=t;let n=Jo(this._lView),o=this._lView[_n];o!==null&&!n&&wv(o,this._lView),sa(this._lView)}};var Nn=(()=>{class e{_declarationLView;_declarationTContainer;elementRef;static __NG_ELEMENT_ID__=TI;constructor(n,o,i){this._declarationLView=n,this._declarationTContainer=o,this.elementRef=i}get ssrId(){return this._declarationTContainer.tView?.ssrId||null}createEmbeddedView(n,o){return this.createEmbeddedViewImpl(n,o)}createEmbeddedViewImpl(n,o,i){let r=lv(this._declarationLView,this._declarationTContainer,n,{embeddedViewInjector:o,dehydratedView:i});return new Rn(r)}}return e})();function TI(){return Jd(Oe(),te())}function Jd(e,t){return e.type&4?new Nn(t,e,li(e,t)):null}function ui(e,t,n,o,i){let r=e.data[t];if(r===null)r=MI(e,t,n,o,i),xm()&&(r.flags|=32);else if(r.type&64){r.type=n,r.value=o,r.attrs=i;let s=Dm();r.injectorIndex=s===null?-1:s.injectorIndex}return ei(r,!0),r}function MI(e,t,n,o,i){let r=Fu(),s=Lu(),a=s?r:r&&r.parent,l=e.data[t]=AI(e,a,n,t,o,i);return kI(e,l,r,s),l}function kI(e,t,n,o){e.firstChild===null&&(e.firstChild=t),n!==null&&(o?n.child==null&&t.parent!==null&&(n.child=t):n.next===null&&(n.next=t,t.prev=n))}function AI(e,t,n,o,i,r){let s=t?t.injectorIndex:-1,a=0;return Nu()&&(a|=128),{type:n,index:o,insertBeforeIndex:null,injectorIndex:s,directiveStart:-1,directiveEnd:-1,directiveStylingLast:-1,componentOffset:-1,propertyBindings:null,flags:a,providerIndexes:0,value:i,attrs:r,mergedAttrs:null,localNames:null,initialInputs:null,inputs:null,hostDirectiveInputs:null,outputs:null,hostDirectiveOutputs:null,directiveToIndex:null,tView:null,next:null,prev:null,projectionNext:null,child:null,parent:t,projection:null,styles:null,stylesWithoutHost:null,residualStyles:void 0,classes:null,classesWithoutHost:null,residualClasses:void 0,classBindings:0,styleBindings:0}}var zL=new RegExp(`^(\\d+)*(${pD}|${fD})*(.*)`);var RI=()=>null;function wd(e,t){return RI(e,t)}var Cv=class{},$a=class{},Cd=class{resolveComponentFactory(t){throw new E(917,!1)}},yr=class{static NULL=new Cd},co=class{},di=(()=>{class e{destroyNode=null;static __NG_ELEMENT_ID__=()=>NI()}return e})();function NI(){let e=te(),t=Oe(),n=ht(t.index,e);return(Lt(n)?n:e)[pe]}var Dv=(()=>{class e{static \u0275prov=I({token:e,providedIn:"root",factory:()=>null})}return e})();var ba={},Dd=class{injector;parentInjector;constructor(t,n){this.injector=t,this.parentInjector=n}get(t,n,o){let i=this.injector.get(t,ba,o);return i!==ba||n===ba?i:this.parentInjector.get(t,n,o)}};function Sa(e,t,n){let o=n?e.styles:null,i=n?e.classes:null,r=0;if(t!==null)for(let s=0;s<t.length;s++){let a=t[s];if(typeof a=="number")r=a;else if(r==1)i=tu(i,a);else if(r==2){let l=a,c=t[++s];o=tu(o,l+": "+c+";")}}n?e.styles=o:e.stylesWithoutHost=o,n?e.classes=i:e.classesWithoutHost=i}function D(e,t=0){let n=te();if(n===null)return S(e,t);let o=Oe();return _g(o,n,Re(e),t)}function Iv(e,t,n,o,i){let r=o===null?null:{"":-1},s=i(e,n);if(s!==null){let a=s,l=null,c=null;for(let u of s)if(u.resolveHostDirectives!==null){[a,l,c]=u.resolveHostDirectives(s);break}FI(e,t,n,a,r,l,c)}r!==null&&o!==null&&OI(n,o,r)}function OI(e,t,n){let o=e.localNames=[];for(let i=0;i<t.length;i+=2){let r=n[t[i+1]];if(r==null)throw new E(-301,!1);o.push(t[i],r)}}function PI(e,t,n){t.componentOffset=n,(e.components??=[]).push(t.index)}function FI(e,t,n,o,i,r,s){let a=o.length,l=!1;for(let p=0;p<a;p++){let f=o[p];!l&&jt(f)&&(l=!0,PI(e,n,p)),cd(Ia(n,t),e,f.type)}$I(n,e.data.length,a);for(let p=0;p<a;p++){let f=o[p];f.providersResolver&&f.providersResolver(f)}let c=!1,u=!1,d=Zg(e,t,a,null);a>0&&(n.directiveToIndex=new Map);for(let p=0;p<a;p++){let f=o[p];if(n.mergedAttrs=oi(n.mergedAttrs,f.hostAttrs),jI(e,n,t,d,f),HI(d,f,i),s!==null&&s.has(f)){let[y,g]=s.get(f);n.directiveToIndex.set(f.type,[d,y+n.directiveStart,g+n.directiveStart])}else(r===null||!r.has(f))&&n.directiveToIndex.set(f.type,d);f.contentQueries!==null&&(n.flags|=4),(f.hostBindings!==null||f.hostAttrs!==null||f.hostVars!==0)&&(n.flags|=64);let h=f.type.prototype;!c&&(h.ngOnChanges||h.ngOnInit||h.ngDoCheck)&&((e.preOrderHooks??=[]).push(n.index),c=!0),!u&&(h.ngOnChanges||h.ngDoCheck)&&((e.preOrderCheckHooks??=[]).push(n.index),u=!0),d++}LI(e,n,r)}function LI(e,t,n){for(let o=t.directiveStart;o<t.directiveEnd;o++){let i=e.data[o];if(n===null||!n.has(i))Ym(0,t,i,o),Ym(1,t,i,o),Qm(t,o,!1);else{let r=n.get(i);Zm(0,t,r,o),Zm(1,t,r,o),Qm(t,o,!0)}}}function Ym(e,t,n,o){let i=e===0?n.inputs:n.outputs;for(let r in i)if(i.hasOwnProperty(r)){let s;e===0?s=t.inputs??={}:s=t.outputs??={},s[r]??=[],s[r].push(o),Ev(t,r)}}function Zm(e,t,n,o){let i=e===0?n.inputs:n.outputs;for(let r in i)if(i.hasOwnProperty(r)){let s=i[r],a;e===0?a=t.hostDirectiveInputs??={}:a=t.hostDirectiveOutputs??={},a[s]??=[],a[s].push(o,r),Ev(t,s)}}function Ev(e,t){t==="class"?e.flags|=8:t==="style"&&(e.flags|=16)}function Qm(e,t,n){let{attrs:o,inputs:i,hostDirectiveInputs:r}=e;if(o===null||!n&&i===null||n&&r===null||Vd(e)){e.initialInputs??=[],e.initialInputs.push(null);return}let s=null,a=0;for(;a<o.length;){let l=o[a];if(l===0){a+=4;continue}else if(l===5){a+=2;continue}else if(typeof l=="number")break;if(!n&&i.hasOwnProperty(l)){let c=i[l];for(let u of c)if(u===t){s??=[],s.push(l,o[a+1]);break}}else if(n&&r.hasOwnProperty(l)){let c=r[l];for(let u=0;u<c.length;u+=2)if(c[u]===t){s??=[],s.push(c[u+1],o[a+1]);break}}a+=2}e.initialInputs??=[],e.initialInputs.push(s)}function jI(e,t,n,o,i){e.data[o]=i;let r=i.factory||(i.factory=Yn(i.type,!0)),s=new lo(r,jt(i),D,null);e.blueprint[o]=s,n[o]=s,VI(e,t,o,Zg(e,n,i.hostVars,Fn),i)}function VI(e,t,n,o,i){let r=i.hostBindings;if(r){let s=e.hostBindingOpCodes;s===null&&(s=e.hostBindingOpCodes=[]);let a=~t.index;BI(s)!=a&&s.push(a),s.push(n,o,r)}}function BI(e){let t=e.length;for(;t>0;){let n=e[--t];if(typeof n=="number"&&n<0)return n}return 0}function HI(e,t,n){if(n){if(t.exportAs)for(let o=0;o<t.exportAs.length;o++)n[t.exportAs[o]]=e;jt(t)&&(n[""]=e)}}function $I(e,t,n){e.flags|=1,e.directiveStart=t,e.directiveEnd=t+n,e.providerIndexes=t}function xv(e,t,n,o,i,r,s,a){let l=t[A],c=l.consts,u=kn(c,s),d=ui(l,e,n,o,u);return r&&Iv(l,t,d,kn(c,a),i),d.mergedAttrs=oi(d.mergedAttrs,d.attrs),d.attrs!==null&&Sa(d,d.attrs,!1),d.mergedAttrs!==null&&Sa(d,d.mergedAttrs,!0),l.queries!==null&&l.queries.elementStart(l,d),d}function ef(e,t){yg(e,t),xu(t)&&e.queries.elementEnd(t)}function zI(e,t,n,o,i,r){let s=t.consts,a=kn(s,i),l=ui(t,e,n,o,a);if(l.mergedAttrs=oi(l.mergedAttrs,l.attrs),r!=null){let c=kn(s,r);l.localNames=[];for(let u=0;u<c.length;u+=2)l.localNames.push(c[u],-1)}return l.attrs!==null&&Sa(l,l.attrs,!1),l.mergedAttrs!==null&&Sa(l,l.mergedAttrs,!0),t.queries!==null&&t.queries.elementStart(t,l),l}function tf(e){return _v(e)?Array.isArray(e)||!(e instanceof Map)&&Symbol.iterator in e:!1}function Sv(e,t){if(Array.isArray(e))for(let n=0;n<e.length;n++)t(e[n]);else{let n=e[Symbol.iterator](),o;for(;!(o=n.next()).done;)t(o.value)}}function _v(e){return e!==null&&(typeof e=="function"||typeof e=="object")}function za(e,t,n){if(n===Fn)return!1;let o=e[t];return Object.is(o,n)?!1:(e[t]=n,!0)}function td(e,t,n){return function o(i){let r=Mn(e)?ht(e.index,t):t;Xd(r,5);let s=t[He],a=Km(t,s,n,i),l=o.__ngNextListenerFn__;for(;l;)a=Km(t,s,l,i)&&a,l=l.__ngNextListenerFn__;return a}}function Km(e,t,n,o){let i=j(null);try{return se(6,t,n),n(o)!==!1}catch(r){return uI(e,r),!1}finally{se(7,t,n),j(i)}}function UI(e,t,n,o,i,r,s,a){let l=sr(e),c=!1,u=null;if(!o&&l&&(u=qI(t,n,r,e.index)),u!==null){let d=u.__ngLastListenerFn__||u;d.__ngNextListenerFn__=s,u.__ngLastListenerFn__=s,c=!0}else{let d=Vt(e,n),p=o?o(d):d;mD(n,p,r,a);let f=i.listen(p,r,a),h=o?y=>o(pt(y[e.index])):e.index;Tv(h,t,n,r,a,f,!1)}return c}function qI(e,t,n,o){let i=e.cleanup;if(i!=null)for(let r=0;r<i.length-1;r+=2){let s=i[r];if(s===n&&i[r+1]===o){let a=t[Ko],l=i[r+2];return a&&a.length>l?a[l]:null}typeof s=="string"&&(r+=2)}return null}function Tv(e,t,n,o,i,r,s){let a=t.firstCreatePass?Au(t):null,l=ku(n),c=l.length;l.push(i,r),a&&a.push(o,e,c,(c+1)*(s?-1:1))}function Xm(e,t,n,o,i,r){let s=t[n],a=t[A],c=a.data[n].outputs[o],d=s[c].subscribe(r);Tv(e.index,a,t,i,r,d,!0)}var Id=Symbol("BINDING");var _a=class extends yr{ngModule;constructor(t){super(),this.ngModule=t}resolveComponentFactory(t){let n=Pt(t);return new On(n,this.ngModule)}};function GI(e){return Object.keys(e).map(t=>{let[n,o,i]=e[t],r={propName:n,templateName:t,isSignal:(o&Va.SignalBased)!==0};return i&&(r.transform=i),r})}function WI(e){return Object.keys(e).map(t=>({propName:e[t],templateName:t}))}function YI(e,t,n){let o=t instanceof ae?t:t?.injector;return o&&e.getStandaloneInjector!==null&&(o=e.getStandaloneInjector(o)||o),o?new Dd(n,o):n}function ZI(e){let t=e.get(co,null);if(t===null)throw new E(407,!1);let n=e.get(Dv,null),o=e.get(Kn,null);return{rendererFactory:t,sanitizer:n,changeDetectionScheduler:o,ngReflect:!1}}function QI(e,t){let n=(e.selectors[0][0]||"div").toLowerCase();return qg(t,n,n==="svg"?um:n==="math"?dm:null)}var On=class extends $a{componentDef;ngModule;selector;componentType;ngContentSelectors;isBoundToModule;cachedInputs=null;cachedOutputs=null;get inputs(){return this.cachedInputs??=GI(this.componentDef.inputs),this.cachedInputs}get outputs(){return this.cachedOutputs??=WI(this.componentDef.outputs),this.cachedOutputs}constructor(t,n){super(),this.componentDef=t,this.ngModule=n,this.componentType=t.type,this.selector=AD(t.selectors),this.ngContentSelectors=t.ngContentSelectors??[],this.isBoundToModule=!!n}create(t,n,o,i,r,s){se(22);let a=j(null);try{let l=this.componentDef,c=KI(o,l,s,r),u=YI(l,i||this.ngModule,t),d=ZI(u),p=d.rendererFactory.createRenderer(null,l),f=o?eI(p,o,l.encapsulation,u):QI(l,p),h=s?.some(Jm)||r?.some(w=>typeof w!="function"&&w.bindings.some(Jm)),y=Hd(null,c,null,512|Yg(l),null,null,d,p,u,null,jg(f,u,!0));y[Ne]=f,ua(y);let g=null;try{let w=xv(Ne,y,2,"#host",()=>c.directiveRegistry,!0,0);f&&(Wg(p,f,w),ii(f,y)),Wd(c,y,w),Bg(c,w,y),ef(c,w),n!==void 0&&JI(w,this.ngContentSelectors,n),g=ht(w.index,y),y[He]=g[He],Qd(c,y,null)}catch(w){throw g!==null&&dd(g),dd(y),w}finally{se(23),da()}return new Ta(this.componentType,y,!!h)}finally{j(a)}}};function KI(e,t,n,o){let i=e?["ng-version","20.1.3"]:RD(t.selectors[0]),r=null,s=null,a=0;if(n)for(let u of n)a+=u[Id].requiredVars,u.create&&(u.targetIdx=0,(r??=[]).push(u)),u.update&&(u.targetIdx=0,(s??=[]).push(u));if(o)for(let u=0;u<o.length;u++){let d=o[u];if(typeof d!="function")for(let p of d.bindings){a+=p[Id].requiredVars;let f=u+1;p.create&&(p.targetIdx=f,(r??=[]).push(p)),p.update&&(p.targetIdx=f,(s??=[]).push(p))}}let l=[t];if(o)for(let u of o){let d=typeof u=="function"?u:u.type,p=bu(d);l.push(p)}return Bd(0,null,XI(r,s),1,a,l,null,null,null,[i],null)}function XI(e,t){return!e&&!t?null:n=>{if(n&1&&e)for(let o of e)o.create();if(n&2&&t)for(let o of t)o.update()}}function Jm(e){let t=e[Id].kind;return t==="input"||t==="twoWay"}var Ta=class extends Cv{_rootLView;_hasInputBindings;instance;hostView;changeDetectorRef;componentType;location;previousInputValues=null;_tNode;constructor(t,n,o){super(),this._rootLView=n,this._hasInputBindings=o,this._tNode=ia(n[A],Ne),this.location=li(this._tNode,n),this.instance=ht(this._tNode.index,n)[He],this.hostView=this.changeDetectorRef=new Rn(n,void 0),this.componentType=t}setInput(t,n){this._hasInputBindings;let o=this._tNode;if(this.previousInputValues??=new Map,this.previousInputValues.has(t)&&Object.is(this.previousInputValues.get(t),n))return;let i=this._rootLView,r=Zd(o,i[A],i,t,n);this.previousInputValues.set(t,n);let s=ht(o.index,i);Xd(s,1)}get injector(){return new ao(this._tNode,this._rootLView)}destroy(){this.hostView.destroy()}onDestroy(t){this.hostView.onDestroy(t)}};function JI(e,t,n){let o=e.projection=[];for(let i=0;i<t.length;i++){let r=n[i];o.push(r!=null&&r.length?Array.from(r):null)}}var mt=(()=>{class e{static __NG_ELEMENT_ID__=eE}return e})();function eE(){let e=Oe();return kv(e,te())}var tE=mt,Mv=class extends tE{_lContainer;_hostTNode;_hostLView;constructor(t,n,o){super(),this._lContainer=t,this._hostTNode=n,this._hostLView=o}get element(){return li(this._hostTNode,this._hostLView)}get injector(){return new ao(this._hostTNode,this._hostLView)}get parentInjector(){let t=Fd(this._hostTNode,this._hostLView);if(Cg(t)){let n=Da(t,this._hostLView),o=Ca(t),i=n[A].data[o+8];return new ao(i,n)}else return new ao(null,this._hostLView)}clear(){for(;this.length>0;)this.remove(this.length-1)}get(t){let n=eg(this._lContainer);return n!==null&&n[t]||null}get length(){return this._lContainer.length-We}createEmbeddedView(t,n,o){let i,r;typeof o=="number"?i=o:o!=null&&(i=o.index,r=o.injector);let s=wd(this._lContainer,t.ssrId),a=t.createEmbeddedViewImpl(n||{},r,s);return this.insertImpl(a,i,vd(this._hostTNode,s)),a}createComponent(t,n,o,i,r,s,a){let l=t&&!BC(t),c;if(l)c=n;else{let g=n||{};c=g.index,o=g.injector,i=g.projectableNodes,r=g.environmentInjector||g.ngModuleRef,s=g.directives,a=g.bindings}let u=l?t:new On(Pt(t)),d=o||this.parentInjector;if(!r&&u.ngModule==null){let w=(l?d:this.parentInjector).get(ae,null);w&&(r=w)}let p=Pt(u.componentType??{}),f=wd(this._lContainer,p?.id??null),h=f?.firstChild??null,y=u.create(d,i,h,r,s,a);return this.insertImpl(y.hostView,c,vd(this._hostTNode,f)),y}insert(t,n){return this.insertImpl(t,n,!0)}insertImpl(t,n,o){let i=t._lView;if(pm(i)){let a=this.indexOf(t);if(a!==-1)this.detach(a);else{let l=i[_e],c=new Mv(l,l[Ge],l[_e]);c.detach(c.indexOf(t))}}let r=this._adjustIndex(n),s=this._lContainer;return bv(s,i,r,o),t.attachToViewContainerRef(),mu(nd(s),r,t),t}move(t,n){return this.insert(t,n)}indexOf(t){let n=eg(this._lContainer);return n!==null?n.indexOf(t):-1}remove(t){let n=this._adjustIndex(t,-1),o=bd(this._lContainer,n);o&&(tr(nd(this._lContainer),n),Jg(o[A],o))}detach(t){let n=this._adjustIndex(t,-1),o=bd(this._lContainer,n);return o&&tr(nd(this._lContainer),n)!=null?new Rn(o):null}_adjustIndex(t,n=0){return t??this.length+n}};function eg(e){return e[rr]}function nd(e){return e[rr]||(e[rr]=[])}function kv(e,t){let n,o=t[e.index];return Et(o)?n=o:(n=yv(o,t,null,e),t[e.index]=n,$d(t,n)),oE(n,t,e,o),new Mv(n,e,t)}function nE(e,t){let n=e[pe],o=n.createComment(""),i=Vt(t,e),r=n.parentNode(i);return xa(n,r,o,n.nextSibling(i),!1),o}var oE=sE,iE=()=>!1;function rE(e,t,n){return iE(e,t,n)}function sE(e,t,n,o){if(e[Tn])return;let i;n.type&8?i=pt(o):i=nE(t,n),e[Tn]=i}var Ed=class e{queryList;matches=null;constructor(t){this.queryList=t}clone(){return new e(this.queryList)}setDirty(){this.queryList.setDirty()}},xd=class e{queries;constructor(t=[]){this.queries=t}createEmbeddedView(t){let n=t.queries;if(n!==null){let o=t.contentQueries!==null?t.contentQueries[0]:n.length,i=[];for(let r=0;r<o;r++){let s=n.getByIndex(r),a=this.queries[s.indexInDeclarationView];i.push(a.clone())}return new e(i)}return null}insertView(t){this.dirtyQueriesWithMatches(t)}detachView(t){this.dirtyQueriesWithMatches(t)}finishViewCreation(t){this.dirtyQueriesWithMatches(t)}dirtyQueriesWithMatches(t){for(let n=0;n<this.queries.length;n++)nf(t,n).matches!==null&&this.queries[n].setDirty()}},Sd=class{flags;read;predicate;constructor(t,n,o=null){this.flags=n,this.read=o,typeof t=="string"?this.predicate=hE(t):this.predicate=t}},_d=class e{queries;constructor(t=[]){this.queries=t}elementStart(t,n){for(let o=0;o<this.queries.length;o++)this.queries[o].elementStart(t,n)}elementEnd(t){for(let n=0;n<this.queries.length;n++)this.queries[n].elementEnd(t)}embeddedTView(t){let n=null;for(let o=0;o<this.length;o++){let i=n!==null?n.length:0,r=this.getByIndex(o).embeddedTView(t,i);r&&(r.indexInDeclarationView=o,n!==null?n.push(r):n=[r])}return n!==null?new e(n):null}template(t,n){for(let o=0;o<this.queries.length;o++)this.queries[o].template(t,n)}getByIndex(t){return this.queries[t]}get length(){return this.queries.length}track(t){this.queries.push(t)}},Td=class e{metadata;matches=null;indexInDeclarationView=-1;crossesNgTemplate=!1;_declarationNodeIndex;_appliesToNextNode=!0;constructor(t,n=-1){this.metadata=t,this._declarationNodeIndex=n}elementStart(t,n){this.isApplyingToNode(n)&&this.matchTNode(t,n)}elementEnd(t){this._declarationNodeIndex===t.index&&(this._appliesToNextNode=!1)}template(t,n){this.elementStart(t,n)}embeddedTView(t,n){return this.isApplyingToNode(t)?(this.crossesNgTemplate=!0,this.addMatch(-t.index,n),new e(this.metadata)):null}isApplyingToNode(t){if(this._appliesToNextNode&&(this.metadata.flags&1)!==1){let n=this._declarationNodeIndex,o=t.parent;for(;o!==null&&o.type&8&&o.index!==n;)o=o.parent;return n===(o!==null?o.index:-1)}return this._appliesToNextNode}matchTNode(t,n){let o=this.metadata.predicate;if(Array.isArray(o))for(let i=0;i<o.length;i++){let r=o[i];this.matchTNodeWithReadOption(t,n,aE(n,r)),this.matchTNodeWithReadOption(t,n,ya(n,t,r,!1,!1))}else o===Nn?n.type&4&&this.matchTNodeWithReadOption(t,n,-1):this.matchTNodeWithReadOption(t,n,ya(n,t,o,!1,!1))}matchTNodeWithReadOption(t,n,o){if(o!==null){let i=this.metadata.read;if(i!==null)if(i===le||i===mt||i===Nn&&n.type&4)this.addMatch(n.index,-2);else{let r=ya(n,t,i,!1,!1);r!==null&&this.addMatch(n.index,r)}else this.addMatch(n.index,o)}}addMatch(t,n){this.matches===null?this.matches=[t,n]:this.matches.push(t,n)}};function aE(e,t){let n=e.localNames;if(n!==null){for(let o=0;o<n.length;o+=2)if(n[o]===t)return n[o+1]}return null}function lE(e,t){return e.type&11?li(e,t):e.type&4?Jd(e,t):null}function cE(e,t,n,o){return n===-1?lE(t,e):n===-2?uE(e,t,o):hr(e,e[A],n,t)}function uE(e,t,n){if(n===le)return li(t,e);if(n===Nn)return Jd(t,e);if(n===mt)return kv(t,e)}function Av(e,t,n,o){let i=t[Ft].queries[o];if(i.matches===null){let r=e.data,s=n.matches,a=[];for(let l=0;s!==null&&l<s.length;l+=2){let c=s[l];if(c<0)a.push(null);else{let u=r[c];a.push(cE(t,u,s[l+1],n.metadata.read))}}i.matches=a}return i.matches}function Md(e,t,n,o){let i=e.queries.getByIndex(n),r=i.matches;if(r!==null){let s=Av(e,t,i,n);for(let a=0;a<r.length;a+=2){let l=r[a];if(l>0)o.push(s[a/2]);else{let c=r[a+1],u=t[-l];for(let d=We;d<u.length;d++){let p=u[d];p[_n]===p[_e]&&Md(p[A],p,c,o)}if(u[no]!==null){let d=u[no];for(let p=0;p<d.length;p++){let f=d[p];Md(f[A],f,c,o)}}}}}return o}function dE(e,t){return e[Ft].queries[t].queryList}function fE(e,t,n){let o=new Ea((n&4)===4);return gm(e,t,o,o.destroy),(t[Ft]??=new xd).queries.push(new Ed(o))-1}function pE(e,t,n){let o=$e();return o.firstCreatePass&&(mE(o,new Sd(e,t,n),-1),(t&2)===2&&(o.staticViewQueries=!0)),fE(o,te(),t)}function hE(e){return e.split(",").map(t=>t.trim())}function mE(e,t,n){e.queries===null&&(e.queries=new _d),e.queries.track(new Td(t,n))}function nf(e,t){return e.queries.getByIndex(t)}function gE(e,t){let n=e[A],o=nf(n,t);return o.crossesNgTemplate?Md(n,e,t,[]):Av(n,e,o,t)}var tg=new Set;function of(e){tg.has(e)||(tg.add(e),performance?.mark?.("mark_feature_usage",{detail:{feature:e}}))}var ri=class{},Ua=class{};var Ma=class extends ri{ngModuleType;_parent;_bootstrapComponents=[];_r3Injector;instance;destroyCbs=[];componentFactoryResolver=new _a(this);constructor(t,n,o,i=!0){super(),this.ngModuleType=t,this._parent=n;let r=yu(t);this._bootstrapComponents=$g(r.bootstrap),this._r3Injector=Gu(t,n,[{provide:ri,useValue:this},{provide:yr,useValue:this.componentFactoryResolver},...o],en(t),new Set(["environment"])),i&&this.resolveInjectorInitializers()}resolveInjectorInitializers(){this._r3Injector.resolveInjectorInitializers(),this.instance=this._r3Injector.get(this.ngModuleType)}get injector(){return this._r3Injector}destroy(){let t=this._r3Injector;!t.destroyed&&t.destroy(),this.destroyCbs.forEach(n=>n()),this.destroyCbs=null}onDestroy(t){this.destroyCbs.push(t)}},ka=class extends Ua{moduleType;constructor(t){super(),this.moduleType=t}create(t){return new Ma(this.moduleType,t,[])}};var gr=class extends ri{injector;componentFactoryResolver=new _a(this);instance=null;constructor(t){super();let n=new Qn([...t.providers,{provide:ri,useValue:this},{provide:yr,useValue:this.componentFactoryResolver}],t.parent||Zo(),t.debugName,new Set(["environment"]));this.injector=n,t.runEnvironmentInitializers&&n.resolveInjectorInitializers()}destroy(){this.injector.destroy()}onDestroy(t){this.injector.onDestroy(t)}};function fi(e,t,n=null){return new gr({providers:e,parent:t,debugName:n,runEnvironmentInitializers:!0}).injector}var vE=(()=>{class e{_injector;cachedInjectors=new Map;constructor(n){this._injector=n}getOrCreateStandaloneInjector(n){if(!n.standalone)return null;if(!this.cachedInjectors.has(n)){let o=wu(!1,n.type),i=o.length>0?fi([o],this._injector,`Standalone[${n.type.name}]`):null;this.cachedInjectors.set(n,i)}return this.cachedInjectors.get(n)}ngOnDestroy(){try{for(let n of this.cachedInjectors.values())n!==null&&n.destroy()}finally{this.cachedInjectors.clear()}}static \u0275prov=I({token:e,providedIn:"environment",factory:()=>new e(S(ae))})}return e})();function Ye(e){return ai(()=>{let t=Rv(e),n=L(b({},t),{decls:e.decls,vars:e.vars,template:e.template,consts:e.consts||null,ngContentSelectors:e.ngContentSelectors,onPush:e.changeDetection===Ld.OnPush,directiveDefs:null,pipeDefs:null,dependencies:t.standalone&&e.dependencies||null,getStandaloneInjector:t.standalone?i=>i.get(vE).getOrCreateStandaloneInjector(n):null,getExternalStyles:null,signals:e.signals??!1,data:e.data||{},encapsulation:e.encapsulation||rn.Emulated,styles:e.styles||ot,_:null,schemas:e.schemas||null,tView:null,id:""});t.standalone&&of("NgStandalone"),Nv(n);let o=e.dependencies;return n.directiveDefs=ng(o,yE),n.pipeDefs=ng(o,om),n.id=CE(n),n})}function yE(e){return Pt(e)||bu(e)}function ln(e){return ai(()=>({type:e.type,bootstrap:e.bootstrap||ot,declarations:e.declarations||ot,imports:e.imports||ot,exports:e.exports||ot,transitiveCompileScopes:null,schemas:e.schemas||null,id:e.id||null}))}function bE(e,t){if(e==null)return xn;let n={};for(let o in e)if(e.hasOwnProperty(o)){let i=e[o],r,s,a,l;Array.isArray(i)?(a=i[0],r=i[1],s=i[2]??r,l=i[3]||null):(r=i,s=i,a=Va.None,l=null),n[r]=[o,a,l],t[r]=s}return n}function wE(e){if(e==null)return xn;let t={};for(let n in e)e.hasOwnProperty(n)&&(t[e[n]]=n);return t}function ze(e){return ai(()=>{let t=Rv(e);return Nv(t),t})}function Rv(e){let t={};return{type:e.type,providersResolver:null,factory:null,hostBindings:e.hostBindings||null,hostVars:e.hostVars||0,hostAttrs:e.hostAttrs||null,contentQueries:e.contentQueries||null,declaredInputs:t,inputConfig:e.inputs||xn,exportAs:e.exportAs||null,standalone:e.standalone??!0,signals:e.signals===!0,selectors:e.selectors||ot,viewQuery:e.viewQuery||null,features:e.features||null,setInput:null,resolveHostDirectives:null,hostDirectives:null,inputs:bE(e.inputs,t),outputs:wE(e.outputs),debugInfo:null}}function Nv(e){e.features?.forEach(t=>t(e))}function ng(e,t){return e?()=>{let n=typeof e=="function"?e():e,o=[];for(let i of n){let r=t(i);r!==null&&o.push(r)}return o}:null}function CE(e){let t=0,n=typeof e.consts=="function"?"":e.consts,o=[e.selectors,e.ngContentSelectors,e.hostVars,e.hostAttrs,n,e.vars,e.decls,e.encapsulation,e.standalone,e.signals,e.exportAs,JSON.stringify(e.inputs),JSON.stringify(e.outputs),Object.getOwnPropertyNames(e.type.prototype),!!e.contentQueries,!!e.viewQuery];for(let r of o.join("|"))t=Math.imul(31,t)+r.charCodeAt(0)<<0;return t+=2147483648,"c"+t}function DE(e){return Object.getPrototypeOf(e.prototype).constructor}function Ht(e){let t=DE(e.type),n=!0,o=[e];for(;t;){let i;if(jt(e))i=t.\u0275cmp||t.\u0275dir;else{if(t.\u0275cmp)throw new E(903,!1);i=t.\u0275dir}if(i){if(n){o.push(i);let s=e;s.inputs=od(e.inputs),s.declaredInputs=od(e.declaredInputs),s.outputs=od(e.outputs);let a=i.hostBindings;a&&_E(e,a);let l=i.viewQuery,c=i.contentQueries;if(l&&xE(e,l),c&&SE(e,c),IE(e,i),Wh(e.outputs,i.outputs),jt(i)&&i.data.animation){let u=e.data;u.animation=(u.animation||[]).concat(i.data.animation)}}let r=i.features;if(r)for(let s=0;s<r.length;s++){let a=r[s];a&&a.ngInherit&&a(e),a===Ht&&(n=!1)}}t=Object.getPrototypeOf(t)}EE(o)}function IE(e,t){for(let n in t.inputs){if(!t.inputs.hasOwnProperty(n)||e.inputs.hasOwnProperty(n))continue;let o=t.inputs[n];o!==void 0&&(e.inputs[n]=o,e.declaredInputs[n]=t.declaredInputs[n])}}function EE(e){let t=0,n=null;for(let o=e.length-1;o>=0;o--){let i=e[o];i.hostVars=t+=i.hostVars,i.hostAttrs=oi(i.hostAttrs,n=oi(n,i.hostAttrs))}}function od(e){return e===xn?{}:e===ot?[]:e}function xE(e,t){let n=e.viewQuery;n?e.viewQuery=(o,i)=>{t(o,i),n(o,i)}:e.viewQuery=t}function SE(e,t){let n=e.contentQueries;n?e.contentQueries=(o,i,r)=>{t(o,i,r),n(o,i,r)}:e.contentQueries=t}function _E(e,t){let n=e.hostBindings;n?e.hostBindings=(o,i)=>{t(o,i),n(o,i)}:e.hostBindings=t}function Ov(e,t,n,o,i,r,s,a){if(n.firstCreatePass){e.mergedAttrs=oi(e.mergedAttrs,e.attrs);let u=e.tView=Bd(2,e,i,r,s,n.directiveRegistry,n.pipeRegistry,null,n.schemas,n.consts,null);n.queries!==null&&(n.queries.template(n,e),u.queries=n.queries.embeddedTView(e))}a&&(e.flags|=a),ei(e,!1);let l=kE(n,t,e,o);fa()&&qd(n,t,l,e),ii(l,t);let c=yv(l,t,l,e);t[o+Ne]=c,$d(t,c),rE(c,e,t)}function TE(e,t,n,o,i,r,s,a,l,c,u){let d=n+Ne,p;return t.firstCreatePass?(p=ui(t,d,4,s||null,a||null),Ru()&&Iv(t,e,p,kn(t.consts,c),sv),yg(t,p)):p=t.data[d],Ov(p,e,t,n,o,i,r,l),sr(p)&&Wd(t,e,p),c!=null&&Ha(e,p,u),p}function ME(e,t,n,o,i,r,s,a,l,c,u){let d=n+Ne,p;if(t.firstCreatePass){if(p=ui(t,d,4,s||null,a||null),c!=null){let f=kn(t.consts,c);p.localNames=[];for(let h=0;h<f.length;h+=2)p.localNames.push(f[h],-1)}}else p=t.data[d];return Ov(p,e,t,n,o,i,r,l),c!=null&&Ha(e,p,u),p}function rf(e,t,n,o,i,r,s,a){let l=te(),c=$e(),u=kn(c.consts,r);return TE(l,c,e,t,n,o,i,u,void 0,s,a),rf}var kE=AE;function AE(e,t,n,o){return ur(!0),t[pe].createComment("")}var sf=function(e){return e[e.CHANGE_DETECTION=0]="CHANGE_DETECTION",e[e.AFTER_NEXT_RENDER=1]="AFTER_NEXT_RENDER",e}(sf||{}),br=new x(""),Pv=!1,kd=class extends re{__isAsync;destroyRef=void 0;pendingTasks=void 0;constructor(t=!1){super(),this.__isAsync=t,lm()&&(this.destroyRef=v(ro,{optional:!0})??void 0,this.pendingTasks=v(on,{optional:!0})??void 0)}emit(t){let n=j(null);try{super.next(t)}finally{j(n)}}subscribe(t,n,o){let i=t,r=n||(()=>null),s=o;if(t&&typeof t=="object"){let l=t;i=l.next?.bind(l),r=l.error?.bind(l),s=l.complete?.bind(l)}this.__isAsync&&(r=this.wrapInTimeout(r),i&&(i=this.wrapInTimeout(i)),s&&(s=this.wrapInTimeout(s)));let a=super.subscribe({next:i,error:r,complete:s});return t instanceof De&&t.add(a),a}wrapInTimeout(t){return n=>{let o=this.pendingTasks?.add();setTimeout(()=>{try{t(n)}finally{o!==void 0&&this.pendingTasks?.remove(o)}})}}},de=kd;function Fv(e){let t,n;function o(){e=dr;try{n!==void 0&&typeof cancelAnimationFrame=="function"&&cancelAnimationFrame(n),t!==void 0&&clearTimeout(t)}catch{}}return t=setTimeout(()=>{e(),o()}),typeof requestAnimationFrame=="function"&&(n=requestAnimationFrame(()=>{e(),o()})),()=>o()}function og(e){return queueMicrotask(()=>e()),()=>{e=dr}}var af="isAngularZone",Aa=af+"_ID",RE=0,W=class e{hasPendingMacrotasks=!1;hasPendingMicrotasks=!1;isStable=!0;onUnstable=new de(!1);onMicrotaskEmpty=new de(!1);onStable=new de(!1);onError=new de(!1);constructor(t){let{enableLongStackTrace:n=!1,shouldCoalesceEventChangeDetection:o=!1,shouldCoalesceRunChangeDetection:i=!1,scheduleInRootZone:r=Pv}=t;if(typeof Zone>"u")throw new E(908,!1);Zone.assertZonePatched();let s=this;s._nesting=0,s._outer=s._inner=Zone.current,Zone.TaskTrackingZoneSpec&&(s._inner=s._inner.fork(new Zone.TaskTrackingZoneSpec)),n&&Zone.longStackTraceZoneSpec&&(s._inner=s._inner.fork(Zone.longStackTraceZoneSpec)),s.shouldCoalesceEventChangeDetection=!i&&o,s.shouldCoalesceRunChangeDetection=i,s.callbackScheduled=!1,s.scheduleInRootZone=r,PE(s)}static isInAngularZone(){return typeof Zone<"u"&&Zone.current.get(af)===!0}static assertInAngularZone(){if(!e.isInAngularZone())throw new E(909,!1)}static assertNotInAngularZone(){if(e.isInAngularZone())throw new E(909,!1)}run(t,n,o){return this._inner.run(t,n,o)}runTask(t,n,o,i){let r=this._inner,s=r.scheduleEventTask("NgZoneEvent: "+i,t,NE,dr,dr);try{return r.runTask(s,n,o)}finally{r.cancelTask(s)}}runGuarded(t,n,o){return this._inner.runGuarded(t,n,o)}runOutsideAngular(t){return this._outer.run(t)}},NE={};function lf(e){if(e._nesting==0&&!e.hasPendingMicrotasks&&!e.isStable)try{e._nesting++,e.onMicrotaskEmpty.emit(null)}finally{if(e._nesting--,!e.hasPendingMicrotasks)try{e.runOutsideAngular(()=>e.onStable.emit(null))}finally{e.isStable=!0}}}function OE(e){if(e.isCheckStableRunning||e.callbackScheduled)return;e.callbackScheduled=!0;function t(){Fv(()=>{e.callbackScheduled=!1,Ad(e),e.isCheckStableRunning=!0,lf(e),e.isCheckStableRunning=!1})}e.scheduleInRootZone?Zone.root.run(()=>{t()}):e._outer.run(()=>{t()}),Ad(e)}function PE(e){let t=()=>{OE(e)},n=RE++;e._inner=e._inner.fork({name:"angular",properties:{[af]:!0,[Aa]:n,[Aa+n]:!0},onInvokeTask:(o,i,r,s,a,l)=>{if(FE(l))return o.invokeTask(r,s,a,l);try{return ig(e),o.invokeTask(r,s,a,l)}finally{(e.shouldCoalesceEventChangeDetection&&s.type==="eventTask"||e.shouldCoalesceRunChangeDetection)&&t(),rg(e)}},onInvoke:(o,i,r,s,a,l,c)=>{try{return ig(e),o.invoke(r,s,a,l,c)}finally{e.shouldCoalesceRunChangeDetection&&!e.callbackScheduled&&!LE(l)&&t(),rg(e)}},onHasTask:(o,i,r,s)=>{o.hasTask(r,s),i===r&&(s.change=="microTask"?(e._hasPendingMicrotasks=s.microTask,Ad(e),lf(e)):s.change=="macroTask"&&(e.hasPendingMacrotasks=s.macroTask))},onHandleError:(o,i,r,s)=>(o.handleError(r,s),e.runOutsideAngular(()=>e.onError.emit(s)),!1)})}function Ad(e){e._hasPendingMicrotasks||(e.shouldCoalesceEventChangeDetection||e.shouldCoalesceRunChangeDetection)&&e.callbackScheduled===!0?e.hasPendingMicrotasks=!0:e.hasPendingMicrotasks=!1}function ig(e){e._nesting++,e.isStable&&(e.isStable=!1,e.onUnstable.emit(null))}function rg(e){e._nesting--,lf(e)}var Ra=class{hasPendingMicrotasks=!1;hasPendingMacrotasks=!1;isStable=!0;onUnstable=new de;onMicrotaskEmpty=new de;onStable=new de;onError=new de;run(t,n,o){return t.apply(n,o)}runGuarded(t,n,o){return t.apply(n,o)}runOutsideAngular(t){return t()}runTask(t,n,o,i){return t.apply(n,o)}};function FE(e){return Lv(e,"__ignore_ng_zone__")}function LE(e){return Lv(e,"__scheduler_tick__")}function Lv(e,t){return!Array.isArray(e)||e.length!==1?!1:e[0]?.data?.[t]===!0}var jv=(()=>{class e{impl=null;execute(){this.impl?.execute()}static \u0275prov=I({token:e,providedIn:"root",factory:()=>new e})}return e})();var cf=(()=>{class e{log(n){console.log(n)}warn(n){console.warn(n)}static \u0275fac=function(o){return new(o||e)};static \u0275prov=I({token:e,factory:e.\u0275fac,providedIn:"platform"})}return e})();var uf=new x("");function po(e){return!!e&&typeof e.then=="function"}function Vv(e){return!!e&&typeof e.subscribe=="function"}var qa=new x("");var df=(()=>{class e{resolve;reject;initialized=!1;done=!1;donePromise=new Promise((n,o)=>{this.resolve=n,this.reject=o});appInits=v(qa,{optional:!0})??[];injector=v(Ee);constructor(){}runInitializers(){if(this.initialized)return;let n=[];for(let i of this.appInits){let r=qe(this.injector,i);if(po(r))n.push(r);else if(Vv(r)){let s=new Promise((a,l)=>{r.subscribe({complete:a,error:l})});n.push(s)}}let o=()=>{this.done=!0,this.resolve()};Promise.all(n).then(()=>{o()}).catch(i=>{this.reject(i)}),n.length===0&&o(),this.initialized=!0}static \u0275fac=function(o){return new(o||e)};static \u0275prov=I({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})(),Ga=new x("");function Bv(){Ec(()=>{let e="";throw new E(600,e)})}function Hv(e){return e.isBoundToModule}var jE=10;var $t=(()=>{class e{_runningTick=!1;_destroyed=!1;_destroyListeners=[];_views=[];internalErrorHandler=v(xt);afterRenderManager=v(jv);zonelessEnabled=v(pa);rootEffectScheduler=v(Ku);dirtyFlags=0;tracingSnapshot=null;allTestViews=new Set;autoDetectTestViews=new Set;includeAllTestViews=!1;afterTick=new re;get allViews(){return[...(this.includeAllTestViews?this.allTestViews:this.autoDetectTestViews).keys(),...this._views]}get destroyed(){return this._destroyed}componentTypes=[];components=[];internalPendingTask=v(on);get isStable(){return this.internalPendingTask.hasPendingTasksObservable.pipe(G(n=>!n))}constructor(){v(br,{optional:!0})}whenStable(){let n;return new Promise(o=>{n=this.isStable.subscribe({next:i=>{i&&o()}})}).finally(()=>{n.unsubscribe()})}_injector=v(ae);_rendererFactory=null;get injector(){return this._injector}bootstrap(n,o){return this.bootstrapImpl(n,o)}bootstrapImpl(n,o,i=Ee.NULL){return this._injector.get(W).run(()=>{se(10);let s=n instanceof $a;if(!this._injector.get(df).done){let h="";throw new E(405,h)}let l;s?l=n:l=this._injector.get(yr).resolveComponentFactory(n),this.componentTypes.push(l.componentType);let c=Hv(l)?void 0:this._injector.get(ri),u=o||l.selector,d=l.create(i,[],u,c),p=d.location.nativeElement,f=d.injector.get(uf,null);return f?.registerApplication(p),d.onDestroy(()=>{this.detachView(d.hostView),pr(this.components,d),f?.unregisterApplication(p)}),this._loadComponent(d),se(11,d),d})}tick(){this.zonelessEnabled||(this.dirtyFlags|=1),this._tick()}_tick(){se(12),this.tracingSnapshot!==null?this.tracingSnapshot.run(sf.CHANGE_DETECTION,this.tickImpl):this.tickImpl()}tickImpl=()=>{if(this._runningTick)throw new E(101,!1);let n=j(null);try{this._runningTick=!0,this.synchronize()}finally{this._runningTick=!1,this.tracingSnapshot?.dispose(),this.tracingSnapshot=null,j(n),this.afterTick.next(),se(13)}};synchronize(){this._rendererFactory===null&&!this._injector.destroyed&&(this._rendererFactory=this._injector.get(co,null,{optional:!0}));let n=0;for(;this.dirtyFlags!==0&&n++<jE;)se(14),this.synchronizeOnce(),se(15)}synchronizeOnce(){this.dirtyFlags&16&&(this.dirtyFlags&=-17,this.rootEffectScheduler.flush());let n=!1;if(this.dirtyFlags&7){let o=!!(this.dirtyFlags&1);this.dirtyFlags&=-8,this.dirtyFlags|=8;for(let{_lView:i}of this.allViews){if(!o&&!ar(i))continue;let r=o&&!this.zonelessEnabled?0:1;Kd(i,r),n=!0}if(this.dirtyFlags&=-5,this.syncDirtyFlagsWithViews(),this.dirtyFlags&23)return}n||(this._rendererFactory?.begin?.(),this._rendererFactory?.end?.()),this.dirtyFlags&8&&(this.dirtyFlags&=-9,this.afterRenderManager.execute()),this.syncDirtyFlagsWithViews()}syncDirtyFlagsWithViews(){if(this.allViews.some(({_lView:n})=>ar(n))){this.dirtyFlags|=2;return}else this.dirtyFlags&=-8}attachView(n){let o=n;this._views.push(o),o.attachToAppRef(this)}detachView(n){let o=n;pr(this._views,o),o.detachFromAppRef()}_loadComponent(n){this.attachView(n.hostView);try{this.tick()}catch(i){this.internalErrorHandler(i)}this.components.push(n),this._injector.get(Ga,[]).forEach(i=>i(n))}ngOnDestroy(){if(!this._destroyed)try{this._destroyListeners.forEach(n=>n()),this._views.slice().forEach(n=>n.destroy())}finally{this._destroyed=!0,this._views=[],this._destroyListeners=[]}}onDestroy(n){return this._destroyListeners.push(n),()=>pr(this._destroyListeners,n)}destroy(){if(this._destroyed)throw new E(406,!1);let n=this._injector;n.destroy&&!n.destroyed&&n.destroy()}get viewCount(){return this._views.length}static \u0275fac=function(o){return new(o||e)};static \u0275prov=I({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})();function pr(e,t){let n=e.indexOf(t);n>-1&&e.splice(n,1)}function ff(e,t,n){let o=te(),i=aa();if(za(o,i,t)){let r=$e(),s=qu();rv(s,o,e,t,o[pe],n)}return ff}function sg(e,t,n,o,i){Zd(t,e,n,i?"class":"style",o)}function Wa(e,t,n,o){let i=te(),r=i[A],s=e+Ne,a=r.firstCreatePass?xv(s,i,2,t,sv,Ru(),n,o):r.data[s];if(av(a,i,e,t,VE),sr(a)){let l=i[A];Wd(l,i,a),Bg(l,a,i)}return o!=null&&Ha(i,a),Wa}function Ya(){let e=$e(),t=Oe(),n=Yd(t);return e.firstCreatePass&&ef(e,n),wm(n)&&Cm(),bm(),n.classesWithoutHost!=null&&GC(n)&&sg(e,n,te(),n.classesWithoutHost,!0),n.stylesWithoutHost!=null&&WC(n)&&sg(e,n,te(),n.stylesWithoutHost,!1),Ya}function Za(e,t,n,o){return Wa(e,t,n,o),Ya(),Za}var VE=(e,t,n,o,i)=>(ur(!0),qg(t[pe],o,Nm()));function pf(){let e=$e(),t=Oe(),n=Yd(t);return e.firstCreatePass&&ef(e,n),pf}function Qa(e,t,n){let o=te(),i=o[A],r=e+Ne,s=i.firstCreatePass?zI(r,i,8,"ng-container",t,n):i.data[r];return av(s,o,e,"ng-container",BE),n!=null&&Ha(o,s),Qa}function hf(){let e=Oe(),t=Yd(e);return pf}var BE=(e,t,n,o,i)=>(ur(!0),PD(t[pe],""));function $v(){return te()}var wr="en-US";var HE=wr;function zv(e){typeof e=="string"&&(HE=e.toLowerCase().replace(/_/g,"-"))}function cn(e,t,n){let o=te(),i=$e(),r=Oe();return Uv(i,o,o[pe],r,e,t,n),cn}function Uv(e,t,n,o,i,r,s){let a=!0,l=null;if((o.type&3||s)&&(l??=td(o,t,r),UI(o,e,t,s,n,i,r,l)&&(a=!1)),a){let c=o.outputs?.[i],u=o.hostDirectiveOutputs?.[i];if(u&&u.length)for(let d=0;d<u.length;d+=2){let p=u[d],f=u[d+1];l??=td(o,t,r),Xm(o,t,p,f,i,l)}if(c&&c.length)for(let d of c)l??=td(o,t,r),Xm(o,t,d,i,i,l)}}function qv(e=1){return Rm(e)}function $E(e,t){let n=null,o=SD(e);for(let i=0;i<t.length;i++){let r=t[i];if(r==="*"){n=i;continue}if(o===null?Ug(e,r,!0):MD(o,r))return i}return n}function rt(e){let t=te()[Je][Ge];if(!t.projection){let n=e?e.length:1,o=t.projection=em(n,null),i=o.slice(),r=t.child;for(;r!==null;){if(r.type!==128){let s=e?$E(r,e):0;s!==null&&(i[s]?i[s].projectionNext=r:o[s]=r,i[s]=r)}r=r.next}}}function st(e,t=0,n,o,i,r){let s=te(),a=$e(),l=o?e+1:null;l!==null&&ME(s,a,l,o,i,r,null,n);let c=ui(a,Ne+e,16,null,n||null);c.projection===null&&(c.projection=t),ju();let d=!s[Qo]||Nu();s[Je][Ge].projection[c.projection]===null&&l!==null?zE(s,a,l):d&&!ja(c)&&KD(a,s,c)}function zE(e,t,n){let o=Ne+n,i=t.data[o],r=e[o],s=wd(r,i.tView.ssrId),a=lv(e,i,void 0,{dehydratedView:s});bv(r,a,0,vd(i,s))}function Ka(e,t,n){pE(e,t,n)}function Cr(e){let t=te(),n=$e(),o=$u();ca(o+1);let i=nf(n,o);if(e.dirty&&fm(t)===((i.metadata.flags&2)===2)){if(i.matches===null)e.reset([]);else{let r=gE(t,o);e.reset(r,sD),e.notifyOnChanges()}return!0}return!1}function Dr(){return dE(te(),$u())}function ma(e,t){return e<<17|t<<2}function uo(e){return e>>17&32767}function UE(e){return(e&2)==2}function qE(e,t){return e&131071|t<<17}function Rd(e){return e|2}function si(e){return(e&131068)>>2}function id(e,t){return e&-131069|t<<2}function GE(e){return(e&1)===1}function Nd(e){return e|1}function WE(e,t,n,o,i,r){let s=r?t.classBindings:t.styleBindings,a=uo(s),l=si(s);e[o]=n;let c=!1,u;if(Array.isArray(n)){let d=n;u=d[1],(u===null||Wo(d,u)>0)&&(c=!0)}else u=n;if(i)if(l!==0){let p=uo(e[a+1]);e[o+1]=ma(p,a),p!==0&&(e[p+1]=id(e[p+1],o)),e[a+1]=qE(e[a+1],o)}else e[o+1]=ma(a,0),a!==0&&(e[a+1]=id(e[a+1],o)),a=o;else e[o+1]=ma(l,0),a===0?a=o:e[l+1]=id(e[l+1],o),l=o;c&&(e[o+1]=Rd(e[o+1])),ag(e,u,o,!0),ag(e,u,o,!1),YE(t,u,e,o,r),s=ma(a,l),r?t.classBindings=s:t.styleBindings=s}function YE(e,t,n,o,i){let r=i?e.residualClasses:e.residualStyles;r!=null&&typeof t=="string"&&Wo(r,t)>=0&&(n[o+1]=Nd(n[o+1]))}function ag(e,t,n,o){let i=e[n+1],r=t===null,s=o?uo(i):si(i),a=!1;for(;s!==0&&(a===!1||r);){let l=e[s],c=e[s+1];ZE(l,t)&&(a=!0,e[s+1]=o?Nd(c):Rd(c)),s=o?uo(c):si(c)}a&&(e[n+1]=o?Rd(i):Nd(i))}function ZE(e,t){return e===null||t==null||(Array.isArray(e)?e[1]:e)===t?!0:Array.isArray(e)&&typeof t=="string"?Wo(e,t)>=0:!1}function Xa(e,t){return QE(e,t,null,!0),Xa}function QE(e,t,n,o){let i=te(),r=$e(),s=Em(2);if(r.firstUpdatePass&&XE(r,e,s,o),t!==Fn&&za(i,s,t)){let a=r.data[io()];ox(r,a,i,i[pe],e,i[s+1]=ix(t,n),o,s)}}function KE(e,t){return t>=e.expandoStartIndex}function XE(e,t,n,o){let i=e.data;if(i[n+1]===null){let r=i[io()],s=KE(e,n);rx(r,o)&&t===null&&!s&&(t=!1),t=JE(i,r,t,o),WE(i,r,t,n,s,o)}}function JE(e,t,n,o){let i=Tm(e),r=o?t.residualClasses:t.residualStyles;if(i===null)(o?t.classBindings:t.styleBindings)===0&&(n=rd(null,e,t,n,o),n=vr(n,t.attrs,o),r=null);else{let s=t.directiveStylingLast;if(s===-1||e[s]!==i)if(n=rd(i,e,t,n,o),r===null){let l=ex(e,t,o);l!==void 0&&Array.isArray(l)&&(l=rd(null,e,t,l[1],o),l=vr(l,t.attrs,o),tx(e,t,o,l))}else r=nx(e,t,o)}return r!==void 0&&(o?t.residualClasses=r:t.residualStyles=r),n}function ex(e,t,n){let o=n?t.classBindings:t.styleBindings;if(si(o)!==0)return e[uo(o)]}function tx(e,t,n,o){let i=n?t.classBindings:t.styleBindings;e[uo(i)]=o}function nx(e,t,n){let o,i=t.directiveEnd;for(let r=1+t.directiveStylingLast;r<i;r++){let s=e[r].hostAttrs;o=vr(o,s,n)}return vr(o,t.attrs,n)}function rd(e,t,n,o,i){let r=null,s=n.directiveEnd,a=n.directiveStylingLast;for(a===-1?a=n.directiveStart:a++;a<s&&(r=t[a],o=vr(o,r.hostAttrs,i),r!==e);)a++;return e!==null&&(n.directiveStylingLast=a),o}function vr(e,t,n){let o=n?1:2,i=-1;if(t!==null)for(let r=0;r<t.length;r++){let s=t[r];typeof s=="number"?i=s:i===o&&(Array.isArray(e)||(e=e===void 0?[]:["",e]),nm(e,s,n?!0:t[++r]))}return e===void 0?null:e}function ox(e,t,n,o,i,r,s,a){if(!(t.type&3))return;let l=e.data,c=l[a+1],u=GE(c)?lg(l,t,n,i,si(c),s):void 0;if(!Na(u)){Na(r)||UE(c)&&(r=lg(l,null,n,i,a,s));let d=Su(io(),n);JD(o,s,d,i,r)}}function lg(e,t,n,o,i,r){let s=t===null,a;for(;i>0;){let l=e[i],c=Array.isArray(l),u=c?l[1]:l,d=u===null,p=n[i+1];p===Fn&&(p=d?ot:void 0);let f=d?ta(p,o):u===o?p:void 0;if(c&&!Na(f)&&(f=ta(l,o)),Na(f)&&(a=f,s))return a;let h=e[i+1];i=s?uo(h):si(h)}if(t!==null){let l=r?t.residualClasses:t.residualStyles;l!=null&&(a=ta(l,o))}return a}function Na(e){return e!==void 0}function ix(e,t){return e==null||e===""||(typeof t=="string"?e=e+t:typeof e=="object"&&(e=en(Hg(e)))),e}function rx(e,t){return(e.flags&(t?8:16))!==0}function sx(e,t=""){let n=te(),o=$e(),i=e+Ne,r=o.firstCreatePass?ui(o,i,1,t,null):o.data[i],s=ax(o,n,r,t,e);n[i]=s,fa()&&qd(o,n,s,r),ei(r,!1)}var ax=(e,t,n,o,i)=>(ur(!0),ND(t[pe],o));function lx(e,t,n,o=""){return za(e,aa(),n)?t+uu(n)+o:Fn}function Gv(e){return mf("",e),Gv}function mf(e,t,n){let o=te(),i=lx(o,e,t,n);return i!==Fn&&cx(o,io(),i),mf}function cx(e,t,n){let o=Su(t,e);OD(e[pe],o,n)}function Wv(e,t,n){Yu(t)&&(t=t());let o=te(),i=aa();if(za(o,i,t)){let r=$e(),s=qu();rv(s,o,e,t,o[pe],n)}return Wv}function ux(e,t){let n=Yu(e);return n&&e.set(t),n}function Yv(e,t){let n=te(),o=$e(),i=Oe();return Uv(o,n,n[pe],i,e,t),Yv}function dx(e,t,n){let o=$e();if(o.firstCreatePass){let i=jt(e);Od(n,o.data,o.blueprint,i,!0),Od(t,o.data,o.blueprint,i,!1)}}function Od(e,t,n,o,i){if(e=Re(e),Array.isArray(e))for(let r=0;r<e.length;r++)Od(e[r],t,n,o,i);else{let r=$e(),s=te(),a=Oe(),l=Zn(e)?e:Re(e.provide),c=Du(e),u=a.providerIndexes&1048575,d=a.directiveStart,p=a.providerIndexes>>20;if(Zn(e)||!e.multi){let f=new lo(c,i,D,null),h=ad(l,t,i?u:u+p,d);h===-1?(cd(Ia(a,s),r,l),sd(r,e,t.length),t.push(l),a.directiveStart++,a.directiveEnd++,i&&(a.providerIndexes+=1048576),n.push(f),s.push(f)):(n[h]=f,s[h]=f)}else{let f=ad(l,t,u+p,d),h=ad(l,t,u,u+p),y=f>=0&&n[f],g=h>=0&&n[h];if(i&&!g||!i&&!y){cd(Ia(a,s),r,l);let w=hx(i?px:fx,n.length,i,o,c,e);!i&&g&&(n[h].providerFactory=w),sd(r,e,t.length,0),t.push(l),a.directiveStart++,a.directiveEnd++,i&&(a.providerIndexes+=1048576),n.push(w),s.push(w)}else{let w=Zv(n[i?h:f],c,!i&&o);sd(r,e,f>-1?f:h,w)}!i&&o&&g&&n[h].componentProviders++}}}function sd(e,t,n,o){let i=Zn(t),r=am(t);if(i||r){let l=(r?Re(t.useClass):t).prototype.ngOnDestroy;if(l){let c=e.destroyHooks||(e.destroyHooks=[]);if(!i&&t.multi){let u=c.indexOf(n);u===-1?c.push(n,[o,l]):c[u+1].push(o,l)}else c.push(n,l)}}}function Zv(e,t,n){return n&&e.componentProviders++,e.multi.push(t)-1}function ad(e,t,n,o){for(let i=n;i<o;i++)if(t[i]===e)return i;return-1}function fx(e,t,n,o,i){return Pd(this.multi,[])}function px(e,t,n,o,i){let r=this.multi,s;if(this.providerFactory){let a=this.providerFactory.componentProviders,l=hr(o,o[A],this.providerFactory.index,i);s=l.slice(0,a),Pd(r,s);for(let c=a;c<l.length;c++)s.push(l[c])}else s=[],Pd(r,s);return s}function Pd(e,t){for(let n=0;n<e.length;n++){let o=e[n];t.push(o())}return t}function hx(e,t,n,o,i,r){let s=new lo(e,n,D,null);return s.multi=[],s.index=t,s.componentProviders=0,Zv(s,i,o&&!n),s}function pi(e,t=[]){return n=>{n.providersResolver=(o,i)=>dx(o,i?i(e):e,t)}}var Oa=class{ngModuleFactory;componentFactories;constructor(t,n){this.ngModuleFactory=t,this.componentFactories=n}},gf=(()=>{class e{compileModuleSync(n){return new ka(n)}compileModuleAsync(n){return Promise.resolve(this.compileModuleSync(n))}compileModuleAndAllComponentsSync(n){let o=this.compileModuleSync(n),i=yu(n),r=$g(i.declarations).reduce((s,a)=>{let l=Pt(a);return l&&s.push(new On(l)),s},[]);return new Oa(o,r)}compileModuleAndAllComponentsAsync(n){return Promise.resolve(this.compileModuleAndAllComponentsSync(n))}clearCache(){}clearCacheFor(n){}getModuleId(n){}static \u0275fac=function(o){return new(o||e)};static \u0275prov=I({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})();var mx=(()=>{class e{zone=v(W);changeDetectionScheduler=v(Kn);applicationRef=v($t);applicationErrorHandler=v(xt);_onMicrotaskEmptySubscription;initialize(){this._onMicrotaskEmptySubscription||(this._onMicrotaskEmptySubscription=this.zone.onMicrotaskEmpty.subscribe({next:()=>{this.changeDetectionScheduler.runningTick||this.zone.run(()=>{try{this.applicationRef.dirtyFlags|=1,this.applicationRef._tick()}catch(n){this.applicationErrorHandler(n)}})}}))}ngOnDestroy(){this._onMicrotaskEmptySubscription?.unsubscribe()}static \u0275fac=function(o){return new(o||e)};static \u0275prov=I({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})();function Qv({ngZoneFactory:e,ignoreChangesOutsideZone:t,scheduleInRootZone:n}){return e??=()=>new W(L(b({},Kv()),{scheduleInRootZone:n})),[{provide:W,useFactory:e},{provide:Sn,multi:!0,useFactory:()=>{let o=v(mx,{optional:!0});return()=>o.initialize()}},{provide:Sn,multi:!0,useFactory:()=>{let o=v(gx);return()=>{o.initialize()}}},t===!0?{provide:Zu,useValue:!0}:[],{provide:Qu,useValue:n??Pv},{provide:xt,useFactory:()=>{let o=v(W),i=v(ae),r;return s=>{o.runOutsideAngular(()=>{i.destroyed&&!r?setTimeout(()=>{throw s}):(r??=i.get(Nt),r.handleError(s))})}}}]}function Kv(e){return{enableLongStackTrace:!1,shouldCoalesceEventChangeDetection:e?.eventCoalescing??!1,shouldCoalesceRunChangeDetection:e?.runCoalescing??!1}}var gx=(()=>{class e{subscription=new De;initialized=!1;zone=v(W);pendingTasks=v(on);initialize(){if(this.initialized)return;this.initialized=!0;let n=null;!this.zone.isStable&&!this.zone.hasPendingMacrotasks&&!this.zone.hasPendingMicrotasks&&(n=this.pendingTasks.add()),this.zone.runOutsideAngular(()=>{this.subscription.add(this.zone.onStable.subscribe(()=>{W.assertNotInAngularZone(),queueMicrotask(()=>{n!==null&&!this.zone.hasPendingMacrotasks&&!this.zone.hasPendingMicrotasks&&(this.pendingTasks.remove(n),n=null)})}))}),this.subscription.add(this.zone.onUnstable.subscribe(()=>{W.assertInAngularZone(),n??=this.pendingTasks.add()}))}ngOnDestroy(){this.subscription.unsubscribe()}static \u0275fac=function(o){return new(o||e)};static \u0275prov=I({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})();var Xv=(()=>{class e{applicationErrorHandler=v(xt);appRef=v($t);taskService=v(on);ngZone=v(W);zonelessEnabled=v(pa);tracing=v(br,{optional:!0});disableScheduling=v(Zu,{optional:!0})??!1;zoneIsDefined=typeof Zone<"u"&&!!Zone.root.run;schedulerTickApplyArgs=[{data:{__scheduler_tick__:!0}}];subscriptions=new De;angularZoneId=this.zoneIsDefined?this.ngZone._inner?.get(Aa):null;scheduleInRootZone=!this.zonelessEnabled&&this.zoneIsDefined&&(v(Qu,{optional:!0})??!1);cancelScheduledCallback=null;useMicrotaskScheduler=!1;runningTick=!1;pendingRenderTaskId=null;constructor(){this.subscriptions.add(this.appRef.afterTick.subscribe(()=>{this.runningTick||this.cleanup()})),this.subscriptions.add(this.ngZone.onUnstable.subscribe(()=>{this.runningTick||this.cleanup()})),this.disableScheduling||=!this.zonelessEnabled&&(this.ngZone instanceof Ra||!this.zoneIsDefined)}notify(n){if(!this.zonelessEnabled&&n===5)return;let o=!1;switch(n){case 0:{this.appRef.dirtyFlags|=2;break}case 3:case 2:case 4:case 5:case 1:{this.appRef.dirtyFlags|=4;break}case 6:{this.appRef.dirtyFlags|=2,o=!0;break}case 12:{this.appRef.dirtyFlags|=16,o=!0;break}case 13:{this.appRef.dirtyFlags|=2,o=!0;break}case 11:{o=!0;break}case 9:case 8:case 7:case 10:default:this.appRef.dirtyFlags|=8}if(this.appRef.tracingSnapshot=this.tracing?.snapshot(this.appRef.tracingSnapshot)??null,!this.shouldScheduleTick(o))return;let i=this.useMicrotaskScheduler?og:Fv;this.pendingRenderTaskId=this.taskService.add(),this.scheduleInRootZone?this.cancelScheduledCallback=Zone.root.run(()=>i(()=>this.tick())):this.cancelScheduledCallback=this.ngZone.runOutsideAngular(()=>i(()=>this.tick()))}shouldScheduleTick(n){return!(this.disableScheduling&&!n||this.appRef.destroyed||this.pendingRenderTaskId!==null||this.runningTick||this.appRef._runningTick||!this.zonelessEnabled&&this.zoneIsDefined&&Zone.current.get(Aa+this.angularZoneId))}tick(){if(this.runningTick||this.appRef.destroyed)return;if(this.appRef.dirtyFlags===0){this.cleanup();return}!this.zonelessEnabled&&this.appRef.dirtyFlags&7&&(this.appRef.dirtyFlags|=1);let n=this.taskService.add();try{this.ngZone.run(()=>{this.runningTick=!0,this.appRef._tick()},void 0,this.schedulerTickApplyArgs)}catch(o){this.taskService.remove(n),this.applicationErrorHandler(o)}finally{this.cleanup()}this.useMicrotaskScheduler=!0,og(()=>{this.useMicrotaskScheduler=!1,this.taskService.remove(n)})}ngOnDestroy(){this.subscriptions.unsubscribe(),this.cleanup()}cleanup(){if(this.runningTick=!1,this.cancelScheduledCallback?.(),this.cancelScheduledCallback=null,this.pendingRenderTaskId!==null){let n=this.pendingRenderTaskId;this.pendingRenderTaskId=null,this.taskService.remove(n)}}static \u0275fac=function(o){return new(o||e)};static \u0275prov=I({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})();function vx(){return typeof $localize<"u"&&$localize.locale||wr}var vf=new x("",{providedIn:"root",factory:()=>v(vf,{optional:!0,skipSelf:!0})||vx()});function un(e){return Uh(e)}function Ir(e,t){return Es(e,t?.equal)}var Jv=class{[Ve];constructor(t){this[Ve]=t}destroy(){this[Ve].destroy()}};var ry=Symbol("InputSignalNode#UNSET"),Mx=L(b({},xs),{transformFn:void 0,applyValueToInputSignal(e,t){ko(e,t)}});function sy(e,t){let n=Object.create(Mx);n.value=e,n.transformFn=t?.transform;function o(){if(To(n),n.value===ry){let i=null;throw new E(-950,i)}return n.value}return o[Ve]=n,o}var kx=new x("");kx.__NG_ELEMENT_ID__=e=>{let t=Oe();if(t===null)throw new E(204,!1);if(t.type&2)return t.value;if(e&8)return null;throw new E(204,!1)};function ey(e,t){return sy(e,t)}function Ax(e){return sy(ry,e)}var ay=(ey.required=Ax,ey);var yf=new x(""),Rx=new x("");function Er(e){return!e.moduleRef}function Nx(e){let t=Er(e)?e.r3Injector:e.moduleRef.injector,n=t.get(W);return n.run(()=>{Er(e)?e.r3Injector.resolveInjectorInitializers():e.moduleRef.resolveInjectorInitializers();let o=t.get(xt),i;if(n.runOutsideAngular(()=>{i=n.onError.subscribe({next:o})}),Er(e)){let r=()=>t.destroy(),s=e.platformInjector.get(yf);s.add(r),t.onDestroy(()=>{i.unsubscribe(),s.delete(r)})}else{let r=()=>e.moduleRef.destroy(),s=e.platformInjector.get(yf);s.add(r),e.moduleRef.onDestroy(()=>{pr(e.allPlatformModules,e.moduleRef),i.unsubscribe(),s.delete(r)})}return Px(o,n,()=>{let r=t.get(on),s=r.add(),a=t.get(df);return a.runInitializers(),a.donePromise.then(()=>{let l=t.get(vf,wr);if(zv(l||wr),!t.get(Rx,!0))return Er(e)?t.get($t):(e.allPlatformModules.push(e.moduleRef),e.moduleRef);if(Er(e)){let u=t.get($t);return e.rootComponent!==void 0&&u.bootstrap(e.rootComponent),u}else return Ox?.(e.moduleRef,e.allPlatformModules),e.moduleRef}).finally(()=>void r.remove(s))})})}var Ox;function Px(e,t,n){try{let o=n();return po(o)?o.catch(i=>{throw t.runOutsideAngular(()=>e(i)),i}):o}catch(o){throw t.runOutsideAngular(()=>e(o)),o}}var Ja=null;function Fx(e=[],t){return Ee.create({name:t,providers:[{provide:nr,useValue:"platform"},{provide:yf,useValue:new Set([()=>Ja=null])},...e]})}function Lx(e=[]){if(Ja)return Ja;let t=Fx(e);return Ja=t,Bv(),jx(t),t}function jx(e){let t=e.get(Fa,null);qe(e,()=>{t?.forEach(n=>n())})}var Te=(()=>{class e{static __NG_ELEMENT_ID__=Vx}return e})();function Vx(e){return Bx(Oe(),te(),(e&16)===16)}function Bx(e,t,n){if(Mn(e)&&!n){let o=ht(e.index,t);return new Rn(o,o)}else if(e.type&175){let o=t[Je];return new Rn(o,t)}return null}var bf=class{constructor(){}supports(t){return tf(t)}create(t){return new wf(t)}},Hx=(e,t)=>t,wf=class{length=0;collection;_linkedRecords=null;_unlinkedRecords=null;_previousItHead=null;_itHead=null;_itTail=null;_additionsHead=null;_additionsTail=null;_movesHead=null;_movesTail=null;_removalsHead=null;_removalsTail=null;_identityChangesHead=null;_identityChangesTail=null;_trackByFn;constructor(t){this._trackByFn=t||Hx}forEachItem(t){let n;for(n=this._itHead;n!==null;n=n._next)t(n)}forEachOperation(t){let n=this._itHead,o=this._removalsHead,i=0,r=null;for(;n||o;){let s=!o||n&&n.currentIndex<ty(o,i,r)?n:o,a=ty(s,i,r),l=s.currentIndex;if(s===o)i--,o=o._nextRemoved;else if(n=n._next,s.previousIndex==null)i++;else{r||(r=[]);let c=a-i,u=l-i;if(c!=u){for(let p=0;p<c;p++){let f=p<r.length?r[p]:r[p]=0,h=f+p;u<=h&&h<c&&(r[p]=f+1)}let d=s.previousIndex;r[d]=u-c}}a!==l&&t(s,a,l)}}forEachPreviousItem(t){let n;for(n=this._previousItHead;n!==null;n=n._nextPrevious)t(n)}forEachAddedItem(t){let n;for(n=this._additionsHead;n!==null;n=n._nextAdded)t(n)}forEachMovedItem(t){let n;for(n=this._movesHead;n!==null;n=n._nextMoved)t(n)}forEachRemovedItem(t){let n;for(n=this._removalsHead;n!==null;n=n._nextRemoved)t(n)}forEachIdentityChange(t){let n;for(n=this._identityChangesHead;n!==null;n=n._nextIdentityChange)t(n)}diff(t){if(t==null&&(t=[]),!tf(t))throw new E(900,!1);return this.check(t)?this:null}onDestroy(){}check(t){this._reset();let n=this._itHead,o=!1,i,r,s;if(Array.isArray(t)){this.length=t.length;for(let a=0;a<this.length;a++)r=t[a],s=this._trackByFn(a,r),n===null||!Object.is(n.trackById,s)?(n=this._mismatch(n,r,s,a),o=!0):(o&&(n=this._verifyReinsertion(n,r,s,a)),Object.is(n.item,r)||this._addIdentityChange(n,r)),n=n._next}else i=0,Sv(t,a=>{s=this._trackByFn(i,a),n===null||!Object.is(n.trackById,s)?(n=this._mismatch(n,a,s,i),o=!0):(o&&(n=this._verifyReinsertion(n,a,s,i)),Object.is(n.item,a)||this._addIdentityChange(n,a)),n=n._next,i++}),this.length=i;return this._truncate(n),this.collection=t,this.isDirty}get isDirty(){return this._additionsHead!==null||this._movesHead!==null||this._removalsHead!==null||this._identityChangesHead!==null}_reset(){if(this.isDirty){let t;for(t=this._previousItHead=this._itHead;t!==null;t=t._next)t._nextPrevious=t._next;for(t=this._additionsHead;t!==null;t=t._nextAdded)t.previousIndex=t.currentIndex;for(this._additionsHead=this._additionsTail=null,t=this._movesHead;t!==null;t=t._nextMoved)t.previousIndex=t.currentIndex;this._movesHead=this._movesTail=null,this._removalsHead=this._removalsTail=null,this._identityChangesHead=this._identityChangesTail=null}}_mismatch(t,n,o,i){let r;return t===null?r=this._itTail:(r=t._prev,this._remove(t)),t=this._unlinkedRecords===null?null:this._unlinkedRecords.get(o,null),t!==null?(Object.is(t.item,n)||this._addIdentityChange(t,n),this._reinsertAfter(t,r,i)):(t=this._linkedRecords===null?null:this._linkedRecords.get(o,i),t!==null?(Object.is(t.item,n)||this._addIdentityChange(t,n),this._moveAfter(t,r,i)):t=this._addAfter(new Cf(n,o),r,i)),t}_verifyReinsertion(t,n,o,i){let r=this._unlinkedRecords===null?null:this._unlinkedRecords.get(o,null);return r!==null?t=this._reinsertAfter(r,t._prev,i):t.currentIndex!=i&&(t.currentIndex=i,this._addToMoves(t,i)),t}_truncate(t){for(;t!==null;){let n=t._next;this._addToRemovals(this._unlink(t)),t=n}this._unlinkedRecords!==null&&this._unlinkedRecords.clear(),this._additionsTail!==null&&(this._additionsTail._nextAdded=null),this._movesTail!==null&&(this._movesTail._nextMoved=null),this._itTail!==null&&(this._itTail._next=null),this._removalsTail!==null&&(this._removalsTail._nextRemoved=null),this._identityChangesTail!==null&&(this._identityChangesTail._nextIdentityChange=null)}_reinsertAfter(t,n,o){this._unlinkedRecords!==null&&this._unlinkedRecords.remove(t);let i=t._prevRemoved,r=t._nextRemoved;return i===null?this._removalsHead=r:i._nextRemoved=r,r===null?this._removalsTail=i:r._prevRemoved=i,this._insertAfter(t,n,o),this._addToMoves(t,o),t}_moveAfter(t,n,o){return this._unlink(t),this._insertAfter(t,n,o),this._addToMoves(t,o),t}_addAfter(t,n,o){return this._insertAfter(t,n,o),this._additionsTail===null?this._additionsTail=this._additionsHead=t:this._additionsTail=this._additionsTail._nextAdded=t,t}_insertAfter(t,n,o){let i=n===null?this._itHead:n._next;return t._next=i,t._prev=n,i===null?this._itTail=t:i._prev=t,n===null?this._itHead=t:n._next=t,this._linkedRecords===null&&(this._linkedRecords=new el),this._linkedRecords.put(t),t.currentIndex=o,t}_remove(t){return this._addToRemovals(this._unlink(t))}_unlink(t){this._linkedRecords!==null&&this._linkedRecords.remove(t);let n=t._prev,o=t._next;return n===null?this._itHead=o:n._next=o,o===null?this._itTail=n:o._prev=n,t}_addToMoves(t,n){return t.previousIndex===n||(this._movesTail===null?this._movesTail=this._movesHead=t:this._movesTail=this._movesTail._nextMoved=t),t}_addToRemovals(t){return this._unlinkedRecords===null&&(this._unlinkedRecords=new el),this._unlinkedRecords.put(t),t.currentIndex=null,t._nextRemoved=null,this._removalsTail===null?(this._removalsTail=this._removalsHead=t,t._prevRemoved=null):(t._prevRemoved=this._removalsTail,this._removalsTail=this._removalsTail._nextRemoved=t),t}_addIdentityChange(t,n){return t.item=n,this._identityChangesTail===null?this._identityChangesTail=this._identityChangesHead=t:this._identityChangesTail=this._identityChangesTail._nextIdentityChange=t,t}},Cf=class{item;trackById;currentIndex=null;previousIndex=null;_nextPrevious=null;_prev=null;_next=null;_prevDup=null;_nextDup=null;_prevRemoved=null;_nextRemoved=null;_nextAdded=null;_nextMoved=null;_nextIdentityChange=null;constructor(t,n){this.item=t,this.trackById=n}},Df=class{_head=null;_tail=null;add(t){this._head===null?(this._head=this._tail=t,t._nextDup=null,t._prevDup=null):(this._tail._nextDup=t,t._prevDup=this._tail,t._nextDup=null,this._tail=t)}get(t,n){let o;for(o=this._head;o!==null;o=o._nextDup)if((n===null||n<=o.currentIndex)&&Object.is(o.trackById,t))return o;return null}remove(t){let n=t._prevDup,o=t._nextDup;return n===null?this._head=o:n._nextDup=o,o===null?this._tail=n:o._prevDup=n,this._head===null}},el=class{map=new Map;put(t){let n=t.trackById,o=this.map.get(n);o||(o=new Df,this.map.set(n,o)),o.add(t)}get(t,n){let o=t,i=this.map.get(o);return i?i.get(t,n):null}remove(t){let n=t.trackById;return this.map.get(n).remove(t)&&this.map.delete(n),t}get isEmpty(){return this.map.size===0}clear(){this.map.clear()}};function ty(e,t,n){let o=e.previousIndex;if(o===null)return o;let i=0;return n&&o<n.length&&(i=n[o]),o+t+i}function ny(){return new Ef([new bf])}var Ef=(()=>{class e{factories;static \u0275prov=I({token:e,providedIn:"root",factory:ny});constructor(n){this.factories=n}static create(n,o){if(o!=null){let i=o.factories.slice();n=n.concat(i)}return new e(n)}static extend(n){return{provide:e,useFactory:o=>e.create(n,o||ny()),deps:[[e,new pg,new fg]]}}find(n){let o=this.factories.find(i=>i.supports(n));if(o!=null)return o;throw new E(901,!1)}}return e})();function ly(e){se(8);try{let{rootComponent:t,appProviders:n,platformProviders:o}=e,i=Lx(o),r=[Qv({}),{provide:Kn,useExisting:Xv},Pm,...n||[]],s=new gr({providers:r,parent:i,debugName:"",runEnvironmentInitializers:!1});return Nx({r3Injector:s.injector,platformInjector:i,rootComponent:t})}catch(t){return Promise.reject(t)}finally{se(9)}}function xf(e){return typeof e=="boolean"?e:e!=null&&e!=="false"}function cy(e,t){let n=Pt(e),o=t.elementInjector||Zo();return new On(n).create(o,t.projectableNodes,t.hostElement,t.environmentInjector,t.directives,t.bindings)}function Sf(e){let t=Pt(e);if(!t)return null;let n=new On(t);return{get selector(){return n.selector},get type(){return n.componentType},get inputs(){return n.inputs},get outputs(){return n.outputs},get ngContentSelectors(){return n.ngContentSelectors},get isStandalone(){return t.standalone},get isSignal(){return t.signals}}}var fy=null;function gt(){return fy}function _f(e){fy??=e}var xr=class{},Tf=(()=>{class e{historyGo(n){throw new Error("")}static \u0275fac=function(o){return new(o||e)};static \u0275prov=I({token:e,factory:()=>v(py),providedIn:"platform"})}return e})();var py=(()=>{class e extends Tf{_location;_history;_doc=v(Ce);constructor(){super(),this._location=window.location,this._history=window.history}getBaseHrefFromDOM(){return gt().getBaseHref(this._doc)}onPopState(n){let o=gt().getGlobalEventTarget(this._doc,"window");return o.addEventListener("popstate",n,!1),()=>o.removeEventListener("popstate",n)}onHashChange(n){let o=gt().getGlobalEventTarget(this._doc,"window");return o.addEventListener("hashchange",n,!1),()=>o.removeEventListener("hashchange",n)}get href(){return this._location.href}get protocol(){return this._location.protocol}get hostname(){return this._location.hostname}get port(){return this._location.port}get pathname(){return this._location.pathname}get search(){return this._location.search}get hash(){return this._location.hash}set pathname(n){this._location.pathname=n}pushState(n,o,i){this._history.pushState(n,o,i)}replaceState(n,o,i){this._history.replaceState(n,o,i)}forward(){this._history.forward()}back(){this._history.back()}historyGo(n=0){this._history.go(n)}getState(){return this._history.state}static \u0275fac=function(o){return new(o||e)};static \u0275prov=I({token:e,factory:()=>new e,providedIn:"platform"})}return e})();function hy(e,t){return e?t?e.endsWith("/")?t.startsWith("/")?e+t.slice(1):e+t:t.startsWith("/")?e+t:`${e}/${t}`:e:t}function uy(e){let t=e.search(/#|\?|$/);return e[t-1]==="/"?e.slice(0,t-1)+e.slice(t):e}function Ln(e){return e&&e[0]!=="?"?`?${e}`:e}var Sr=(()=>{class e{historyGo(n){throw new Error("")}static \u0275fac=function(o){return new(o||e)};static \u0275prov=I({token:e,factory:()=>v(gy),providedIn:"root"})}return e})(),my=new x(""),gy=(()=>{class e extends Sr{_platformLocation;_baseHref;_removeListenerFns=[];constructor(n,o){super(),this._platformLocation=n,this._baseHref=o??this._platformLocation.getBaseHrefFromDOM()??v(Ce).location?.origin??""}ngOnDestroy(){for(;this._removeListenerFns.length;)this._removeListenerFns.pop()()}onPopState(n){this._removeListenerFns.push(this._platformLocation.onPopState(n),this._platformLocation.onHashChange(n))}getBaseHref(){return this._baseHref}prepareExternalUrl(n){return hy(this._baseHref,n)}path(n=!1){let o=this._platformLocation.pathname+Ln(this._platformLocation.search),i=this._platformLocation.hash;return i&&n?`${o}${i}`:o}pushState(n,o,i,r){let s=this.prepareExternalUrl(i+Ln(r));this._platformLocation.pushState(n,o,s)}replaceState(n,o,i,r){let s=this.prepareExternalUrl(i+Ln(r));this._platformLocation.replaceState(n,o,s)}forward(){this._platformLocation.forward()}back(){this._platformLocation.back()}getState(){return this._platformLocation.getState()}historyGo(n=0){this._platformLocation.historyGo?.(n)}static \u0275fac=function(o){return new(o||e)(S(Tf),S(my,8))};static \u0275prov=I({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})(),Tt=(()=>{class e{_subject=new re;_basePath;_locationStrategy;_urlChangeListeners=[];_urlChangeSubscription=null;constructor(n){this._locationStrategy=n;let o=this._locationStrategy.getBaseHref();this._basePath=Ux(uy(dy(o))),this._locationStrategy.onPopState(i=>{this._subject.next({url:this.path(!0),pop:!0,state:i.state,type:i.type})})}ngOnDestroy(){this._urlChangeSubscription?.unsubscribe(),this._urlChangeListeners=[]}path(n=!1){return this.normalize(this._locationStrategy.path(n))}getState(){return this._locationStrategy.getState()}isCurrentPathEqualTo(n,o=""){return this.path()==this.normalize(n+Ln(o))}normalize(n){return e.stripTrailingSlash(zx(this._basePath,dy(n)))}prepareExternalUrl(n){return n&&n[0]!=="/"&&(n="/"+n),this._locationStrategy.prepareExternalUrl(n)}go(n,o="",i=null){this._locationStrategy.pushState(i,"",n,o),this._notifyUrlChangeListeners(this.prepareExternalUrl(n+Ln(o)),i)}replaceState(n,o="",i=null){this._locationStrategy.replaceState(i,"",n,o),this._notifyUrlChangeListeners(this.prepareExternalUrl(n+Ln(o)),i)}forward(){this._locationStrategy.forward()}back(){this._locationStrategy.back()}historyGo(n=0){this._locationStrategy.historyGo?.(n)}onUrlChange(n){return this._urlChangeListeners.push(n),this._urlChangeSubscription??=this.subscribe(o=>{this._notifyUrlChangeListeners(o.url,o.state)}),()=>{let o=this._urlChangeListeners.indexOf(n);this._urlChangeListeners.splice(o,1),this._urlChangeListeners.length===0&&(this._urlChangeSubscription?.unsubscribe(),this._urlChangeSubscription=null)}}_notifyUrlChangeListeners(n="",o){this._urlChangeListeners.forEach(i=>i(n,o))}subscribe(n,o,i){return this._subject.subscribe({next:n,error:o??void 0,complete:i??void 0})}static normalizeQueryParams=Ln;static joinWithSlash=hy;static stripTrailingSlash=uy;static \u0275fac=function(o){return new(o||e)(S(Sr))};static \u0275prov=I({token:e,factory:()=>$x(),providedIn:"root"})}return e})();function $x(){return new Tt(S(Sr))}function zx(e,t){if(!e||!t.startsWith(e))return t;let n=t.substring(e.length);return n===""||["/",";","?","#"].includes(n[0])?n:t}function dy(e){return e.replace(/\/index.html$/,"")}function Ux(e){if(new RegExp("^(https?:)?//").test(e)){let[,n]=e.split(/\/\/[^\/]+/);return n}return e}var tl=class{$implicit;ngForOf;index;count;constructor(t,n,o,i){this.$implicit=t,this.ngForOf=n,this.index=o,this.count=i}get first(){return this.index===0}get last(){return this.index===this.count-1}get even(){return this.index%2===0}get odd(){return!this.even}},yy=(()=>{class e{_viewContainer;_template;_differs;set ngForOf(n){this._ngForOf=n,this._ngForOfDirty=!0}set ngForTrackBy(n){this._trackByFn=n}get ngForTrackBy(){return this._trackByFn}_ngForOf=null;_ngForOfDirty=!0;_differ=null;_trackByFn;constructor(n,o,i){this._viewContainer=n,this._template=o,this._differs=i}set ngForTemplate(n){n&&(this._template=n)}ngDoCheck(){if(this._ngForOfDirty){this._ngForOfDirty=!1;let n=this._ngForOf;!this._differ&&n&&(this._differ=this._differs.find(n).create(this.ngForTrackBy))}if(this._differ){let n=this._differ.diff(this._ngForOf);n&&this._applyChanges(n)}}_applyChanges(n){let o=this._viewContainer;n.forEachOperation((i,r,s)=>{if(i.previousIndex==null)o.createEmbeddedView(this._template,new tl(i.item,this._ngForOf,-1,-1),s===null?void 0:s);else if(s==null)o.remove(r===null?void 0:r);else if(r!==null){let a=o.get(r);o.move(a,s),vy(a,i)}});for(let i=0,r=o.length;i<r;i++){let a=o.get(i).context;a.index=i,a.count=r,a.ngForOf=this._ngForOf}n.forEachIdentityChange(i=>{let r=o.get(i.currentIndex);vy(r,i)})}static ngTemplateContextGuard(n,o){return!0}static \u0275fac=function(o){return new(o||e)(D(mt),D(Nn),D(Ef))};static \u0275dir=ze({type:e,selectors:[["","ngFor","","ngForOf",""]],inputs:{ngForOf:"ngForOf",ngForTrackBy:"ngForTrackBy",ngForTemplate:"ngForTemplate"}})}return e})();function vy(e,t){e.context.$implicit=t.item}var Mf=(()=>{class e{static \u0275fac=function(o){return new(o||e)};static \u0275mod=ln({type:e});static \u0275inj=Ot({})}return e})();function kf(e,t){t=encodeURIComponent(t);for(let n of e.split(";")){let o=n.indexOf("="),[i,r]=o==-1?[n,""]:[n.slice(0,o),n.slice(o+1)];if(i.trim()===t)return decodeURIComponent(r)}return null}var _r=class{};var wy="browser";var ol=new x(""),Pf=(()=>{class e{_zone;_plugins;_eventNameToPlugin=new Map;constructor(n,o){this._zone=o,n.forEach(i=>{i.manager=this}),this._plugins=n.slice().reverse()}addEventListener(n,o,i,r){return this._findPluginFor(o).addEventListener(n,o,i,r)}getZone(){return this._zone}_findPluginFor(n){let o=this._eventNameToPlugin.get(n);if(o)return o;if(o=this._plugins.find(r=>r.supports(n)),!o)throw new E(5101,!1);return this._eventNameToPlugin.set(n,o),o}static \u0275fac=function(o){return new(o||e)(S(ol),S(W))};static \u0275prov=I({token:e,factory:e.\u0275fac})}return e})(),Tr=class{_doc;constructor(t){this._doc=t}manager},Af="ng-app-id";function Dy(e){for(let t of e)t.remove()}function Iy(e,t){let n=t.createElement("style");return n.textContent=e,n}function Gx(e,t,n,o){let i=e.head?.querySelectorAll(`style[${Af}="${t}"],link[${Af}="${t}"]`);if(i)for(let r of i)r.removeAttribute(Af),r instanceof HTMLLinkElement?o.set(r.href.slice(r.href.lastIndexOf("/")+1),{usage:0,elements:[r]}):r.textContent&&n.set(r.textContent,{usage:0,elements:[r]})}function Nf(e,t){let n=t.createElement("link");return n.setAttribute("rel","stylesheet"),n.setAttribute("href",e),n}var Ff=(()=>{class e{doc;appId;nonce;inline=new Map;external=new Map;hosts=new Set;constructor(n,o,i,r={}){this.doc=n,this.appId=o,this.nonce=i,Gx(n,o,this.inline,this.external),this.hosts.add(n.head)}addStyles(n,o){for(let i of n)this.addUsage(i,this.inline,Iy);o?.forEach(i=>this.addUsage(i,this.external,Nf))}removeStyles(n,o){for(let i of n)this.removeUsage(i,this.inline);o?.forEach(i=>this.removeUsage(i,this.external))}addUsage(n,o,i){let r=o.get(n);r?r.usage++:o.set(n,{usage:1,elements:[...this.hosts].map(s=>this.addElement(s,i(n,this.doc)))})}removeUsage(n,o){let i=o.get(n);i&&(i.usage--,i.usage<=0&&(Dy(i.elements),o.delete(n)))}ngOnDestroy(){for(let[,{elements:n}]of[...this.inline,...this.external])Dy(n);this.hosts.clear()}addHost(n){this.hosts.add(n);for(let[o,{elements:i}]of this.inline)i.push(this.addElement(n,Iy(o,this.doc)));for(let[o,{elements:i}]of this.external)i.push(this.addElement(n,Nf(o,this.doc)))}removeHost(n){this.hosts.delete(n)}addElement(n,o){return this.nonce&&o.setAttribute("nonce",this.nonce),n.appendChild(o)}static \u0275fac=function(o){return new(o||e)(S(Ce),S(Pa),S(La,8),S(ci))};static \u0275prov=I({token:e,factory:e.\u0275fac})}return e})(),Rf={svg:"http://www.w3.org/2000/svg",xhtml:"http://www.w3.org/1999/xhtml",xlink:"http://www.w3.org/1999/xlink",xml:"http://www.w3.org/XML/1998/namespace",xmlns:"http://www.w3.org/2000/xmlns/",math:"http://www.w3.org/1998/Math/MathML"},Lf=/%COMP%/g;var xy="%COMP%",Wx=`_nghost-${xy}`,Yx=`_ngcontent-${xy}`,Zx=!0,Qx=new x("",{providedIn:"root",factory:()=>Zx});function Kx(e){return Yx.replace(Lf,e)}function Xx(e){return Wx.replace(Lf,e)}function Sy(e,t){return t.map(n=>n.replace(Lf,e))}var jf=(()=>{class e{eventManager;sharedStylesHost;appId;removeStylesOnCompDestroy;doc;platformId;ngZone;nonce;tracingService;rendererByCompId=new Map;defaultRenderer;platformIsServer;constructor(n,o,i,r,s,a,l,c=null,u=null){this.eventManager=n,this.sharedStylesHost=o,this.appId=i,this.removeStylesOnCompDestroy=r,this.doc=s,this.platformId=a,this.ngZone=l,this.nonce=c,this.tracingService=u,this.platformIsServer=!1,this.defaultRenderer=new Mr(n,s,l,this.platformIsServer,this.tracingService)}createRenderer(n,o){if(!n||!o)return this.defaultRenderer;let i=this.getOrCreateRenderer(n,o);return i instanceof nl?i.applyToHost(n):i instanceof kr&&i.applyStyles(),i}getOrCreateRenderer(n,o){let i=this.rendererByCompId,r=i.get(o.id);if(!r){let s=this.doc,a=this.ngZone,l=this.eventManager,c=this.sharedStylesHost,u=this.removeStylesOnCompDestroy,d=this.platformIsServer,p=this.tracingService;switch(o.encapsulation){case rn.Emulated:r=new nl(l,c,o,this.appId,u,s,a,d,p);break;case rn.ShadowDom:return new Of(l,c,n,o,s,a,this.nonce,d,p);default:r=new kr(l,c,o,u,s,a,d,p);break}i.set(o.id,r)}return r}ngOnDestroy(){this.rendererByCompId.clear()}componentReplaced(n){this.rendererByCompId.delete(n)}static \u0275fac=function(o){return new(o||e)(S(Pf),S(Ff),S(Pa),S(Qx),S(Ce),S(ci),S(W),S(La),S(br,8))};static \u0275prov=I({token:e,factory:e.\u0275fac})}return e})(),Mr=class{eventManager;doc;ngZone;platformIsServer;tracingService;data=Object.create(null);throwOnSyntheticProps=!0;constructor(t,n,o,i,r){this.eventManager=t,this.doc=n,this.ngZone=o,this.platformIsServer=i,this.tracingService=r}destroy(){}destroyNode=null;createElement(t,n){return n?this.doc.createElementNS(Rf[n]||n,t):this.doc.createElement(t)}createComment(t){return this.doc.createComment(t)}createText(t){return this.doc.createTextNode(t)}appendChild(t,n){(Ey(t)?t.content:t).appendChild(n)}insertBefore(t,n,o){t&&(Ey(t)?t.content:t).insertBefore(n,o)}removeChild(t,n){n.remove()}selectRootElement(t,n){let o=typeof t=="string"?this.doc.querySelector(t):t;if(!o)throw new E(-5104,!1);return n||(o.textContent=""),o}parentNode(t){return t.parentNode}nextSibling(t){return t.nextSibling}setAttribute(t,n,o,i){if(i){n=i+":"+n;let r=Rf[i];r?t.setAttributeNS(r,n,o):t.setAttribute(n,o)}else t.setAttribute(n,o)}removeAttribute(t,n,o){if(o){let i=Rf[o];i?t.removeAttributeNS(i,n):t.removeAttribute(`${o}:${n}`)}else t.removeAttribute(n)}addClass(t,n){t.classList.add(n)}removeClass(t,n){t.classList.remove(n)}setStyle(t,n,o,i){i&(sn.DashCase|sn.Important)?t.style.setProperty(n,o,i&sn.Important?"important":""):t.style[n]=o}removeStyle(t,n,o){o&sn.DashCase?t.style.removeProperty(n):t.style[n]=""}setProperty(t,n,o){t!=null&&(t[n]=o)}setValue(t,n){t.nodeValue=n}listen(t,n,o,i){if(typeof t=="string"&&(t=gt().getGlobalEventTarget(this.doc,t),!t))throw new E(5102,!1);let r=this.decoratePreventDefault(o);return this.tracingService?.wrapEventListener&&(r=this.tracingService.wrapEventListener(t,n,r)),this.eventManager.addEventListener(t,n,r,i)}decoratePreventDefault(t){return n=>{if(n==="__ngUnwrap__")return t;t(n)===!1&&n.preventDefault()}}};function Ey(e){return e.tagName==="TEMPLATE"&&e.content!==void 0}var Of=class extends Mr{sharedStylesHost;hostEl;shadowRoot;constructor(t,n,o,i,r,s,a,l,c){super(t,r,s,l,c),this.sharedStylesHost=n,this.hostEl=o,this.shadowRoot=o.attachShadow({mode:"open"}),this.sharedStylesHost.addHost(this.shadowRoot);let u=i.styles;u=Sy(i.id,u);for(let p of u){let f=document.createElement("style");a&&f.setAttribute("nonce",a),f.textContent=p,this.shadowRoot.appendChild(f)}let d=i.getExternalStyles?.();if(d)for(let p of d){let f=Nf(p,r);a&&f.setAttribute("nonce",a),this.shadowRoot.appendChild(f)}}nodeOrShadowRoot(t){return t===this.hostEl?this.shadowRoot:t}appendChild(t,n){return super.appendChild(this.nodeOrShadowRoot(t),n)}insertBefore(t,n,o){return super.insertBefore(this.nodeOrShadowRoot(t),n,o)}removeChild(t,n){return super.removeChild(null,n)}parentNode(t){return this.nodeOrShadowRoot(super.parentNode(this.nodeOrShadowRoot(t)))}destroy(){this.sharedStylesHost.removeHost(this.shadowRoot)}},kr=class extends Mr{sharedStylesHost;removeStylesOnCompDestroy;styles;styleUrls;constructor(t,n,o,i,r,s,a,l,c){super(t,r,s,a,l),this.sharedStylesHost=n,this.removeStylesOnCompDestroy=i;let u=o.styles;this.styles=c?Sy(c,u):u,this.styleUrls=o.getExternalStyles?.(c)}applyStyles(){this.sharedStylesHost.addStyles(this.styles,this.styleUrls)}destroy(){this.removeStylesOnCompDestroy&&this.sharedStylesHost.removeStyles(this.styles,this.styleUrls)}},nl=class extends kr{contentAttr;hostAttr;constructor(t,n,o,i,r,s,a,l,c){let u=i+"-"+o.id;super(t,n,o,r,s,a,l,c,u),this.contentAttr=Kx(u),this.hostAttr=Xx(u)}applyToHost(t){this.applyStyles(),this.setAttribute(t,this.hostAttr,"")}createElement(t,n){let o=super.createElement(t,n);return super.setAttribute(o,this.contentAttr,""),o}};var il=class e extends xr{supportsDOMEvents=!0;static makeCurrent(){_f(new e)}onAndCancel(t,n,o,i){return t.addEventListener(n,o,i),()=>{t.removeEventListener(n,o,i)}}dispatchEvent(t,n){t.dispatchEvent(n)}remove(t){t.remove()}createElement(t,n){return n=n||this.getDefaultDocument(),n.createElement(t)}createHtmlDocument(){return document.implementation.createHTMLDocument("fakeTitle")}getDefaultDocument(){return document}isElementNode(t){return t.nodeType===Node.ELEMENT_NODE}isShadowRoot(t){return t instanceof DocumentFragment}getGlobalEventTarget(t,n){return n==="window"?window:n==="document"?t:n==="body"?t.body:null}getBaseHref(t){let n=Jx();return n==null?null:eS(n)}resetBaseElement(){Ar=null}getUserAgent(){return window.navigator.userAgent}getCookie(t){return kf(document.cookie,t)}},Ar=null;function Jx(){return Ar=Ar||document.head.querySelector("base"),Ar?Ar.getAttribute("href"):null}function eS(e){return new URL(e,document.baseURI).pathname}var tS=(()=>{class e{build(){return new XMLHttpRequest}static \u0275fac=function(o){return new(o||e)};static \u0275prov=I({token:e,factory:e.\u0275fac})}return e})(),Ty=(()=>{class e extends Tr{constructor(n){super(n)}supports(n){return!0}addEventListener(n,o,i,r){return n.addEventListener(o,i,r),()=>this.removeEventListener(n,o,i,r)}removeEventListener(n,o,i,r){return n.removeEventListener(o,i,r)}static \u0275fac=function(o){return new(o||e)(S(Ce))};static \u0275prov=I({token:e,factory:e.\u0275fac})}return e})(),_y=["alt","control","meta","shift"],nS={"\b":"Backspace","	":"Tab","\x7F":"Delete","\x1B":"Escape",Del:"Delete",Esc:"Escape",Left:"ArrowLeft",Right:"ArrowRight",Up:"ArrowUp",Down:"ArrowDown",Menu:"ContextMenu",Scroll:"ScrollLock",Win:"OS"},oS={alt:e=>e.altKey,control:e=>e.ctrlKey,meta:e=>e.metaKey,shift:e=>e.shiftKey},My=(()=>{class e extends Tr{constructor(n){super(n)}supports(n){return e.parseEventName(n)!=null}addEventListener(n,o,i,r){let s=e.parseEventName(o),a=e.eventCallback(s.fullKey,i,this.manager.getZone());return this.manager.getZone().runOutsideAngular(()=>gt().onAndCancel(n,s.domEventName,a,r))}static parseEventName(n){let o=n.toLowerCase().split("."),i=o.shift();if(o.length===0||!(i==="keydown"||i==="keyup"))return null;let r=e._normalizeKey(o.pop()),s="",a=o.indexOf("code");if(a>-1&&(o.splice(a,1),s="code."),_y.forEach(c=>{let u=o.indexOf(c);u>-1&&(o.splice(u,1),s+=c+".")}),s+=r,o.length!=0||r.length===0)return null;let l={};return l.domEventName=i,l.fullKey=s,l}static matchEventFullKeyCode(n,o){let i=nS[n.key]||n.key,r="";return o.indexOf("code.")>-1&&(i=n.code,r="code."),i==null||!i?!1:(i=i.toLowerCase(),i===" "?i="space":i==="."&&(i="dot"),_y.forEach(s=>{if(s!==i){let a=oS[s];a(n)&&(r+=s+".")}}),r+=i,r===o)}static eventCallback(n,o,i){return r=>{e.matchEventFullKeyCode(r,n)&&i.runGuarded(()=>o(r))}}static _normalizeKey(n){return n==="esc"?"escape":n}static \u0275fac=function(o){return new(o||e)(S(Ce))};static \u0275prov=I({token:e,factory:e.\u0275fac})}return e})();function iS(e,t){let n=b({rootComponent:e},rS(t));return ly(n)}function rS(e){return{appProviders:[...uS,...e?.providers??[]],platformProviders:cS}}function sS(){il.makeCurrent()}function aS(){return new Nt}function lS(){return jd(document),document}var cS=[{provide:ci,useValue:wy},{provide:Fa,useValue:sS,multi:!0},{provide:Ce,useFactory:lS}];var uS=[{provide:nr,useValue:"root"},{provide:Nt,useFactory:aS},{provide:ol,useClass:Ty,multi:!0,deps:[Ce]},{provide:ol,useClass:My,multi:!0,deps:[Ce]},jf,Ff,Pf,{provide:co,useExisting:jf},{provide:_r,useClass:tS},[]];var ky=(()=>{class e{_doc;constructor(n){this._doc=n}getTitle(){return this._doc.title}setTitle(n){this._doc.title=n||""}static \u0275fac=function(o){return new(o||e)(S(Ce))};static \u0275prov=I({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})();var R="primary",Gr=Symbol("RouteTitle"),zf=class{params;constructor(t){this.params=t||{}}has(t){return Object.prototype.hasOwnProperty.call(this.params,t)}get(t){if(this.has(t)){let n=this.params[t];return Array.isArray(n)?n[0]:n}return null}getAll(t){if(this.has(t)){let n=this.params[t];return Array.isArray(n)?n:[n]}return[]}get keys(){return Object.keys(this.params)}};function go(e){return new zf(e)}function jy(e,t,n){let o=n.path.split("/");if(o.length>e.length||n.pathMatch==="full"&&(t.hasChildren()||o.length<e.length))return null;let i={};for(let r=0;r<o.length;r++){let s=o[r],a=e[r];if(s[0]===":")i[s.substring(1)]=a;else if(s!==a.path)return null}return{consumed:e.slice(0,o.length),posParams:i}}function fS(e,t){if(e.length!==t.length)return!1;for(let n=0;n<e.length;++n)if(!zt(e[n],t[n]))return!1;return!0}function zt(e,t){let n=e?Uf(e):void 0,o=t?Uf(t):void 0;if(!n||!o||n.length!=o.length)return!1;let i;for(let r=0;r<n.length;r++)if(i=n[r],!Vy(e[i],t[i]))return!1;return!0}function Uf(e){return[...Object.keys(e),...Object.getOwnPropertySymbols(e)]}function Vy(e,t){if(Array.isArray(e)&&Array.isArray(t)){if(e.length!==t.length)return!1;let n=[...e].sort(),o=[...t].sort();return n.every((i,r)=>o[r]===i)}else return e===t}function By(e){return e.length>0?e[e.length-1]:null}function pn(e){return Fc(e)?e:po(e)?ue(Promise.resolve(e)):k(e)}var pS={exact:$y,subset:zy},Hy={exact:hS,subset:mS,ignored:()=>!0};function Ay(e,t,n){return pS[n.paths](e.root,t.root,n.matrixParams)&&Hy[n.queryParams](e.queryParams,t.queryParams)&&!(n.fragment==="exact"&&e.fragment!==t.fragment)}function hS(e,t){return zt(e,t)}function $y(e,t,n){if(!ho(e.segments,t.segments)||!al(e.segments,t.segments,n)||e.numberOfChildren!==t.numberOfChildren)return!1;for(let o in t.children)if(!e.children[o]||!$y(e.children[o],t.children[o],n))return!1;return!0}function mS(e,t){return Object.keys(t).length<=Object.keys(e).length&&Object.keys(t).every(n=>Vy(e[n],t[n]))}function zy(e,t,n){return Uy(e,t,t.segments,n)}function Uy(e,t,n,o){if(e.segments.length>n.length){let i=e.segments.slice(0,n.length);return!(!ho(i,n)||t.hasChildren()||!al(i,n,o))}else if(e.segments.length===n.length){if(!ho(e.segments,n)||!al(e.segments,n,o))return!1;for(let i in t.children)if(!e.children[i]||!zy(e.children[i],t.children[i],o))return!1;return!0}else{let i=n.slice(0,e.segments.length),r=n.slice(e.segments.length);return!ho(e.segments,i)||!al(e.segments,i,o)||!e.children[R]?!1:Uy(e.children[R],t,r,o)}}function al(e,t,n){return t.every((o,i)=>Hy[n](e[i].parameters,o.parameters))}var qt=class{root;queryParams;fragment;_queryParamMap;constructor(t=new K([],{}),n={},o=null){this.root=t,this.queryParams=n,this.fragment=o}get queryParamMap(){return this._queryParamMap??=go(this.queryParams),this._queryParamMap}toString(){return yS.serialize(this)}},K=class{segments;children;parent=null;constructor(t,n){this.segments=t,this.children=n,Object.values(n).forEach(o=>o.parent=this)}hasChildren(){return this.numberOfChildren>0}get numberOfChildren(){return Object.keys(this.children).length}toString(){return ll(this)}},jn=class{path;parameters;_parameterMap;constructor(t,n){this.path=t,this.parameters=n}get parameterMap(){return this._parameterMap??=go(this.parameters),this._parameterMap}toString(){return Gy(this)}};function gS(e,t){return ho(e,t)&&e.every((n,o)=>zt(n.parameters,t[o].parameters))}function ho(e,t){return e.length!==t.length?!1:e.every((n,o)=>n.path===t[o].path)}function vS(e,t){let n=[];return Object.entries(e.children).forEach(([o,i])=>{o===R&&(n=n.concat(t(i,o)))}),Object.entries(e.children).forEach(([o,i])=>{o!==R&&(n=n.concat(t(i,o)))}),n}var yo=(()=>{class e{static \u0275fac=function(o){return new(o||e)};static \u0275prov=I({token:e,factory:()=>new vo,providedIn:"root"})}return e})(),vo=class{parse(t){let n=new Gf(t);return new qt(n.parseRootSegment(),n.parseQueryParams(),n.parseFragment())}serialize(t){let n=`/${Rr(t.root,!0)}`,o=CS(t.queryParams),i=typeof t.fragment=="string"?`#${bS(t.fragment)}`:"";return`${n}${o}${i}`}},yS=new vo;function ll(e){return e.segments.map(t=>Gy(t)).join("/")}function Rr(e,t){if(!e.hasChildren())return ll(e);if(t){let n=e.children[R]?Rr(e.children[R],!1):"",o=[];return Object.entries(e.children).forEach(([i,r])=>{i!==R&&o.push(`${i}:${Rr(r,!1)}`)}),o.length>0?`${n}(${o.join("//")})`:n}else{let n=vS(e,(o,i)=>i===R?[Rr(e.children[R],!1)]:[`${i}:${Rr(o,!1)}`]);return Object.keys(e.children).length===1&&e.children[R]!=null?`${ll(e)}/${n[0]}`:`${ll(e)}/(${n.join("//")})`}}function qy(e){return encodeURIComponent(e).replace(/%40/g,"@").replace(/%3A/gi,":").replace(/%24/g,"$").replace(/%2C/gi,",")}function rl(e){return qy(e).replace(/%3B/gi,";")}function bS(e){return encodeURI(e)}function qf(e){return qy(e).replace(/\(/g,"%28").replace(/\)/g,"%29").replace(/%26/gi,"&")}function cl(e){return decodeURIComponent(e)}function Ry(e){return cl(e.replace(/\+/g,"%20"))}function Gy(e){return`${qf(e.path)}${wS(e.parameters)}`}function wS(e){return Object.entries(e).map(([t,n])=>`;${qf(t)}=${qf(n)}`).join("")}function CS(e){let t=Object.entries(e).map(([n,o])=>Array.isArray(o)?o.map(i=>`${rl(n)}=${rl(i)}`).join("&"):`${rl(n)}=${rl(o)}`).filter(n=>n);return t.length?`?${t.join("&")}`:""}var DS=/^[^\/()?;#]+/;function Vf(e){let t=e.match(DS);return t?t[0]:""}var IS=/^[^\/()?;=#]+/;function ES(e){let t=e.match(IS);return t?t[0]:""}var xS=/^[^=?&#]+/;function SS(e){let t=e.match(xS);return t?t[0]:""}var _S=/^[^&#]+/;function TS(e){let t=e.match(_S);return t?t[0]:""}var Gf=class{url;remaining;constructor(t){this.url=t,this.remaining=t}parseRootSegment(){return this.consumeOptional("/"),this.remaining===""||this.peekStartsWith("?")||this.peekStartsWith("#")?new K([],{}):new K([],this.parseChildren())}parseQueryParams(){let t={};if(this.consumeOptional("?"))do this.parseQueryParam(t);while(this.consumeOptional("&"));return t}parseFragment(){return this.consumeOptional("#")?decodeURIComponent(this.remaining):null}parseChildren(){if(this.remaining==="")return{};this.consumeOptional("/");let t=[];for(this.peekStartsWith("(")||t.push(this.parseSegment());this.peekStartsWith("/")&&!this.peekStartsWith("//")&&!this.peekStartsWith("/(");)this.capture("/"),t.push(this.parseSegment());let n={};this.peekStartsWith("/(")&&(this.capture("/"),n=this.parseParens(!0));let o={};return this.peekStartsWith("(")&&(o=this.parseParens(!1)),(t.length>0||Object.keys(n).length>0)&&(o[R]=new K(t,n)),o}parseSegment(){let t=Vf(this.remaining);if(t===""&&this.peekStartsWith(";"))throw new E(4009,!1);return this.capture(t),new jn(cl(t),this.parseMatrixParams())}parseMatrixParams(){let t={};for(;this.consumeOptional(";");)this.parseParam(t);return t}parseParam(t){let n=ES(this.remaining);if(!n)return;this.capture(n);let o="";if(this.consumeOptional("=")){let i=Vf(this.remaining);i&&(o=i,this.capture(o))}t[cl(n)]=cl(o)}parseQueryParam(t){let n=SS(this.remaining);if(!n)return;this.capture(n);let o="";if(this.consumeOptional("=")){let s=TS(this.remaining);s&&(o=s,this.capture(o))}let i=Ry(n),r=Ry(o);if(t.hasOwnProperty(i)){let s=t[i];Array.isArray(s)||(s=[s],t[i]=s),s.push(r)}else t[i]=r}parseParens(t){let n={};for(this.capture("(");!this.consumeOptional(")")&&this.remaining.length>0;){let o=Vf(this.remaining),i=this.remaining[o.length];if(i!=="/"&&i!==")"&&i!==";")throw new E(4010,!1);let r;o.indexOf(":")>-1?(r=o.slice(0,o.indexOf(":")),this.capture(r),this.capture(":")):t&&(r=R);let s=this.parseChildren();n[r]=Object.keys(s).length===1?s[R]:new K([],s),this.consumeOptional("//")}return n}peekStartsWith(t){return this.remaining.startsWith(t)}consumeOptional(t){return this.peekStartsWith(t)?(this.remaining=this.remaining.substring(t.length),!0):!1}capture(t){if(!this.consumeOptional(t))throw new E(4011,!1)}};function Wy(e){return e.segments.length>0?new K([],{[R]:e}):e}function Yy(e){let t={};for(let[o,i]of Object.entries(e.children)){let r=Yy(i);if(o===R&&r.segments.length===0&&r.hasChildren())for(let[s,a]of Object.entries(r.children))t[s]=a;else(r.segments.length>0||r.hasChildren())&&(t[o]=r)}let n=new K(e.segments,t);return MS(n)}function MS(e){if(e.numberOfChildren===1&&e.children[R]){let t=e.children[R];return new K(e.segments.concat(t.segments),t.children)}return e}function yi(e){return e instanceof qt}function Zy(e,t,n=null,o=null){let i=Qy(e);return Ky(i,t,n,o)}function Qy(e){let t;function n(r){let s={};for(let l of r.children){let c=n(l);s[l.outlet]=c}let a=new K(r.url,s);return r===e&&(t=a),a}let o=n(e.root),i=Wy(o);return t??i}function Ky(e,t,n,o){let i=e;for(;i.parent;)i=i.parent;if(t.length===0)return Bf(i,i,i,n,o);let r=kS(t);if(r.toRoot())return Bf(i,i,new K([],{}),n,o);let s=AS(r,i,e),a=s.processChildren?Or(s.segmentGroup,s.index,r.commands):Jy(s.segmentGroup,s.index,r.commands);return Bf(i,s.segmentGroup,a,n,o)}function ul(e){return typeof e=="object"&&e!=null&&!e.outlets&&!e.segmentPath}function Lr(e){return typeof e=="object"&&e!=null&&e.outlets}function Bf(e,t,n,o,i){let r={};o&&Object.entries(o).forEach(([l,c])=>{r[l]=Array.isArray(c)?c.map(u=>`${u}`):`${c}`});let s;e===t?s=n:s=Xy(e,t,n);let a=Wy(Yy(s));return new qt(a,r,i)}function Xy(e,t,n){let o={};return Object.entries(e.children).forEach(([i,r])=>{r===t?o[i]=n:o[i]=Xy(r,t,n)}),new K(e.segments,o)}var dl=class{isAbsolute;numberOfDoubleDots;commands;constructor(t,n,o){if(this.isAbsolute=t,this.numberOfDoubleDots=n,this.commands=o,t&&o.length>0&&ul(o[0]))throw new E(4003,!1);let i=o.find(Lr);if(i&&i!==By(o))throw new E(4004,!1)}toRoot(){return this.isAbsolute&&this.commands.length===1&&this.commands[0]=="/"}};function kS(e){if(typeof e[0]=="string"&&e.length===1&&e[0]==="/")return new dl(!0,0,e);let t=0,n=!1,o=e.reduce((i,r,s)=>{if(typeof r=="object"&&r!=null){if(r.outlets){let a={};return Object.entries(r.outlets).forEach(([l,c])=>{a[l]=typeof c=="string"?c.split("/"):c}),[...i,{outlets:a}]}if(r.segmentPath)return[...i,r.segmentPath]}return typeof r!="string"?[...i,r]:s===0?(r.split("/").forEach((a,l)=>{l==0&&a==="."||(l==0&&a===""?n=!0:a===".."?t++:a!=""&&i.push(a))}),i):[...i,r]},[]);return new dl(n,t,o)}var gi=class{segmentGroup;processChildren;index;constructor(t,n,o){this.segmentGroup=t,this.processChildren=n,this.index=o}};function AS(e,t,n){if(e.isAbsolute)return new gi(t,!0,0);if(!n)return new gi(t,!1,NaN);if(n.parent===null)return new gi(n,!0,0);let o=ul(e.commands[0])?0:1,i=n.segments.length-1+o;return RS(n,i,e.numberOfDoubleDots)}function RS(e,t,n){let o=e,i=t,r=n;for(;r>i;){if(r-=i,o=o.parent,!o)throw new E(4005,!1);i=o.segments.length}return new gi(o,!1,i-r)}function NS(e){return Lr(e[0])?e[0].outlets:{[R]:e}}function Jy(e,t,n){if(e??=new K([],{}),e.segments.length===0&&e.hasChildren())return Or(e,t,n);let o=OS(e,t,n),i=n.slice(o.commandIndex);if(o.match&&o.pathIndex<e.segments.length){let r=new K(e.segments.slice(0,o.pathIndex),{});return r.children[R]=new K(e.segments.slice(o.pathIndex),e.children),Or(r,0,i)}else return o.match&&i.length===0?new K(e.segments,{}):o.match&&!e.hasChildren()?Wf(e,t,n):o.match?Or(e,0,i):Wf(e,t,n)}function Or(e,t,n){if(n.length===0)return new K(e.segments,{});{let o=NS(n),i={};if(Object.keys(o).some(r=>r!==R)&&e.children[R]&&e.numberOfChildren===1&&e.children[R].segments.length===0){let r=Or(e.children[R],t,n);return new K(e.segments,r.children)}return Object.entries(o).forEach(([r,s])=>{typeof s=="string"&&(s=[s]),s!==null&&(i[r]=Jy(e.children[r],t,s))}),Object.entries(e.children).forEach(([r,s])=>{o[r]===void 0&&(i[r]=s)}),new K(e.segments,i)}}function OS(e,t,n){let o=0,i=t,r={match:!1,pathIndex:0,commandIndex:0};for(;i<e.segments.length;){if(o>=n.length)return r;let s=e.segments[i],a=n[o];if(Lr(a))break;let l=`${a}`,c=o<n.length-1?n[o+1]:null;if(i>0&&l===void 0)break;if(l&&c&&typeof c=="object"&&c.outlets===void 0){if(!Oy(l,c,s))return r;o+=2}else{if(!Oy(l,{},s))return r;o++}i++}return{match:!0,pathIndex:i,commandIndex:o}}function Wf(e,t,n){let o=e.segments.slice(0,t),i=0;for(;i<n.length;){let r=n[i];if(Lr(r)){let l=PS(r.outlets);return new K(o,l)}if(i===0&&ul(n[0])){let l=e.segments[t];o.push(new jn(l.path,Ny(n[0]))),i++;continue}let s=Lr(r)?r.outlets[R]:`${r}`,a=i<n.length-1?n[i+1]:null;s&&a&&ul(a)?(o.push(new jn(s,Ny(a))),i+=2):(o.push(new jn(s,{})),i++)}return new K(o,{})}function PS(e){let t={};return Object.entries(e).forEach(([n,o])=>{typeof o=="string"&&(o=[o]),o!==null&&(t[n]=Wf(new K([],{}),0,o))}),t}function Ny(e){let t={};return Object.entries(e).forEach(([n,o])=>t[n]=`${o}`),t}function Oy(e,t,n){return e==n.path&&zt(t,n.parameters)}var Pr="imperative",Me=function(e){return e[e.NavigationStart=0]="NavigationStart",e[e.NavigationEnd=1]="NavigationEnd",e[e.NavigationCancel=2]="NavigationCancel",e[e.NavigationError=3]="NavigationError",e[e.RoutesRecognized=4]="RoutesRecognized",e[e.ResolveStart=5]="ResolveStart",e[e.ResolveEnd=6]="ResolveEnd",e[e.GuardsCheckStart=7]="GuardsCheckStart",e[e.GuardsCheckEnd=8]="GuardsCheckEnd",e[e.RouteConfigLoadStart=9]="RouteConfigLoadStart",e[e.RouteConfigLoadEnd=10]="RouteConfigLoadEnd",e[e.ChildActivationStart=11]="ChildActivationStart",e[e.ChildActivationEnd=12]="ChildActivationEnd",e[e.ActivationStart=13]="ActivationStart",e[e.ActivationEnd=14]="ActivationEnd",e[e.Scroll=15]="Scroll",e[e.NavigationSkipped=16]="NavigationSkipped",e}(Me||{}),lt=class{id;url;constructor(t,n){this.id=t,this.url=n}},dn=class extends lt{type=Me.NavigationStart;navigationTrigger;restoredState;constructor(t,n,o="imperative",i=null){super(t,n),this.navigationTrigger=o,this.restoredState=i}toString(){return`NavigationStart(id: ${this.id}, url: '${this.url}')`}},Gt=class extends lt{urlAfterRedirects;type=Me.NavigationEnd;constructor(t,n,o){super(t,n),this.urlAfterRedirects=o}toString(){return`NavigationEnd(id: ${this.id}, url: '${this.url}', urlAfterRedirects: '${this.urlAfterRedirects}')`}},Ze=function(e){return e[e.Redirect=0]="Redirect",e[e.SupersededByNewNavigation=1]="SupersededByNewNavigation",e[e.NoDataFromResolver=2]="NoDataFromResolver",e[e.GuardRejected=3]="GuardRejected",e[e.Aborted=4]="Aborted",e}(Ze||{}),jr=function(e){return e[e.IgnoredSameUrlNavigation=0]="IgnoredSameUrlNavigation",e[e.IgnoredByUrlHandlingStrategy=1]="IgnoredByUrlHandlingStrategy",e}(jr||{}),Ut=class extends lt{reason;code;type=Me.NavigationCancel;constructor(t,n,o,i){super(t,n),this.reason=o,this.code=i}toString(){return`NavigationCancel(id: ${this.id}, url: '${this.url}')`}},fn=class extends lt{reason;code;type=Me.NavigationSkipped;constructor(t,n,o,i){super(t,n),this.reason=o,this.code=i}},bi=class extends lt{error;target;type=Me.NavigationError;constructor(t,n,o,i){super(t,n),this.error=o,this.target=i}toString(){return`NavigationError(id: ${this.id}, url: '${this.url}', error: ${this.error})`}},Vr=class extends lt{urlAfterRedirects;state;type=Me.RoutesRecognized;constructor(t,n,o,i){super(t,n),this.urlAfterRedirects=o,this.state=i}toString(){return`RoutesRecognized(id: ${this.id}, url: '${this.url}', urlAfterRedirects: '${this.urlAfterRedirects}', state: ${this.state})`}},fl=class extends lt{urlAfterRedirects;state;type=Me.GuardsCheckStart;constructor(t,n,o,i){super(t,n),this.urlAfterRedirects=o,this.state=i}toString(){return`GuardsCheckStart(id: ${this.id}, url: '${this.url}', urlAfterRedirects: '${this.urlAfterRedirects}', state: ${this.state})`}},pl=class extends lt{urlAfterRedirects;state;shouldActivate;type=Me.GuardsCheckEnd;constructor(t,n,o,i,r){super(t,n),this.urlAfterRedirects=o,this.state=i,this.shouldActivate=r}toString(){return`GuardsCheckEnd(id: ${this.id}, url: '${this.url}', urlAfterRedirects: '${this.urlAfterRedirects}', state: ${this.state}, shouldActivate: ${this.shouldActivate})`}},hl=class extends lt{urlAfterRedirects;state;type=Me.ResolveStart;constructor(t,n,o,i){super(t,n),this.urlAfterRedirects=o,this.state=i}toString(){return`ResolveStart(id: ${this.id}, url: '${this.url}', urlAfterRedirects: '${this.urlAfterRedirects}', state: ${this.state})`}},ml=class extends lt{urlAfterRedirects;state;type=Me.ResolveEnd;constructor(t,n,o,i){super(t,n),this.urlAfterRedirects=o,this.state=i}toString(){return`ResolveEnd(id: ${this.id}, url: '${this.url}', urlAfterRedirects: '${this.urlAfterRedirects}', state: ${this.state})`}},gl=class{route;type=Me.RouteConfigLoadStart;constructor(t){this.route=t}toString(){return`RouteConfigLoadStart(path: ${this.route.path})`}},vl=class{route;type=Me.RouteConfigLoadEnd;constructor(t){this.route=t}toString(){return`RouteConfigLoadEnd(path: ${this.route.path})`}},yl=class{snapshot;type=Me.ChildActivationStart;constructor(t){this.snapshot=t}toString(){return`ChildActivationStart(path: '${this.snapshot.routeConfig&&this.snapshot.routeConfig.path||""}')`}},bl=class{snapshot;type=Me.ChildActivationEnd;constructor(t){this.snapshot=t}toString(){return`ChildActivationEnd(path: '${this.snapshot.routeConfig&&this.snapshot.routeConfig.path||""}')`}},wl=class{snapshot;type=Me.ActivationStart;constructor(t){this.snapshot=t}toString(){return`ActivationStart(path: '${this.snapshot.routeConfig&&this.snapshot.routeConfig.path||""}')`}},Cl=class{snapshot;type=Me.ActivationEnd;constructor(t){this.snapshot=t}toString(){return`ActivationEnd(path: '${this.snapshot.routeConfig&&this.snapshot.routeConfig.path||""}')`}};var Br=class{},wi=class{url;navigationBehaviorOptions;constructor(t,n){this.url=t,this.navigationBehaviorOptions=n}};function FS(e){return!(e instanceof Br)&&!(e instanceof wi)}function LS(e,t){return e.providers&&!e._injector&&(e._injector=fi(e.providers,t,`Route: ${e.path}`)),e._injector??t}function Mt(e){return e.outlet||R}function jS(e,t){let n=e.filter(o=>Mt(o)===t);return n.push(...e.filter(o=>Mt(o)!==t)),n}function Ii(e){if(!e)return null;if(e.routeConfig?._injector)return e.routeConfig._injector;for(let t=e.parent;t;t=t.parent){let n=t.routeConfig;if(n?._loadedInjector)return n._loadedInjector;if(n?._injector)return n._injector}return null}var Dl=class{rootInjector;outlet=null;route=null;children;attachRef=null;get injector(){return Ii(this.route?.snapshot)??this.rootInjector}constructor(t){this.rootInjector=t,this.children=new hn(this.rootInjector)}},hn=(()=>{class e{rootInjector;contexts=new Map;constructor(n){this.rootInjector=n}onChildOutletCreated(n,o){let i=this.getOrCreateContext(n);i.outlet=o,this.contexts.set(n,i)}onChildOutletDestroyed(n){let o=this.getContext(n);o&&(o.outlet=null,o.attachRef=null)}onOutletDeactivated(){let n=this.contexts;return this.contexts=new Map,n}onOutletReAttached(n){this.contexts=n}getOrCreateContext(n){let o=this.getContext(n);return o||(o=new Dl(this.rootInjector),this.contexts.set(n,o)),o}getContext(n){return this.contexts.get(n)||null}static \u0275fac=function(o){return new(o||e)(S(ae))};static \u0275prov=I({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})(),Il=class{_root;constructor(t){this._root=t}get root(){return this._root.value}parent(t){let n=this.pathFromRoot(t);return n.length>1?n[n.length-2]:null}children(t){let n=Yf(t,this._root);return n?n.children.map(o=>o.value):[]}firstChild(t){let n=Yf(t,this._root);return n&&n.children.length>0?n.children[0].value:null}siblings(t){let n=Zf(t,this._root);return n.length<2?[]:n[n.length-2].children.map(i=>i.value).filter(i=>i!==t)}pathFromRoot(t){return Zf(t,this._root).map(n=>n.value)}};function Yf(e,t){if(e===t.value)return t;for(let n of t.children){let o=Yf(e,n);if(o)return o}return null}function Zf(e,t){if(e===t.value)return[t];for(let n of t.children){let o=Zf(e,n);if(o.length)return o.unshift(t),o}return[]}var at=class{value;children;constructor(t,n){this.value=t,this.children=n}toString(){return`TreeNode(${this.value})`}};function mi(e){let t={};return e&&e.children.forEach(n=>t[n.value.outlet]=n),t}var Hr=class extends Il{snapshot;constructor(t,n){super(t),this.snapshot=n,op(this,t)}toString(){return this.snapshot.toString()}};function eb(e){let t=VS(e),n=new Ie([new jn("",{})]),o=new Ie({}),i=new Ie({}),r=new Ie({}),s=new Ie(""),a=new Qe(n,o,r,s,i,R,e,t.root);return a.snapshot=t.root,new Hr(new at(a,[]),t)}function VS(e){let t={},n={},o={},i="",r=new mo([],t,o,i,n,R,e,null,{});return new $r("",new at(r,[]))}var Qe=class{urlSubject;paramsSubject;queryParamsSubject;fragmentSubject;dataSubject;outlet;component;snapshot;_futureSnapshot;_routerState;_paramMap;_queryParamMap;title;url;params;queryParams;fragment;data;constructor(t,n,o,i,r,s,a,l){this.urlSubject=t,this.paramsSubject=n,this.queryParamsSubject=o,this.fragmentSubject=i,this.dataSubject=r,this.outlet=s,this.component=a,this._futureSnapshot=l,this.title=this.dataSubject?.pipe(G(c=>c[Gr]))??k(void 0),this.url=t,this.params=n,this.queryParams=o,this.fragment=i,this.data=r}get routeConfig(){return this._futureSnapshot.routeConfig}get root(){return this._routerState.root}get parent(){return this._routerState.parent(this)}get firstChild(){return this._routerState.firstChild(this)}get children(){return this._routerState.children(this)}get pathFromRoot(){return this._routerState.pathFromRoot(this)}get paramMap(){return this._paramMap??=this.params.pipe(G(t=>go(t))),this._paramMap}get queryParamMap(){return this._queryParamMap??=this.queryParams.pipe(G(t=>go(t))),this._queryParamMap}toString(){return this.snapshot?this.snapshot.toString():`Future(${this._futureSnapshot})`}};function El(e,t,n="emptyOnly"){let o,{routeConfig:i}=e;return t!==null&&(n==="always"||i?.path===""||!t.component&&!t.routeConfig?.loadComponent)?o={params:b(b({},t.params),e.params),data:b(b({},t.data),e.data),resolve:b(b(b(b({},e.data),t.data),i?.data),e._resolvedData)}:o={params:b({},e.params),data:b({},e.data),resolve:b(b({},e.data),e._resolvedData??{})},i&&nb(i)&&(o.resolve[Gr]=i.title),o}var mo=class{url;params;queryParams;fragment;data;outlet;component;routeConfig;_resolve;_resolvedData;_routerState;_paramMap;_queryParamMap;get title(){return this.data?.[Gr]}constructor(t,n,o,i,r,s,a,l,c){this.url=t,this.params=n,this.queryParams=o,this.fragment=i,this.data=r,this.outlet=s,this.component=a,this.routeConfig=l,this._resolve=c}get root(){return this._routerState.root}get parent(){return this._routerState.parent(this)}get firstChild(){return this._routerState.firstChild(this)}get children(){return this._routerState.children(this)}get pathFromRoot(){return this._routerState.pathFromRoot(this)}get paramMap(){return this._paramMap??=go(this.params),this._paramMap}get queryParamMap(){return this._queryParamMap??=go(this.queryParams),this._queryParamMap}toString(){let t=this.url.map(o=>o.toString()).join("/"),n=this.routeConfig?this.routeConfig.path:"";return`Route(url:'${t}', path:'${n}')`}},$r=class extends Il{url;constructor(t,n){super(n),this.url=t,op(this,n)}toString(){return tb(this._root)}};function op(e,t){t.value._routerState=e,t.children.forEach(n=>op(e,n))}function tb(e){let t=e.children.length>0?` { ${e.children.map(tb).join(", ")} } `:"";return`${e.value}${t}`}function Hf(e){if(e.snapshot){let t=e.snapshot,n=e._futureSnapshot;e.snapshot=n,zt(t.queryParams,n.queryParams)||e.queryParamsSubject.next(n.queryParams),t.fragment!==n.fragment&&e.fragmentSubject.next(n.fragment),zt(t.params,n.params)||e.paramsSubject.next(n.params),fS(t.url,n.url)||e.urlSubject.next(n.url),zt(t.data,n.data)||e.dataSubject.next(n.data)}else e.snapshot=e._futureSnapshot,e.dataSubject.next(e._futureSnapshot.data)}function Qf(e,t){let n=zt(e.params,t.params)&&gS(e.url,t.url),o=!e.parent!=!t.parent;return n&&!o&&(!e.parent||Qf(e.parent,t.parent))}function nb(e){return typeof e.title=="string"||e.title===null}var ob=new x(""),ip=(()=>{class e{activated=null;get activatedComponentRef(){return this.activated}_activatedRoute=null;name=R;activateEvents=new de;deactivateEvents=new de;attachEvents=new de;detachEvents=new de;routerOutletData=ay(void 0);parentContexts=v(hn);location=v(mt);changeDetector=v(Te);inputBinder=v(Tl,{optional:!0});supportsBindingToComponentInputs=!0;ngOnChanges(n){if(n.name){let{firstChange:o,previousValue:i}=n.name;if(o)return;this.isTrackedInParentContexts(i)&&(this.deactivate(),this.parentContexts.onChildOutletDestroyed(i)),this.initializeOutletWithName()}}ngOnDestroy(){this.isTrackedInParentContexts(this.name)&&this.parentContexts.onChildOutletDestroyed(this.name),this.inputBinder?.unsubscribeFromRouteData(this)}isTrackedInParentContexts(n){return this.parentContexts.getContext(n)?.outlet===this}ngOnInit(){this.initializeOutletWithName()}initializeOutletWithName(){if(this.parentContexts.onChildOutletCreated(this.name,this),this.activated)return;let n=this.parentContexts.getContext(this.name);n?.route&&(n.attachRef?this.attach(n.attachRef,n.route):this.activateWith(n.route,n.injector))}get isActivated(){return!!this.activated}get component(){if(!this.activated)throw new E(4012,!1);return this.activated.instance}get activatedRoute(){if(!this.activated)throw new E(4012,!1);return this._activatedRoute}get activatedRouteData(){return this._activatedRoute?this._activatedRoute.snapshot.data:{}}detach(){if(!this.activated)throw new E(4012,!1);this.location.detach();let n=this.activated;return this.activated=null,this._activatedRoute=null,this.detachEvents.emit(n.instance),n}attach(n,o){this.activated=n,this._activatedRoute=o,this.location.insert(n.hostView),this.inputBinder?.bindActivatedRouteToOutletComponent(this),this.attachEvents.emit(n.instance)}deactivate(){if(this.activated){let n=this.component;this.activated.destroy(),this.activated=null,this._activatedRoute=null,this.deactivateEvents.emit(n)}}activateWith(n,o){if(this.isActivated)throw new E(4013,!1);this._activatedRoute=n;let i=this.location,s=n.snapshot.component,a=this.parentContexts.getOrCreateContext(this.name).children,l=new Kf(n,a,i.injector,this.routerOutletData);this.activated=i.createComponent(s,{index:i.length,injector:l,environmentInjector:o}),this.changeDetector.markForCheck(),this.inputBinder?.bindActivatedRouteToOutletComponent(this),this.activateEvents.emit(this.activated.instance)}static \u0275fac=function(o){return new(o||e)};static \u0275dir=ze({type:e,selectors:[["router-outlet"]],inputs:{name:"name",routerOutletData:[1,"routerOutletData"]},outputs:{activateEvents:"activate",deactivateEvents:"deactivate",attachEvents:"attach",detachEvents:"detach"},exportAs:["outlet"],features:[Pn]})}return e})(),Kf=class{route;childContexts;parent;outletData;constructor(t,n,o,i){this.route=t,this.childContexts=n,this.parent=o,this.outletData=i}get(t,n){return t===Qe?this.route:t===hn?this.childContexts:t===ob?this.outletData:this.parent.get(t,n)}},Tl=new x("");var rp=(()=>{class e{static \u0275fac=function(o){return new(o||e)};static \u0275cmp=Ye({type:e,selectors:[["ng-component"]],exportAs:["emptyRouterOutlet"],decls:1,vars:0,template:function(o,i){o&1&&Za(0,"router-outlet")},dependencies:[ip],encapsulation:2})}return e})();function sp(e){let t=e.children&&e.children.map(sp),n=t?L(b({},e),{children:t}):b({},e);return!n.component&&!n.loadComponent&&(t||n.loadChildren)&&n.outlet&&n.outlet!==R&&(n.component=rp),n}function BS(e,t,n){let o=zr(e,t._root,n?n._root:void 0);return new Hr(o,t)}function zr(e,t,n){if(n&&e.shouldReuseRoute(t.value,n.value.snapshot)){let o=n.value;o._futureSnapshot=t.value;let i=HS(e,t,n);return new at(o,i)}else{if(e.shouldAttach(t.value)){let r=e.retrieve(t.value);if(r!==null){let s=r.route;return s.value._futureSnapshot=t.value,s.children=t.children.map(a=>zr(e,a)),s}}let o=$S(t.value),i=t.children.map(r=>zr(e,r));return new at(o,i)}}function HS(e,t,n){return t.children.map(o=>{for(let i of n.children)if(e.shouldReuseRoute(o.value,i.value.snapshot))return zr(e,o,i);return zr(e,o)})}function $S(e){return new Qe(new Ie(e.url),new Ie(e.params),new Ie(e.queryParams),new Ie(e.fragment),new Ie(e.data),e.outlet,e.component,e)}var Ci=class{redirectTo;navigationBehaviorOptions;constructor(t,n){this.redirectTo=t,this.navigationBehaviorOptions=n}},ib="ngNavigationCancelingError";function xl(e,t){let{redirectTo:n,navigationBehaviorOptions:o}=yi(t)?{redirectTo:t,navigationBehaviorOptions:void 0}:t,i=rb(!1,Ze.Redirect);return i.url=n,i.navigationBehaviorOptions=o,i}function rb(e,t){let n=new Error(`NavigationCancelingError: ${e||""}`);return n[ib]=!0,n.cancellationCode=t,n}function zS(e){return sb(e)&&yi(e.url)}function sb(e){return!!e&&e[ib]}var US=(e,t,n,o)=>G(i=>(new Xf(t,i.targetRouterState,i.currentRouterState,n,o).activate(e),i)),Xf=class{routeReuseStrategy;futureState;currState;forwardEvent;inputBindingEnabled;constructor(t,n,o,i,r){this.routeReuseStrategy=t,this.futureState=n,this.currState=o,this.forwardEvent=i,this.inputBindingEnabled=r}activate(t){let n=this.futureState._root,o=this.currState?this.currState._root:null;this.deactivateChildRoutes(n,o,t),Hf(this.futureState.root),this.activateChildRoutes(n,o,t)}deactivateChildRoutes(t,n,o){let i=mi(n);t.children.forEach(r=>{let s=r.value.outlet;this.deactivateRoutes(r,i[s],o),delete i[s]}),Object.values(i).forEach(r=>{this.deactivateRouteAndItsChildren(r,o)})}deactivateRoutes(t,n,o){let i=t.value,r=n?n.value:null;if(i===r)if(i.component){let s=o.getContext(i.outlet);s&&this.deactivateChildRoutes(t,n,s.children)}else this.deactivateChildRoutes(t,n,o);else r&&this.deactivateRouteAndItsChildren(n,o)}deactivateRouteAndItsChildren(t,n){t.value.component&&this.routeReuseStrategy.shouldDetach(t.value.snapshot)?this.detachAndStoreRouteSubtree(t,n):this.deactivateRouteAndOutlet(t,n)}detachAndStoreRouteSubtree(t,n){let o=n.getContext(t.value.outlet),i=o&&t.value.component?o.children:n,r=mi(t);for(let s of Object.values(r))this.deactivateRouteAndItsChildren(s,i);if(o&&o.outlet){let s=o.outlet.detach(),a=o.children.onOutletDeactivated();this.routeReuseStrategy.store(t.value.snapshot,{componentRef:s,route:t,contexts:a})}}deactivateRouteAndOutlet(t,n){let o=n.getContext(t.value.outlet),i=o&&t.value.component?o.children:n,r=mi(t);for(let s of Object.values(r))this.deactivateRouteAndItsChildren(s,i);o&&(o.outlet&&(o.outlet.deactivate(),o.children.onOutletDeactivated()),o.attachRef=null,o.route=null)}activateChildRoutes(t,n,o){let i=mi(n);t.children.forEach(r=>{this.activateRoutes(r,i[r.value.outlet],o),this.forwardEvent(new Cl(r.value.snapshot))}),t.children.length&&this.forwardEvent(new bl(t.value.snapshot))}activateRoutes(t,n,o){let i=t.value,r=n?n.value:null;if(Hf(i),i===r)if(i.component){let s=o.getOrCreateContext(i.outlet);this.activateChildRoutes(t,n,s.children)}else this.activateChildRoutes(t,n,o);else if(i.component){let s=o.getOrCreateContext(i.outlet);if(this.routeReuseStrategy.shouldAttach(i.snapshot)){let a=this.routeReuseStrategy.retrieve(i.snapshot);this.routeReuseStrategy.store(i.snapshot,null),s.children.onOutletReAttached(a.contexts),s.attachRef=a.componentRef,s.route=a.route.value,s.outlet&&s.outlet.attach(a.componentRef,a.route.value),Hf(a.route.value),this.activateChildRoutes(t,null,s.children)}else s.attachRef=null,s.route=i,s.outlet&&s.outlet.activateWith(i,s.injector),this.activateChildRoutes(t,null,s.children)}else this.activateChildRoutes(t,null,o)}},Sl=class{path;route;constructor(t){this.path=t,this.route=this.path[this.path.length-1]}},vi=class{component;route;constructor(t,n){this.component=t,this.route=n}};function qS(e,t,n){let o=e._root,i=t?t._root:null;return Nr(o,i,n,[o.value])}function GS(e){let t=e.routeConfig?e.routeConfig.canActivateChild:null;return!t||t.length===0?null:{node:e,guards:t}}function Ei(e,t){let n=Symbol(),o=t.get(e,n);return o===n?typeof e=="function"&&!iu(e)?e:t.get(e):o}function Nr(e,t,n,o,i={canDeactivateChecks:[],canActivateChecks:[]}){let r=mi(t);return e.children.forEach(s=>{WS(s,r[s.value.outlet],n,o.concat([s.value]),i),delete r[s.value.outlet]}),Object.entries(r).forEach(([s,a])=>Fr(a,n.getContext(s),i)),i}function WS(e,t,n,o,i={canDeactivateChecks:[],canActivateChecks:[]}){let r=e.value,s=t?t.value:null,a=n?n.getContext(e.value.outlet):null;if(s&&r.routeConfig===s.routeConfig){let l=YS(s,r,r.routeConfig.runGuardsAndResolvers);l?i.canActivateChecks.push(new Sl(o)):(r.data=s.data,r._resolvedData=s._resolvedData),r.component?Nr(e,t,a?a.children:null,o,i):Nr(e,t,n,o,i),l&&a&&a.outlet&&a.outlet.isActivated&&i.canDeactivateChecks.push(new vi(a.outlet.component,s))}else s&&Fr(t,a,i),i.canActivateChecks.push(new Sl(o)),r.component?Nr(e,null,a?a.children:null,o,i):Nr(e,null,n,o,i);return i}function YS(e,t,n){if(typeof n=="function")return n(e,t);switch(n){case"pathParamsChange":return!ho(e.url,t.url);case"pathParamsOrQueryParamsChange":return!ho(e.url,t.url)||!zt(e.queryParams,t.queryParams);case"always":return!0;case"paramsOrQueryParamsChange":return!Qf(e,t)||!zt(e.queryParams,t.queryParams);case"paramsChange":default:return!Qf(e,t)}}function Fr(e,t,n){let o=mi(e),i=e.value;Object.entries(o).forEach(([r,s])=>{i.component?t?Fr(s,t.children.getContext(r),n):Fr(s,null,n):Fr(s,t,n)}),i.component?t&&t.outlet&&t.outlet.isActivated?n.canDeactivateChecks.push(new vi(t.outlet.component,i)):n.canDeactivateChecks.push(new vi(null,i)):n.canDeactivateChecks.push(new vi(null,i))}function Wr(e){return typeof e=="function"}function ZS(e){return typeof e=="boolean"}function QS(e){return e&&Wr(e.canLoad)}function KS(e){return e&&Wr(e.canActivate)}function XS(e){return e&&Wr(e.canActivateChild)}function JS(e){return e&&Wr(e.canDeactivate)}function e_(e){return e&&Wr(e.canMatch)}function ab(e){return e instanceof Qt||e?.name==="EmptyError"}var sl=Symbol("INITIAL_VALUE");function Di(){return Be(e=>Ho(e.map(t=>t.pipe(Kt(1),Hc(sl)))).pipe(G(t=>{for(let n of t)if(n!==!0){if(n===sl)return sl;if(n===!1||t_(n))return n}return!0}),Ae(t=>t!==sl),Kt(1)))}function t_(e){return yi(e)||e instanceof Ci}function n_(e,t){return fe(n=>{let{targetSnapshot:o,currentSnapshot:i,guards:{canActivateChecks:r,canDeactivateChecks:s}}=n;return s.length===0&&r.length===0?k(L(b({},n),{guardsResult:!0})):o_(s,o,i,e).pipe(fe(a=>a&&ZS(a)?i_(o,r,e,t):k(a)),G(a=>L(b({},n),{guardsResult:a})))})}function o_(e,t,n,o){return ue(e).pipe(fe(i=>c_(i.component,i.route,n,t,o)),Xt(i=>i!==!0,!0))}function i_(e,t,n,o){return ue(t).pipe(Dn(i=>zo(s_(i.route.parent,o),r_(i.route,o),l_(e,i.path,n),a_(e,i.route,n))),Xt(i=>i!==!0,!0))}function r_(e,t){return e!==null&&t&&t(new wl(e)),k(!0)}function s_(e,t){return e!==null&&t&&t(new yl(e)),k(!0)}function a_(e,t,n){let o=t.routeConfig?t.routeConfig.canActivate:null;if(!o||o.length===0)return k(!0);let i=o.map(r=>Yi(()=>{let s=Ii(t)??n,a=Ei(r,s),l=KS(a)?a.canActivate(t,e):qe(s,()=>a(t,e));return pn(l).pipe(Xt())}));return k(i).pipe(Di())}function l_(e,t,n){let o=t[t.length-1],r=t.slice(0,t.length-1).reverse().map(s=>GS(s)).filter(s=>s!==null).map(s=>Yi(()=>{let a=s.guards.map(l=>{let c=Ii(s.node)??n,u=Ei(l,c),d=XS(u)?u.canActivateChild(o,e):qe(c,()=>u(o,e));return pn(d).pipe(Xt())});return k(a).pipe(Di())}));return k(r).pipe(Di())}function c_(e,t,n,o,i){let r=t&&t.routeConfig?t.routeConfig.canDeactivate:null;if(!r||r.length===0)return k(!0);let s=r.map(a=>{let l=Ii(t)??i,c=Ei(a,l),u=JS(c)?c.canDeactivate(e,t,n,o):qe(l,()=>c(e,t,n,o));return pn(u).pipe(Xt())});return k(s).pipe(Di())}function u_(e,t,n,o){let i=t.canLoad;if(i===void 0||i.length===0)return k(!0);let r=i.map(s=>{let a=Ei(s,e),l=QS(a)?a.canLoad(t,n):qe(e,()=>a(t,n));return pn(l)});return k(r).pipe(Di(),lb(o))}function lb(e){return Rc(Se(t=>{if(typeof t!="boolean")throw xl(e,t)}),G(t=>t===!0))}function d_(e,t,n,o){let i=t.canMatch;if(!i||i.length===0)return k(!0);let r=i.map(s=>{let a=Ei(s,e),l=e_(a)?a.canMatch(t,n):qe(e,()=>a(t,n));return pn(l)});return k(r).pipe(Di(),lb(o))}var Ur=class{segmentGroup;constructor(t){this.segmentGroup=t||null}},qr=class extends Error{urlTree;constructor(t){super(),this.urlTree=t}};function hi(e){return Vo(new Ur(e))}function f_(e){return Vo(new E(4e3,!1))}function p_(e){return Vo(rb(!1,Ze.GuardRejected))}var Jf=class{urlSerializer;urlTree;constructor(t,n){this.urlSerializer=t,this.urlTree=n}lineralizeSegments(t,n){let o=[],i=n.root;for(;;){if(o=o.concat(i.segments),i.numberOfChildren===0)return k(o);if(i.numberOfChildren>1||!i.children[R])return f_(`${t.redirectTo}`);i=i.children[R]}}applyRedirectCommands(t,n,o,i,r){return h_(n,i,r).pipe(G(s=>{if(s instanceof qt)throw new qr(s);let a=this.applyRedirectCreateUrlTree(s,this.urlSerializer.parse(s),t,o);if(s[0]==="/")throw new qr(a);return a}))}applyRedirectCreateUrlTree(t,n,o,i){let r=this.createSegmentGroup(t,n.root,o,i);return new qt(r,this.createQueryParams(n.queryParams,this.urlTree.queryParams),n.fragment)}createQueryParams(t,n){let o={};return Object.entries(t).forEach(([i,r])=>{if(typeof r=="string"&&r[0]===":"){let a=r.substring(1);o[i]=n[a]}else o[i]=r}),o}createSegmentGroup(t,n,o,i){let r=this.createSegments(t,n.segments,o,i),s={};return Object.entries(n.children).forEach(([a,l])=>{s[a]=this.createSegmentGroup(t,l,o,i)}),new K(r,s)}createSegments(t,n,o,i){return n.map(r=>r.path[0]===":"?this.findPosParam(t,r,i):this.findOrReturn(r,o))}findPosParam(t,n,o){let i=o[n.path.substring(1)];if(!i)throw new E(4001,!1);return i}findOrReturn(t,n){let o=0;for(let i of n){if(i.path===t.path)return n.splice(o),i;o++}return t}};function h_(e,t,n){if(typeof e=="string")return k(e);let o=e,{queryParams:i,fragment:r,routeConfig:s,url:a,outlet:l,params:c,data:u,title:d}=t;return pn(qe(n,()=>o({params:c,data:u,queryParams:i,fragment:r,routeConfig:s,url:a,outlet:l,title:d})))}var ep={matched:!1,consumedSegments:[],remainingSegments:[],parameters:{},positionalParamSegments:{}};function m_(e,t,n,o,i){let r=cb(e,t,n);return r.matched?(o=LS(t,o),d_(o,t,n,i).pipe(G(s=>s===!0?r:b({},ep)))):k(r)}function cb(e,t,n){if(t.path==="**")return g_(n);if(t.path==="")return t.pathMatch==="full"&&(e.hasChildren()||n.length>0)?b({},ep):{matched:!0,consumedSegments:[],remainingSegments:n,parameters:{},positionalParamSegments:{}};let i=(t.matcher||jy)(n,e,t);if(!i)return b({},ep);let r={};Object.entries(i.posParams??{}).forEach(([a,l])=>{r[a]=l.path});let s=i.consumed.length>0?b(b({},r),i.consumed[i.consumed.length-1].parameters):r;return{matched:!0,consumedSegments:i.consumed,remainingSegments:n.slice(i.consumed.length),parameters:s,positionalParamSegments:i.posParams??{}}}function g_(e){return{matched:!0,parameters:e.length>0?By(e).parameters:{},consumedSegments:e,remainingSegments:[],positionalParamSegments:{}}}function Py(e,t,n,o){return n.length>0&&b_(e,n,o)?{segmentGroup:new K(t,y_(o,new K(n,e.children))),slicedSegments:[]}:n.length===0&&w_(e,n,o)?{segmentGroup:new K(e.segments,v_(e,n,o,e.children)),slicedSegments:n}:{segmentGroup:new K(e.segments,e.children),slicedSegments:n}}function v_(e,t,n,o){let i={};for(let r of n)if(Ml(e,t,r)&&!o[Mt(r)]){let s=new K([],{});i[Mt(r)]=s}return b(b({},o),i)}function y_(e,t){let n={};n[R]=t;for(let o of e)if(o.path===""&&Mt(o)!==R){let i=new K([],{});n[Mt(o)]=i}return n}function b_(e,t,n){return n.some(o=>Ml(e,t,o)&&Mt(o)!==R)}function w_(e,t,n){return n.some(o=>Ml(e,t,o))}function Ml(e,t,n){return(e.hasChildren()||t.length>0)&&n.pathMatch==="full"?!1:n.path===""}function C_(e,t,n){return t.length===0&&!e.children[n]}var tp=class{};function D_(e,t,n,o,i,r,s="emptyOnly"){return new np(e,t,n,o,i,s,r).recognize()}var I_=31,np=class{injector;configLoader;rootComponentType;config;urlTree;paramsInheritanceStrategy;urlSerializer;applyRedirects;absoluteRedirectCount=0;allowRedirects=!0;constructor(t,n,o,i,r,s,a){this.injector=t,this.configLoader=n,this.rootComponentType=o,this.config=i,this.urlTree=r,this.paramsInheritanceStrategy=s,this.urlSerializer=a,this.applyRedirects=new Jf(this.urlSerializer,this.urlTree)}noMatchError(t){return new E(4002,`'${t.segmentGroup}'`)}recognize(){let t=Py(this.urlTree.root,[],[],this.config).segmentGroup;return this.match(t).pipe(G(({children:n,rootSnapshot:o})=>{let i=new at(o,n),r=new $r("",i),s=Zy(o,[],this.urlTree.queryParams,this.urlTree.fragment);return s.queryParams=this.urlTree.queryParams,r.url=this.urlSerializer.serialize(s),{state:r,tree:s}}))}match(t){let n=new mo([],Object.freeze({}),Object.freeze(b({},this.urlTree.queryParams)),this.urlTree.fragment,Object.freeze({}),R,this.rootComponentType,null,{});return this.processSegmentGroup(this.injector,this.config,t,R,n).pipe(G(o=>({children:o,rootSnapshot:n})),Rt(o=>{if(o instanceof qr)return this.urlTree=o.urlTree,this.match(o.urlTree.root);throw o instanceof Ur?this.noMatchError(o):o}))}processSegmentGroup(t,n,o,i,r){return o.segments.length===0&&o.hasChildren()?this.processChildren(t,n,o,r):this.processSegment(t,n,o,o.segments,i,!0,r).pipe(G(s=>s instanceof at?[s]:[]))}processChildren(t,n,o,i){let r=[];for(let s of Object.keys(o.children))s==="primary"?r.unshift(s):r.push(s);return ue(r).pipe(Dn(s=>{let a=o.children[s],l=jS(n,s);return this.processSegmentGroup(t,l,a,s,i)}),Bc((s,a)=>(s.push(...a),s)),In(null),Vc(),fe(s=>{if(s===null)return hi(o);let a=ub(s);return E_(a),k(a)}))}processSegment(t,n,o,i,r,s,a){return ue(n).pipe(Dn(l=>this.processSegmentAgainstRoute(l._injector??t,n,l,o,i,r,s,a).pipe(Rt(c=>{if(c instanceof Ur)return k(null);throw c}))),Xt(l=>!!l),Rt(l=>{if(ab(l))return C_(o,i,r)?k(new tp):hi(o);throw l}))}processSegmentAgainstRoute(t,n,o,i,r,s,a,l){return Mt(o)!==s&&(s===R||!Ml(i,r,o))?hi(i):o.redirectTo===void 0?this.matchSegmentAgainstRoute(t,i,o,r,s,l):this.allowRedirects&&a?this.expandSegmentAgainstRouteUsingRedirect(t,i,n,o,r,s,l):hi(i)}expandSegmentAgainstRouteUsingRedirect(t,n,o,i,r,s,a){let{matched:l,parameters:c,consumedSegments:u,positionalParamSegments:d,remainingSegments:p}=cb(n,i,r);if(!l)return hi(n);typeof i.redirectTo=="string"&&i.redirectTo[0]==="/"&&(this.absoluteRedirectCount++,this.absoluteRedirectCount>I_&&(this.allowRedirects=!1));let f=new mo(r,c,Object.freeze(b({},this.urlTree.queryParams)),this.urlTree.fragment,Fy(i),Mt(i),i.component??i._loadedComponent??null,i,Ly(i)),h=El(f,a,this.paramsInheritanceStrategy);return f.params=Object.freeze(h.params),f.data=Object.freeze(h.data),this.applyRedirects.applyRedirectCommands(u,i.redirectTo,d,f,t).pipe(Be(g=>this.applyRedirects.lineralizeSegments(i,g)),fe(g=>this.processSegment(t,o,n,g.concat(p),s,!1,a)))}matchSegmentAgainstRoute(t,n,o,i,r,s){let a=m_(n,o,i,t,this.urlSerializer);return o.path==="**"&&(n.children={}),a.pipe(Be(l=>l.matched?(t=o._injector??t,this.getChildConfig(t,o,i).pipe(Be(({routes:c})=>{let u=o._loadedInjector??t,{parameters:d,consumedSegments:p,remainingSegments:f}=l,h=new mo(p,d,Object.freeze(b({},this.urlTree.queryParams)),this.urlTree.fragment,Fy(o),Mt(o),o.component??o._loadedComponent??null,o,Ly(o)),y=El(h,s,this.paramsInheritanceStrategy);h.params=Object.freeze(y.params),h.data=Object.freeze(y.data);let{segmentGroup:g,slicedSegments:w}=Py(n,p,f,c);if(w.length===0&&g.hasChildren())return this.processChildren(u,c,g,h).pipe(G(M=>new at(h,M)));if(c.length===0&&w.length===0)return k(new at(h,[]));let P=Mt(o)===r;return this.processSegment(u,c,g,w,P?R:r,!0,h).pipe(G(M=>new at(h,M instanceof at?[M]:[])))}))):hi(n)))}getChildConfig(t,n,o){return n.children?k({routes:n.children,injector:t}):n.loadChildren?n._loadedRoutes!==void 0?k({routes:n._loadedRoutes,injector:n._loadedInjector}):u_(t,n,o,this.urlSerializer).pipe(fe(i=>i?this.configLoader.loadChildren(t,n).pipe(Se(r=>{n._loadedRoutes=r.routes,n._loadedInjector=r.injector})):p_(n))):k({routes:[],injector:t})}};function E_(e){e.sort((t,n)=>t.value.outlet===R?-1:n.value.outlet===R?1:t.value.outlet.localeCompare(n.value.outlet))}function x_(e){let t=e.value.routeConfig;return t&&t.path===""}function ub(e){let t=[],n=new Set;for(let o of e){if(!x_(o)){t.push(o);continue}let i=t.find(r=>o.value.routeConfig===r.value.routeConfig);i!==void 0?(i.children.push(...o.children),n.add(i)):t.push(o)}for(let o of n){let i=ub(o.children);t.push(new at(o.value,i))}return t.filter(o=>!n.has(o))}function Fy(e){return e.data||{}}function Ly(e){return e.resolve||{}}function S_(e,t,n,o,i,r){return fe(s=>D_(e,t,n,o,s.extractedUrl,i,r).pipe(G(({state:a,tree:l})=>L(b({},s),{targetSnapshot:a,urlAfterRedirects:l}))))}function __(e,t){return fe(n=>{let{targetSnapshot:o,guards:{canActivateChecks:i}}=n;if(!i.length)return k(n);let r=new Set(i.map(l=>l.route)),s=new Set;for(let l of r)if(!s.has(l))for(let c of db(l))s.add(c);let a=0;return ue(s).pipe(Dn(l=>r.has(l)?T_(l,o,e,t):(l.data=El(l,l.parent,e).resolve,k(void 0))),Se(()=>a++),qo(1),fe(l=>a===s.size?k(n):Ke))})}function db(e){let t=e.children.map(n=>db(n)).flat();return[e,...t]}function T_(e,t,n,o){let i=e.routeConfig,r=e._resolve;return i?.title!==void 0&&!nb(i)&&(r[Gr]=i.title),Yi(()=>(e.data=El(e,e.parent,n).resolve,M_(r,e,t,o).pipe(G(s=>(e._resolvedData=s,e.data=b(b({},e.data),s),null)))))}function M_(e,t,n,o){let i=Uf(e);if(i.length===0)return k({});let r={};return ue(i).pipe(fe(s=>k_(e[s],t,n,o).pipe(Xt(),Se(a=>{if(a instanceof Ci)throw xl(new vo,a);r[s]=a}))),qo(1),G(()=>r),Rt(s=>ab(s)?Ke:Vo(s)))}function k_(e,t,n,o){let i=Ii(t)??o,r=Ei(e,i),s=r.resolve?r.resolve(t,n):qe(i,()=>r(t,n));return pn(s)}function $f(e){return Be(t=>{let n=e(t);return n?ue(n).pipe(G(()=>t)):k(t)})}var ap=(()=>{class e{buildTitle(n){let o,i=n.root;for(;i!==void 0;)o=this.getResolvedTitleForRoute(i)??o,i=i.children.find(r=>r.outlet===R);return o}getResolvedTitleForRoute(n){return n.data[Gr]}static \u0275fac=function(o){return new(o||e)};static \u0275prov=I({token:e,factory:()=>v(fb),providedIn:"root"})}return e})(),fb=(()=>{class e extends ap{title;constructor(n){super(),this.title=n}updateTitle(n){let o=this.buildTitle(n);o!==void 0&&this.title.setTitle(o)}static \u0275fac=function(o){return new(o||e)(S(ky))};static \u0275prov=I({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})(),Yr=new x("",{providedIn:"root",factory:()=>({})}),Zr=new x(""),lp=(()=>{class e{componentLoaders=new WeakMap;childrenLoaders=new WeakMap;onLoadStartListener;onLoadEndListener;compiler=v(gf);loadComponent(n,o){if(this.componentLoaders.get(o))return this.componentLoaders.get(o);if(o._loadedComponent)return k(o._loadedComponent);this.onLoadStartListener&&this.onLoadStartListener(o);let i=pn(qe(n,()=>o.loadComponent())).pipe(G(hb),Se(s=>{this.onLoadEndListener&&this.onLoadEndListener(o),o._loadedComponent=s}),Zi(()=>{this.componentLoaders.delete(o)})),r=new Lo(i,()=>new re).pipe(Fo());return this.componentLoaders.set(o,r),r}loadChildren(n,o){if(this.childrenLoaders.get(o))return this.childrenLoaders.get(o);if(o._loadedRoutes)return k({routes:o._loadedRoutes,injector:o._loadedInjector});this.onLoadStartListener&&this.onLoadStartListener(o);let r=pb(o,this.compiler,n,this.onLoadEndListener).pipe(Zi(()=>{this.childrenLoaders.delete(o)})),s=new Lo(r,()=>new re).pipe(Fo());return this.childrenLoaders.set(o,s),s}static \u0275fac=function(o){return new(o||e)};static \u0275prov=I({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})();function pb(e,t,n,o){return pn(qe(n,()=>e.loadChildren())).pipe(G(hb),fe(i=>i instanceof Ua||Array.isArray(i)?k(i):ue(t.compileModuleAsync(i))),G(i=>{o&&o(e);let r,s,a=!1;return Array.isArray(i)?(s=i,a=!0):(r=i.create(n).injector,s=r.get(Zr,[],{optional:!0,self:!0}).flat()),{routes:s.map(sp),injector:r}}))}function A_(e){return e&&typeof e=="object"&&"default"in e}function hb(e){return A_(e)?e.default:e}var kl=(()=>{class e{static \u0275fac=function(o){return new(o||e)};static \u0275prov=I({token:e,factory:()=>v(R_),providedIn:"root"})}return e})(),R_=(()=>{class e{shouldProcessUrl(n){return!0}extract(n){return n}merge(n,o){return n}static \u0275fac=function(o){return new(o||e)};static \u0275prov=I({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})(),mb=new x("");var gb=new x(""),vb=(()=>{class e{currentNavigation=null;currentTransition=null;lastSuccessfulNavigation=null;events=new re;transitionAbortWithErrorSubject=new re;configLoader=v(lp);environmentInjector=v(ae);destroyRef=v(ro);urlSerializer=v(yo);rootContexts=v(hn);location=v(Tt);inputBindingEnabled=v(Tl,{optional:!0})!==null;titleStrategy=v(ap);options=v(Yr,{optional:!0})||{};paramsInheritanceStrategy=this.options.paramsInheritanceStrategy||"emptyOnly";urlHandlingStrategy=v(kl);createViewTransition=v(mb,{optional:!0});navigationErrorHandler=v(gb,{optional:!0});navigationId=0;get hasRequestedNavigation(){return this.navigationId!==0}transitions;afterPreactivation=()=>k(void 0);rootComponentType=null;destroyed=!1;constructor(){let n=i=>this.events.next(new gl(i)),o=i=>this.events.next(new vl(i));this.configLoader.onLoadEndListener=o,this.configLoader.onLoadStartListener=n,this.destroyRef.onDestroy(()=>{this.destroyed=!0})}complete(){this.transitions?.complete()}handleNavigationRequest(n){let o=++this.navigationId;this.transitions?.next(L(b({},n),{extractedUrl:this.urlHandlingStrategy.extract(n.rawUrl),targetSnapshot:null,targetRouterState:null,guards:{canActivateChecks:[],canDeactivateChecks:[]},guardsResult:null,abortController:new AbortController,id:o}))}setupNavigations(n){return this.transitions=new Ie(null),this.transitions.pipe(Ae(o=>o!==null),Be(o=>{let i=!1;return k(o).pipe(Be(r=>{if(this.navigationId>o.id)return this.cancelNavigationTransition(o,"",Ze.SupersededByNewNavigation),Ke;this.currentTransition=o,this.currentNavigation={id:r.id,initialUrl:r.rawUrl,extractedUrl:r.extractedUrl,targetBrowserUrl:typeof r.extras.browserUrl=="string"?this.urlSerializer.parse(r.extras.browserUrl):r.extras.browserUrl,trigger:r.source,extras:r.extras,previousNavigation:this.lastSuccessfulNavigation?L(b({},this.lastSuccessfulNavigation),{previousNavigation:null}):null,abort:()=>r.abortController.abort()};let s=!n.navigated||this.isUpdatingInternalState()||this.isUpdatedBrowserUrl(),a=r.extras.onSameUrlNavigation??n.onSameUrlNavigation;if(!s&&a!=="reload"){let l="";return this.events.next(new fn(r.id,this.urlSerializer.serialize(r.rawUrl),l,jr.IgnoredSameUrlNavigation)),r.resolve(!1),Ke}if(this.urlHandlingStrategy.shouldProcessUrl(r.rawUrl))return k(r).pipe(Be(l=>(this.events.next(new dn(l.id,this.urlSerializer.serialize(l.extractedUrl),l.source,l.restoredState)),l.id!==this.navigationId?Ke:Promise.resolve(l))),S_(this.environmentInjector,this.configLoader,this.rootComponentType,n.config,this.urlSerializer,this.paramsInheritanceStrategy),Se(l=>{o.targetSnapshot=l.targetSnapshot,o.urlAfterRedirects=l.urlAfterRedirects,this.currentNavigation=L(b({},this.currentNavigation),{finalUrl:l.urlAfterRedirects});let c=new Vr(l.id,this.urlSerializer.serialize(l.extractedUrl),this.urlSerializer.serialize(l.urlAfterRedirects),l.targetSnapshot);this.events.next(c)}));if(s&&this.urlHandlingStrategy.shouldProcessUrl(r.currentRawUrl)){let{id:l,extractedUrl:c,source:u,restoredState:d,extras:p}=r,f=new dn(l,this.urlSerializer.serialize(c),u,d);this.events.next(f);let h=eb(this.rootComponentType).snapshot;return this.currentTransition=o=L(b({},r),{targetSnapshot:h,urlAfterRedirects:c,extras:L(b({},p),{skipLocationChange:!1,replaceUrl:!1})}),this.currentNavigation.finalUrl=c,k(o)}else{let l="";return this.events.next(new fn(r.id,this.urlSerializer.serialize(r.extractedUrl),l,jr.IgnoredByUrlHandlingStrategy)),r.resolve(!1),Ke}}),Se(r=>{let s=new fl(r.id,this.urlSerializer.serialize(r.extractedUrl),this.urlSerializer.serialize(r.urlAfterRedirects),r.targetSnapshot);this.events.next(s)}),G(r=>(this.currentTransition=o=L(b({},r),{guards:qS(r.targetSnapshot,r.currentSnapshot,this.rootContexts)}),o)),n_(this.environmentInjector,r=>this.events.next(r)),Se(r=>{if(o.guardsResult=r.guardsResult,r.guardsResult&&typeof r.guardsResult!="boolean")throw xl(this.urlSerializer,r.guardsResult);let s=new pl(r.id,this.urlSerializer.serialize(r.extractedUrl),this.urlSerializer.serialize(r.urlAfterRedirects),r.targetSnapshot,!!r.guardsResult);this.events.next(s)}),Ae(r=>r.guardsResult?!0:(this.cancelNavigationTransition(r,"",Ze.GuardRejected),!1)),$f(r=>{if(r.guards.canActivateChecks.length!==0)return k(r).pipe(Se(s=>{let a=new hl(s.id,this.urlSerializer.serialize(s.extractedUrl),this.urlSerializer.serialize(s.urlAfterRedirects),s.targetSnapshot);this.events.next(a)}),Be(s=>{let a=!1;return k(s).pipe(__(this.paramsInheritanceStrategy,this.environmentInjector),Se({next:()=>a=!0,complete:()=>{a||this.cancelNavigationTransition(s,"",Ze.NoDataFromResolver)}}))}),Se(s=>{let a=new ml(s.id,this.urlSerializer.serialize(s.extractedUrl),this.urlSerializer.serialize(s.urlAfterRedirects),s.targetSnapshot);this.events.next(a)}))}),$f(r=>{let s=a=>{let l=[];if(a.routeConfig?.loadComponent&&!a.routeConfig._loadedComponent){let c=Ii(a)??this.environmentInjector;l.push(this.configLoader.loadComponent(c,a.routeConfig).pipe(Se(u=>{a.component=u}),G(()=>{})))}for(let c of a.children)l.push(...s(c));return l};return Ho(s(r.targetSnapshot.root)).pipe(In(null),Kt(1))}),$f(()=>this.afterPreactivation()),Be(()=>{let{currentSnapshot:r,targetSnapshot:s}=o,a=this.createViewTransition?.(this.environmentInjector,r.root,s.root);return a?ue(a).pipe(G(()=>o)):k(o)}),G(r=>{let s=BS(n.routeReuseStrategy,r.targetSnapshot,r.currentRouterState);return this.currentTransition=o=L(b({},r),{targetRouterState:s}),this.currentNavigation.targetRouterState=s,o}),Se(()=>{this.events.next(new Br)}),US(this.rootContexts,n.routeReuseStrategy,r=>this.events.next(r),this.inputBindingEnabled),Kt(1),Gs(new z(r=>{let s=o.abortController.signal,a=()=>r.next();return s.addEventListener("abort",a),()=>s.removeEventListener("abort",a)}).pipe(Ae(()=>!i&&!o.targetRouterState),Se(()=>{this.cancelNavigationTransition(o,o.abortController.signal.reason+"",Ze.Aborted)}))),Se({next:r=>{i=!0,this.lastSuccessfulNavigation=this.currentNavigation,this.events.next(new Gt(r.id,this.urlSerializer.serialize(r.extractedUrl),this.urlSerializer.serialize(r.urlAfterRedirects))),this.titleStrategy?.updateTitle(r.targetRouterState.snapshot),r.resolve(!0)},complete:()=>{i=!0}}),Gs(this.transitionAbortWithErrorSubject.pipe(Se(r=>{throw r}))),Zi(()=>{i||this.cancelNavigationTransition(o,"",Ze.SupersededByNewNavigation),this.currentTransition?.id===o.id&&(this.currentNavigation=null,this.currentTransition=null)}),Rt(r=>{if(this.destroyed)return o.resolve(!1),Ke;if(i=!0,sb(r))this.events.next(new Ut(o.id,this.urlSerializer.serialize(o.extractedUrl),r.message,r.cancellationCode)),zS(r)?this.events.next(new wi(r.url,r.navigationBehaviorOptions)):o.resolve(!1);else{let s=new bi(o.id,this.urlSerializer.serialize(o.extractedUrl),r,o.targetSnapshot??void 0);try{let a=qe(this.environmentInjector,()=>this.navigationErrorHandler?.(s));if(a instanceof Ci){let{message:l,cancellationCode:c}=xl(this.urlSerializer,a);this.events.next(new Ut(o.id,this.urlSerializer.serialize(o.extractedUrl),l,c)),this.events.next(new wi(a.redirectTo,a.navigationBehaviorOptions))}else throw this.events.next(s),r}catch(a){this.options.resolveNavigationPromiseOnError?o.resolve(!1):o.reject(a)}}return Ke}))}))}cancelNavigationTransition(n,o,i){let r=new Ut(n.id,this.urlSerializer.serialize(n.extractedUrl),o,i);this.events.next(r),n.resolve(!1)}isUpdatingInternalState(){return this.currentTransition?.extractedUrl.toString()!==this.currentTransition?.currentUrlTree.toString()}isUpdatedBrowserUrl(){let n=this.urlHandlingStrategy.extract(this.urlSerializer.parse(this.location.path(!0))),o=this.currentNavigation?.targetBrowserUrl??this.currentNavigation?.extractedUrl;return n.toString()!==o?.toString()&&!this.currentNavigation?.extras.skipLocationChange}static \u0275fac=function(o){return new(o||e)};static \u0275prov=I({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})();function N_(e){return e!==Pr}var yb=(()=>{class e{static \u0275fac=function(o){return new(o||e)};static \u0275prov=I({token:e,factory:()=>v(O_),providedIn:"root"})}return e})(),_l=class{shouldDetach(t){return!1}store(t,n){}shouldAttach(t){return!1}retrieve(t){return null}shouldReuseRoute(t,n){return t.routeConfig===n.routeConfig}},O_=(()=>{class e extends _l{static \u0275fac=(()=>{let n;return function(i){return(n||(n=fo(e)))(i||e)}})();static \u0275prov=I({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})(),bb=(()=>{class e{urlSerializer=v(yo);options=v(Yr,{optional:!0})||{};canceledNavigationResolution=this.options.canceledNavigationResolution||"replace";location=v(Tt);urlHandlingStrategy=v(kl);urlUpdateStrategy=this.options.urlUpdateStrategy||"deferred";currentUrlTree=new qt;getCurrentUrlTree(){return this.currentUrlTree}rawUrlTree=this.currentUrlTree;getRawUrlTree(){return this.rawUrlTree}createBrowserPath({finalUrl:n,initialUrl:o,targetBrowserUrl:i}){let r=n!==void 0?this.urlHandlingStrategy.merge(n,o):o,s=i??r;return s instanceof qt?this.urlSerializer.serialize(s):s}commitTransition({targetRouterState:n,finalUrl:o,initialUrl:i}){o&&n?(this.currentUrlTree=o,this.rawUrlTree=this.urlHandlingStrategy.merge(o,i),this.routerState=n):this.rawUrlTree=i}routerState=eb(null);getRouterState(){return this.routerState}stateMemento=this.createStateMemento();updateStateMemento(){this.stateMemento=this.createStateMemento()}createStateMemento(){return{rawUrlTree:this.rawUrlTree,currentUrlTree:this.currentUrlTree,routerState:this.routerState}}resetInternalState({finalUrl:n}){this.routerState=this.stateMemento.routerState,this.currentUrlTree=this.stateMemento.currentUrlTree,this.rawUrlTree=this.urlHandlingStrategy.merge(this.currentUrlTree,n??this.rawUrlTree)}static \u0275fac=function(o){return new(o||e)};static \u0275prov=I({token:e,factory:()=>v(P_),providedIn:"root"})}return e})(),P_=(()=>{class e extends bb{currentPageId=0;lastSuccessfulId=-1;restoredState(){return this.location.getState()}get browserPageId(){return this.canceledNavigationResolution!=="computed"?this.currentPageId:this.restoredState()?.\u0275routerPageId??this.currentPageId}registerNonRouterCurrentEntryChangeListener(n){return this.location.subscribe(o=>{o.type==="popstate"&&setTimeout(()=>{n(o.url,o.state,"popstate")})})}handleRouterEvent(n,o){n instanceof dn?this.updateStateMemento():n instanceof fn?this.commitTransition(o):n instanceof Vr?this.urlUpdateStrategy==="eager"&&(o.extras.skipLocationChange||this.setBrowserUrl(this.createBrowserPath(o),o)):n instanceof Br?(this.commitTransition(o),this.urlUpdateStrategy==="deferred"&&!o.extras.skipLocationChange&&this.setBrowserUrl(this.createBrowserPath(o),o)):n instanceof Ut&&n.code!==Ze.SupersededByNewNavigation&&n.code!==Ze.Redirect?this.restoreHistory(o):n instanceof bi?this.restoreHistory(o,!0):n instanceof Gt&&(this.lastSuccessfulId=n.id,this.currentPageId=this.browserPageId)}setBrowserUrl(n,{extras:o,id:i}){let{replaceUrl:r,state:s}=o;if(this.location.isCurrentPathEqualTo(n)||r){let a=this.browserPageId,l=b(b({},s),this.generateNgRouterState(i,a));this.location.replaceState(n,"",l)}else{let a=b(b({},s),this.generateNgRouterState(i,this.browserPageId+1));this.location.go(n,"",a)}}restoreHistory(n,o=!1){if(this.canceledNavigationResolution==="computed"){let i=this.browserPageId,r=this.currentPageId-i;r!==0?this.location.historyGo(r):this.getCurrentUrlTree()===n.finalUrl&&r===0&&(this.resetInternalState(n),this.resetUrlToCurrentUrlTree())}else this.canceledNavigationResolution==="replace"&&(o&&this.resetInternalState(n),this.resetUrlToCurrentUrlTree())}resetUrlToCurrentUrlTree(){this.location.replaceState(this.urlSerializer.serialize(this.getRawUrlTree()),"",this.generateNgRouterState(this.lastSuccessfulId,this.currentPageId))}generateNgRouterState(n,o){return this.canceledNavigationResolution==="computed"?{navigationId:n,\u0275routerPageId:o}:{navigationId:n}}static \u0275fac=(()=>{let n;return function(i){return(n||(n=fo(e)))(i||e)}})();static \u0275prov=I({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})();function cp(e,t){e.events.pipe(Ae(n=>n instanceof Gt||n instanceof Ut||n instanceof bi||n instanceof fn),G(n=>n instanceof Gt||n instanceof fn?0:(n instanceof Ut?n.code===Ze.Redirect||n.code===Ze.SupersededByNewNavigation:!1)?2:1),Ae(n=>n!==2),Kt(1)).subscribe(()=>{t()})}var F_={paths:"exact",fragment:"ignored",matrixParams:"ignored",queryParams:"exact"},L_={paths:"subset",fragment:"ignored",matrixParams:"ignored",queryParams:"subset"},vt=(()=>{class e{get currentUrlTree(){return this.stateManager.getCurrentUrlTree()}get rawUrlTree(){return this.stateManager.getRawUrlTree()}disposed=!1;nonRouterCurrentEntryChangeSubscription;console=v(cf);stateManager=v(bb);options=v(Yr,{optional:!0})||{};pendingTasks=v(on);urlUpdateStrategy=this.options.urlUpdateStrategy||"deferred";navigationTransitions=v(vb);urlSerializer=v(yo);location=v(Tt);urlHandlingStrategy=v(kl);injector=v(ae);_events=new re;get events(){return this._events}get routerState(){return this.stateManager.getRouterState()}navigated=!1;routeReuseStrategy=v(yb);onSameUrlNavigation=this.options.onSameUrlNavigation||"ignore";config=v(Zr,{optional:!0})?.flat()??[];componentInputBindingEnabled=!!v(Tl,{optional:!0});constructor(){this.resetConfig(this.config),this.navigationTransitions.setupNavigations(this).subscribe({error:n=>{this.console.warn(n)}}),this.subscribeToNavigationEvents()}eventsSubscription=new De;subscribeToNavigationEvents(){let n=this.navigationTransitions.events.subscribe(o=>{try{let i=this.navigationTransitions.currentTransition,r=this.navigationTransitions.currentNavigation;if(i!==null&&r!==null){if(this.stateManager.handleRouterEvent(o,r),o instanceof Ut&&o.code!==Ze.Redirect&&o.code!==Ze.SupersededByNewNavigation)this.navigated=!0;else if(o instanceof Gt)this.navigated=!0;else if(o instanceof wi){let s=o.navigationBehaviorOptions,a=this.urlHandlingStrategy.merge(o.url,i.currentRawUrl),l=b({browserUrl:i.extras.browserUrl,info:i.extras.info,skipLocationChange:i.extras.skipLocationChange,replaceUrl:i.extras.replaceUrl||this.urlUpdateStrategy==="eager"||N_(i.source)},s);this.scheduleNavigation(a,Pr,null,l,{resolve:i.resolve,reject:i.reject,promise:i.promise})}}FS(o)&&this._events.next(o)}catch(i){this.navigationTransitions.transitionAbortWithErrorSubject.next(i)}});this.eventsSubscription.add(n)}resetRootComponentType(n){this.routerState.root.component=n,this.navigationTransitions.rootComponentType=n}initialNavigation(){this.setUpLocationChangeListener(),this.navigationTransitions.hasRequestedNavigation||this.navigateToSyncWithBrowser(this.location.path(!0),Pr,this.stateManager.restoredState())}setUpLocationChangeListener(){this.nonRouterCurrentEntryChangeSubscription??=this.stateManager.registerNonRouterCurrentEntryChangeListener((n,o,i)=>{this.navigateToSyncWithBrowser(n,i,o)})}navigateToSyncWithBrowser(n,o,i){let r={replaceUrl:!0},s=i?.navigationId?i:null;if(i){let l=b({},i);delete l.navigationId,delete l.\u0275routerPageId,Object.keys(l).length!==0&&(r.state=l)}let a=this.parseUrl(n);this.scheduleNavigation(a,o,s,r).catch(l=>{this.disposed||this.injector.get(xt)(l)})}get url(){return this.serializeUrl(this.currentUrlTree)}getCurrentNavigation(){return this.navigationTransitions.currentNavigation}get lastSuccessfulNavigation(){return this.navigationTransitions.lastSuccessfulNavigation}resetConfig(n){this.config=n.map(sp),this.navigated=!1}ngOnDestroy(){this.dispose()}dispose(){this._events.unsubscribe(),this.navigationTransitions.complete(),this.nonRouterCurrentEntryChangeSubscription&&(this.nonRouterCurrentEntryChangeSubscription.unsubscribe(),this.nonRouterCurrentEntryChangeSubscription=void 0),this.disposed=!0,this.eventsSubscription.unsubscribe()}createUrlTree(n,o={}){let{relativeTo:i,queryParams:r,fragment:s,queryParamsHandling:a,preserveFragment:l}=o,c=l?this.currentUrlTree.fragment:s,u=null;switch(a??this.options.defaultQueryParamsHandling){case"merge":u=b(b({},this.currentUrlTree.queryParams),r);break;case"preserve":u=this.currentUrlTree.queryParams;break;default:u=r||null}u!==null&&(u=this.removeEmptyProps(u));let d;try{let p=i?i.snapshot:this.routerState.snapshot.root;d=Qy(p)}catch{(typeof n[0]!="string"||n[0][0]!=="/")&&(n=[]),d=this.currentUrlTree.root}return Ky(d,n,u,c??null)}navigateByUrl(n,o={skipLocationChange:!1}){let i=yi(n)?n:this.parseUrl(n),r=this.urlHandlingStrategy.merge(i,this.rawUrlTree);return this.scheduleNavigation(r,Pr,null,o)}navigate(n,o={skipLocationChange:!1}){return j_(n),this.navigateByUrl(this.createUrlTree(n,o),o)}serializeUrl(n){return this.urlSerializer.serialize(n)}parseUrl(n){try{return this.urlSerializer.parse(n)}catch{return this.urlSerializer.parse("/")}}isActive(n,o){let i;if(o===!0?i=b({},F_):o===!1?i=b({},L_):i=o,yi(n))return Ay(this.currentUrlTree,n,i);let r=this.parseUrl(n);return Ay(this.currentUrlTree,r,i)}removeEmptyProps(n){return Object.entries(n).reduce((o,[i,r])=>(r!=null&&(o[i]=r),o),{})}scheduleNavigation(n,o,i,r,s){if(this.disposed)return Promise.resolve(!1);let a,l,c;s?(a=s.resolve,l=s.reject,c=s.promise):c=new Promise((d,p)=>{a=d,l=p});let u=this.pendingTasks.add();return cp(this,()=>{queueMicrotask(()=>this.pendingTasks.remove(u))}),this.navigationTransitions.handleNavigationRequest({source:o,restoredState:i,currentUrlTree:this.currentUrlTree,currentRawUrl:this.currentUrlTree,rawUrl:n,extras:r,resolve:a,reject:l,promise:c,currentSnapshot:this.routerState.snapshot,currentRouterState:this.routerState}),c.catch(d=>Promise.reject(d))}static \u0275fac=function(o){return new(o||e)};static \u0275prov=I({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})();function j_(e){for(let t=0;t<e.length;t++)if(e[t]==null)throw new E(4008,!1)}var Qr=class{},B_=(()=>{class e{preload(n,o){return o().pipe(Rt(()=>k(null)))}static \u0275fac=function(o){return new(o||e)};static \u0275prov=I({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})();var wb=(()=>{class e{router;injector;preloadingStrategy;loader;subscription;constructor(n,o,i,r){this.router=n,this.injector=o,this.preloadingStrategy=i,this.loader=r}setUpPreloading(){this.subscription=this.router.events.pipe(Ae(n=>n instanceof Gt),Dn(()=>this.preload())).subscribe(()=>{})}preload(){return this.processRoutes(this.injector,this.router.config)}ngOnDestroy(){this.subscription&&this.subscription.unsubscribe()}processRoutes(n,o){let i=[];for(let r of o){r.providers&&!r._injector&&(r._injector=fi(r.providers,n,`Route: ${r.path}`));let s=r._injector??n,a=r._loadedInjector??s;(r.loadChildren&&!r._loadedRoutes&&r.canLoad===void 0||r.loadComponent&&!r._loadedComponent)&&i.push(this.preloadConfig(s,r)),(r.children||r._loadedRoutes)&&i.push(this.processRoutes(a,r.children??r._loadedRoutes))}return ue(i).pipe($o())}preloadConfig(n,o){return this.preloadingStrategy.preload(o,()=>{let i;o.loadChildren&&o.canLoad===void 0?i=this.loader.loadChildren(n,o):i=k(null);let r=i.pipe(fe(s=>s===null?k(void 0):(o._loadedRoutes=s.routes,o._loadedInjector=s.injector,this.processRoutes(s.injector??n,s.routes))));if(o.loadComponent&&!o._loadedComponent){let s=this.loader.loadComponent(n,o);return ue([r,s]).pipe($o())}else return r})}static \u0275fac=function(o){return new(o||e)(S(vt),S(ae),S(Qr),S(lp))};static \u0275prov=I({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})(),H_=new x("");function $_(e,...t){return Yo([{provide:Zr,multi:!0,useValue:e},[],{provide:Qe,useFactory:z_,deps:[vt]},{provide:Ga,multi:!0,useFactory:q_},t.map(n=>n.\u0275providers)])}function z_(e){return e.routerState.root}function U_(e,t){return{\u0275kind:e,\u0275providers:t}}function q_(){let e=v(Ee);return t=>{let n=e.get($t);if(t!==n.components[0])return;let o=e.get(vt),i=e.get(G_);e.get(W_)===1&&o.initialNavigation(),e.get(Cb,null,{optional:!0})?.setUpPreloading(),e.get(H_,null,{optional:!0})?.init(),o.resetRootComponentType(n.componentTypes[0]),i.closed||(i.next(),i.complete(),i.unsubscribe())}}var G_=new x("",{factory:()=>new re}),W_=new x("",{providedIn:"root",factory:()=>1});var Cb=new x("");function Y_(e){return U_(0,[{provide:Cb,useExisting:wb},{provide:Qr,useExisting:e}])}var Ab=(()=>{class e{_renderer;_elementRef;onChange=n=>{};onTouched=()=>{};constructor(n,o){this._renderer=n,this._elementRef=o}setProperty(n,o){this._renderer.setProperty(this._elementRef.nativeElement,n,o)}registerOnTouched(n){this.onTouched=n}registerOnChange(n){this.onChange=n}setDisabledState(n){this.setProperty("disabled",n)}static \u0275fac=function(o){return new(o||e)(D(di),D(le))};static \u0275dir=ze({type:e})}return e})(),Z_=(()=>{class e extends Ab{static \u0275fac=(()=>{let n;return function(i){return(n||(n=fo(e)))(i||e)}})();static \u0275dir=ze({type:e,features:[Ht]})}return e})(),Pl=new x("");var Q_={provide:Pl,useExisting:tn(()=>Rb),multi:!0};function K_(){let e=gt()?gt().getUserAgent():"";return/android (\d+)/.test(e.toLowerCase())}var X_=new x(""),Rb=(()=>{class e extends Ab{_compositionMode;_composing=!1;constructor(n,o,i){super(n,o),this._compositionMode=i,this._compositionMode==null&&(this._compositionMode=!K_())}writeValue(n){let o=n??"";this.setProperty("value",o)}_handleInput(n){(!this._compositionMode||this._compositionMode&&!this._composing)&&this.onChange(n)}_compositionStart(){this._composing=!0}_compositionEnd(n){this._composing=!1,this._compositionMode&&this.onChange(n)}static \u0275fac=function(o){return new(o||e)(D(di),D(le),D(X_,8))};static \u0275dir=ze({type:e,selectors:[["input","formControlName","",3,"type","checkbox"],["textarea","formControlName",""],["input","formControl","",3,"type","checkbox"],["textarea","formControl",""],["input","ngModel","",3,"type","checkbox"],["textarea","ngModel",""],["","ngDefaultControl",""]],hostBindings:function(o,i){o&1&&cn("input",function(s){return i._handleInput(s.target.value)})("blur",function(){return i.onTouched()})("compositionstart",function(){return i._compositionStart()})("compositionend",function(s){return i._compositionEnd(s.target.value)})},standalone:!1,features:[pi([Q_]),Ht]})}return e})();var J_=new x(""),eT=new x("");function Nb(e){return e!=null}function Ob(e){return po(e)?ue(e):e}function Pb(e){let t={};return e.forEach(n=>{t=n!=null?b(b({},t),n):t}),Object.keys(t).length===0?null:t}function Fb(e,t){return t.map(n=>n(e))}function tT(e){return!e.validate}function Lb(e){return e.map(t=>tT(t)?t:n=>t.validate(n))}function nT(e){if(!e)return null;let t=e.filter(Nb);return t.length==0?null:function(n){return Pb(Fb(n,t))}}function jb(e){return e!=null?nT(Lb(e)):null}function oT(e){if(!e)return null;let t=e.filter(Nb);return t.length==0?null:function(n){let o=Fb(n,t).map(Ob);return Lc(o).pipe(G(Pb))}}function Vb(e){return e!=null?oT(Lb(e)):null}function Eb(e,t){return e===null?[t]:Array.isArray(e)?[...e,t]:[e,t]}function iT(e){return e._rawValidators}function rT(e){return e._rawAsyncValidators}function up(e){return e?Array.isArray(e)?e:[e]:[]}function Rl(e,t){return Array.isArray(e)?e.includes(t):e===t}function xb(e,t){let n=up(t);return up(e).forEach(i=>{Rl(n,i)||n.push(i)}),n}function Sb(e,t){return up(t).filter(n=>!Rl(e,n))}var Nl=class{get value(){return this.control?this.control.value:null}get valid(){return this.control?this.control.valid:null}get invalid(){return this.control?this.control.invalid:null}get pending(){return this.control?this.control.pending:null}get disabled(){return this.control?this.control.disabled:null}get enabled(){return this.control?this.control.enabled:null}get errors(){return this.control?this.control.errors:null}get pristine(){return this.control?this.control.pristine:null}get dirty(){return this.control?this.control.dirty:null}get touched(){return this.control?this.control.touched:null}get status(){return this.control?this.control.status:null}get untouched(){return this.control?this.control.untouched:null}get statusChanges(){return this.control?this.control.statusChanges:null}get valueChanges(){return this.control?this.control.valueChanges:null}get path(){return null}_composedValidatorFn;_composedAsyncValidatorFn;_rawValidators=[];_rawAsyncValidators=[];_setValidators(t){this._rawValidators=t||[],this._composedValidatorFn=jb(this._rawValidators)}_setAsyncValidators(t){this._rawAsyncValidators=t||[],this._composedAsyncValidatorFn=Vb(this._rawAsyncValidators)}get validator(){return this._composedValidatorFn||null}get asyncValidator(){return this._composedAsyncValidatorFn||null}_onDestroyCallbacks=[];_registerOnDestroy(t){this._onDestroyCallbacks.push(t)}_invokeOnDestroyCallbacks(){this._onDestroyCallbacks.forEach(t=>t()),this._onDestroyCallbacks=[]}reset(t=void 0){this.control&&this.control.reset(t)}hasError(t,n){return this.control?this.control.hasError(t,n):!1}getError(t,n){return this.control?this.control.getError(t,n):null}},dp=class extends Nl{name;get formDirective(){return null}get path(){return null}},bo=class extends Nl{_parent=null;name=null;valueAccessor=null},fp=class{_cd;constructor(t){this._cd=t}get isTouched(){return this._cd?.control?._touched?.(),!!this._cd?.control?.touched}get isUntouched(){return!!this._cd?.control?.untouched}get isPristine(){return this._cd?.control?._pristine?.(),!!this._cd?.control?.pristine}get isDirty(){return!!this._cd?.control?.dirty}get isValid(){return this._cd?.control?._status?.(),!!this._cd?.control?.valid}get isInvalid(){return!!this._cd?.control?.invalid}get isPending(){return!!this._cd?.control?.pending}get isSubmitted(){return this._cd?._submitted?.(),!!this._cd?.submitted}},sT={"[class.ng-untouched]":"isUntouched","[class.ng-touched]":"isTouched","[class.ng-pristine]":"isPristine","[class.ng-dirty]":"isDirty","[class.ng-valid]":"isValid","[class.ng-invalid]":"isInvalid","[class.ng-pending]":"isPending"},OU=L(b({},sT),{"[class.ng-submitted]":"isSubmitted"}),PU=(()=>{class e extends fp{constructor(n){super(n)}static \u0275fac=function(o){return new(o||e)(D(bo,2))};static \u0275dir=ze({type:e,selectors:[["","formControlName",""],["","ngModel",""],["","formControl",""]],hostVars:14,hostBindings:function(o,i){o&2&&Xa("ng-untouched",i.isUntouched)("ng-touched",i.isTouched)("ng-pristine",i.isPristine)("ng-dirty",i.isDirty)("ng-valid",i.isValid)("ng-invalid",i.isInvalid)("ng-pending",i.isPending)},standalone:!1,features:[Ht]})}return e})();var Kr="VALID",Al="INVALID",xi="PENDING",Xr="DISABLED",_i=class{},Ol=class extends _i{value;source;constructor(t,n){super(),this.value=t,this.source=n}},Jr=class extends _i{pristine;source;constructor(t,n){super(),this.pristine=t,this.source=n}},es=class extends _i{touched;source;constructor(t,n){super(),this.touched=t,this.source=n}},Si=class extends _i{status;source;constructor(t,n){super(),this.status=t,this.source=n}};function aT(e){return(Fl(e)?e.validators:e)||null}function lT(e){return Array.isArray(e)?jb(e):e||null}function cT(e,t){return(Fl(t)?t.asyncValidators:e)||null}function uT(e){return Array.isArray(e)?Vb(e):e||null}function Fl(e){return e!=null&&!Array.isArray(e)&&typeof e=="object"}var pp=class{_pendingDirty=!1;_hasOwnPendingAsyncValidator=null;_pendingTouched=!1;_onCollectionChange=()=>{};_updateOn;_parent=null;_asyncValidationSubscription;_composedValidatorFn;_composedAsyncValidatorFn;_rawValidators;_rawAsyncValidators;value;constructor(t,n){this._assignValidators(t),this._assignAsyncValidators(n)}get validator(){return this._composedValidatorFn}set validator(t){this._rawValidators=this._composedValidatorFn=t}get asyncValidator(){return this._composedAsyncValidatorFn}set asyncValidator(t){this._rawAsyncValidators=this._composedAsyncValidatorFn=t}get parent(){return this._parent}get status(){return un(this.statusReactive)}set status(t){un(()=>this.statusReactive.set(t))}_status=Ir(()=>this.statusReactive());statusReactive=so(void 0);get valid(){return this.status===Kr}get invalid(){return this.status===Al}get pending(){return this.status==xi}get disabled(){return this.status===Xr}get enabled(){return this.status!==Xr}errors;get pristine(){return un(this.pristineReactive)}set pristine(t){un(()=>this.pristineReactive.set(t))}_pristine=Ir(()=>this.pristineReactive());pristineReactive=so(!0);get dirty(){return!this.pristine}get touched(){return un(this.touchedReactive)}set touched(t){un(()=>this.touchedReactive.set(t))}_touched=Ir(()=>this.touchedReactive());touchedReactive=so(!1);get untouched(){return!this.touched}_events=new re;events=this._events.asObservable();valueChanges;statusChanges;get updateOn(){return this._updateOn?this._updateOn:this.parent?this.parent.updateOn:"change"}setValidators(t){this._assignValidators(t)}setAsyncValidators(t){this._assignAsyncValidators(t)}addValidators(t){this.setValidators(xb(t,this._rawValidators))}addAsyncValidators(t){this.setAsyncValidators(xb(t,this._rawAsyncValidators))}removeValidators(t){this.setValidators(Sb(t,this._rawValidators))}removeAsyncValidators(t){this.setAsyncValidators(Sb(t,this._rawAsyncValidators))}hasValidator(t){return Rl(this._rawValidators,t)}hasAsyncValidator(t){return Rl(this._rawAsyncValidators,t)}clearValidators(){this.validator=null}clearAsyncValidators(){this.asyncValidator=null}markAsTouched(t={}){let n=this.touched===!1;this.touched=!0;let o=t.sourceControl??this;this._parent&&!t.onlySelf&&this._parent.markAsTouched(L(b({},t),{sourceControl:o})),n&&t.emitEvent!==!1&&this._events.next(new es(!0,o))}markAllAsDirty(t={}){this.markAsDirty({onlySelf:!0,emitEvent:t.emitEvent,sourceControl:this}),this._forEachChild(n=>n.markAllAsDirty(t))}markAllAsTouched(t={}){this.markAsTouched({onlySelf:!0,emitEvent:t.emitEvent,sourceControl:this}),this._forEachChild(n=>n.markAllAsTouched(t))}markAsUntouched(t={}){let n=this.touched===!0;this.touched=!1,this._pendingTouched=!1;let o=t.sourceControl??this;this._forEachChild(i=>{i.markAsUntouched({onlySelf:!0,emitEvent:t.emitEvent,sourceControl:o})}),this._parent&&!t.onlySelf&&this._parent._updateTouched(t,o),n&&t.emitEvent!==!1&&this._events.next(new es(!1,o))}markAsDirty(t={}){let n=this.pristine===!0;this.pristine=!1;let o=t.sourceControl??this;this._parent&&!t.onlySelf&&this._parent.markAsDirty(L(b({},t),{sourceControl:o})),n&&t.emitEvent!==!1&&this._events.next(new Jr(!1,o))}markAsPristine(t={}){let n=this.pristine===!1;this.pristine=!0,this._pendingDirty=!1;let o=t.sourceControl??this;this._forEachChild(i=>{i.markAsPristine({onlySelf:!0,emitEvent:t.emitEvent})}),this._parent&&!t.onlySelf&&this._parent._updatePristine(t,o),n&&t.emitEvent!==!1&&this._events.next(new Jr(!0,o))}markAsPending(t={}){this.status=xi;let n=t.sourceControl??this;t.emitEvent!==!1&&(this._events.next(new Si(this.status,n)),this.statusChanges.emit(this.status)),this._parent&&!t.onlySelf&&this._parent.markAsPending(L(b({},t),{sourceControl:n}))}disable(t={}){let n=this._parentMarkedDirty(t.onlySelf);this.status=Xr,this.errors=null,this._forEachChild(i=>{i.disable(L(b({},t),{onlySelf:!0}))}),this._updateValue();let o=t.sourceControl??this;t.emitEvent!==!1&&(this._events.next(new Ol(this.value,o)),this._events.next(new Si(this.status,o)),this.valueChanges.emit(this.value),this.statusChanges.emit(this.status)),this._updateAncestors(L(b({},t),{skipPristineCheck:n}),this),this._onDisabledChange.forEach(i=>i(!0))}enable(t={}){let n=this._parentMarkedDirty(t.onlySelf);this.status=Kr,this._forEachChild(o=>{o.enable(L(b({},t),{onlySelf:!0}))}),this.updateValueAndValidity({onlySelf:!0,emitEvent:t.emitEvent}),this._updateAncestors(L(b({},t),{skipPristineCheck:n}),this),this._onDisabledChange.forEach(o=>o(!1))}_updateAncestors(t,n){this._parent&&!t.onlySelf&&(this._parent.updateValueAndValidity(t),t.skipPristineCheck||this._parent._updatePristine({},n),this._parent._updateTouched({},n))}setParent(t){this._parent=t}getRawValue(){return this.value}updateValueAndValidity(t={}){if(this._setInitialStatus(),this._updateValue(),this.enabled){let o=this._cancelExistingSubscription();this.errors=this._runValidator(),this.status=this._calculateStatus(),(this.status===Kr||this.status===xi)&&this._runAsyncValidator(o,t.emitEvent)}let n=t.sourceControl??this;t.emitEvent!==!1&&(this._events.next(new Ol(this.value,n)),this._events.next(new Si(this.status,n)),this.valueChanges.emit(this.value),this.statusChanges.emit(this.status)),this._parent&&!t.onlySelf&&this._parent.updateValueAndValidity(L(b({},t),{sourceControl:n}))}_updateTreeValidity(t={emitEvent:!0}){this._forEachChild(n=>n._updateTreeValidity(t)),this.updateValueAndValidity({onlySelf:!0,emitEvent:t.emitEvent})}_setInitialStatus(){this.status=this._allControlsDisabled()?Xr:Kr}_runValidator(){return this.validator?this.validator(this):null}_runAsyncValidator(t,n){if(this.asyncValidator){this.status=xi,this._hasOwnPendingAsyncValidator={emitEvent:n!==!1,shouldHaveEmitted:t!==!1};let o=Ob(this.asyncValidator(this));this._asyncValidationSubscription=o.subscribe(i=>{this._hasOwnPendingAsyncValidator=null,this.setErrors(i,{emitEvent:n,shouldHaveEmitted:t})})}}_cancelExistingSubscription(){if(this._asyncValidationSubscription){this._asyncValidationSubscription.unsubscribe();let t=(this._hasOwnPendingAsyncValidator?.emitEvent||this._hasOwnPendingAsyncValidator?.shouldHaveEmitted)??!1;return this._hasOwnPendingAsyncValidator=null,t}return!1}setErrors(t,n={}){this.errors=t,this._updateControlsErrors(n.emitEvent!==!1,this,n.shouldHaveEmitted)}get(t){let n=t;return n==null||(Array.isArray(n)||(n=n.split(".")),n.length===0)?null:n.reduce((o,i)=>o&&o._find(i),this)}getError(t,n){let o=n?this.get(n):this;return o&&o.errors?o.errors[t]:null}hasError(t,n){return!!this.getError(t,n)}get root(){let t=this;for(;t._parent;)t=t._parent;return t}_updateControlsErrors(t,n,o){this.status=this._calculateStatus(),t&&this.statusChanges.emit(this.status),(t||o)&&this._events.next(new Si(this.status,n)),this._parent&&this._parent._updateControlsErrors(t,n,o)}_initObservables(){this.valueChanges=new de,this.statusChanges=new de}_calculateStatus(){return this._allControlsDisabled()?Xr:this.errors?Al:this._hasOwnPendingAsyncValidator||this._anyControlsHaveStatus(xi)?xi:this._anyControlsHaveStatus(Al)?Al:Kr}_anyControlsHaveStatus(t){return this._anyControls(n=>n.status===t)}_anyControlsDirty(){return this._anyControls(t=>t.dirty)}_anyControlsTouched(){return this._anyControls(t=>t.touched)}_updatePristine(t,n){let o=!this._anyControlsDirty(),i=this.pristine!==o;this.pristine=o,this._parent&&!t.onlySelf&&this._parent._updatePristine(t,n),i&&this._events.next(new Jr(this.pristine,n))}_updateTouched(t={},n){this.touched=this._anyControlsTouched(),this._events.next(new es(this.touched,n)),this._parent&&!t.onlySelf&&this._parent._updateTouched(t,n)}_onDisabledChange=[];_registerOnCollectionChange(t){this._onCollectionChange=t}_setUpdateStrategy(t){Fl(t)&&t.updateOn!=null&&(this._updateOn=t.updateOn)}_parentMarkedDirty(t){let n=this._parent&&this._parent.dirty;return!t&&!!n&&!this._parent._anyControlsDirty()}_find(t){return null}_assignValidators(t){this._rawValidators=Array.isArray(t)?t.slice():t,this._composedValidatorFn=lT(this._rawValidators)}_assignAsyncValidators(t){this._rawAsyncValidators=Array.isArray(t)?t.slice():t,this._composedAsyncValidatorFn=uT(this._rawAsyncValidators)}};var Bb=new x("",{providedIn:"root",factory:()=>hp}),hp="always";function dT(e,t){return[...t.path,e]}function fT(e,t,n=hp){hT(e,t),t.valueAccessor.writeValue(e.value),(e.disabled||n==="always")&&t.valueAccessor.setDisabledState?.(e.disabled),mT(e,t),vT(e,t),gT(e,t),pT(e,t)}function _b(e,t){e.forEach(n=>{n.registerOnValidatorChange&&n.registerOnValidatorChange(t)})}function pT(e,t){if(t.valueAccessor.setDisabledState){let n=o=>{t.valueAccessor.setDisabledState(o)};e.registerOnDisabledChange(n),t._registerOnDestroy(()=>{e._unregisterOnDisabledChange(n)})}}function hT(e,t){let n=iT(e);t.validator!==null?e.setValidators(Eb(n,t.validator)):typeof n=="function"&&e.setValidators([n]);let o=rT(e);t.asyncValidator!==null?e.setAsyncValidators(Eb(o,t.asyncValidator)):typeof o=="function"&&e.setAsyncValidators([o]);let i=()=>e.updateValueAndValidity();_b(t._rawValidators,i),_b(t._rawAsyncValidators,i)}function mT(e,t){t.valueAccessor.registerOnChange(n=>{e._pendingValue=n,e._pendingChange=!0,e._pendingDirty=!0,e.updateOn==="change"&&Hb(e,t)})}function gT(e,t){t.valueAccessor.registerOnTouched(()=>{e._pendingTouched=!0,e.updateOn==="blur"&&e._pendingChange&&Hb(e,t),e.updateOn!=="submit"&&e.markAsTouched()})}function Hb(e,t){e._pendingDirty&&e.markAsDirty(),e.setValue(e._pendingValue,{emitModelToViewChange:!1}),t.viewToModelUpdate(e._pendingValue),e._pendingChange=!1}function vT(e,t){let n=(o,i)=>{t.valueAccessor.writeValue(o),i&&t.viewToModelUpdate(o)};e.registerOnChange(n),t._registerOnDestroy(()=>{e._unregisterOnChange(n)})}function yT(e,t){if(!e.hasOwnProperty("model"))return!1;let n=e.model;return n.isFirstChange()?!0:!Object.is(t,n.currentValue)}function bT(e){return Object.getPrototypeOf(e.constructor)===Z_}function wT(e,t){if(!t)return null;Array.isArray(t);let n,o,i;return t.forEach(r=>{r.constructor===Rb?n=r:bT(r)?o=r:i=r}),i||o||n||null}function Tb(e,t){let n=e.indexOf(t);n>-1&&e.splice(n,1)}function Mb(e){return typeof e=="object"&&e!==null&&Object.keys(e).length===2&&"value"in e&&"disabled"in e}var CT=class extends pp{defaultValue=null;_onChange=[];_pendingValue;_pendingChange=!1;constructor(t=null,n,o){super(aT(n),cT(o,n)),this._applyFormState(t),this._setUpdateStrategy(n),this._initObservables(),this.updateValueAndValidity({onlySelf:!0,emitEvent:!!this.asyncValidator}),Fl(n)&&(n.nonNullable||n.initialValueIsDefault)&&(Mb(t)?this.defaultValue=t.value:this.defaultValue=t)}setValue(t,n={}){this.value=this._pendingValue=t,this._onChange.length&&n.emitModelToViewChange!==!1&&this._onChange.forEach(o=>o(this.value,n.emitViewToModelChange!==!1)),this.updateValueAndValidity(n)}patchValue(t,n={}){this.setValue(t,n)}reset(t=this.defaultValue,n={}){this._applyFormState(t),this.markAsPristine(n),this.markAsUntouched(n),this.setValue(this.value,n),this._pendingChange=!1}_updateValue(){}_anyControls(t){return!1}_allControlsDisabled(){return this.disabled}registerOnChange(t){this._onChange.push(t)}_unregisterOnChange(t){Tb(this._onChange,t)}registerOnDisabledChange(t){this._onDisabledChange.push(t)}_unregisterOnDisabledChange(t){Tb(this._onDisabledChange,t)}_forEachChild(t){}_syncPendingControls(){return this.updateOn==="submit"&&(this._pendingDirty&&this.markAsDirty(),this._pendingTouched&&this.markAsTouched(),this._pendingChange)?(this.setValue(this._pendingValue,{onlySelf:!0,emitModelToViewChange:!1}),!0):!1}_applyFormState(t){Mb(t)?(this.value=this._pendingValue=t.value,t.disabled?this.disable({onlySelf:!0,emitEvent:!1}):this.enable({onlySelf:!0,emitEvent:!1})):this.value=this._pendingValue=t}};var DT={provide:bo,useExisting:tn(()=>IT)},kb=Promise.resolve(),IT=(()=>{class e extends bo{_changeDetectorRef;callSetDisabledState;control=new CT;static ngAcceptInputType_isDisabled;_registered=!1;viewModel;name="";isDisabled;model;options;update=new de;constructor(n,o,i,r,s,a){super(),this._changeDetectorRef=s,this.callSetDisabledState=a,this._parent=n,this._setValidators(o),this._setAsyncValidators(i),this.valueAccessor=wT(this,r)}ngOnChanges(n){if(this._checkForErrors(),!this._registered||"name"in n){if(this._registered&&(this._checkName(),this.formDirective)){let o=n.name.previousValue;this.formDirective.removeControl({name:o,path:this._getPath(o)})}this._setUpControl()}"isDisabled"in n&&this._updateDisabled(n),yT(n,this.viewModel)&&(this._updateValue(this.model),this.viewModel=this.model)}ngOnDestroy(){this.formDirective&&this.formDirective.removeControl(this)}get path(){return this._getPath(this.name)}get formDirective(){return this._parent?this._parent.formDirective:null}viewToModelUpdate(n){this.viewModel=n,this.update.emit(n)}_setUpControl(){this._setUpdateStrategy(),this._isStandalone()?this._setUpStandalone():this.formDirective.addControl(this),this._registered=!0}_setUpdateStrategy(){this.options&&this.options.updateOn!=null&&(this.control._updateOn=this.options.updateOn)}_isStandalone(){return!this._parent||!!(this.options&&this.options.standalone)}_setUpStandalone(){fT(this.control,this,this.callSetDisabledState),this.control.updateValueAndValidity({emitEvent:!1})}_checkForErrors(){this._checkName()}_checkName(){this.options&&this.options.name&&(this.name=this.options.name),!this._isStandalone()&&this.name}_updateValue(n){kb.then(()=>{this.control.setValue(n,{emitViewToModelChange:!1}),this._changeDetectorRef?.markForCheck()})}_updateDisabled(n){let o=n.isDisabled.currentValue,i=o!==0&&xf(o);kb.then(()=>{i&&!this.control.disabled?this.control.disable():!i&&this.control.disabled&&this.control.enable(),this._changeDetectorRef?.markForCheck()})}_getPath(n){return this._parent?dT(n,this._parent):[n]}static \u0275fac=function(o){return new(o||e)(D(dp,9),D(J_,10),D(eT,10),D(Pl,10),D(Te,8),D(Bb,8))};static \u0275dir=ze({type:e,selectors:[["","ngModel","",3,"formControlName","",3,"formControl",""]],inputs:{name:"name",isDisabled:[0,"disabled","isDisabled"],model:[0,"ngModel","model"],options:[0,"ngModelOptions","options"]},outputs:{update:"ngModelChange"},exportAs:["ngModel"],standalone:!1,features:[pi([DT]),Ht,Pn]})}return e})();var ET=(()=>{class e{static \u0275fac=function(o){return new(o||e)};static \u0275mod=ln({type:e});static \u0275inj=Ot({})}return e})();var LU=(()=>{class e{static withConfig(n){return{ngModule:e,providers:[{provide:Bb,useValue:n.callSetDisabledState??hp}]}}static \u0275fac=function(o){return new(o||e)};static \u0275mod=ln({type:e});static \u0275inj=Ot({imports:[ET]})}return e})();var wo=(e,t,n,o,i)=>ST(e[1],t[1],n[1],o[1],i).map(r=>xT(e[0],t[0],n[0],o[0],r)),xT=(e,t,n,o,i)=>{let r=3*t*Math.pow(i-1,2),s=-3*n*i+3*n+o*i,a=e*Math.pow(i-1,3);return i*(r+i*s)-a},ST=(e,t,n,o,i)=>(e-=i,t-=i,n-=i,o-=i,TT(o-3*n+3*t-e,3*n-6*t+3*e,3*t-3*e,e).filter(s=>s>=0&&s<=1)),_T=(e,t,n)=>{let o=t*t-4*e*n;return o<0?[]:[(-t+Math.sqrt(o))/(2*e),(-t-Math.sqrt(o))/(2*e)]},TT=(e,t,n,o)=>{if(e===0)return _T(t,n,o);t/=e,n/=e,o/=e;let i=(3*n-t*t)/3,r=(2*t*t*t-9*t*n+27*o)/27;if(i===0)return[Math.pow(-r,.3333333333333333)];if(r===0)return[Math.sqrt(-i),-Math.sqrt(-i)];let s=Math.pow(r/2,2)+Math.pow(i/3,3);if(s===0)return[Math.pow(r/2,.5)-t/3];if(s>0)return[Math.pow(-(r/2)+Math.sqrt(s),.3333333333333333)-Math.pow(r/2+Math.sqrt(s),.3333333333333333)-t/3];let a=Math.sqrt(Math.pow(-(i/3),3)),l=Math.acos(-(r/(2*Math.sqrt(Math.pow(-(i/3),3))))),c=2*Math.pow(a,1/3);return[c*Math.cos(l/3)-t/3,c*Math.cos((l+2*Math.PI)/3)-t/3,c*Math.cos((l+4*Math.PI)/3)-t/3]};var Ll=e=>zb(e),Pe=(e,t)=>(typeof e=="string"&&(t=e,e=void 0),Ll(e).includes(t)),zb=(e=window)=>{if(typeof e>"u")return[];e.Ionic=e.Ionic||{};let t=e.Ionic.platforms;return t==null&&(t=e.Ionic.platforms=MT(e),t.forEach(n=>e.document.documentElement.classList.add(`plt-${n}`))),t},MT=e=>{let t=ie.get("platform");return Object.keys($b).filter(n=>{let o=t?.[n];return typeof o=="function"?o(e):$b[n](e)})},kT=e=>jl(e)&&!qb(e),mp=e=>!!(Co(e,/iPad/i)||Co(e,/Macintosh/i)&&jl(e)),AT=e=>Co(e,/iPhone/i),RT=e=>Co(e,/iPhone|iPod/i)||mp(e),Ub=e=>Co(e,/android|sink/i),NT=e=>Ub(e)&&!Co(e,/mobile/i),OT=e=>{let t=e.innerWidth,n=e.innerHeight,o=Math.min(t,n),i=Math.max(t,n);return o>390&&o<520&&i>620&&i<800},PT=e=>{let t=e.innerWidth,n=e.innerHeight,o=Math.min(t,n),i=Math.max(t,n);return mp(e)||NT(e)||o>460&&o<820&&i>780&&i<1400},jl=e=>VT(e,"(any-pointer:coarse)"),FT=e=>!jl(e),qb=e=>Gb(e)||Wb(e),Gb=e=>!!(e.cordova||e.phonegap||e.PhoneGap),Wb=e=>{let t=e.Capacitor;return!!(t?.isNative||t?.isNativePlatform&&t.isNativePlatform())},LT=e=>Co(e,/electron/i),jT=e=>{var t;return!!(!((t=e.matchMedia)===null||t===void 0)&&t.call(e,"(display-mode: standalone)").matches||e.navigator.standalone)},Co=(e,t)=>t.test(e.navigator.userAgent),VT=(e,t)=>{var n;return(n=e.matchMedia)===null||n===void 0?void 0:n.call(e,t).matches},$b={ipad:mp,iphone:AT,ios:RT,android:Ub,phablet:OT,tablet:PT,cordova:Gb,capacitor:Wb,electron:LT,pwa:jT,mobile:jl,mobileweb:kT,desktop:FT,hybrid:qb},Ti,F=e=>e&&Kp(e)||Ti,gp=(e={})=>{if(typeof window>"u")return;let t=window.document,n=window,o=n.Ionic=n.Ionic||{},i=Object.assign(Object.assign(Object.assign(Object.assign(Object.assign({},Xp(n)),{persistConfig:!1}),o.config),eh(n)),e);ie.reset(i),ie.getBoolean("persistConfig")&&Jp(n,i),zb(n),o.config=ie,o.mode=Ti=ie.get("mode",t.documentElement.getAttribute("mode")||(Pe(n,"ios")?"ios":"md")),ie.set("mode",Ti),t.documentElement.setAttribute("mode",Ti),t.documentElement.classList.add(Ti),ie.getBoolean("_testing")&&ie.set("animated",!1);let r=a=>{var l;return(l=a.tagName)===null||l===void 0?void 0:l.startsWith("ION-")},s=a=>["ios","md"].includes(a);Qp(a=>{for(;a;){let l=a.mode||a.getAttribute("mode");if(l){if(s(l))return l;r(a)&&Le('Invalid ionic mode: "'+l+'", expected: "ios" or "md"')}a=a.parentElement}return Ti})};var ke=(e,t)=>t.closest(e)!==null,et=(e,t)=>typeof e=="string"&&e.length>0?Object.assign({"ion-color":!0,[`ion-color-${e}`]:!0},t):t,BT=e=>e!==void 0?(Array.isArray(e)?e:e.split(" ")).filter(n=>n!=null).map(n=>n.trim()).filter(n=>n!==""):[],Vl=e=>{let t={};return BT(e).forEach(n=>t[n]=!0),t},HT=/^[a-z][a-z0-9+\-.]*:/,Bl=(e,t,n,o)=>C(null,null,function*(){if(e!=null&&e[0]!=="#"&&!HT.test(e)){let i=document.querySelector("ion-router");if(i)return t?.preventDefault(),i.push(e,n,o)}return!1});var Mi=(e,t,n,o,i,r)=>C(null,null,function*(){var s;if(e)return e.attachViewToDom(t,n,i,o);if(!r&&typeof n!="string"&&!(n instanceof HTMLElement))throw new Error("framework delegate is missing");let a=typeof n=="string"?(s=t.ownerDocument)===null||s===void 0?void 0:s.createElement(n):n;return o&&o.forEach(l=>a.classList.add(l)),i&&Object.assign(a,i),t.appendChild(a),yield new Promise(l=>tt(a,l)),a}),ki=(e,t)=>{if(t){if(e){let n=t.parentElement;return e.removeViewFromDom(n,t)}t.remove()}return Promise.resolve()},Hl=()=>{let e,t;return{attachViewToDom:(l,c,...u)=>C(null,[l,c,...u],function*(i,r,s={},a=[]){var d,p;e=i;let f;if(r){let y=typeof r=="string"?(d=e.ownerDocument)===null||d===void 0?void 0:d.createElement(r):r;a.forEach(g=>y.classList.add(g)),Object.assign(y,s),e.appendChild(y),f=y,yield new Promise(g=>tt(y,g))}else if(e.children.length>0&&(e.tagName==="ION-MODAL"||e.tagName==="ION-POPOVER")&&!(f=e.children[0]).classList.contains("ion-delegate-host")){let g=(p=e.ownerDocument)===null||p===void 0?void 0:p.createElement("div");g.classList.add("ion-delegate-host"),a.forEach(w=>g.classList.add(w)),g.append(...e.children),e.appendChild(g),f=g}let h=document.querySelector("ion-app")||document.body;return t=document.createComment("ionic teleport"),e.parentNode.insertBefore(t,e),h.appendChild(e),f??e}),removeViewFromDom:()=>(e&&t&&(t.parentNode.insertBefore(e,t),t.remove()),Promise.resolve())}};var ns='[tabindex]:not([tabindex^="-"]):not([hidden]):not([disabled]), input:not([type=hidden]):not([tabindex^="-"]):not([hidden]):not([disabled]), textarea:not([tabindex^="-"]):not([hidden]):not([disabled]), button:not([tabindex^="-"]):not([hidden]):not([disabled]), select:not([tabindex^="-"]):not([hidden]):not([disabled]), ion-checkbox:not([tabindex^="-"]):not([hidden]):not([disabled]), ion-radio:not([tabindex^="-"]):not([hidden]):not([disabled]), .ion-focusable:not([tabindex^="-"]):not([hidden]):not([disabled]), .ion-focusable[disabled="false"]:not([tabindex^="-"]):not([hidden])',$l=(e,t)=>{let n=e.querySelector(ns);Qb(n,t??e)},Yb=(e,t)=>{let n=Array.from(e.querySelectorAll(ns)),o=n.length>0?n[n.length-1]:null;Qb(o,t??e)},Qb=(e,t)=>{let n=e,o=e?.shadowRoot;if(o&&(n=o.querySelector(ns)||e),n){let i=n.closest("ion-radio-group");i?i.setFocus():uc(n)}else t.focus()},vp=0,$T=0,zl=new WeakMap,Kb=e=>({create(n){return zT(e,n)},dismiss(n,o,i){return WT(document,n,o,e,i)},getTop(){return C(this,null,function*(){return ts(document,e)})}});var yp=Kb("ion-modal");var bp=Kb("ion-popover");var Ul=e=>{typeof document<"u"&&GT(document);let t=vp++;e.overlayIndex=t},ql=e=>(e.hasAttribute("id")||(e.id=`ion-overlay-${++$T}`),e.id),zT=(e,t)=>typeof window<"u"&&typeof window.customElements<"u"?window.customElements.whenDefined(e).then(()=>{let n=document.createElement(e);return n.classList.add("overlay-hidden"),Object.assign(n,Object.assign(Object.assign({},t),{hasController:!0})),Jb(document).appendChild(n),new Promise(o=>tt(n,o))}):Promise.resolve(),UT=e=>e.classList.contains("overlay-hidden"),Zb=(e,t)=>{let n=e,o=e?.shadowRoot;o&&(n=o.querySelector(ns)||e),n?uc(n):t.focus()},qT=(e,t)=>{let n=ts(t,"ion-alert,ion-action-sheet,ion-loading,ion-modal,ion-picker-legacy,ion-popover"),o=e.target;if(!n||!o||n.classList.contains(Do))return;let i=()=>{if(n===o)n.lastFocus=void 0;else if(o.tagName==="ION-TOAST")Zb(n.lastFocus,n);else{let s=ce(n);if(!s.contains(o))return;let a=s.querySelector(".ion-overlay-wrapper");if(!a)return;if(a.contains(o)||o===s.querySelector("ion-backdrop"))n.lastFocus=o;else{let l=n.lastFocus;$l(a,n),l===t.activeElement&&Yb(a,n),n.lastFocus=t.activeElement}}},r=()=>{if(n.contains(o))n.lastFocus=o;else if(o.tagName==="ION-TOAST")Zb(n.lastFocus,n);else{let s=n.lastFocus;$l(n),s===t.activeElement&&Yb(n),n.lastFocus=t.activeElement}};n.shadowRoot?r():i()},GT=e=>{vp===0&&(vp=1,e.addEventListener("focus",t=>{qT(t,e)},!0),e.addEventListener("ionBackButton",t=>{let n=ts(e);n?.backdropDismiss&&t.detail.register(sh,()=>{n.dismiss(void 0,Ai)})}),Bi()||e.addEventListener("keydown",t=>{if(t.key==="Escape"){let n=ts(e);n?.backdropDismiss&&n.dismiss(void 0,Ai)}}))},WT=(e,t,n,o,i)=>{let r=ts(e,o,i);return r?r.dismiss(t,n):Promise.reject("overlay does not exist")},YT=(e,t)=>(t===void 0&&(t="ion-alert,ion-action-sheet,ion-loading,ion-modal,ion-picker-legacy,ion-popover,ion-toast"),Array.from(e.querySelectorAll(t)).filter(n=>n.overlayIndex>0)),Gl=(e,t)=>YT(e,t).filter(n=>!UT(n)),ts=(e,t,n)=>{let o=Gl(e,t);return n===void 0?o[o.length-1]:o.find(i=>i.id===n)},Xb=(e=!1)=>{let n=Jb(document).querySelector("ion-router-outlet, ion-nav, #ion-view-container-root");n&&(e?n.setAttribute("aria-hidden","true"):n.removeAttribute("aria-hidden"))},Wl=(e,t,n,o,i)=>C(null,null,function*(){var r,s;if(e.presented)return;e.el.tagName!=="ION-TOAST"&&(Xb(!0),document.body.classList.add(gc)),KT(e.el),o0(e.el),e.presented=!0,e.willPresent.emit(),(r=e.willPresentShorthand)===null||r===void 0||r.emit();let a=F(e),l=e.enterAnimation?e.enterAnimation:ie.get(t,a==="ios"?n:o);(yield e0(e,l,e.el,i))&&(e.didPresent.emit(),(s=e.didPresentShorthand)===null||s===void 0||s.emit()),e.el.tagName!=="ION-TOAST"&&ZT(e.el),e.keyboardClose&&(document.activeElement===null||!e.el.contains(document.activeElement))&&e.el.focus(),e.el.removeAttribute("aria-hidden")}),ZT=e=>C(null,null,function*(){let t=document.activeElement;if(!t)return;let n=t?.shadowRoot;n&&(t=n.querySelector(ns)||t),yield e.onDidDismiss(),(document.activeElement===null||document.activeElement===document.body)&&t.focus()}),Yl=(e,t,n,o,i,r,s)=>C(null,null,function*(){var a,l;if(!e.presented)return!1;let u=(yn!==void 0?Gl(yn):[]).filter(p=>p.tagName!=="ION-TOAST");u.length===1&&u[0].id===e.el.id&&(Xb(!1),document.body.classList.remove(gc)),e.presented=!1;try{o0(e.el),e.el.style.setProperty("pointer-events","none"),e.willDismiss.emit({data:t,role:n}),(a=e.willDismissShorthand)===null||a===void 0||a.emit({data:t,role:n});let p=F(e),f=e.leaveAnimation?e.leaveAnimation:ie.get(o,p==="ios"?i:r);n!==Ni&&(yield e0(e,f,e.el,s)),e.didDismiss.emit({data:t,role:n}),(l=e.didDismissShorthand)===null||l===void 0||l.emit({data:t,role:n}),(zl.get(e)||[]).forEach(y=>y.destroy()),zl.delete(e),e.el.classList.add("overlay-hidden"),e.el.style.removeProperty("pointer-events"),e.el.lastFocus!==void 0&&(e.el.lastFocus=void 0)}catch(p){Eo(`[${e.el.tagName.toLowerCase()}] - `,p)}return e.el.remove(),XT(),!0}),Jb=e=>e.querySelector("ion-app")||e.body,e0=(e,t,n,o)=>C(null,null,function*(){n.classList.remove("overlay-hidden");let i=e.el,r=t(i,o);(!e.animated||!ie.getBoolean("animated",!0))&&r.duration(0),e.keyboardClose&&r.beforeAddWrite(()=>{let a=n.ownerDocument.activeElement;a?.matches("input,ion-input, ion-textarea")&&a.blur()});let s=zl.get(e)||[];return zl.set(e,[...s,r]),yield r.play(),!0}),Ri=(e,t)=>{let n,o=new Promise(i=>n=i);return QT(e,t,i=>{n(i.detail)}),o},QT=(e,t,n)=>{let o=i=>{th(e,t,o),n(i)};as(e,t,o)};var Ai="backdrop",Ni="gesture",t0=39;var n0=()=>{let e,t=()=>{e&&(e(),e=void 0)};return{addClickListener:(o,i)=>{t();let r=i!==void 0?document.getElementById(i):null;if(!r){Le(`[${o.tagName.toLowerCase()}] - A trigger element with the ID "${i}" was not found in the DOM. The trigger element must be in the DOM when the "trigger" property is set on an overlay component.`,o);return}e=((a,l)=>{let c=()=>{l.present()};return a.addEventListener("click",c),()=>{a.removeEventListener("click",c)}})(r,o)},removeClickListener:t}},o0=e=>{yn!==void 0&&Pe("android")&&e.setAttribute("aria-hidden","true")},KT=e=>{var t;if(yn===void 0)return;let n=Gl(yn);for(let o=n.length-1;o>=0;o--){let i=n[o],r=(t=n[o+1])!==null&&t!==void 0?t:e;(r.hasAttribute("aria-hidden")||r.tagName!=="ION-TOAST")&&i.setAttribute("aria-hidden","true")}},XT=()=>{if(yn===void 0)return;let e=Gl(yn);for(let t=e.length-1;t>=0;t--){let n=e[t];if(n.removeAttribute("aria-hidden"),n.tagName!=="ION-TOAST")break}},Do="ion-disable-focus-trap";var a0=(()=>{class e{doc;_readyPromise;win;backButton=new re;keyboardDidShow=new re;keyboardDidHide=new re;pause=new re;resume=new re;resize=new re;constructor(n,o){this.doc=n,o.run(()=>{this.win=n.defaultView,this.backButton.subscribeWithPriority=function(r,s){return this.subscribe(a=>a.register(r,l=>o.run(()=>s(l))))},Oi(this.pause,n,"pause",o),Oi(this.resume,n,"resume",o),Oi(this.backButton,n,"ionBackButton",o),Oi(this.resize,this.win,"resize",o),Oi(this.keyboardDidShow,this.win,"ionKeyboardDidShow",o),Oi(this.keyboardDidHide,this.win,"ionKeyboardDidHide",o);let i;this._readyPromise=new Promise(r=>{i=r}),this.win?.cordova?n.addEventListener("deviceready",()=>{i("cordova")},{once:!0}):i("dom")})}is(n){return Pe(this.win,n)}platforms(){return Ll(this.win)}ready(){return this._readyPromise}get isRTL(){return this.doc.dir==="rtl"}getQueryParam(n){return iM(this.win.location.href,n)}isLandscape(){return!this.isPortrait()}isPortrait(){return this.win.matchMedia?.("(orientation: portrait)").matches}testUserAgent(n){let o=this.win.navigator;return!!(o?.userAgent&&o.userAgent.indexOf(n)>=0)}url(){return this.win.location.href}width(){return this.win.innerWidth}height(){return this.win.innerHeight}static \u0275fac=function(o){return new(o||e)(S(Ce),S(W))};static \u0275prov=I({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})(),iM=(e,t)=>{t=t.replace(/[[\]\\]/g,"\\$&");let o=new RegExp("[\\?&]"+t+"=([^&#]*)").exec(e);return o?decodeURIComponent(o[1].replace(/\+/g," ")):null},Oi=(e,t,n,o)=>{t&&t.addEventListener(n,i=>{o.run(()=>{let r=i?.detail;e.next(r)})})},l0=(()=>{class e{location;serializer;router;topOutlet;direction=i0;animated=r0;animationBuilder;guessDirection="forward";guessAnimation;lastNavId=-1;constructor(n,o,i,r){this.location=o,this.serializer=i,this.router=r,r&&r.events.subscribe(s=>{if(s instanceof dn){let a=s.restoredState?s.restoredState.navigationId:s.id;this.guessDirection=this.guessAnimation=a<this.lastNavId?"back":"forward",this.lastNavId=this.guessDirection==="forward"?s.id:a}}),n.backButton.subscribeWithPriority(0,s=>{this.pop(),s()})}navigateForward(n,o={}){return this.setDirection("forward",o.animated,o.animationDirection,o.animation),this.navigate(n,o)}navigateBack(n,o={}){return this.setDirection("back",o.animated,o.animationDirection,o.animation),this.navigate(n,o)}navigateRoot(n,o={}){return this.setDirection("root",o.animated,o.animationDirection,o.animation),this.navigate(n,o)}back(n={animated:!0,animationDirection:"back"}){return this.setDirection("back",n.animated,n.animationDirection,n.animation),this.location.back()}pop(){return C(this,null,function*(){let n=this.topOutlet;for(;n;){if(yield n.pop())return!0;n=n.parentOutlet}return!1})}setDirection(n,o,i,r){this.direction=n,this.animated=rM(n,o,i),this.animationBuilder=r}setTopOutlet(n){this.topOutlet=n}consumeTransition(){let n="root",o,i=this.animationBuilder;return this.direction==="auto"?(n=this.guessDirection,o=this.guessAnimation):(o=this.animated,n=this.direction),this.direction=i0,this.animated=r0,this.animationBuilder=void 0,{direction:n,animation:o,animationBuilder:i}}navigate(n,o){if(Array.isArray(n))return this.router.navigate(n,o);{let i=this.serializer.parse(n.toString());return o.queryParams!==void 0&&(i.queryParams=b({},o.queryParams)),o.fragment!==void 0&&(i.fragment=o.fragment),this.router.navigateByUrl(i,o)}}static \u0275fac=function(o){return new(o||e)(S(a0),S(Tt),S(yo),S(vt,8))};static \u0275prov=I({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})(),rM=(e,t,n)=>{if(t!==!1){if(n!==void 0)return n;if(e==="forward"||e==="back")return e;if(e==="root"&&t===!0)return"forward"}},i0="auto",r0=void 0,c0=(()=>{class e{get(n,o){let i=wp();return i?i.get(n,o):null}getBoolean(n,o){let i=wp();return i?i.getBoolean(n,o):!1}getNumber(n,o){let i=wp();return i?i.getNumber(n,o):0}static \u0275fac=function(o){return new(o||e)};static \u0275prov=I({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})(),Zl=new x("USERCONFIG"),wp=()=>{if(typeof window<"u"){let e=window.Ionic;if(e?.config)return e.config}return null},os=class{data;constructor(t={}){this.data=t,console.warn("[Ionic Warning]: NavParams has been deprecated in favor of using Angular's input API. Developers should migrate to either the @Input decorator or the Signals-based input API.")}get(t){return this.data[t]}},Ql=(()=>{class e{zone=v(W);applicationRef=v($t);config=v(Zl);create(n,o,i){return new Dp(n,o,this.applicationRef,this.zone,i,this.config.useSetInputAPI??!1)}static \u0275fac=function(o){return new(o||e)};static \u0275prov=I({token:e,factory:e.\u0275fac})}return e})(),Dp=class{environmentInjector;injector;applicationRef;zone;elementReferenceKey;enableSignalsSupport;elRefMap=new WeakMap;elEventsMap=new WeakMap;constructor(t,n,o,i,r,s){this.environmentInjector=t,this.injector=n,this.applicationRef=o,this.zone=i,this.elementReferenceKey=r,this.enableSignalsSupport=s}attachViewToDom(t,n,o,i){return this.zone.run(()=>new Promise(r=>{let s=b({},o);this.elementReferenceKey!==void 0&&(s[this.elementReferenceKey]=t);let a=sM(this.zone,this.environmentInjector,this.injector,this.applicationRef,this.elRefMap,this.elEventsMap,t,n,s,i,this.elementReferenceKey,this.enableSignalsSupport);r(a)}))}removeViewFromDom(t,n){return this.zone.run(()=>new Promise(o=>{let i=this.elRefMap.get(n);if(i){i.destroy(),this.elRefMap.delete(n);let r=this.elEventsMap.get(n);r&&(r(),this.elEventsMap.delete(n))}o()}))}},sM=(e,t,n,o,i,r,s,a,l,c,u,d)=>{let p=Ee.create({providers:lM(l),parent:n}),f=cy(a,{environmentInjector:t,elementInjector:p}),h=f.instance,y=f.location.nativeElement;if(l)if(u&&h[u]!==void 0&&console.error(`[Ionic Error]: ${u} is a reserved property when using ${s.tagName.toLowerCase()}. Rename or remove the "${u}" property from ${a.name}.`),d===!0&&f.setInput!==void 0){let w=l,{modal:P,popover:M}=w,H=Yp(w,["modal","popover"]);for(let $ in H)f.setInput($,H[$]);P!==void 0&&Object.assign(h,{modal:P}),M!==void 0&&Object.assign(h,{popover:M})}else Object.assign(h,l);if(c)for(let P of c)y.classList.add(P);let g=u0(e,h,y);return s.appendChild(y),o.attachView(f.hostView),i.set(y,f),r.set(y,g),y},aM=[dc,fc,pc,hc,mc],u0=(e,t,n)=>e.run(()=>{let o=aM.filter(i=>typeof t[i]=="function").map(i=>{let r=s=>t[i](s.detail);return n.addEventListener(i,r),()=>n.removeEventListener(i,r)});return()=>o.forEach(i=>i())}),s0=new x("NavParamsToken"),lM=e=>[{provide:s0,useValue:e},{provide:os,useFactory:cM,deps:[s0]}],cM=e=>new os(e),uM=(e,t)=>{let n=e.prototype;t.forEach(o=>{Object.defineProperty(n,o,{get(){return this.el[o]},set(i){this.z.runOutsideAngular(()=>this.el[o]=i)}})})},dM=(e,t)=>{let n=e.prototype;t.forEach(o=>{n[o]=function(){let i=arguments;return this.z.runOutsideAngular(()=>this.el[o].apply(this.el,i))}})};function d0(e){return function(n){let{defineCustomElementFn:o,inputs:i,methods:r}=e;return o!==void 0&&o(),i&&uM(n,i),r&&dM(n,r),n}}var fM=(e,t,n)=>n==="root"?f0(e,t):n==="forward"?pM(e,t):hM(e,t),f0=(e,t)=>(e=e.filter(n=>n.stackId!==t.stackId),e.push(t),e),pM=(e,t)=>(e.indexOf(t)>=0?e=e.filter(o=>o.stackId!==t.stackId||o.id<=t.id):e.push(t),e),hM=(e,t)=>e.indexOf(t)>=0?e.filter(o=>o.stackId!==t.stackId||o.id<=t.id):f0(e,t),Ip=(e,t)=>{let n=e.createUrlTree(["."],{relativeTo:t});return e.serializeUrl(n)},p0=(e,t)=>t?e.stackId!==t.stackId:!0,mM=(e,t)=>{if(!e)return;let n=h0(t);for(let o=0;o<n.length;o++){if(o>=e.length)return n[o];if(n[o]!==e[o])return}},h0=e=>e.split("/").map(t=>t.trim()).filter(t=>t!==""),m0=e=>{e&&(e.ref.destroy(),e.unlistenEvents())},Ep=class{containerEl;router;navCtrl;zone;location;views=[];runningTask;skipTransition=!1;tabsPrefix;activeView;nextId=0;constructor(t,n,o,i,r,s){this.containerEl=n,this.router=o,this.navCtrl=i,this.zone=r,this.location=s,this.tabsPrefix=t!==void 0?h0(t):void 0}createView(t,n){let o=Ip(this.router,n),i=t?.location?.nativeElement,r=u0(this.zone,t.instance,i);return{id:this.nextId++,stackId:mM(this.tabsPrefix,o),unlistenEvents:r,element:i,ref:t,url:o}}getExistingView(t){let n=Ip(this.router,t),o=this.views.find(i=>i.url===n);return o&&o.ref.changeDetectorRef.reattach(),o}setActive(t){let n=this.navCtrl.consumeTransition(),{direction:o,animation:i,animationBuilder:r}=n,s=this.activeView,a=p0(t,s);a&&(o="back",i=void 0);let l=this.views.slice(),c,u=this.router;u.getCurrentNavigation?c=u.getCurrentNavigation():u.navigations?.value&&(c=u.navigations.value),c?.extras?.replaceUrl&&this.views.length>0&&this.views.splice(-1,1);let d=this.views.includes(t),p=this.insertView(t,o);d||t.ref.changeDetectorRef.detectChanges();let f=t.animationBuilder;return r===void 0&&o==="back"&&!a&&f!==void 0&&(r=f),s&&(s.animationBuilder=r),this.zone.runOutsideAngular(()=>this.wait(()=>(s&&s.ref.changeDetectorRef.detach(),t.ref.changeDetectorRef.reattach(),this.transition(t,s,i,this.canGoBack(1),!1,r).then(()=>gM(t,p,l,this.location,this.zone)).then(()=>({enteringView:t,direction:o,animation:i,tabSwitch:a})))))}canGoBack(t,n=this.getActiveStackId()){return this.getStack(n).length>t}pop(t,n=this.getActiveStackId()){return this.zone.run(()=>{let o=this.getStack(n);if(o.length<=t)return Promise.resolve(!1);let i=o[o.length-t-1],r=i.url,s=i.savedData;if(s){let l=s.get("primary");l?.route?._routerState?.snapshot.url&&(r=l.route._routerState.snapshot.url)}let{animationBuilder:a}=this.navCtrl.consumeTransition();return this.navCtrl.navigateBack(r,L(b({},i.savedExtras),{animation:a})).then(()=>!0)})}startBackTransition(){let t=this.activeView;if(t){let n=this.getStack(t.stackId),o=n[n.length-2],i=o.animationBuilder;return this.wait(()=>this.transition(o,t,"back",this.canGoBack(2),!0,i))}return Promise.resolve()}endBackTransition(t){t?(this.skipTransition=!0,this.pop(1)):this.activeView&&g0(this.activeView,this.views,this.views,this.location,this.zone)}getLastUrl(t){let n=this.getStack(t);return n.length>0?n[n.length-1]:void 0}getRootUrl(t){let n=this.getStack(t);return n.length>0?n[0]:void 0}getActiveStackId(){return this.activeView?this.activeView.stackId:void 0}getActiveView(){return this.activeView}hasRunningTask(){return this.runningTask!==void 0}destroy(){this.containerEl=void 0,this.views.forEach(m0),this.activeView=void 0,this.views=[]}getStack(t){return this.views.filter(n=>n.stackId===t)}insertView(t,n){return this.activeView=t,this.views=fM(this.views,t,n),this.views.slice()}transition(t,n,o,i,r,s){if(this.skipTransition)return this.skipTransition=!1,Promise.resolve(!1);if(n===t)return Promise.resolve(!1);let a=t?t.element:void 0,l=n?n.element:void 0,c=this.containerEl;return a&&a!==l&&(a.classList.add("ion-page"),a.classList.add("ion-page-invisible"),c.commit)?c.commit(a,l,{duration:o===void 0?0:void 0,direction:o,showGoBack:i,progressAnimation:r,animationBuilder:s}):Promise.resolve(!1)}wait(t){return C(this,null,function*(){this.runningTask!==void 0&&(yield this.runningTask,this.runningTask=void 0);let n=this.runningTask=t();return n.finally(()=>this.runningTask=void 0),n})}},gM=(e,t,n,o,i)=>typeof requestAnimationFrame=="function"?new Promise(r=>{requestAnimationFrame(()=>{g0(e,t,n,o,i),r()})}):Promise.resolve(),g0=(e,t,n,o,i)=>{i.run(()=>n.filter(r=>!t.includes(r)).forEach(m0)),t.forEach(r=>{let a=o.path().split("?")[0].split("#")[0];if(r!==e&&r.url!==a){let l=r.element;l.setAttribute("aria-hidden","true"),l.classList.add("ion-page-hidden"),r.ref.changeDetectorRef.detach()}})},v0=(()=>{class e{parentOutlet;nativeEl;activatedView=null;tabsPrefix;_swipeGesture;stackCtrl;proxyMap=new WeakMap;currentActivatedRoute$=new Ie(null);activated=null;get activatedComponentRef(){return this.activated}_activatedRoute=null;name=R;stackWillChange=new de;stackDidChange=new de;activateEvents=new de;deactivateEvents=new de;parentContexts=v(hn);location=v(mt);environmentInjector=v(ae);inputBinder=v(y0,{optional:!0});supportsBindingToComponentInputs=!0;config=v(c0);navCtrl=v(l0);set animation(n){this.nativeEl.animation=n}set animated(n){this.nativeEl.animated=n}set swipeGesture(n){this._swipeGesture=n,this.nativeEl.swipeHandler=n?{canStart:()=>this.stackCtrl.canGoBack(1)&&!this.stackCtrl.hasRunningTask(),onStart:()=>this.stackCtrl.startBackTransition(),onEnd:o=>this.stackCtrl.endBackTransition(o)}:void 0}constructor(n,o,i,r,s,a,l,c){this.parentOutlet=c,this.nativeEl=r.nativeElement,this.name=n||R,this.tabsPrefix=o==="true"?Ip(s,l):void 0,this.stackCtrl=new Ep(this.tabsPrefix,this.nativeEl,s,this.navCtrl,a,i),this.parentContexts.onChildOutletCreated(this.name,this)}ngOnDestroy(){this.stackCtrl.destroy(),this.inputBinder?.unsubscribeFromRouteData(this)}getContext(){return this.parentContexts.getContext(this.name)}ngOnInit(){this.initializeOutletWithName()}initializeOutletWithName(){if(!this.activated){let n=this.getContext();n?.route&&this.activateWith(n.route,n.injector)}new Promise(n=>tt(this.nativeEl,n)).then(()=>{this._swipeGesture===void 0&&(this.swipeGesture=this.config.getBoolean("swipeBackEnabled",this.nativeEl.mode==="ios"))})}get isActivated(){return!!this.activated}get component(){if(!this.activated)throw new Error("Outlet is not activated");return this.activated.instance}get activatedRoute(){if(!this.activated)throw new Error("Outlet is not activated");return this._activatedRoute}get activatedRouteData(){return this._activatedRoute?this._activatedRoute.snapshot.data:{}}detach(){throw new Error("incompatible reuse strategy")}attach(n,o){throw new Error("incompatible reuse strategy")}deactivate(){if(this.activated){if(this.activatedView){let o=this.getContext();this.activatedView.savedData=new Map(o.children.contexts);let i=this.activatedView.savedData.get("primary");if(i&&o.route&&(i.route=b({},o.route)),this.activatedView.savedExtras={},o.route){let r=o.route.snapshot;this.activatedView.savedExtras.queryParams=r.queryParams,this.activatedView.savedExtras.fragment=r.fragment}}let n=this.component;this.activatedView=null,this.activated=null,this._activatedRoute=null,this.deactivateEvents.emit(n)}}activateWith(n,o){if(this.isActivated)throw new Error("Cannot activate an already activated outlet");this._activatedRoute=n;let i,r=this.stackCtrl.getExistingView(n);if(r){i=this.activated=r.ref;let a=r.savedData;if(a){let l=this.getContext();l.children.contexts=a}this.updateActivatedRouteProxy(i.instance,n)}else{let a=n._futureSnapshot,l=this.parentContexts.getOrCreateContext(this.name).children,c=new Ie(null),u=this.createActivatedRouteProxy(c,n),d=new xp(u,l,this.location.injector),p=a.routeConfig.component??a.component;i=this.activated=this.outletContent.createComponent(p,{index:this.outletContent.length,injector:d,environmentInjector:o??this.environmentInjector}),c.next(i.instance),r=this.stackCtrl.createView(this.activated,n),this.proxyMap.set(i.instance,u),this.currentActivatedRoute$.next({component:i.instance,activatedRoute:n})}this.inputBinder?.bindActivatedRouteToOutletComponent(this),this.activatedView=r,this.navCtrl.setTopOutlet(this);let s=this.stackCtrl.getActiveView();this.stackWillChange.emit({enteringView:r,tabSwitch:p0(r,s)}),this.stackCtrl.setActive(r).then(a=>{this.activateEvents.emit(i.instance),this.stackDidChange.emit(a)})}canGoBack(n=1,o){return this.stackCtrl.canGoBack(n,o)}pop(n=1,o){return this.stackCtrl.pop(n,o)}getLastUrl(n){let o=this.stackCtrl.getLastUrl(n);return o?o.url:void 0}getLastRouteView(n){return this.stackCtrl.getLastUrl(n)}getRootView(n){return this.stackCtrl.getRootUrl(n)}getActiveStackId(){return this.stackCtrl.getActiveStackId()}createActivatedRouteProxy(n,o){let i=new Qe;return i._futureSnapshot=o._futureSnapshot,i._routerState=o._routerState,i.snapshot=o.snapshot,i.outlet=o.outlet,i.component=o.component,i._paramMap=this.proxyObservable(n,"paramMap"),i._queryParamMap=this.proxyObservable(n,"queryParamMap"),i.url=this.proxyObservable(n,"url"),i.params=this.proxyObservable(n,"params"),i.queryParams=this.proxyObservable(n,"queryParams"),i.fragment=this.proxyObservable(n,"fragment"),i.data=this.proxyObservable(n,"data"),i}proxyObservable(n,o){return n.pipe(Ae(i=>!!i),Be(i=>this.currentActivatedRoute$.pipe(Ae(r=>r!==null&&r.component===i),Be(r=>r&&r.activatedRoute[o]),jc())))}updateActivatedRouteProxy(n,o){let i=this.proxyMap.get(n);if(!i)throw new Error("Could not find activated route proxy for view");i._futureSnapshot=o._futureSnapshot,i._routerState=o._routerState,i.snapshot=o.snapshot,i.outlet=o.outlet,i.component=o.component,this.currentActivatedRoute$.next({component:n,activatedRoute:o})}static \u0275fac=function(o){return new(o||e)(an("name"),an("tabs"),D(Tt),D(le),D(vt),D(W),D(Qe),D(e,12))};static \u0275dir=ze({type:e,selectors:[["ion-router-outlet"]],inputs:{animated:"animated",animation:"animation",mode:"mode",swipeGesture:"swipeGesture",name:"name"},outputs:{stackWillChange:"stackWillChange",stackDidChange:"stackDidChange",activateEvents:"activate",deactivateEvents:"deactivate"},exportAs:["outlet"],standalone:!1})}return e})(),xp=class{route;childContexts;parent;constructor(t,n,o){this.route=t,this.childContexts=n,this.parent=o}get(t,n){return t===Qe?this.route:t===hn?this.childContexts:this.parent.get(t,n)}},y0=new x(""),vM=(()=>{class e{outletDataSubscriptions=new Map;bindActivatedRouteToOutletComponent(n){this.unsubscribeFromRouteData(n),this.subscribeToRouteData(n)}unsubscribeFromRouteData(n){this.outletDataSubscriptions.get(n)?.unsubscribe(),this.outletDataSubscriptions.delete(n)}subscribeToRouteData(n){let{activatedRoute:o}=n,i=Ho([o.queryParams,o.params,o.data]).pipe(Be(([r,s,a],l)=>(a=b(b(b({},r),s),a),l===0?k(a):Promise.resolve(a)))).subscribe(r=>{if(!n.isActivated||!n.activatedComponentRef||n.activatedRoute!==o||o.component===null){this.unsubscribeFromRouteData(n);return}let s=Sf(o.component);if(!s){this.unsubscribeFromRouteData(n);return}for(let{templateName:a}of s.inputs)n.activatedComponentRef.setInput(a,r[a])});this.outletDataSubscriptions.set(n,i)}static \u0275fac=function(o){return new(o||e)};static \u0275prov=I({token:e,factory:e.\u0275fac})}return e})(),b0=()=>({provide:y0,useFactory:yM,deps:[vt]});function yM(e){return e?.componentInputBindingEnabled?new vM:null}var bM=e=>typeof __zone_symbol__requestAnimationFrame=="function"?__zone_symbol__requestAnimationFrame(e):typeof requestAnimationFrame=="function"?requestAnimationFrame(e):setTimeout(e),w0=(()=>{class e{injector;elementRef;onChange=()=>{};onTouched=()=>{};lastValue;statusChanges;constructor(n,o){this.injector=n,this.elementRef=o}writeValue(n){this.elementRef.nativeElement.value=this.lastValue=n,Pi(this.elementRef)}handleValueChange(n,o){n===this.elementRef.nativeElement&&(o!==this.lastValue&&(this.lastValue=o,this.onChange(o)),Pi(this.elementRef))}_handleBlurEvent(n){n===this.elementRef.nativeElement?(this.onTouched(),Pi(this.elementRef)):n.closest("ion-radio-group")===this.elementRef.nativeElement&&this.onTouched()}registerOnChange(n){this.onChange=n}registerOnTouched(n){this.onTouched=n}setDisabledState(n){this.elementRef.nativeElement.disabled=n}ngOnDestroy(){this.statusChanges&&this.statusChanges.unsubscribe()}ngAfterViewInit(){let n;try{n=this.injector.get(bo)}catch{}if(!n)return;n.statusChanges&&(this.statusChanges=n.statusChanges.subscribe(()=>Pi(this.elementRef)));let o=n.control;o&&["markAsTouched","markAllAsTouched","markAsUntouched","markAsDirty","markAsPristine"].forEach(r=>{if(typeof o[r]<"u"){let s=o[r].bind(o);o[r]=(...a)=>{s(...a),Pi(this.elementRef)}}})}static \u0275fac=function(o){return new(o||e)(D(Ee),D(le))};static \u0275dir=ze({type:e,hostBindings:function(o,i){o&1&&cn("ionBlur",function(s){return i._handleBlurEvent(s.target)})},standalone:!1})}return e})(),Pi=e=>{bM(()=>{let t=e.nativeElement,n=t.value!=null&&t.value.toString().length>0,o=wM(t);Cp(t,o);let i=t.closest("ion-item");i&&(n?Cp(i,[...o,"item-has-value"]):Cp(i,o))})},wM=e=>{let t=e.classList,n=[];for(let o=0;o<t.length;o++){let i=t.item(o);i!==null&&CM(i,"ng-")&&n.push(`ion-${i.substring(3)}`)}return n},Cp=(e,t)=>{let n=e.classList;n.remove("ion-valid","ion-invalid","ion-touched","ion-untouched","ion-dirty","ion-pristine"),n.add(...t)},CM=(e,t)=>e.substring(0,t.length)===t,Sp=class{shouldDetach(t){return!1}shouldAttach(t){return!1}store(t,n){}retrieve(t){return null}shouldReuseRoute(t,n){if(t.routeConfig!==n.routeConfig)return!1;let o=t.params,i=n.params,r=Object.keys(o),s=Object.keys(i);if(r.length!==s.length)return!1;for(let a of r)if(i[a]!==o[a])return!1;return!0}},is=class{ctrl;constructor(t){this.ctrl=t}create(t){return this.ctrl.create(t||{})}dismiss(t,n,o){return this.ctrl.dismiss(t,n,o)}getTop(){return this.ctrl.getTop()}};var _p="data:image/svg+xml;utf8,<svg xmlns='http://www.w3.org/2000/svg' class='ionicon' viewBox='0 0 512 512'><path stroke-linecap='round' stroke-linejoin='round' stroke-width='48' d='M184 112l144 144-144 144' class='ionicon-fill-none'/></svg>";var C0="data:image/svg+xml;utf8,<svg xmlns='http://www.w3.org/2000/svg' class='ionicon' viewBox='0 0 512 512'><path d='M256 48C141.31 48 48 141.31 48 256s93.31 208 208 208 208-93.31 208-208S370.69 48 256 48zm75.31 260.69a16 16 0 11-22.62 22.62L256 278.63l-52.69 52.68a16 16 0 01-22.62-22.62L233.37 256l-52.68-52.69a16 16 0 0122.62-22.62L256 233.37l52.69-52.68a16 16 0 0122.62 22.62L278.63 256z'/></svg>",D0="data:image/svg+xml;utf8,<svg xmlns='http://www.w3.org/2000/svg' class='ionicon' viewBox='0 0 512 512'><path d='M400 145.49L366.51 112 256 222.51 145.49 112 112 145.49 222.51 256 112 366.51 145.49 400 256 289.49 366.51 400 400 366.51 289.49 256 400 145.49z'/></svg>";var Tp,DM=()=>{if(typeof window>"u")return new Map;if(!Tp){let e=window;e.Ionicons=e.Ionicons||{},Tp=e.Ionicons.map=e.Ionicons.map||new Map}return Tp},IM=e=>{let t=Mp(e.src);return t||(t=E0(e.name,e.icon,e.mode,e.ios,e.md),t?EM(t,e):e.icon&&(t=Mp(e.icon),t||(t=Mp(e.icon[e.mode]),t))?t:null)},EM=(e,t)=>{let n=DM().get(e);if(n)return n;try{return Zp(`svg/${e}.svg`)}catch{console.warn(`[Ionicons Warning]: Could not load icon with name "${e}". Ensure that the icon is registered using addIcons or that the icon SVG data is passed directly to the icon component.`,t)}},E0=(e,t,n,o,i)=>(n=(n&&Kl(n))==="ios"?"ios":"md",o&&n==="ios"?e=Kl(o):i&&n==="md"?e=Kl(i):(!e&&t&&!x0(t)&&(e=t),Xl(e)&&(e=Kl(e))),!Xl(e)||e.trim()===""||e.replace(/[a-z]|-|\d/gi,"")!==""?null:e),Mp=e=>Xl(e)&&(e=e.trim(),x0(e))?e:null,x0=e=>e.length>0&&/(\/|\.)/.test(e),Xl=e=>typeof e=="string",Kl=e=>e.toLowerCase(),xM=(e,t=[])=>{let n={};return t.forEach(o=>{e.hasAttribute(o)&&(e.getAttribute(o)!==null&&(n[o]=e.getAttribute(o)),e.removeAttribute(o))}),n},SM=e=>e&&e.dir!==""?e.dir.toLowerCase()==="rtl":document?.dir.toLowerCase()==="rtl",_M=e=>{let t=document.createElement("div");t.innerHTML=e;for(let o=t.childNodes.length-1;o>=0;o--)t.childNodes[o].nodeName.toLowerCase()!=="svg"&&t.removeChild(t.childNodes[o]);let n=t.firstElementChild;if(n&&n.nodeName.toLowerCase()==="svg"){let o=n.getAttribute("class")||"";if(n.setAttribute("class",(o+" s-ion-icon").trim()),S0(n))return t.innerHTML}return""},S0=e=>{if(e.nodeType===1){if(e.nodeName.toLowerCase()==="script")return!1;for(let t=0;t<e.attributes.length;t++){let n=e.attributes[t].name;if(Xl(n)&&n.toLowerCase().indexOf("on")===0)return!1}for(let t=0;t<e.childNodes.length;t++)if(!S0(e.childNodes[t]))return!1}return!0},TM=e=>e.startsWith("data:image/svg+xml"),MM=e=>e.indexOf(";utf8,")!==-1,Io=new Map,I0=new Map,kp,kM=(e,t)=>{let n=I0.get(e);if(!n)if(typeof fetch<"u"&&typeof document<"u")if(TM(e)&&MM(e)){kp||(kp=new DOMParser);let i=kp.parseFromString(e,"text/html").querySelector("svg");return i&&Io.set(e,i.outerHTML),Promise.resolve()}else n=fetch(e).then(o=>{if(o.ok)return o.text().then(i=>{i&&t!==!1&&(i=_M(i)),Io.set(e,i||"")});Io.set(e,"")}),I0.set(e,n);else return Io.set(e,""),Promise.resolve();return n},AM=":host{display:inline-block;width:1em;height:1em;contain:strict;fill:currentColor;-webkit-box-sizing:content-box !important;box-sizing:content-box !important}:host .ionicon{stroke:currentColor}.ionicon-fill-none{fill:none}.ionicon-stroke-width{stroke-width:32px;stroke-width:var(--ionicon-stroke-width, 32px)}.icon-inner,.ionicon,svg{display:block;height:100%;width:100%}@supports (background: -webkit-named-image(i)){:host(.icon-rtl) .icon-inner{-webkit-transform:scaleX(-1);transform:scaleX(-1)}}@supports not selector(:dir(rtl)) and selector(:host-context([dir='rtl'])){:host(.icon-rtl) .icon-inner{-webkit-transform:scaleX(-1);transform:scaleX(-1)}}:host(.flip-rtl):host-context([dir='rtl']) .icon-inner{-webkit-transform:scaleX(-1);transform:scaleX(-1)}@supports selector(:dir(rtl)){:host(.flip-rtl:dir(rtl)) .icon-inner{-webkit-transform:scaleX(-1);transform:scaleX(-1)}:host(.flip-rtl:dir(ltr)) .icon-inner{-webkit-transform:scaleX(1);transform:scaleX(1)}}:host(.icon-small){font-size:1.125rem !important}:host(.icon-large){font-size:2rem !important}:host(.ion-color){color:var(--ion-color-base) !important}:host(.ion-color-primary){--ion-color-base:var(--ion-color-primary, #3880ff)}:host(.ion-color-secondary){--ion-color-base:var(--ion-color-secondary, #0cd1e8)}:host(.ion-color-tertiary){--ion-color-base:var(--ion-color-tertiary, #f4a942)}:host(.ion-color-success){--ion-color-base:var(--ion-color-success, #10dc60)}:host(.ion-color-warning){--ion-color-base:var(--ion-color-warning, #ffce00)}:host(.ion-color-danger){--ion-color-base:var(--ion-color-danger, #f14141)}:host(.ion-color-light){--ion-color-base:var(--ion-color-light, #f4f5f8)}:host(.ion-color-medium){--ion-color-base:var(--ion-color-medium, #989aa2)}:host(.ion-color-dark){--ion-color-base:var(--ion-color-dark, #222428)}",RM=Q(class extends Z{constructor(){super(),this.__registerHost(),this.__attachShadow(),this.iconName=null,this.inheritedAttributes={},this.didLoadIcon=!1,this.svgContent=void 0,this.isVisible=!1,this.mode=NM(),this.color=void 0,this.ios=void 0,this.md=void 0,this.flipRtl=void 0,this.name=void 0,this.src=void 0,this.icon=void 0,this.size=void 0,this.lazy=!1,this.sanitize=!0}componentWillLoad(){this.inheritedAttributes=xM(this.el,["aria-label"])}connectedCallback(){this.waitUntilVisible(this.el,"50px",()=>{this.isVisible=!0,this.loadIcon()})}componentDidLoad(){this.didLoadIcon||this.loadIcon()}disconnectedCallback(){this.io&&(this.io.disconnect(),this.io=void 0)}waitUntilVisible(t,n,o){if(ct.isBrowser&&this.lazy&&typeof window<"u"&&window.IntersectionObserver){let i=this.io=new window.IntersectionObserver(r=>{r[0].isIntersecting&&(i.disconnect(),this.io=void 0,o())},{rootMargin:n});i.observe(t)}else o()}loadIcon(){if(ct.isBrowser&&this.isVisible){let t=IM(this);t&&(Io.has(t)?this.svgContent=Io.get(t):kM(t,this.sanitize).then(()=>this.svgContent=Io.get(t)),this.didLoadIcon=!0)}this.iconName=E0(this.name,this.icon,this.mode,this.ios,this.md)}render(){let{flipRtl:t,iconName:n,inheritedAttributes:o,el:i}=this,r=this.mode||"md",s=n?(n.includes("arrow")||n.includes("chevron"))&&t!==!1:!1,a=t||s;return m(J,Object.assign({role:"img",class:Object.assign(Object.assign({[r]:!0},OM(this.color)),{[`icon-${this.size}`]:!!this.size,"flip-rtl":a,"icon-rtl":a&&SM(i)})},o),ct.isBrowser&&this.svgContent?m("div",{class:"icon-inner",innerHTML:this.svgContent}):m("div",{class:"icon-inner"}))}static get assetsDirs(){return["svg"]}get el(){return this}static get watchers(){return{name:["loadIcon"],src:["loadIcon"],icon:["loadIcon"],ios:["loadIcon"],md:["loadIcon"]}}static get style(){return AM}},[1,"ion-icon",{mode:[1025],color:[1],ios:[1],md:[1],flipRtl:[4,"flip-rtl"],name:[513],src:[1],icon:[8],size:[1],lazy:[4],sanitize:[4],svgContent:[32],isVisible:[32]},void 0,{name:["loadIcon"],src:["loadIcon"],icon:["loadIcon"],ios:["loadIcon"],md:["loadIcon"]}]),NM=()=>ct.isBrowser&&typeof document<"u"&&document.documentElement.getAttribute("mode")||"md",OM=e=>e?{"ion-color":!0,[`ion-color-${e}`]:!0}:null;function Jl(){if(typeof customElements>"u")return;["ion-icon"].forEach(t=>{switch(t){case"ion-icon":customElements.get(t)||customElements.define(t,RM);break}})}var PM=":host{left:0;right:0;top:0;bottom:0;position:absolute;contain:strict;pointer-events:none}:host(.unbounded){contain:layout size style}.ripple-effect{border-radius:50%;position:absolute;background-color:currentColor;color:inherit;contain:strict;opacity:0;-webkit-animation:225ms rippleAnimation forwards, 75ms fadeInAnimation forwards;animation:225ms rippleAnimation forwards, 75ms fadeInAnimation forwards;will-change:transform, opacity;pointer-events:none}.fade-out{-webkit-transform:translate(var(--translate-end)) scale(var(--final-scale, 1));transform:translate(var(--translate-end)) scale(var(--final-scale, 1));-webkit-animation:150ms fadeOutAnimation forwards;animation:150ms fadeOutAnimation forwards}@-webkit-keyframes rippleAnimation{from{-webkit-animation-timing-function:cubic-bezier(0.4, 0, 0.2, 1);animation-timing-function:cubic-bezier(0.4, 0, 0.2, 1);-webkit-transform:scale(1);transform:scale(1)}to{-webkit-transform:translate(var(--translate-end)) scale(var(--final-scale, 1));transform:translate(var(--translate-end)) scale(var(--final-scale, 1))}}@keyframes rippleAnimation{from{-webkit-animation-timing-function:cubic-bezier(0.4, 0, 0.2, 1);animation-timing-function:cubic-bezier(0.4, 0, 0.2, 1);-webkit-transform:scale(1);transform:scale(1)}to{-webkit-transform:translate(var(--translate-end)) scale(var(--final-scale, 1));transform:translate(var(--translate-end)) scale(var(--final-scale, 1))}}@-webkit-keyframes fadeInAnimation{from{-webkit-animation-timing-function:linear;animation-timing-function:linear;opacity:0}to{opacity:0.16}}@keyframes fadeInAnimation{from{-webkit-animation-timing-function:linear;animation-timing-function:linear;opacity:0}to{opacity:0.16}}@-webkit-keyframes fadeOutAnimation{from{-webkit-animation-timing-function:linear;animation-timing-function:linear;opacity:0.16}to{opacity:0}}@keyframes fadeOutAnimation{from{-webkit-animation-timing-function:linear;animation-timing-function:linear;opacity:0.16}to{opacity:0}}",FM=Q(class extends Z{constructor(){super(),this.__registerHost(),this.__attachShadow(),this.type="bounded"}addRipple(t,n){return C(this,null,function*(){return new Promise(o=>{vn(()=>{let i=this.el.getBoundingClientRect(),r=i.width,s=i.height,a=Math.sqrt(r*r+s*s),l=Math.max(s,r),c=this.unbounded?l:a+jM,u=Math.floor(l*VM),d=c/u,p=t-i.left,f=n-i.top;this.unbounded&&(p=r*.5,f=s*.5);let h=p-u*.5,y=f-u*.5,g=r*.5-p,w=s*.5-f;wt(()=>{let P=document.createElement("div");P.classList.add("ripple-effect");let M=P.style;M.top=y+"px",M.left=h+"px",M.width=M.height=u+"px",M.setProperty("--final-scale",`${d}`),M.setProperty("--translate-end",`${g}px, ${w}px`),(this.el.shadowRoot||this.el).appendChild(P),setTimeout(()=>{o(()=>{LM(P)})},325)})})})})}get unbounded(){return this.type==="unbounded"}render(){let t=F(this);return m(J,{key:"ae9d3b1ed6773a9b9bb2267129f7e9af23b6c9fc",role:"presentation",class:{[t]:!0,unbounded:this.unbounded}})}get el(){return this}static get style(){return PM}},[1,"ion-ripple-effect",{type:[1],addRipple:[64]}]),LM=e=>{e.classList.add("fade-out"),setTimeout(()=>{e.remove()},200)},jM=10,VM=.5;function ec(){if(typeof customElements>"u")return;["ion-ripple-effect"].forEach(t=>{switch(t){case"ion-ripple-effect":customElements.get(t)||customElements.define(t,FM);break}})}var Fi=()=>{let e;return{lock:()=>C(null,null,function*(){let n=e,o;return e=new Promise(i=>o=i),n!==void 0&&(yield n),o})}};var BM=":host{left:0;right:0;top:0;bottom:0;position:absolute;contain:layout size style;z-index:0}",HM=Q(class extends Z{constructor(){super(),this.__registerHost(),this.__attachShadow(),this.ionNavWillLoad=N(this,"ionNavWillLoad",7),this.ionNavWillChange=N(this,"ionNavWillChange",3),this.ionNavDidChange=N(this,"ionNavDidChange",3),this.lockController=Fi(),this.gestureOrAnimationInProgress=!1,this.mode=F(this),this.animated=!0}swipeHandlerChanged(){this.gesture&&this.gesture.enable(this.swipeHandler!==void 0)}connectedCallback(){return C(this,null,function*(){let t=()=>{this.gestureOrAnimationInProgress=!0,this.swipeHandler&&this.swipeHandler.onStart()};this.gesture=(yield import("./chunk-3IDTDWAV.js")).createSwipeBackGesture(this.el,()=>!this.gestureOrAnimationInProgress&&!!this.swipeHandler&&this.swipeHandler.canStart(),()=>t(),n=>{var o;return(o=this.ani)===null||o===void 0?void 0:o.progressStep(n)},(n,o,i)=>{if(this.ani){this.ani.onFinish(()=>{this.gestureOrAnimationInProgress=!1,this.swipeHandler&&this.swipeHandler.onEnd(n)},{oneTimeCallback:!0});let r=n?-.001:.001;n?r+=wo([0,0],[.32,.72],[0,1],[1,1],o)[0]:(this.ani.easing("cubic-bezier(1, 0, 0.68, 0.28)"),r+=wo([0,0],[1,0],[.68,.28],[1,1],o)[0]),this.ani.progressEnd(n?1:0,r,i)}else this.gestureOrAnimationInProgress=!1}),this.swipeHandlerChanged()})}componentWillLoad(){this.ionNavWillLoad.emit()}disconnectedCallback(){this.gesture&&(this.gesture.destroy(),this.gesture=void 0)}commit(t,n,o){return C(this,null,function*(){let i=yield this.lockController.lock(),r=!1;try{r=yield this.transition(t,n,o)}catch(s){Eo("[ion-router-outlet] - Exception in commit:",s)}return i(),r})}setRouteId(t,n,o,i){return C(this,null,function*(){return{changed:yield this.setRoot(t,n,{duration:o==="root"?0:void 0,direction:o==="back"?"back":"forward",animationBuilder:i}),element:this.activeEl}})}getRouteId(){return C(this,null,function*(){let t=this.activeEl;return t?{id:t.tagName,element:t,params:this.activeParams}:void 0})}setRoot(t,n,o){return C(this,null,function*(){if(this.activeComponent===t&&ih(n,this.activeParams))return!1;let i=this.activeEl,r=yield Mi(this.delegate,this.el,t,["ion-page","ion-page-invisible"],n);return this.activeComponent=t,this.activeEl=r,this.activeParams=n,yield this.commit(r,i,o),yield ki(this.delegate,i),!0})}transition(i,r){return C(this,arguments,function*(t,n,o={}){if(n===t)return!1;this.ionNavWillChange.emit();let{el:s,mode:a}=this,l=this.animated&&ie.getBoolean("animated",!0),c=o.animationBuilder||this.animation||ie.get("navAnimation");return yield rh(Object.assign(Object.assign({mode:a,animated:l,enteringEl:t,leavingEl:n,baseEl:s,deepWait:Zt(s),progressCallback:o.progressAnimation?u=>{u!==void 0&&!this.gestureOrAnimationInProgress?(this.gestureOrAnimationInProgress=!0,u.onFinish(()=>{this.gestureOrAnimationInProgress=!1,this.swipeHandler&&this.swipeHandler.onEnd(!1)},{oneTimeCallback:!0}),u.progressEnd(0,0,0)):this.ani=u}:void 0},o),{animationBuilder:c})),this.ionNavDidChange.emit(),!0})}render(){return m("slot",{key:"84b50f1155b0d780dff802ee13223287259fd525"})}get el(){return this}static get watchers(){return{swipeHandler:["swipeHandlerChanged"]}}static get style(){return BM}},[1,"ion-router-outlet",{mode:[1025],delegate:[16],animated:[4],animation:[16],swipeHandler:[16,"swipe-handler"],commit:[64],setRouteId:[64],getRouteId:[64]},void 0,{swipeHandler:["swipeHandlerChanged"]}]);function $M(){if(typeof customElements>"u")return;["ion-router-outlet"].forEach(t=>{switch(t){case"ion-router-outlet":customElements.get(t)||customElements.define(t,HM);break}})}var _0=$M;var zM=":host{left:0;right:0;top:0;bottom:0;display:block;position:absolute;-webkit-transform:translateZ(0);transform:translateZ(0);contain:strict;cursor:pointer;opacity:0.01;-ms-touch-action:none;touch-action:none;z-index:2}:host(.backdrop-hide){background:transparent}:host(.backdrop-no-tappable){cursor:auto}:host{background-color:var(--ion-backdrop-color, #000)}",UM=":host{left:0;right:0;top:0;bottom:0;display:block;position:absolute;-webkit-transform:translateZ(0);transform:translateZ(0);contain:strict;cursor:pointer;opacity:0.01;-ms-touch-action:none;touch-action:none;z-index:2}:host(.backdrop-hide){background:transparent}:host(.backdrop-no-tappable){cursor:auto}:host{background-color:var(--ion-backdrop-color, #000)}",qM=Q(class extends Z{constructor(){super(),this.__registerHost(),this.__attachShadow(),this.ionBackdropTap=N(this,"ionBackdropTap",7),this.visible=!0,this.tappable=!0,this.stopPropagation=!0}onMouseDown(t){this.emitTap(t)}emitTap(t){this.stopPropagation&&(t.preventDefault(),t.stopPropagation()),this.tappable&&this.ionBackdropTap.emit()}render(){let t=F(this);return m(J,{key:"7abaf2c310aa399607451b14063265e8a5846938","aria-hidden":"true",class:{[t]:!0,"backdrop-hide":!this.visible,"backdrop-no-tappable":!this.tappable}})}static get style(){return{ios:zM,md:UM}}},[33,"ion-backdrop",{visible:[4],tappable:[4],stopPropagation:[4,"stop-propagation"]},[[2,"click","onMouseDown"]]]);function tc(){if(typeof customElements>"u")return;["ion-backdrop"].forEach(t=>{switch(t){case"ion-backdrop":customElements.get(t)||customElements.define(t,qM);break}})}var rs=function(e){return e.Dark="DARK",e.Light="LIGHT",e.Default="DEFAULT",e}(rs||{}),Op={getEngine(){let e=ch();if(e?.isPluginAvailable("StatusBar"))return e.Plugins.StatusBar},setStyle(e){let t=this.getEngine();t&&t.setStyle(e)},getStyle:function(){return C(this,null,function*(){let e=this.getEngine();if(!e)return rs.Default;let{style:t}=yield e.getInfo();return t})}},Ap=(e,t)=>{if(t===1)return 0;let n=1/(1-t),o=-(t*n);return e*n+o},A0=()=>{!Ct||Ct.innerWidth>=768||Op.setStyle({style:rs.Dark})},Rp=(e=rs.Default)=>{!Ct||Ct.innerWidth>=768||Op.setStyle({style:e})},R0=(e,t)=>C(null,null,function*(){typeof e.canDismiss!="function"||!(yield e.canDismiss(void 0,Ni))||(t.isRunning()?t.onFinish(()=>{e.dismiss(void 0,"handler")},{oneTimeCallback:!0}):e.dismiss(void 0,"handler"))}),Np=e=>.00255275*2.71828**(-14.9619*e)-1.00255*2.71828**(-.0380968*e)+1,mn={MIN_PRESENTING_SCALE:.915},GM=(e,t,n,o)=>{let r=e.offsetHeight,s=!1,a=!1,l=null,c=null,u=.2,d=!0,p=0,f=()=>l&&So(l)?l.scrollY:!0,P=us({el:e,gestureName:"modalSwipeToClose",gesturePriority:t0,direction:"y",threshold:10,canStart:M=>{let H=M.event.target;return H===null||!H.closest?!0:(l=ds(H),l?(So(l)?c=ce(l).querySelector(".inner-scroll"):c=l,!!!l.querySelector("ion-refresher")&&c.scrollTop===0):H.closest("ion-footer")===null)},onStart:M=>{let{deltaY:H}=M;d=f(),a=e.canDismiss!==void 0&&e.canDismiss!==!0,H>0&&l&&yc(l),t.progressStart(!0,s?1:0)},onMove:M=>{let{deltaY:H}=M;H>0&&l&&yc(l);let $=M.deltaY/r,V=$>=0&&a,ve=V?u:.9999,ye=V?Np($/ve):$,be=wn(1e-4,ye,ve);t.progressStep(be),be>=.5&&p<.5?Rp(n):be<.5&&p>=.5&&A0(),p=be},onEnd:M=>{let H=M.velocityY,$=M.deltaY/r,V=$>=0&&a,ve=V?u:.9999,ye=V?Np($/ve):$,be=wn(1e-4,ye,ve),he=(M.deltaY+H*1e3)/r,we=!V&&he>=.5,me=we?-.001:.001;we?(t.easing("cubic-bezier(0.32, 0.72, 0, 1)"),me+=wo([0,0],[.32,.72],[0,1],[1,1],be)[0]):(t.easing("cubic-bezier(1, 0, 0.68, 0.28)"),me+=wo([0,0],[1,0],[.68,.28],[1,1],be)[0]);let yt=T0(we?$*r:(1-be)*r,H);s=we,P.enable(!1),l&&lh(l,d),t.onFinish(()=>{we||P.enable(!0)}).progressEnd(we?1:0,me,yt),V&&be>ve/4?R0(e,t):we&&o()}});return P},T0=(e,t)=>wn(400,e/Math.abs(t*1.1),500),N0=e=>{let{currentBreakpoint:t,backdropBreakpoint:n,expandToScroll:o}=e,i=n===void 0||n<t,r=i?`calc(var(--backdrop-opacity) * ${t})`:"0",s=T("backdropAnimation").fromTo("opacity",0,r);i&&s.beforeStyles({"pointer-events":"none"}).afterClearStyles(["pointer-events"]);let a=T("wrapperAnimation").keyframes([{offset:0,opacity:1,transform:"translateY(100%)"},{offset:1,opacity:1,transform:`translateY(${100-t*100}%)`}]),l=o?void 0:T("contentAnimation").keyframes([{offset:0,opacity:1,maxHeight:`${(1-t)*100}%`},{offset:1,opacity:1,maxHeight:`${t*100}%`}]);return{wrapperAnimation:a,backdropAnimation:s,contentAnimation:l}},O0=e=>{let{currentBreakpoint:t,backdropBreakpoint:n}=e,o=`calc(var(--backdrop-opacity) * ${Ap(t,n)})`,i=[{offset:0,opacity:o},{offset:1,opacity:0}],r=[{offset:0,opacity:o},{offset:n,opacity:0},{offset:1,opacity:0}],s=T("backdropAnimation").keyframes(n!==0?r:i);return{wrapperAnimation:T("wrapperAnimation").keyframes([{offset:0,opacity:1,transform:`translateY(${100-t*100}%)`},{offset:1,opacity:1,transform:"translateY(100%)"}]),backdropAnimation:s}},WM=()=>{let e=T().fromTo("opacity",.01,"var(--backdrop-opacity)").beforeStyles({"pointer-events":"none"}).afterClearStyles(["pointer-events"]),t=T().fromTo("transform","translateY(100vh)","translateY(0vh)");return{backdropAnimation:e,wrapperAnimation:t,contentAnimation:void 0}},M0=(e,t)=>{let{presentingEl:n,currentBreakpoint:o,expandToScroll:i}=t,r=ce(e),{wrapperAnimation:s,backdropAnimation:a,contentAnimation:l}=o!==void 0?N0(t):WM();a.addElement(r.querySelector("ion-backdrop")),s.addElement(r.querySelectorAll(".modal-wrapper, .modal-shadow")).beforeStyles({opacity:1}),!i&&l?.addElement(e.querySelector(".ion-page"));let c=T("entering-base").addElement(e).easing("cubic-bezier(0.32,0.72,0,1)").duration(500).addAnimation([s]);if(l&&c.addAnimation(l),n){let u=window.innerWidth<768,d=n.tagName==="ION-MODAL"&&n.presentingElement!==void 0,p=ce(n),f=T().beforeStyles({transform:"translateY(0)","transform-origin":"top center",overflow:"hidden"}),h=document.body;if(u){let y=CSS.supports("width","max(0px, 1px)")?"max(30px, var(--ion-safe-area-top))":"30px",g=d?"-10px":y,w=mn.MIN_PRESENTING_SCALE,P=`translateY(${g}) scale(${w})`;f.afterStyles({transform:P}).beforeAddWrite(()=>h.style.setProperty("background-color","black")).addElement(n).keyframes([{offset:0,filter:"contrast(1)",transform:"translateY(0px) scale(1)",borderRadius:"0px"},{offset:1,filter:"contrast(0.85)",transform:P,borderRadius:"10px 10px 0 0"}]),c.addAnimation(f)}else if(c.addAnimation(a),!d)s.fromTo("opacity","0","1");else{let g=`translateY(-10px) scale(${d?mn.MIN_PRESENTING_SCALE:1})`;f.afterStyles({transform:g}).addElement(p.querySelector(".modal-wrapper")).keyframes([{offset:0,filter:"contrast(1)",transform:"translateY(0) scale(1)"},{offset:1,filter:"contrast(0.85)",transform:g}]);let w=T().afterStyles({transform:g}).addElement(p.querySelector(".modal-shadow")).keyframes([{offset:0,opacity:"1",transform:"translateY(0) scale(1)"},{offset:1,opacity:"0",transform:g}]);c.addAnimation([f,w])}}else c.addAnimation(a);return c},YM=()=>{let e=T().fromTo("opacity","var(--backdrop-opacity)",0),t=T().fromTo("transform","translateY(0vh)","translateY(100vh)");return{backdropAnimation:e,wrapperAnimation:t}},k0=(e,t,n=500)=>{let{presentingEl:o,currentBreakpoint:i}=t,r=ce(e),{wrapperAnimation:s,backdropAnimation:a}=i!==void 0?O0(t):YM();a.addElement(r.querySelector("ion-backdrop")),s.addElement(r.querySelectorAll(".modal-wrapper, .modal-shadow")).beforeStyles({opacity:1});let l=T("leaving-base").addElement(e).easing("cubic-bezier(0.32,0.72,0,1)").duration(n).addAnimation(s);if(o){let c=window.innerWidth<768,u=o.tagName==="ION-MODAL"&&o.presentingElement!==void 0,d=ce(o),p=T().beforeClearStyles(["transform"]).afterClearStyles(["transform"]).onFinish(h=>{if(h!==1)return;o.style.setProperty("overflow",""),Array.from(f.querySelectorAll("ion-modal:not(.overlay-hidden)")).filter(g=>g.presentingElement!==void 0).length<=1&&f.style.setProperty("background-color","")}),f=document.body;if(c){let h=CSS.supports("width","max(0px, 1px)")?"max(30px, var(--ion-safe-area-top))":"30px",y=u?"-10px":h,g=mn.MIN_PRESENTING_SCALE,w=`translateY(${y}) scale(${g})`;p.addElement(o).keyframes([{offset:0,filter:"contrast(0.85)",transform:w,borderRadius:"10px 10px 0 0"},{offset:1,filter:"contrast(1)",transform:"translateY(0px) scale(1)",borderRadius:"0px"}]),l.addAnimation(p)}else if(l.addAnimation(a),!u)s.fromTo("opacity","1","0");else{let y=`translateY(-10px) scale(${u?mn.MIN_PRESENTING_SCALE:1})`;p.addElement(d.querySelector(".modal-wrapper")).afterStyles({transform:"translate3d(0, 0, 0)"}).keyframes([{offset:0,filter:"contrast(0.85)",transform:y},{offset:1,filter:"contrast(1)",transform:"translateY(0) scale(1)"}]);let g=T().addElement(d.querySelector(".modal-shadow")).afterStyles({transform:"translateY(0) scale(1)"}).keyframes([{offset:0,opacity:"0",transform:y},{offset:1,opacity:"1",transform:"translateY(0) scale(1)"}]);l.addAnimation([p,g])}}else l.addAnimation(a);return l},ZM=(e,t,n=300)=>{let{presentingEl:o}=t;if(!o)return T("portrait-to-landscape-transition");let i=o.tagName==="ION-MODAL"&&o.presentingElement!==void 0,r=ce(o),s=document.body,a=T("portrait-to-landscape-transition").addElement(e).easing("cubic-bezier(0.32,0.72,0,1)").duration(n),l=T().beforeStyles({transform:"translateY(0)","transform-origin":"top center",overflow:"hidden"});if(i){let u=`translateY(-10px) scale(${mn.MIN_PRESENTING_SCALE})`,d="translateY(0px) scale(1)";l.addElement(o).afterStyles({transform:d}).fromTo("transform",u,d).fromTo("filter","contrast(0.85)","contrast(1)");let p=T().addElement(r.querySelector(".modal-shadow")).afterStyles({transform:d,opacity:"0"}).fromTo("transform",u,d);a.addAnimation([l,p])}else{let c=ce(e),u=T().addElement(c.querySelectorAll(".modal-wrapper, .modal-shadow")).fromTo("opacity","1","1"),d=T().addElement(c.querySelector("ion-backdrop")).fromTo("opacity","var(--backdrop-opacity)","var(--backdrop-opacity)"),p=CSS.supports("width","max(0px, 1px)")?"max(30px, var(--ion-safe-area-top))":"30px",f=mn.MIN_PRESENTING_SCALE,h=`translateY(${p}) scale(${f})`;l.addElement(o).afterStyles({transform:"translateY(0px) scale(1)","border-radius":"0px"}).beforeAddWrite(()=>s.style.setProperty("background-color","")).fromTo("transform",h,"translateY(0px) scale(1)").fromTo("filter","contrast(0.85)","contrast(1)").fromTo("border-radius","10px 10px 0 0","0px"),a.addAnimation([l,u,d])}return a},QM=(e,t,n=300)=>{let{presentingEl:o}=t;if(!o)return T("landscape-to-portrait-transition");let i=o.tagName==="ION-MODAL"&&o.presentingElement!==void 0,r=ce(o),s=document.body,a=T("landscape-to-portrait-transition").addElement(e).easing("cubic-bezier(0.32,0.72,0,1)").duration(n),l=T().beforeStyles({transform:"translateY(0)","transform-origin":"top center",overflow:"hidden"});if(i){let u=`translateY(-10px) scale(${mn.MIN_PRESENTING_SCALE})`,d="translateY(0) scale(1)";l.addElement(o).afterStyles({transform:d}).fromTo("transform",u,d);let p=T().addElement(r.querySelector(".modal-shadow")).afterStyles({transform:d,opacity:"0"}).fromTo("transform",u,d);a.addAnimation([l,p])}else{let c=ce(e),u=T().addElement(c.querySelectorAll(".modal-wrapper, .modal-shadow")).fromTo("opacity","1","1"),d=T().addElement(c.querySelector("ion-backdrop")).fromTo("opacity","var(--backdrop-opacity)","var(--backdrop-opacity)"),p=CSS.supports("width","max(0px, 1px)")?"max(30px, var(--ion-safe-area-top))":"30px",f=mn.MIN_PRESENTING_SCALE,h=`translateY(${p}) scale(${f})`;l.addElement(o).afterStyles({transform:h}).beforeAddWrite(()=>s.style.setProperty("background-color","black")).keyframes([{offset:0,transform:"translateY(0px) scale(1)",filter:"contrast(1)",borderRadius:"0px"},{offset:.2,transform:"translateY(0px) scale(1)",filter:"contrast(1)",borderRadius:"10px 10px 0 0"},{offset:1,transform:h,filter:"contrast(0.85)",borderRadius:"10px 10px 0 0"}]),a.addAnimation([l,u,d])}return a},KM=()=>{let e=T().fromTo("opacity",.01,"var(--backdrop-opacity)").beforeStyles({"pointer-events":"none"}).afterClearStyles(["pointer-events"]),t=T().keyframes([{offset:0,opacity:.01,transform:"translateY(40px)"},{offset:1,opacity:1,transform:"translateY(0px)"}]);return{backdropAnimation:e,wrapperAnimation:t,contentAnimation:void 0}},XM=(e,t)=>{let{currentBreakpoint:n,expandToScroll:o}=t,i=ce(e),{wrapperAnimation:r,backdropAnimation:s,contentAnimation:a}=n!==void 0?N0(t):KM();s.addElement(i.querySelector("ion-backdrop")),r.addElement(i.querySelector(".modal-wrapper")),!o&&a?.addElement(e.querySelector(".ion-page"));let l=T().addElement(e).easing("cubic-bezier(0.36,0.66,0.04,1)").duration(280).addAnimation([s,r]);return a&&l.addAnimation(a),l},JM=()=>{let e=T().fromTo("opacity","var(--backdrop-opacity)",0),t=T().keyframes([{offset:0,opacity:.99,transform:"translateY(0px)"},{offset:1,opacity:0,transform:"translateY(40px)"}]);return{backdropAnimation:e,wrapperAnimation:t}},ek=(e,t)=>{let{currentBreakpoint:n}=t,o=ce(e),{wrapperAnimation:i,backdropAnimation:r}=n!==void 0?O0(t):JM();return r.addElement(o.querySelector("ion-backdrop")),i.addElement(o.querySelector(".modal-wrapper")),T().easing("cubic-bezier(0.47,0,0.745,0.715)").duration(200).addAnimation([r,i])},tk=(e,t,n,o,i,r,s=[],a,l,c,u)=>{let d=[{offset:0,opacity:"var(--backdrop-opacity)"},{offset:1,opacity:.01}],p=[{offset:0,opacity:"var(--backdrop-opacity)"},{offset:1-i,opacity:0},{offset:1,opacity:0}],f={WRAPPER_KEYFRAMES:[{offset:0,transform:"translateY(0%)"},{offset:1,transform:"translateY(100%)"}],BACKDROP_KEYFRAMES:i!==0?p:d,CONTENT_KEYFRAMES:[{offset:0,maxHeight:"100%"},{offset:1,maxHeight:"0%"}]},h=e.querySelector("ion-content"),y=n.clientHeight,g=o,w=0,P=!1,M=null,H=null,$=null,V=null,ve=.95,ye=s[s.length-1],be=s[0],he=r.childAnimations.find(ne=>ne.id==="wrapperAnimation"),we=r.childAnimations.find(ne=>ne.id==="backdropAnimation"),me=r.childAnimations.find(ne=>ne.id==="contentAnimation"),yt=()=>{e.style.setProperty("pointer-events","auto"),t.style.setProperty("pointer-events","auto"),e.classList.remove(Do)},Vn=()=>{e.style.setProperty("pointer-events","none"),t.style.setProperty("pointer-events","none"),e.classList.add(Do)},gn=ne=>{if(!H&&(H=Array.from(e.querySelectorAll("ion-footer")),!H.length))return;let Y=e.querySelector(".ion-page");if(V=ne,ne==="stationary")H.forEach(oe=>{oe.classList.remove("modal-footer-moving"),oe.style.removeProperty("position"),oe.style.removeProperty("width"),oe.style.removeProperty("height"),oe.style.removeProperty("top"),oe.style.removeProperty("left"),Y?.style.removeProperty("padding-bottom"),Y?.appendChild(oe)});else{let oe=0;H.forEach((X,Yt)=>{let bt=X.getBoundingClientRect(),Fe=document.body.getBoundingClientRect();oe+=X.clientHeight;let ji=bt.top-Fe.top,Vi=bt.left-Fe.left;if(X.style.setProperty("--pinned-width",`${X.clientWidth}px`),X.style.setProperty("--pinned-height",`${X.clientHeight}px`),X.style.setProperty("--pinned-top",`${ji}px`),X.style.setProperty("--pinned-left",`${Vi}px`),Yt===0){$=ji;let cc=e.querySelector("ion-header");cc&&($-=cc.clientHeight)}}),H.forEach(X=>{Y?.style.setProperty("padding-bottom",`${oe}px`),X.classList.add("modal-footer-moving"),X.style.setProperty("position","absolute"),X.style.setProperty("width","var(--pinned-width)"),X.style.setProperty("height","var(--pinned-height)"),X.style.setProperty("top","var(--pinned-top)"),X.style.setProperty("left","var(--pinned-left)"),document.body.appendChild(X)})}};he&&we&&(he.keyframes([...f.WRAPPER_KEYFRAMES]),we.keyframes([...f.BACKDROP_KEYFRAMES]),me?.keyframes([...f.CONTENT_KEYFRAMES]),r.progressStart(!0,1-g),g>i?yt():Vn()),h&&g!==ye&&a&&(h.scrollY=!1);let rc=ne=>{let Y=ds(ne.event.target);if(g=l(),!a&&Y)return(So(Y)?ce(Y).querySelector(".inner-scroll"):Y).scrollTop===0;if(g===1&&Y){let oe=So(Y)?ce(Y).querySelector(".inner-scroll"):Y;return!!!Y.querySelector("ion-refresher")&&oe.scrollTop===0}return!0},sc=ne=>{if(P=e.canDismiss!==void 0&&e.canDismiss!==!0&&be===0,!a){let Y=ds(ne.event.target);M=Y&&So(Y)?ce(Y).querySelector(".inner-scroll"):Y}a||gn("moving"),ne.deltaY>0&&h&&(h.scrollY=!1),je(()=>{e.focus()}),r.progressStart(!0,1-g)},ac=ne=>{if(!a&&$!==null&&V!==null&&(ne.currentY>=$&&V==="moving"?gn("stationary"):ne.currentY<$&&V==="stationary"&&gn("moving")),!a&&ne.deltaY<=0&&M)return;ne.deltaY>0&&h&&(h.scrollY=!1);let Y=1-g,oe=s.length>1?1-s[1]:void 0,X=Y+ne.deltaY/y,Yt=oe!==void 0&&X>=oe&&P,bt=Yt?ve:.9999,Fe=Yt&&oe!==void 0?oe+Np((X-oe)/(bt-oe)):X;w=wn(1e-4,Fe,bt),r.progressStep(w)},lc=ne=>{if(!a&&ne.deltaY<=0&&M&&M.scrollTop>0){gn("stationary");return}let Y=ne.velocityY,oe=(ne.deltaY+Y*350)/y,X=g-oe,Yt=s.reduce((bt,Fe)=>Math.abs(Fe-X)<Math.abs(bt-X)?Fe:bt);Li({breakpoint:Yt,breakpointOffset:w,canDismiss:P,animated:!0})},Li=ne=>{let{breakpoint:Y,canDismiss:oe,breakpointOffset:X,animated:Yt}=ne,bt=oe&&Y===0,Fe=bt?g:Y,ji=Fe!==0;return g=0,he&&we&&(he.keyframes([{offset:0,transform:`translateY(${X*100}%)`},{offset:1,transform:`translateY(${(1-Fe)*100}%)`}]),we.keyframes([{offset:0,opacity:`calc(var(--backdrop-opacity) * ${Ap(1-X,i)})`},{offset:1,opacity:`calc(var(--backdrop-opacity) * ${Ap(Fe,i)})`}]),me&&me.keyframes([{offset:0,maxHeight:`${(1-X)*100}%`},{offset:1,maxHeight:`${Fe*100}%`}]),r.progressStep(0)),Bn.enable(!1),bt?R0(e,r):ji||c(),h&&(Fe===s[s.length-1]||!a)&&(h.scrollY=!0),!a&&Fe===0&&gn("stationary"),new Promise(Vi=>{r.onFinish(()=>{ji?(a||gn("stationary"),he&&we?je(()=>{he.keyframes([...f.WRAPPER_KEYFRAMES]),we.keyframes([...f.BACKDROP_KEYFRAMES]),me?.keyframes([...f.CONTENT_KEYFRAMES]),r.progressStart(!0,1-Fe),g=Fe,u(g),g>i?yt():Vn(),Bn.enable(!0),Vi()}):(Bn.enable(!0),Vi())):Vi()},{oneTimeCallback:!0}).progressEnd(1,0,Yt?500:0)})},Bn=us({el:n,gestureName:"modalSheet",gesturePriority:40,direction:"y",threshold:10,canStart:rc,onStart:sc,onMove:ac,onEnd:lc});return{gesture:Bn,moveSheetToBreakpoint:Li}},nk=':host{--width:100%;--min-width:auto;--max-width:auto;--height:100%;--min-height:auto;--max-height:auto;--overflow:hidden;--border-radius:0;--border-width:0;--border-style:none;--border-color:transparent;--background:var(--ion-background-color, #fff);--box-shadow:none;--backdrop-opacity:0;left:0;right:0;top:0;bottom:0;display:-ms-flexbox;display:flex;position:absolute;-ms-flex-align:center;align-items:center;-ms-flex-pack:center;justify-content:center;outline:none;color:var(--ion-text-color, #000);contain:strict}.modal-wrapper,ion-backdrop{pointer-events:auto}:host(.overlay-hidden){display:none}.modal-wrapper,.modal-shadow{border-radius:var(--border-radius);width:var(--width);min-width:var(--min-width);max-width:var(--max-width);height:var(--height);min-height:var(--min-height);max-height:var(--max-height);border-width:var(--border-width);border-style:var(--border-style);border-color:var(--border-color);background:var(--background);-webkit-box-shadow:var(--box-shadow);box-shadow:var(--box-shadow);overflow:var(--overflow);z-index:10}.modal-shadow{position:absolute;background:transparent}@media only screen and (min-width: 768px) and (min-height: 600px){:host{--width:600px;--height:500px;--ion-safe-area-top:0px;--ion-safe-area-bottom:0px;--ion-safe-area-right:0px;--ion-safe-area-left:0px}}@media only screen and (min-width: 768px) and (min-height: 768px){:host{--width:600px;--height:600px}}.modal-handle{left:0px;right:0px;top:5px;border-radius:8px;-webkit-margin-start:auto;margin-inline-start:auto;-webkit-margin-end:auto;margin-inline-end:auto;position:absolute;width:36px;height:5px;-webkit-transform:translateZ(0);transform:translateZ(0);border:0;background:var(--ion-color-step-350, var(--ion-background-color-step-350, #c0c0be));cursor:pointer;z-index:11}.modal-handle::before{-webkit-padding-start:4px;padding-inline-start:4px;-webkit-padding-end:4px;padding-inline-end:4px;padding-top:4px;padding-bottom:4px;position:absolute;width:36px;height:5px;-webkit-transform:translate(-50%, -50%);transform:translate(-50%, -50%);content:""}:host(.modal-sheet){--height:calc(100% - (var(--ion-safe-area-top) + 10px))}:host(.modal-sheet) .modal-wrapper,:host(.modal-sheet) .modal-shadow{position:absolute;bottom:0}:host(.modal-sheet.modal-no-expand-scroll) ion-footer{position:absolute;bottom:0;width:var(--width)}:host{--backdrop-opacity:var(--ion-backdrop-opacity, 0.4)}:host(.modal-card),:host(.modal-sheet){--border-radius:10px}@media only screen and (min-width: 768px) and (min-height: 600px){:host{--border-radius:10px}}.modal-wrapper{-webkit-transform:translate3d(0,  100%,  0);transform:translate3d(0,  100%,  0)}@media screen and (max-width: 767px){@supports (width: max(0px, 1px)){:host(.modal-card){--height:calc(100% - max(30px, var(--ion-safe-area-top)) - 10px)}}@supports not (width: max(0px, 1px)){:host(.modal-card){--height:calc(100% - 40px)}}:host(.modal-card) .modal-wrapper{border-start-start-radius:var(--border-radius);border-start-end-radius:var(--border-radius);border-end-end-radius:0;border-end-start-radius:0}:host(.modal-card){--backdrop-opacity:0;--width:100%;-ms-flex-align:end;align-items:flex-end}:host(.modal-card) .modal-shadow{display:none}:host(.modal-card) ion-backdrop{pointer-events:none}}@media screen and (min-width: 768px){:host(.modal-card){--width:calc(100% - 120px);--height:calc(100% - (120px + var(--ion-safe-area-top) + var(--ion-safe-area-bottom)));--max-width:720px;--max-height:1000px;--backdrop-opacity:0;--box-shadow:0px 0px 30px 10px rgba(0, 0, 0, 0.1);-webkit-transition:all 0.5s ease-in-out;transition:all 0.5s ease-in-out}:host(.modal-card) .modal-wrapper{-webkit-box-shadow:none;box-shadow:none}:host(.modal-card) .modal-shadow{-webkit-box-shadow:var(--box-shadow);box-shadow:var(--box-shadow)}}:host(.modal-sheet) .modal-wrapper{border-start-start-radius:var(--border-radius);border-start-end-radius:var(--border-radius);border-end-end-radius:0;border-end-start-radius:0}',ok=':host{--width:100%;--min-width:auto;--max-width:auto;--height:100%;--min-height:auto;--max-height:auto;--overflow:hidden;--border-radius:0;--border-width:0;--border-style:none;--border-color:transparent;--background:var(--ion-background-color, #fff);--box-shadow:none;--backdrop-opacity:0;left:0;right:0;top:0;bottom:0;display:-ms-flexbox;display:flex;position:absolute;-ms-flex-align:center;align-items:center;-ms-flex-pack:center;justify-content:center;outline:none;color:var(--ion-text-color, #000);contain:strict}.modal-wrapper,ion-backdrop{pointer-events:auto}:host(.overlay-hidden){display:none}.modal-wrapper,.modal-shadow{border-radius:var(--border-radius);width:var(--width);min-width:var(--min-width);max-width:var(--max-width);height:var(--height);min-height:var(--min-height);max-height:var(--max-height);border-width:var(--border-width);border-style:var(--border-style);border-color:var(--border-color);background:var(--background);-webkit-box-shadow:var(--box-shadow);box-shadow:var(--box-shadow);overflow:var(--overflow);z-index:10}.modal-shadow{position:absolute;background:transparent}@media only screen and (min-width: 768px) and (min-height: 600px){:host{--width:600px;--height:500px;--ion-safe-area-top:0px;--ion-safe-area-bottom:0px;--ion-safe-area-right:0px;--ion-safe-area-left:0px}}@media only screen and (min-width: 768px) and (min-height: 768px){:host{--width:600px;--height:600px}}.modal-handle{left:0px;right:0px;top:5px;border-radius:8px;-webkit-margin-start:auto;margin-inline-start:auto;-webkit-margin-end:auto;margin-inline-end:auto;position:absolute;width:36px;height:5px;-webkit-transform:translateZ(0);transform:translateZ(0);border:0;background:var(--ion-color-step-350, var(--ion-background-color-step-350, #c0c0be));cursor:pointer;z-index:11}.modal-handle::before{-webkit-padding-start:4px;padding-inline-start:4px;-webkit-padding-end:4px;padding-inline-end:4px;padding-top:4px;padding-bottom:4px;position:absolute;width:36px;height:5px;-webkit-transform:translate(-50%, -50%);transform:translate(-50%, -50%);content:""}:host(.modal-sheet){--height:calc(100% - (var(--ion-safe-area-top) + 10px))}:host(.modal-sheet) .modal-wrapper,:host(.modal-sheet) .modal-shadow{position:absolute;bottom:0}:host(.modal-sheet.modal-no-expand-scroll) ion-footer{position:absolute;bottom:0;width:var(--width)}:host{--backdrop-opacity:var(--ion-backdrop-opacity, 0.32)}@media only screen and (min-width: 768px) and (min-height: 600px){:host{--border-radius:2px;--box-shadow:0 28px 48px rgba(0, 0, 0, 0.4)}}.modal-wrapper{-webkit-transform:translate3d(0,  40px,  0);transform:translate3d(0,  40px,  0);opacity:0.01}',P0=Q(class extends Z{constructor(){super(),this.__registerHost(),this.__attachShadow(),this.didPresent=N(this,"ionModalDidPresent",7),this.willPresent=N(this,"ionModalWillPresent",7),this.willDismiss=N(this,"ionModalWillDismiss",7),this.didDismiss=N(this,"ionModalDidDismiss",7),this.ionBreakpointDidChange=N(this,"ionBreakpointDidChange",7),this.didPresentShorthand=N(this,"didPresent",7),this.willPresentShorthand=N(this,"willPresent",7),this.willDismissShorthand=N(this,"willDismiss",7),this.didDismissShorthand=N(this,"didDismiss",7),this.ionMount=N(this,"ionMount",7),this.lockController=Fi(),this.triggerController=n0(),this.coreDelegate=Hl(),this.isSheetModal=!1,this.inheritedAttributes={},this.inline=!1,this.gestureAnimationDismissing=!1,this.presented=!1,this.hasController=!1,this.keyboardClose=!0,this.expandToScroll=!0,this.backdropBreakpoint=0,this.handleBehavior="none",this.backdropDismiss=!0,this.showBackdrop=!0,this.animated=!0,this.isOpen=!1,this.keepContentsMounted=!1,this.focusTrap=!0,this.canDismiss=!0,this.onHandleClick=()=>{let{sheetTransition:t,handleBehavior:n}=this;n!=="cycle"||t!==void 0||this.moveToNextBreakpoint()},this.onBackdropTap=()=>{let{sheetTransition:t}=this;t===void 0&&this.dismiss(void 0,Ai)},this.onLifecycle=t=>{let n=this.usersElement,o=ik[t.type];if(n&&o){let i=new CustomEvent(o,{bubbles:!1,cancelable:!1,detail:t.detail});n.dispatchEvent(i)}},this.onModalFocus=t=>{let{dragHandleEl:n,el:o}=this;t.target===o&&n&&n.tabIndex!==-1&&n.focus()},this.onSlotChange=({target:t})=>{t.assignedElements().forEach(o=>{o.querySelectorAll("ion-modal").forEach(i=>{i.getAttribute("data-parent-ion-modal")===null&&i.setAttribute("data-parent-ion-modal",this.el.id)})})}}onIsOpenChange(t,n){t===!0&&n===!1?this.present():t===!1&&n===!0&&this.dismiss()}triggerChanged(){let{trigger:t,el:n,triggerController:o}=this;t&&o.addClickListener(n,t)}onWindowResize(){F(this)!=="ios"||!this.presentingElement||this.enterAnimation||this.leaveAnimation||(clearTimeout(this.resizeTimeout),this.resizeTimeout=setTimeout(()=>{this.handleViewTransition()},50))}breakpointsChanged(t){t!==void 0&&(this.sortedBreakpoints=t.sort((n,o)=>n-o))}connectedCallback(){let{el:t}=this;Ul(t),this.triggerChanged()}disconnectedCallback(){this.triggerController.removeClickListener(),this.cleanupViewTransitionListener(),this.cleanupParentRemovalObserver()}componentWillLoad(){var t;let{breakpoints:n,initialBreakpoint:o,el:i,htmlAttributes:r}=this,s=this.isSheetModal=n!==void 0&&o!==void 0,a=["aria-label","role"];this.inheritedAttributes=xo(i,a),i.parentNode&&(this.cachedOriginalParent=i.parentNode),r!==void 0&&a.forEach(l=>{r[l]&&(this.inheritedAttributes=Object.assign(Object.assign({},this.inheritedAttributes),{[l]:r[l]}),delete r[l])}),s&&(this.currentBreakpoint=this.initialBreakpoint),n!==void 0&&o!==void 0&&!n.includes(o)&&Le("[ion-modal] - Your breakpoints array must include the initialBreakpoint value."),!((t=this.htmlAttributes)===null||t===void 0)&&t.id||ql(this.el)}componentDidLoad(){this.isOpen===!0&&je(()=>this.present()),this.breakpointsChanged(this.breakpoints),this.triggerChanged()}getDelegate(t=!1){if(this.workingDelegate&&!t)return{delegate:this.workingDelegate,inline:this.inline};let n=this.el.parentNode,o=this.inline=n!==null&&!this.hasController,i=this.workingDelegate=o?this.delegate||this.coreDelegate:this.delegate;return{inline:o,delegate:i}}checkCanDismiss(t,n){return C(this,null,function*(){let{canDismiss:o}=this;return typeof o=="function"?o(t,n):o})}present(){return C(this,null,function*(){let t=yield this.lockController.lock();if(this.presented){t();return}let{presentingElement:n,el:o}=this;this.currentBreakpoint=this.initialBreakpoint;let{inline:i,delegate:r}=this.getDelegate(!0);this.ionMount.emit(),this.usersElement=yield Mi(r,o,this.component,["ion-page"],this.componentProps,i),Zt(o)?yield cs(this.usersElement):this.keepContentsMounted||(yield ls()),wt(()=>this.el.classList.add("show-modal"));let s=n!==void 0;s&&F(this)==="ios"&&(this.statusBarStyle=yield Op.getStyle(),A0()),yield Wl(this,"modalEnter",M0,XM,{presentingEl:n,currentBreakpoint:this.initialBreakpoint,backdropBreakpoint:this.backdropBreakpoint,expandToScroll:this.expandToScroll}),typeof window<"u"&&(this.keyboardOpenCallback=()=>{this.gesture&&(this.gesture.enable(!1),je(()=>{this.gesture&&this.gesture.enable(!0)}))},window.addEventListener(bc,this.keyboardOpenCallback)),this.isSheetModal?this.initSheetGesture():s&&this.initSwipeToClose(),this.initViewTransitionListener(),this.initParentRemovalObserver(),t()})}initSwipeToClose(){var t;if(F(this)!=="ios")return;let{el:n}=this,o=this.leaveAnimation||ie.get("modalLeave",k0),i=this.animation=o(n,{presentingEl:this.presentingElement,expandToScroll:this.expandToScroll});if(!Hi(n)){$i(n);return}let s=(t=this.statusBarStyle)!==null&&t!==void 0?t:rs.Default;this.gesture=GM(n,i,s,()=>{this.gestureAnimationDismissing=!0,Rp(this.statusBarStyle),this.animation.onFinish(()=>C(this,null,function*(){yield this.dismiss(void 0,Ni),this.gestureAnimationDismissing=!1}))}),this.gesture.enable(!0)}initSheetGesture(){let{wrapperEl:t,initialBreakpoint:n,backdropBreakpoint:o}=this;if(!t||n===void 0)return;let i=this.enterAnimation||ie.get("modalEnter",M0),r=this.animation=i(this.el,{presentingEl:this.presentingElement,currentBreakpoint:n,backdropBreakpoint:o,expandToScroll:this.expandToScroll});r.progressStart(!0,1);let{gesture:s,moveSheetToBreakpoint:a}=tk(this.el,this.backdropEl,t,n,o,r,this.sortedBreakpoints,this.expandToScroll,()=>{var l;return(l=this.currentBreakpoint)!==null&&l!==void 0?l:0},()=>this.sheetOnDismiss(),l=>{this.currentBreakpoint!==l&&(this.currentBreakpoint=l,this.ionBreakpointDidChange.emit({breakpoint:l}))});this.gesture=s,this.moveSheetToBreakpoint=a,this.gesture.enable(!0)}sheetOnDismiss(){this.gestureAnimationDismissing=!0,this.animation.onFinish(()=>C(this,null,function*(){this.currentBreakpoint=0,this.ionBreakpointDidChange.emit({breakpoint:this.currentBreakpoint}),yield this.dismiss(void 0,Ni),this.gestureAnimationDismissing=!1}))}dismiss(t,n){return C(this,null,function*(){var o;if(this.gestureAnimationDismissing&&n!==Ni)return!1;let i=yield this.lockController.lock();if(yield this.dismissNestedModals(),n!=="handler"&&!(yield this.checkCanDismiss(t,n)))return i(),!1;let{presentingElement:r}=this;r!==void 0&&F(this)==="ios"&&Rp(this.statusBarStyle),typeof window<"u"&&this.keyboardOpenCallback&&(window.removeEventListener(bc,this.keyboardOpenCallback),this.keyboardOpenCallback=void 0);let a=yield Yl(this,t,n,"modalLeave",k0,ek,{presentingEl:r,currentBreakpoint:(o=this.currentBreakpoint)!==null&&o!==void 0?o:this.initialBreakpoint,backdropBreakpoint:this.backdropBreakpoint,expandToScroll:this.expandToScroll});if(a){let{delegate:l}=this.getDelegate();yield ki(l,this.usersElement),wt(()=>this.el.classList.remove("show-modal")),this.animation&&this.animation.destroy(),this.gesture&&this.gesture.destroy(),this.cleanupViewTransitionListener(),this.cleanupParentRemovalObserver()}return this.currentBreakpoint=void 0,this.animation=void 0,i(),a})}onDidDismiss(){return Ri(this.el,"ionModalDidDismiss")}onWillDismiss(){return Ri(this.el,"ionModalWillDismiss")}setCurrentBreakpoint(t){return C(this,null,function*(){if(!this.isSheetModal){Le("[ion-modal] - setCurrentBreakpoint is only supported on sheet modals.");return}if(!this.breakpoints.includes(t)){Le(`[ion-modal] - Attempted to set invalid breakpoint value ${t}. Please double check that the breakpoint value is part of your defined breakpoints.`);return}let{currentBreakpoint:n,moveSheetToBreakpoint:o,canDismiss:i,breakpoints:r,animated:s}=this;n!==t&&o&&(this.sheetTransition=o({breakpoint:t,breakpointOffset:1-n,canDismiss:i!==void 0&&i!==!0&&r[0]===0,animated:s}),yield this.sheetTransition,this.sheetTransition=void 0)})}getCurrentBreakpoint(){return C(this,null,function*(){return this.currentBreakpoint})}moveToNextBreakpoint(){return C(this,null,function*(){let{breakpoints:t,currentBreakpoint:n}=this;if(!t||n==null)return!1;let o=t.filter(a=>a!==0),r=(o.indexOf(n)+1)%o.length,s=o[r];return yield this.setCurrentBreakpoint(s),!0})}initViewTransitionListener(){F(this)!=="ios"||!this.presentingElement||this.enterAnimation||this.leaveAnimation||(this.currentViewIsPortrait=window.innerWidth<768)}handleViewTransition(){let t=window.innerWidth<768;if(this.currentViewIsPortrait===t)return;this.viewTransitionAnimation&&(this.viewTransitionAnimation.destroy(),this.viewTransitionAnimation=void 0);let{presentingElement:n}=this;if(!n)return;let o;this.currentViewIsPortrait&&!t?o=ZM(this.el,{presentingEl:n}):o=QM(this.el,{presentingEl:n}),this.currentViewIsPortrait=t,this.viewTransitionAnimation=o,o.play().then(()=>{this.viewTransitionAnimation=void 0,this.reinitSwipeToClose()})}cleanupViewTransitionListener(){this.resizeTimeout&&(clearTimeout(this.resizeTimeout),this.resizeTimeout=void 0),this.viewTransitionAnimation&&(this.viewTransitionAnimation.destroy(),this.viewTransitionAnimation=void 0)}reinitSwipeToClose(){F(this)!=="ios"||!this.presentingElement||(this.gesture&&(this.gesture.destroy(),this.gesture=void 0),this.animation&&(this.animation.progressEnd(0,0,0),this.animation.destroy(),this.animation=void 0),je(()=>{this.ensureCorrectModalPosition(),this.initSwipeToClose()}))}ensureCorrectModalPosition(){let{el:t,presentingElement:n}=this,i=ce(t).querySelector(".modal-wrapper");if(i&&(i.style.transform="translateY(0vh)",i.style.opacity="1"),n?.tagName==="ION-MODAL")if(window.innerWidth<768){let s=CSS.supports("width","max(0px, 1px)")?"max(30px, var(--ion-safe-area-top))":"30px",a=mn.MIN_PRESENTING_SCALE;n.style.transform=`translateY(${s}) scale(${a})`}else n.style.transform="translateY(0px) scale(1)"}dismissNestedModals(){return C(this,null,function*(){let t=document.querySelectorAll(`ion-modal[data-parent-ion-modal="${this.el.id}"]`);t?.forEach(n=>C(this,null,function*(){yield n.dismiss(void 0,"parent-dismissed")}))})}initParentRemovalObserver(){typeof MutationObserver>"u"||typeof window>"u"||!this.cachedOriginalParent||this.cachedOriginalParent.nodeType===Node.DOCUMENT_NODE||this.cachedOriginalParent.nodeType===Node.DOCUMENT_FRAGMENT_NODE||(this.parentRemovalObserver=new MutationObserver(t=>{t.forEach(n=>{if(n.type==="childList"&&n.removedNodes.length>0){let o=Array.from(n.removedNodes).some(r=>{var s,a;let l=r===this.cachedOriginalParent,c=this.cachedOriginalParent?(a=(s=r).contains)===null||a===void 0?void 0:a.call(s,this.cachedOriginalParent):!1;return l||c}),i=this.cachedOriginalParent&&!this.cachedOriginalParent.isConnected;(o||i)&&(this.dismiss(void 0,"parent-removed"),this.cachedOriginalParent=void 0)}})}),this.parentRemovalObserver.observe(document.body,{childList:!0,subtree:!0}))}cleanupParentRemovalObserver(){var t;(t=this.parentRemovalObserver)===null||t===void 0||t.disconnect(),this.parentRemovalObserver=void 0}render(){let{handle:t,isSheetModal:n,presentingElement:o,htmlAttributes:i,handleBehavior:r,inheritedAttributes:s,focusTrap:a,expandToScroll:l}=this,c=t!==!1&&n,u=F(this),d=o!==void 0&&u==="ios",p=r==="cycle";return m(J,Object.assign({key:"9e9a7bd591eb17a225a00b4fa2e379e94601d17f","no-router":!0,tabIndex:p&&(n&&c)?0:-1},i,{style:{zIndex:`${2e4+this.overlayIndex}`},class:Object.assign({[u]:!0,"modal-default":!d&&!n,"modal-card":d,"modal-sheet":n,"modal-no-expand-scroll":n&&!l,"overlay-hidden":!0,[Do]:a===!1},Vl(this.cssClass)),onIonBackdropTap:this.onBackdropTap,onIonModalDidPresent:this.onLifecycle,onIonModalWillPresent:this.onLifecycle,onIonModalWillDismiss:this.onLifecycle,onIonModalDidDismiss:this.onLifecycle,onFocus:this.onModalFocus}),m("ion-backdrop",{key:"e5eae2c14f830f75e308fcd7f4c10c86fac5b962",ref:h=>this.backdropEl=h,visible:this.showBackdrop,tappable:this.backdropDismiss,part:"backdrop"}),u==="ios"&&m("div",{key:"e268f9cd310c3cf4e051b5b92524ce4fb70d005e",class:"modal-shadow"}),m("div",Object.assign({key:"9c380f36c18144c153077b15744d1c3346bce63e",role:"dialog"},s,{"aria-modal":"true",class:"modal-wrapper ion-overlay-wrapper",part:"content",ref:h=>this.wrapperEl=h}),c&&m("button",{key:"2d5ee6d5959d97309c306e8ce72eb0f2c19be144",class:"modal-handle",tabIndex:p?0:-1,"aria-label":"Activate to adjust the size of the dialog overlaying the screen",onClick:p?this.onHandleClick:void 0,part:"handle",ref:h=>this.dragHandleEl=h}),m("slot",{key:"5590434c35ea04c42fc006498bc189038e15a298",onSlotchange:this.onSlotChange})))}get el(){return this}static get watchers(){return{isOpen:["onIsOpenChange"],trigger:["triggerChanged"]}}static get style(){return{ios:nk,md:ok}}},[33,"ion-modal",{hasController:[4,"has-controller"],overlayIndex:[2,"overlay-index"],delegate:[16],keyboardClose:[4,"keyboard-close"],enterAnimation:[16,"enter-animation"],leaveAnimation:[16,"leave-animation"],breakpoints:[16],expandToScroll:[4,"expand-to-scroll"],initialBreakpoint:[2,"initial-breakpoint"],backdropBreakpoint:[2,"backdrop-breakpoint"],handle:[4],handleBehavior:[1,"handle-behavior"],component:[1],componentProps:[16,"component-props"],cssClass:[1,"css-class"],backdropDismiss:[4,"backdrop-dismiss"],showBackdrop:[4,"show-backdrop"],animated:[4],presentingElement:[16,"presenting-element"],htmlAttributes:[16,"html-attributes"],isOpen:[4,"is-open"],trigger:[1],keepContentsMounted:[4,"keep-contents-mounted"],focusTrap:[4,"focus-trap"],canDismiss:[4,"can-dismiss"],presented:[32],present:[64],dismiss:[64],onDidDismiss:[64],onWillDismiss:[64],setCurrentBreakpoint:[64],getCurrentBreakpoint:[64]},[[9,"resize","onWindowResize"]],{isOpen:["onIsOpenChange"],trigger:["triggerChanged"]}]),ik={ionModalDidPresent:"ionViewDidEnter",ionModalWillPresent:"ionViewWillEnter",ionModalWillDismiss:"ionViewWillLeave",ionModalDidDismiss:"ionViewDidLeave"};function F0(){if(typeof customElements>"u")return;["ion-modal","ion-backdrop"].forEach(t=>{switch(t){case"ion-modal":customElements.get(t)||customElements.define(t,P0);break;case"ion-backdrop":customElements.get(t)||tc();break}})}var L0=F0;var rk=e=>{if(!e)return{arrowWidth:0,arrowHeight:0};let{width:t,height:n}=e.getBoundingClientRect();return{arrowWidth:t,arrowHeight:n}},V0=(e,t,n)=>{let o=t.getBoundingClientRect(),i=o.height,r=o.width;return e==="cover"&&n&&(r=n.getBoundingClientRect().width),{contentWidth:r,contentHeight:i}},sk=(e,t,n,o)=>{let i=[],s=ce(o).querySelector(".popover-content");switch(t){case"hover":i=[{eventName:"mouseenter",callback:a=>{document.elementFromPoint(a.clientX,a.clientY)!==e&&n.dismiss(void 0,void 0,!1)}}];break;case"context-menu":case"click":default:i=[{eventName:"click",callback:a=>{if(a.target.closest("[data-ion-popover-trigger]")===e){a.stopPropagation();return}n.dismiss(void 0,void 0,!1)}}];break}return i.forEach(({eventName:a,callback:l})=>s.addEventListener(a,l)),()=>{i.forEach(({eventName:a,callback:l})=>s.removeEventListener(a,l))}},ak=(e,t,n)=>{let o=[];switch(t){case"hover":let i;o=[{eventName:"mouseenter",callback:r=>C(null,null,function*(){r.stopPropagation(),i&&clearTimeout(i),i=setTimeout(()=>{je(()=>{n.presentFromTrigger(r),i=void 0})},100)})},{eventName:"mouseleave",callback:r=>{i&&clearTimeout(i);let s=r.relatedTarget;s&&s.closest("ion-popover")!==n&&n.dismiss(void 0,void 0,!1)}},{eventName:"click",callback:r=>r.stopPropagation()},{eventName:"ionPopoverActivateTrigger",callback:r=>n.presentFromTrigger(r,!0)}];break;case"context-menu":o=[{eventName:"contextmenu",callback:r=>{r.preventDefault(),n.presentFromTrigger(r)}},{eventName:"click",callback:r=>r.stopPropagation()},{eventName:"ionPopoverActivateTrigger",callback:r=>n.presentFromTrigger(r,!0)}];break;case"click":default:o=[{eventName:"click",callback:r=>n.presentFromTrigger(r)},{eventName:"ionPopoverActivateTrigger",callback:r=>n.presentFromTrigger(r,!0)}];break}return o.forEach(({eventName:i,callback:r})=>e.addEventListener(i,r)),e.setAttribute("data-ion-popover-trigger","true"),()=>{o.forEach(({eventName:i,callback:r})=>e.removeEventListener(i,r)),e.removeAttribute("data-ion-popover-trigger")}},B0=(e,t)=>!t||t.tagName!=="ION-ITEM"?-1:e.findIndex(n=>n===t),lk=(e,t)=>{let n=B0(e,t);return e[n+1]},ck=(e,t)=>{let n=B0(e,t);return e[n-1]},nc=e=>{let n=ce(e).querySelector("button");n&&je(()=>n.focus())},uk=e=>e.hasAttribute("data-ion-popover-trigger"),dk=e=>{let t=n=>C(null,null,function*(){var o;let i=document.activeElement,r=[],s=(o=n.target)===null||o===void 0?void 0:o.tagName;if(!(s!=="ION-POPOVER"&&s!=="ION-ITEM")){try{r=Array.from(e.querySelectorAll("ion-item:not(ion-popover ion-popover *):not([disabled])"))}catch{}switch(n.key){case"ArrowLeft":(yield e.getParentPopover())&&e.dismiss(void 0,void 0,!1);break;case"ArrowDown":n.preventDefault();let l=lk(r,i);l!==void 0&&nc(l);break;case"ArrowUp":n.preventDefault();let c=ck(r,i);c!==void 0&&nc(c);break;case"Home":n.preventDefault();let u=r[0];u!==void 0&&nc(u);break;case"End":n.preventDefault();let d=r[r.length-1];d!==void 0&&nc(d);break;case"ArrowRight":case" ":case"Enter":if(i&&uk(i)){let p=new CustomEvent("ionPopoverActivateTrigger");i.dispatchEvent(p)}break}}});return e.addEventListener("keydown",t),()=>e.removeEventListener("keydown",t)},H0=(e,t,n,o,i,r,s,a,l,c,u)=>{var d;let p={top:0,left:0,width:0,height:0};switch(r){case"event":if(!u)return l;let $=u;p={top:$.clientY,left:$.clientX,width:1,height:1};break;case"trigger":default:let V=u,ve=c||((d=V?.detail)===null||d===void 0?void 0:d.ionShadowTarget)||V?.target;if(!ve)return l;let ye=ve.getBoundingClientRect();p={top:ye.top,left:ye.left,width:ye.width,height:ye.height};break}let f=hk(s,p,t,n,o,i,e),h=mk(a,s,p,t,n),y=f.top+h.top,g=f.left+h.left,{arrowTop:w,arrowLeft:P}=pk(s,o,i,y,g,t,n,e),{originX:M,originY:H}=fk(s,a,e);return{top:y,left:g,referenceCoordinates:p,arrowTop:w,arrowLeft:P,originX:M,originY:H}},fk=(e,t,n)=>{switch(e){case"top":return{originX:j0(t),originY:"bottom"};case"bottom":return{originX:j0(t),originY:"top"};case"left":return{originX:"right",originY:oc(t)};case"right":return{originX:"left",originY:oc(t)};case"start":return{originX:n?"left":"right",originY:oc(t)};case"end":return{originX:n?"right":"left",originY:oc(t)}}},j0=e=>{switch(e){case"start":return"left";case"center":return"center";case"end":return"right"}},oc=e=>{switch(e){case"start":return"top";case"center":return"center";case"end":return"bottom"}},pk=(e,t,n,o,i,r,s,a)=>{let l={arrowTop:o+s/2-t/2,arrowLeft:i+r-t/2},c={arrowTop:o+s/2-t/2,arrowLeft:i-t*1.5};switch(e){case"top":return{arrowTop:o+s,arrowLeft:i+r/2-t/2};case"bottom":return{arrowTop:o-n,arrowLeft:i+r/2-t/2};case"left":return l;case"right":return c;case"start":return a?c:l;case"end":return a?l:c;default:return{arrowTop:0,arrowLeft:0}}},hk=(e,t,n,o,i,r,s)=>{let a={top:t.top,left:t.left-n-i},l={top:t.top,left:t.left+t.width+i};switch(e){case"top":return{top:t.top-o-r,left:t.left};case"right":return l;case"bottom":return{top:t.top+t.height+r,left:t.left};case"left":return a;case"start":return s?l:a;case"end":return s?a:l}},mk=(e,t,n,o,i)=>{switch(e){case"center":return vk(t,n,o,i);case"end":return gk(t,n,o,i);case"start":default:return{top:0,left:0}}},gk=(e,t,n,o)=>{switch(e){case"start":case"end":case"left":case"right":return{top:-(o-t.height),left:0};case"top":case"bottom":default:return{top:0,left:-(n-t.width)}}},vk=(e,t,n,o)=>{switch(e){case"start":case"end":case"left":case"right":return{top:-(o/2-t.height/2),left:0};case"top":case"bottom":default:return{top:0,left:-(n/2-t.width/2)}}},$0=(e,t,n,o,i,r,s,a,l,c,u,d,p=0,f=0,h=0)=>{let y=p,g=f,w=n,P=t,M,H=c,$=u,V=!1,ve=!1,ye=d?d.top+d.height:r/2-a/2,be=d?d.height:0,he=!1;return w<o+l?(w=o,V=!0,H="left"):s+o+w+l>i&&(ve=!0,w=i-s-o,H="right"),ye+be+a>r&&(e==="top"||e==="bottom")&&(ye-a>0?(P=Math.max(12,ye-a-be-(h-1)),y=P+a,$="bottom",he=!0):M=o),{top:P,left:w,bottom:M,originX:H,originY:$,checkSafeAreaLeft:V,checkSafeAreaRight:ve,arrowTop:y,arrowLeft:g,addPopoverBottomClass:he}},yk=(e,t=!1,n,o)=>!(!n&&!o||e!=="top"&&e!=="bottom"&&t),bk=5,wk=(e,t)=>{var n;let{event:o,size:i,trigger:r,reference:s,side:a,align:l}=t,c=e.ownerDocument,u=c.dir==="rtl",d=c.defaultView.innerWidth,p=c.defaultView.innerHeight,f=ce(e),h=f.querySelector(".popover-content"),y=f.querySelector(".popover-arrow"),g=r||((n=o?.detail)===null||n===void 0?void 0:n.ionShadowTarget)||o?.target,{contentWidth:w,contentHeight:P}=V0(i,h,g),{arrowWidth:M,arrowHeight:H}=rk(y),$={top:p/2-P/2,left:d/2-w/2,originX:u?"right":"left",originY:"top"},V=H0(u,w,P,M,H,s,a,l,$,r,o),ve=i==="cover"?0:bk,ye=i==="cover"?0:25,{originX:be,originY:he,top:we,left:me,bottom:yt,checkSafeAreaLeft:Vn,checkSafeAreaRight:gn,arrowTop:rc,arrowLeft:sc,addPopoverBottomClass:ac}=$0(a,V.top,V.left,ve,d,p,w,P,ye,V.originX,V.originY,V.referenceCoordinates,V.arrowTop,V.arrowLeft,H),lc=T(),Li=T(),Bn=T();return Li.addElement(f.querySelector("ion-backdrop")).fromTo("opacity",.01,"var(--backdrop-opacity)").beforeStyles({"pointer-events":"none"}).afterClearStyles(["pointer-events"]),Bn.addElement(f.querySelector(".popover-arrow")).addElement(f.querySelector(".popover-content")).fromTo("opacity",.01,1),lc.easing("ease").duration(100).beforeAddWrite(()=>{i==="cover"&&e.style.setProperty("--width",`${w}px`),ac&&e.classList.add("popover-bottom"),yt!==void 0&&h.style.setProperty("bottom",`${yt}px`);let ne=" + var(--ion-safe-area-left, 0)",Y=" - var(--ion-safe-area-right, 0)",oe=`${me}px`;if(Vn&&(oe=`${me}px${ne}`),gn&&(oe=`${me}px${Y}`),h.style.setProperty("top",`calc(${we}px + var(--offset-y, 0))`),h.style.setProperty("left",`calc(${oe} + var(--offset-x, 0))`),h.style.setProperty("transform-origin",`${he} ${be}`),y!==null){let X=V.top!==we||V.left!==me;yk(a,X,o,r)?(y.style.setProperty("top",`calc(${rc}px + var(--offset-y, 0))`),y.style.setProperty("left",`calc(${sc}px + var(--offset-x, 0))`)):y.style.setProperty("display","none")}}).addAnimation([Li,Bn])},Ck=e=>{let t=ce(e),n=t.querySelector(".popover-content"),o=t.querySelector(".popover-arrow"),i=T(),r=T(),s=T();return r.addElement(t.querySelector("ion-backdrop")).fromTo("opacity","var(--backdrop-opacity)",0),s.addElement(t.querySelector(".popover-arrow")).addElement(t.querySelector(".popover-content")).fromTo("opacity",.99,0),i.easing("ease").afterAddWrite(()=>{e.style.removeProperty("--width"),e.classList.remove("popover-bottom"),n.style.removeProperty("top"),n.style.removeProperty("left"),n.style.removeProperty("bottom"),n.style.removeProperty("transform-origin"),o&&(o.style.removeProperty("top"),o.style.removeProperty("left"),o.style.removeProperty("display"))}).duration(300).addAnimation([r,s])},Dk=12,Ik=(e,t)=>{var n;let{event:o,size:i,trigger:r,reference:s,side:a,align:l}=t,c=e.ownerDocument,u=c.dir==="rtl",d=c.defaultView.innerWidth,p=c.defaultView.innerHeight,f=ce(e),h=f.querySelector(".popover-content"),y=r||((n=o?.detail)===null||n===void 0?void 0:n.ionShadowTarget)||o?.target,{contentWidth:g,contentHeight:w}=V0(i,h,y),P={top:p/2-w/2,left:d/2-g/2,originX:u?"right":"left",originY:"top"},M=H0(u,g,w,0,0,s,a,l,P,r,o),H=i==="cover"?0:Dk,{originX:$,originY:V,top:ve,left:ye,bottom:be}=$0(a,M.top,M.left,H,d,p,g,w,0,M.originX,M.originY,M.referenceCoordinates),he=T(),we=T(),me=T(),yt=T(),Vn=T();return we.addElement(f.querySelector("ion-backdrop")).fromTo("opacity",.01,"var(--backdrop-opacity)").beforeStyles({"pointer-events":"none"}).afterClearStyles(["pointer-events"]),me.addElement(f.querySelector(".popover-wrapper")).duration(150).fromTo("opacity",.01,1),yt.addElement(h).beforeStyles({top:`calc(${ve}px + var(--offset-y, 0px))`,left:`calc(${ye}px + var(--offset-x, 0px))`,"transform-origin":`${V} ${$}`}).beforeAddWrite(()=>{be!==void 0&&h.style.setProperty("bottom",`${be}px`)}).fromTo("transform","scale(0.8)","scale(1)"),Vn.addElement(f.querySelector(".popover-viewport")).fromTo("opacity",.01,1),he.easing("cubic-bezier(0.36,0.66,0.04,1)").duration(300).beforeAddWrite(()=>{i==="cover"&&e.style.setProperty("--width",`${g}px`),V==="bottom"&&e.classList.add("popover-bottom")}).addAnimation([we,me,yt,Vn])},Ek=e=>{let t=ce(e),n=t.querySelector(".popover-content"),o=T(),i=T(),r=T();return i.addElement(t.querySelector("ion-backdrop")).fromTo("opacity","var(--backdrop-opacity)",0),r.addElement(t.querySelector(".popover-wrapper")).fromTo("opacity",.99,0),o.easing("ease").afterAddWrite(()=>{e.style.removeProperty("--width"),e.classList.remove("popover-bottom"),n.style.removeProperty("top"),n.style.removeProperty("left"),n.style.removeProperty("bottom"),n.style.removeProperty("transform-origin")}).duration(150).addAnimation([i,r])},xk=':host{--background:var(--ion-background-color, #fff);--min-width:0;--min-height:0;--max-width:auto;--height:auto;--offset-x:0px;--offset-y:0px;left:0;right:0;top:0;bottom:0;display:-ms-flexbox;display:flex;position:fixed;-ms-flex-align:center;align-items:center;-ms-flex-pack:center;justify-content:center;outline:none;color:var(--ion-text-color, #000);z-index:1001}:host(.popover-nested){pointer-events:none}:host(.popover-nested) .popover-wrapper{pointer-events:auto}:host(.overlay-hidden){display:none}.popover-wrapper{z-index:10}.popover-content{display:-ms-flexbox;display:flex;position:absolute;-ms-flex-direction:column;flex-direction:column;width:var(--width);min-width:var(--min-width);max-width:var(--max-width);height:var(--height);min-height:var(--min-height);max-height:var(--max-height);background:var(--background);-webkit-box-shadow:var(--box-shadow);box-shadow:var(--box-shadow);overflow:auto;z-index:10}::slotted(.popover-viewport){--ion-safe-area-top:0px;--ion-safe-area-right:0px;--ion-safe-area-bottom:0px;--ion-safe-area-left:0px;display:-ms-flexbox;display:flex;-ms-flex-direction:column;flex-direction:column}:host(.popover-nested.popover-side-left){--offset-x:5px}:host(.popover-nested.popover-side-right){--offset-x:-5px}:host(.popover-nested.popover-side-start){--offset-x:5px}:host-context([dir=rtl]):host(.popover-nested.popover-side-start),:host-context([dir=rtl]).popover-nested.popover-side-start{--offset-x:-5px}@supports selector(:dir(rtl)){:host(.popover-nested.popover-side-start:dir(rtl)){--offset-x:-5px}}:host(.popover-nested.popover-side-end){--offset-x:-5px}:host-context([dir=rtl]):host(.popover-nested.popover-side-end),:host-context([dir=rtl]).popover-nested.popover-side-end{--offset-x:5px}@supports selector(:dir(rtl)){:host(.popover-nested.popover-side-end:dir(rtl)){--offset-x:5px}}:host{--width:200px;--max-height:90%;--box-shadow:none;--backdrop-opacity:var(--ion-backdrop-opacity, 0.08)}:host(.popover-desktop){--box-shadow:0px 4px 16px 0px rgba(0, 0, 0, 0.12)}.popover-content{border-radius:10px}:host(.popover-desktop) .popover-content{border:0.5px solid var(--ion-color-step-100, var(--ion-background-color-step-100, #e6e6e6))}.popover-arrow{display:block;position:absolute;width:20px;height:10px;overflow:hidden;z-index:11}.popover-arrow::after{top:3px;border-radius:3px;position:absolute;width:14px;height:14px;-webkit-transform:rotate(45deg);transform:rotate(45deg);background:var(--background);content:"";z-index:10}.popover-arrow::after{inset-inline-start:3px}:host(.popover-bottom) .popover-arrow{top:auto;bottom:-10px}:host(.popover-bottom) .popover-arrow::after{top:-6px}:host(.popover-side-left) .popover-arrow{-webkit-transform:rotate(90deg);transform:rotate(90deg)}:host(.popover-side-right) .popover-arrow{-webkit-transform:rotate(-90deg);transform:rotate(-90deg)}:host(.popover-side-top) .popover-arrow{-webkit-transform:rotate(180deg);transform:rotate(180deg)}:host(.popover-side-start) .popover-arrow{-webkit-transform:rotate(90deg);transform:rotate(90deg)}:host-context([dir=rtl]):host(.popover-side-start) .popover-arrow,:host-context([dir=rtl]).popover-side-start .popover-arrow{-webkit-transform:rotate(-90deg);transform:rotate(-90deg)}@supports selector(:dir(rtl)){:host(.popover-side-start:dir(rtl)) .popover-arrow{-webkit-transform:rotate(-90deg);transform:rotate(-90deg)}}:host(.popover-side-end) .popover-arrow{-webkit-transform:rotate(-90deg);transform:rotate(-90deg)}:host-context([dir=rtl]):host(.popover-side-end) .popover-arrow,:host-context([dir=rtl]).popover-side-end .popover-arrow{-webkit-transform:rotate(90deg);transform:rotate(90deg)}@supports selector(:dir(rtl)){:host(.popover-side-end:dir(rtl)) .popover-arrow{-webkit-transform:rotate(90deg);transform:rotate(90deg)}}.popover-arrow,.popover-content{opacity:0}@supports ((-webkit-backdrop-filter: blur(0)) or (backdrop-filter: blur(0))){:host(.popover-translucent) .popover-content,:host(.popover-translucent) .popover-arrow::after{background:rgba(var(--ion-background-color-rgb, 255, 255, 255), 0.8);-webkit-backdrop-filter:saturate(180%) blur(20px);backdrop-filter:saturate(180%) blur(20px)}}',Sk=":host{--background:var(--ion-background-color, #fff);--min-width:0;--min-height:0;--max-width:auto;--height:auto;--offset-x:0px;--offset-y:0px;left:0;right:0;top:0;bottom:0;display:-ms-flexbox;display:flex;position:fixed;-ms-flex-align:center;align-items:center;-ms-flex-pack:center;justify-content:center;outline:none;color:var(--ion-text-color, #000);z-index:1001}:host(.popover-nested){pointer-events:none}:host(.popover-nested) .popover-wrapper{pointer-events:auto}:host(.overlay-hidden){display:none}.popover-wrapper{z-index:10}.popover-content{display:-ms-flexbox;display:flex;position:absolute;-ms-flex-direction:column;flex-direction:column;width:var(--width);min-width:var(--min-width);max-width:var(--max-width);height:var(--height);min-height:var(--min-height);max-height:var(--max-height);background:var(--background);-webkit-box-shadow:var(--box-shadow);box-shadow:var(--box-shadow);overflow:auto;z-index:10}::slotted(.popover-viewport){--ion-safe-area-top:0px;--ion-safe-area-right:0px;--ion-safe-area-bottom:0px;--ion-safe-area-left:0px;display:-ms-flexbox;display:flex;-ms-flex-direction:column;flex-direction:column}:host(.popover-nested.popover-side-left){--offset-x:5px}:host(.popover-nested.popover-side-right){--offset-x:-5px}:host(.popover-nested.popover-side-start){--offset-x:5px}:host-context([dir=rtl]):host(.popover-nested.popover-side-start),:host-context([dir=rtl]).popover-nested.popover-side-start{--offset-x:-5px}@supports selector(:dir(rtl)){:host(.popover-nested.popover-side-start:dir(rtl)){--offset-x:-5px}}:host(.popover-nested.popover-side-end){--offset-x:-5px}:host-context([dir=rtl]):host(.popover-nested.popover-side-end),:host-context([dir=rtl]).popover-nested.popover-side-end{--offset-x:5px}@supports selector(:dir(rtl)){:host(.popover-nested.popover-side-end:dir(rtl)){--offset-x:5px}}:host{--width:250px;--max-height:90%;--box-shadow:0 5px 5px -3px rgba(0, 0, 0, 0.2), 0 8px 10px 1px rgba(0, 0, 0, 0.14), 0 3px 14px 2px rgba(0, 0, 0, 0.12);--backdrop-opacity:var(--ion-backdrop-opacity, 0.32)}.popover-content{border-radius:4px;-webkit-transform-origin:left top;transform-origin:left top}:host-context([dir=rtl]) .popover-content{-webkit-transform-origin:right top;transform-origin:right top}[dir=rtl] .popover-content{-webkit-transform-origin:right top;transform-origin:right top}@supports selector(:dir(rtl)){.popover-content:dir(rtl){-webkit-transform-origin:right top;transform-origin:right top}}.popover-viewport{-webkit-transition-delay:100ms;transition-delay:100ms}.popover-wrapper{opacity:0}",z0=Q(class extends Z{constructor(){super(),this.__registerHost(),this.__attachShadow(),this.didPresent=N(this,"ionPopoverDidPresent",7),this.willPresent=N(this,"ionPopoverWillPresent",7),this.willDismiss=N(this,"ionPopoverWillDismiss",7),this.didDismiss=N(this,"ionPopoverDidDismiss",7),this.didPresentShorthand=N(this,"didPresent",7),this.willPresentShorthand=N(this,"willPresent",7),this.willDismissShorthand=N(this,"willDismiss",7),this.didDismissShorthand=N(this,"didDismiss",7),this.ionMount=N(this,"ionMount",7),this.parentPopover=null,this.coreDelegate=Hl(),this.lockController=Fi(),this.inline=!1,this.focusDescendantOnPresent=!1,this.presented=!1,this.hasController=!1,this.keyboardClose=!0,this.backdropDismiss=!0,this.showBackdrop=!0,this.translucent=!1,this.animated=!0,this.triggerAction="click",this.size="auto",this.dismissOnSelect=!1,this.reference="trigger",this.side="bottom",this.arrow=!0,this.isOpen=!1,this.keyboardEvents=!1,this.focusTrap=!0,this.keepContentsMounted=!1,this.onBackdropTap=()=>{this.dismiss(void 0,Ai)},this.onLifecycle=t=>{let n=this.usersElement,o=_k[t.type];if(n&&o){let i=new CustomEvent(o,{bubbles:!1,cancelable:!1,detail:t.detail});n.dispatchEvent(i)}},this.configureTriggerInteraction=()=>{let{trigger:t,triggerAction:n,el:o,destroyTriggerInteraction:i}=this;if(i&&i(),t===void 0)return;let r=this.triggerEl=t!==void 0?document.getElementById(t):null;if(!r){Le(`[ion-popover] - A trigger element with the ID "${t}" was not found in the DOM. The trigger element must be in the DOM when the "trigger" property is set on ion-popover.`,this.el);return}this.destroyTriggerInteraction=ak(r,n,o)},this.configureKeyboardInteraction=()=>{let{destroyKeyboardInteraction:t,el:n}=this;t&&t(),this.destroyKeyboardInteraction=dk(n)},this.configureDismissInteraction=()=>{let{destroyDismissInteraction:t,parentPopover:n,triggerAction:o,triggerEl:i,el:r}=this;!n||!i||(t&&t(),this.destroyDismissInteraction=sk(i,o,r,n))}}onTriggerChange(){this.configureTriggerInteraction()}onIsOpenChange(t,n){t===!0&&n===!1?this.present():t===!1&&n===!0&&this.dismiss()}connectedCallback(){let{configureTriggerInteraction:t,el:n}=this;Ul(n),t()}disconnectedCallback(){let{destroyTriggerInteraction:t}=this;t&&t()}componentWillLoad(){var t,n;let{el:o}=this,i=(n=(t=this.htmlAttributes)===null||t===void 0?void 0:t.id)!==null&&n!==void 0?n:ql(o);this.parentPopover=o.closest(`ion-popover:not(#${i})`),this.alignment===void 0&&(this.alignment=F(this)==="ios"?"center":"start")}componentDidLoad(){let{parentPopover:t,isOpen:n}=this;n===!0&&je(()=>this.present()),t&&as(t,"ionPopoverWillDismiss",()=>{this.dismiss(void 0,void 0,!1)}),this.configureTriggerInteraction()}presentFromTrigger(t,n=!1){return C(this,null,function*(){this.focusDescendantOnPresent=n,yield this.present(t),this.focusDescendantOnPresent=!1})}getDelegate(t=!1){if(this.workingDelegate&&!t)return{delegate:this.workingDelegate,inline:this.inline};let n=this.el.parentNode,o=this.inline=n!==null&&!this.hasController,i=this.workingDelegate=o?this.delegate||this.coreDelegate:this.delegate;return{inline:o,delegate:i}}present(t){return C(this,null,function*(){let n=yield this.lockController.lock();if(this.presented){n();return}let{el:o}=this,{inline:i,delegate:r}=this.getDelegate(!0);this.ionMount.emit(),this.usersElement=yield Mi(r,o,this.component,["popover-viewport"],this.componentProps,i),this.keyboardEvents||this.configureKeyboardInteraction(),this.configureDismissInteraction(),Zt(o)?yield cs(this.usersElement):this.keepContentsMounted||(yield ls()),yield Wl(this,"popoverEnter",wk,Ik,{event:t||this.event,size:this.size,trigger:this.triggerEl,reference:this.reference,side:this.side,align:this.alignment}),this.focusDescendantOnPresent&&$l(o),n()})}dismiss(t,n,o=!0){return C(this,null,function*(){let i=yield this.lockController.lock(),{destroyKeyboardInteraction:r,destroyDismissInteraction:s}=this;o&&this.parentPopover&&this.parentPopover.dismiss(t,n,o);let a=yield Yl(this,t,n,"popoverLeave",Ck,Ek,this.event);if(a){r&&(r(),this.destroyKeyboardInteraction=void 0),s&&(s(),this.destroyDismissInteraction=void 0);let{delegate:l}=this.getDelegate();yield ki(l,this.usersElement)}return i(),a})}getParentPopover(){return C(this,null,function*(){return this.parentPopover})}onDidDismiss(){return Ri(this.el,"ionPopoverDidDismiss")}onWillDismiss(){return Ri(this.el,"ionPopoverWillDismiss")}render(){let t=F(this),{onLifecycle:n,parentPopover:o,dismissOnSelect:i,side:r,arrow:s,htmlAttributes:a,focusTrap:l}=this,c=Pe("desktop"),u=s&&!o;return m(J,Object.assign({key:"16866c02534968c982cf4730d2936d03a5107c8b","aria-modal":"true","no-router":!0,tabindex:"-1"},a,{style:{zIndex:`${2e4+this.overlayIndex}`},class:Object.assign(Object.assign({},Vl(this.cssClass)),{[t]:!0,"popover-translucent":this.translucent,"overlay-hidden":!0,"popover-desktop":c,[`popover-side-${r}`]:!0,[Do]:l===!1,"popover-nested":!!o}),onIonPopoverDidPresent:n,onIonPopoverWillPresent:n,onIonPopoverWillDismiss:n,onIonPopoverDidDismiss:n,onIonBackdropTap:this.onBackdropTap}),!o&&m("ion-backdrop",{key:"0df258601a4d30df3c27aa8234a7d5e056c3ecbb",tappable:this.backdropDismiss,visible:this.showBackdrop,part:"backdrop"}),m("div",{key:"f94e80ed996b957b5cd09b826472b4f60e8fcc78",class:"popover-wrapper ion-overlay-wrapper",onClick:i?()=>this.dismiss():void 0},u&&m("div",{key:"185ce22f6386e8444a9cc7b8818dbfc16c463c99",class:"popover-arrow",part:"arrow"}),m("div",{key:"206202b299404e110de5397b229678cca18568d3",class:"popover-content",part:"content"},m("slot",{key:"ee543a0b92d6e35a837c0a0e4617c7b0fc4ad0b0"}))))}get el(){return this}static get watchers(){return{trigger:["onTriggerChange"],triggerAction:["onTriggerChange"],isOpen:["onIsOpenChange"]}}static get style(){return{ios:xk,md:Sk}}},[33,"ion-popover",{hasController:[4,"has-controller"],delegate:[16],overlayIndex:[2,"overlay-index"],enterAnimation:[16,"enter-animation"],leaveAnimation:[16,"leave-animation"],component:[1],componentProps:[16,"component-props"],keyboardClose:[4,"keyboard-close"],cssClass:[1,"css-class"],backdropDismiss:[4,"backdrop-dismiss"],event:[8],showBackdrop:[4,"show-backdrop"],translucent:[4],animated:[4],htmlAttributes:[16,"html-attributes"],triggerAction:[1,"trigger-action"],trigger:[1],size:[1],dismissOnSelect:[4,"dismiss-on-select"],reference:[1],side:[1],alignment:[1025],arrow:[4],isOpen:[4,"is-open"],keyboardEvents:[4,"keyboard-events"],focusTrap:[4,"focus-trap"],keepContentsMounted:[4,"keep-contents-mounted"],presented:[32],presentFromTrigger:[64],present:[64],dismiss:[64],getParentPopover:[64],onDidDismiss:[64],onWillDismiss:[64]},void 0,{trigger:["onTriggerChange"],triggerAction:["onTriggerChange"],isOpen:["onIsOpenChange"]}]),_k={ionPopoverDidPresent:"ionViewDidEnter",ionPopoverWillPresent:"ionViewWillEnter",ionPopoverWillDismiss:"ionViewWillLeave",ionPopoverDidDismiss:"ionViewDidLeave"};function U0(){if(typeof customElements>"u")return;["ion-popover","ion-backdrop"].forEach(t=>{switch(t){case"ion-popover":customElements.get(t)||customElements.define(t,z0);break;case"ion-backdrop":customElements.get(t)||tc();break}})}var q0=U0;var Tk="html.plt-mobile ion-app{-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none}html.plt-mobile ion-app [contenteditable]{-webkit-user-select:text;-moz-user-select:text;-ms-user-select:text;user-select:text}ion-app.force-statusbar-padding{--ion-safe-area-top:20px}",Mk=Q(class extends Z{constructor(){super(),this.__registerHost()}componentDidLoad(){ct.isBrowser&&Ak(()=>C(this,null,function*(){let t=Pe(window,"hybrid");if(ie.getBoolean("_testing")||import("./chunk-CJYL5T3I.js").then(i=>i.startTapClick(ie)),ie.getBoolean("statusTap",t)&&import("./chunk-5ZJQD5L7.js").then(i=>i.startStatusTap()),ie.getBoolean("inputShims",kk())){let i=Pe(window,"ios")?"ios":"android";import("./chunk-EN5Y6YEK.js").then(r=>r.startInputShims(ie,i))}let n=yield import("./chunk-VCHLP75Z.js"),o=t||Bi();ie.getBoolean("hardwareBackButton",o)?n.startHardwareBackButton():(Bi()&&Le("[ion-app] - experimentalCloseWatcher was set to `true`, but hardwareBackButton was set to `false`. Both config options must be `true` for the Close Watcher API to be used."),n.blockHardwareBackButton()),typeof window<"u"&&import("./chunk-R5SHVWC7.js").then(i=>i.startKeyboardAssist(window)),import("./chunk-FA25AEA4.js").then(i=>this.focusVisible=i.startFocusVisible())}))}setFocus(t){return C(this,null,function*(){this.focusVisible&&this.focusVisible.setFocus(t)})}render(){let t=F(this);return m(J,{key:"9be440c65819e4fa67c2c3c6477ab40b3ad3eed3",class:{[t]:!0,"ion-page":!0,"force-statusbar-padding":ie.getBoolean("_forceStatusbarPadding")}})}get el(){return this}static get style(){return Tk}},[0,"ion-app",{setFocus:[64]}]),kk=()=>!!(Pe(window,"ios")&&Pe(window,"mobile")||Pe(window,"android")&&Pe(window,"mobileweb")),Ak=e=>{"requestIdleCallback"in window?window.requestIdleCallback(e):setTimeout(e,32)};function Rk(){if(typeof customElements>"u")return;["ion-app"].forEach(t=>{switch(t){case"ion-app":customElements.get(t)||customElements.define(t,Mk);break}})}var G0=Rk;var Nk=':host{--overflow:hidden;--ripple-color:currentColor;--border-width:initial;--border-color:initial;--border-style:initial;--color-activated:var(--color);--color-focused:var(--color);--color-hover:var(--color);--box-shadow:none;display:inline-block;width:auto;color:var(--color);font-family:var(--ion-font-family, inherit);text-align:center;text-decoration:none;white-space:normal;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none;vertical-align:top;vertical-align:-webkit-baseline-middle;-webkit-font-kerning:none;font-kerning:none}:host(.button-disabled){cursor:default;opacity:0.5;pointer-events:none}:host(.button-solid){--background:var(--ion-color-primary, #0054e9);--color:var(--ion-color-primary-contrast, #fff)}:host(.button-outline){--border-color:var(--ion-color-primary, #0054e9);--background:transparent;--color:var(--ion-color-primary, #0054e9)}:host(.button-clear){--border-width:0;--background:transparent;--color:var(--ion-color-primary, #0054e9)}:host(.button-block){display:block}:host(.button-block) .button-native{margin-left:0;margin-right:0;width:100%;clear:both;contain:content}:host(.button-block) .button-native::after{clear:both}:host(.button-full){display:block}:host(.button-full) .button-native{margin-left:0;margin-right:0;width:100%;contain:content}:host(.button-full:not(.button-round)) .button-native{border-radius:0;border-right-width:0;border-left-width:0}.button-native{border-radius:var(--border-radius);-moz-osx-font-smoothing:grayscale;-webkit-font-smoothing:antialiased;margin-left:0;margin-right:0;margin-top:0;margin-bottom:0;-webkit-padding-start:var(--padding-start);padding-inline-start:var(--padding-start);-webkit-padding-end:var(--padding-end);padding-inline-end:var(--padding-end);padding-top:var(--padding-top);padding-bottom:var(--padding-bottom);font-family:inherit;font-size:inherit;font-style:inherit;font-weight:inherit;letter-spacing:inherit;text-decoration:inherit;text-indent:inherit;text-overflow:inherit;text-transform:inherit;text-align:inherit;white-space:inherit;color:inherit;display:-ms-flexbox;display:flex;position:relative;-ms-flex-align:center;align-items:center;width:100%;height:100%;min-height:inherit;-webkit-transition:var(--transition);transition:var(--transition);border-width:var(--border-width);border-style:var(--border-style);border-color:var(--border-color);outline:none;background:var(--background);line-height:1;-webkit-box-shadow:var(--box-shadow);box-shadow:var(--box-shadow);contain:layout style;cursor:pointer;opacity:var(--opacity);overflow:var(--overflow);z-index:0;-webkit-box-sizing:border-box;box-sizing:border-box;-webkit-appearance:none;-moz-appearance:none;appearance:none}.button-native::-moz-focus-inner{border:0}.button-inner{display:-ms-flexbox;display:flex;position:relative;-ms-flex-flow:row nowrap;flex-flow:row nowrap;-ms-flex-negative:0;flex-shrink:0;-ms-flex-align:center;align-items:center;-ms-flex-pack:center;justify-content:center;width:100%;height:100%;z-index:1}::slotted([slot=start]),::slotted([slot=end]){-ms-flex-negative:0;flex-shrink:0}::slotted(ion-icon){font-size:1.35em;pointer-events:none}::slotted(ion-icon[slot=start]){-webkit-margin-start:-0.3em;margin-inline-start:-0.3em;-webkit-margin-end:0.3em;margin-inline-end:0.3em;margin-top:0;margin-bottom:0}::slotted(ion-icon[slot=end]){-webkit-margin-start:0.3em;margin-inline-start:0.3em;-webkit-margin-end:-0.2em;margin-inline-end:-0.2em;margin-top:0;margin-bottom:0}ion-ripple-effect{color:var(--ripple-color)}.button-native::after{left:0;right:0;top:0;bottom:0;position:absolute;content:"";opacity:0}:host(.ion-focused){color:var(--color-focused)}:host(.ion-focused) .button-native::after{background:var(--background-focused);opacity:var(--background-focused-opacity)}@media (any-hover: hover){:host(:hover){color:var(--color-hover)}:host(:hover) .button-native::after{background:var(--background-hover);opacity:var(--background-hover-opacity)}}:host(.ion-activated){color:var(--color-activated)}:host(.ion-activated) .button-native::after{background:var(--background-activated);opacity:var(--background-activated-opacity)}:host(.button-solid.ion-color) .button-native{background:var(--ion-color-base);color:var(--ion-color-contrast)}:host(.button-outline.ion-color) .button-native{border-color:var(--ion-color-base);background:transparent;color:var(--ion-color-base)}:host(.button-clear.ion-color) .button-native{background:transparent;color:var(--ion-color-base)}:host(.in-toolbar:not(.ion-color):not(.in-toolbar-color)) .button-native{color:var(--ion-toolbar-color, var(--color))}:host(.button-outline.in-toolbar:not(.ion-color):not(.in-toolbar-color)) .button-native{border-color:var(--ion-toolbar-color, var(--color, var(--border-color)))}:host(.button-solid.in-toolbar:not(.ion-color):not(.in-toolbar-color)) .button-native{background:var(--ion-toolbar-color, var(--background));color:var(--ion-toolbar-background, var(--color))}:host{--border-radius:14px;--padding-top:13px;--padding-bottom:13px;--padding-start:1em;--padding-end:1em;--transition:background-color, opacity 100ms linear;-webkit-margin-start:2px;margin-inline-start:2px;-webkit-margin-end:2px;margin-inline-end:2px;margin-top:4px;margin-bottom:4px;min-height:3.1em;font-size:min(1rem, 48px);font-weight:500;letter-spacing:0}:host(.button-solid){--background-activated:var(--ion-color-primary-shade, #004acd);--background-focused:var(--ion-color-primary-shade, #004acd);--background-hover:var(--ion-color-primary-tint, #1a65eb);--background-activated-opacity:1;--background-focused-opacity:1;--background-hover-opacity:1}:host(.button-outline){--border-radius:14px;--border-width:1px;--border-style:solid;--background-activated:var(--ion-color-primary, #0054e9);--background-focused:var(--ion-color-primary, #0054e9);--background-hover:transparent;--background-focused-opacity:.1;--color-activated:var(--ion-color-primary-contrast, #fff)}:host(.button-clear){--background-activated:transparent;--background-activated-opacity:0;--background-focused:var(--ion-color-primary, #0054e9);--background-hover:transparent;--background-focused-opacity:.1;font-size:min(1.0625rem, 51px);font-weight:normal}:host(.in-buttons){font-size:clamp(17px, 1.0625rem, 21.08px);font-weight:400}:host(.button-large){--border-radius:16px;--padding-top:17px;--padding-start:1em;--padding-end:1em;--padding-bottom:17px;min-height:3.1em;font-size:min(1.25rem, 60px)}:host(.button-small){--border-radius:6px;--padding-top:4px;--padding-start:0.9em;--padding-end:0.9em;--padding-bottom:4px;min-height:2.1em;font-size:min(0.8125rem, 39px)}:host(.button-round){--border-radius:999px;--padding-top:0;--padding-start:26px;--padding-end:26px;--padding-bottom:0}:host(.button-strong){font-weight:600}:host(.button-has-icon-only){--padding-top:0;--padding-bottom:var(--padding-top);--padding-end:var(--padding-top);--padding-start:var(--padding-end);min-width:clamp(30px, 2.125em, 60px);min-height:clamp(30px, 2.125em, 60px)}::slotted(ion-icon[slot=icon-only]){font-size:clamp(15.12px, 1.125em, 43.02px)}:host(.button-small.button-has-icon-only){min-width:clamp(23px, 2.16em, 54px);min-height:clamp(23px, 2.16em, 54px)}:host(.button-small) ::slotted(ion-icon[slot=icon-only]){font-size:clamp(12.1394px, 1.308125em, 40.1856px)}:host(.button-large.button-has-icon-only){min-width:clamp(46px, 2.5em, 78px);min-height:clamp(46px, 2.5em, 78px)}:host(.button-large) ::slotted(ion-icon[slot=icon-only]){font-size:clamp(15.12px, 0.9em, 43.056px)}:host(.button-outline.ion-focused.ion-color) .button-native,:host(.button-clear.ion-focused.ion-color) .button-native{color:var(--ion-color-base)}:host(.button-outline.ion-focused.ion-color) .button-native::after,:host(.button-clear.ion-focused.ion-color) .button-native::after{background:var(--ion-color-base)}:host(.button-solid.ion-color.ion-focused) .button-native::after{background:var(--ion-color-shade)}@media (any-hover: hover){:host(.button-clear:not(.ion-activated):hover),:host(.button-outline:not(.ion-activated):hover){opacity:0.6}:host(.button-clear.ion-color:hover) .button-native,:host(.button-outline.ion-color:hover) .button-native{color:var(--ion-color-base)}:host(.button-clear.ion-color:hover) .button-native::after,:host(.button-outline.ion-color:hover) .button-native::after{background:transparent}:host(.button-solid.ion-color:hover) .button-native::after{background:var(--ion-color-tint)}:host(:hover.button-solid.in-toolbar:not(.ion-color):not(.in-toolbar-color):not(.ion-activated)) .button-native::after{background:#fff;opacity:0.1}}:host(.button-clear.ion-activated){opacity:0.4}:host(.button-outline.ion-activated.ion-color) .button-native{color:var(--ion-color-contrast)}:host(.button-outline.ion-activated.ion-color) .button-native::after{background:var(--ion-color-base)}:host(.button-solid.ion-color.ion-activated) .button-native::after{background:var(--ion-color-shade)}:host(.button-outline.ion-activated.in-toolbar:not(.ion-color):not(.in-toolbar-color)) .button-native{background:var(--ion-toolbar-color, var(--color));color:var(--ion-toolbar-background, var(--background), var(--ion-color-primary-contrast, #fff))}',Ok=`:host{--overflow:hidden;--ripple-color:currentColor;--border-width:initial;--border-color:initial;--border-style:initial;--color-activated:var(--color);--color-focused:var(--color);--color-hover:var(--color);--box-shadow:none;display:inline-block;width:auto;color:var(--color);font-family:var(--ion-font-family, inherit);text-align:center;text-decoration:none;white-space:normal;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none;vertical-align:top;vertical-align:-webkit-baseline-middle;-webkit-font-kerning:none;font-kerning:none}:host(.button-disabled){cursor:default;opacity:0.5;pointer-events:none}:host(.button-solid){--background:var(--ion-color-primary, #0054e9);--color:var(--ion-color-primary-contrast, #fff)}:host(.button-outline){--border-color:var(--ion-color-primary, #0054e9);--background:transparent;--color:var(--ion-color-primary, #0054e9)}:host(.button-clear){--border-width:0;--background:transparent;--color:var(--ion-color-primary, #0054e9)}:host(.button-block){display:block}:host(.button-block) .button-native{margin-left:0;margin-right:0;width:100%;clear:both;contain:content}:host(.button-block) .button-native::after{clear:both}:host(.button-full){display:block}:host(.button-full) .button-native{margin-left:0;margin-right:0;width:100%;contain:content}:host(.button-full:not(.button-round)) .button-native{border-radius:0;border-right-width:0;border-left-width:0}.button-native{border-radius:var(--border-radius);-moz-osx-font-smoothing:grayscale;-webkit-font-smoothing:antialiased;margin-left:0;margin-right:0;margin-top:0;margin-bottom:0;-webkit-padding-start:var(--padding-start);padding-inline-start:var(--padding-start);-webkit-padding-end:var(--padding-end);padding-inline-end:var(--padding-end);padding-top:var(--padding-top);padding-bottom:var(--padding-bottom);font-family:inherit;font-size:inherit;font-style:inherit;font-weight:inherit;letter-spacing:inherit;text-decoration:inherit;text-indent:inherit;text-overflow:inherit;text-transform:inherit;text-align:inherit;white-space:inherit;color:inherit;display:-ms-flexbox;display:flex;position:relative;-ms-flex-align:center;align-items:center;width:100%;height:100%;min-height:inherit;-webkit-transition:var(--transition);transition:var(--transition);border-width:var(--border-width);border-style:var(--border-style);border-color:var(--border-color);outline:none;background:var(--background);line-height:1;-webkit-box-shadow:var(--box-shadow);box-shadow:var(--box-shadow);contain:layout style;cursor:pointer;opacity:var(--opacity);overflow:var(--overflow);z-index:0;-webkit-box-sizing:border-box;box-sizing:border-box;-webkit-appearance:none;-moz-appearance:none;appearance:none}.button-native::-moz-focus-inner{border:0}.button-inner{display:-ms-flexbox;display:flex;position:relative;-ms-flex-flow:row nowrap;flex-flow:row nowrap;-ms-flex-negative:0;flex-shrink:0;-ms-flex-align:center;align-items:center;-ms-flex-pack:center;justify-content:center;width:100%;height:100%;z-index:1}::slotted([slot=start]),::slotted([slot=end]){-ms-flex-negative:0;flex-shrink:0}::slotted(ion-icon){font-size:1.35em;pointer-events:none}::slotted(ion-icon[slot=start]){-webkit-margin-start:-0.3em;margin-inline-start:-0.3em;-webkit-margin-end:0.3em;margin-inline-end:0.3em;margin-top:0;margin-bottom:0}::slotted(ion-icon[slot=end]){-webkit-margin-start:0.3em;margin-inline-start:0.3em;-webkit-margin-end:-0.2em;margin-inline-end:-0.2em;margin-top:0;margin-bottom:0}ion-ripple-effect{color:var(--ripple-color)}.button-native::after{left:0;right:0;top:0;bottom:0;position:absolute;content:"";opacity:0}:host(.ion-focused){color:var(--color-focused)}:host(.ion-focused) .button-native::after{background:var(--background-focused);opacity:var(--background-focused-opacity)}@media (any-hover: hover){:host(:hover){color:var(--color-hover)}:host(:hover) .button-native::after{background:var(--background-hover);opacity:var(--background-hover-opacity)}}:host(.ion-activated){color:var(--color-activated)}:host(.ion-activated) .button-native::after{background:var(--background-activated);opacity:var(--background-activated-opacity)}:host(.button-solid.ion-color) .button-native{background:var(--ion-color-base);color:var(--ion-color-contrast)}:host(.button-outline.ion-color) .button-native{border-color:var(--ion-color-base);background:transparent;color:var(--ion-color-base)}:host(.button-clear.ion-color) .button-native{background:transparent;color:var(--ion-color-base)}:host(.in-toolbar:not(.ion-color):not(.in-toolbar-color)) .button-native{color:var(--ion-toolbar-color, var(--color))}:host(.button-outline.in-toolbar:not(.ion-color):not(.in-toolbar-color)) .button-native{border-color:var(--ion-toolbar-color, var(--color, var(--border-color)))}:host(.button-solid.in-toolbar:not(.ion-color):not(.in-toolbar-color)) .button-native{background:var(--ion-toolbar-color, var(--background));color:var(--ion-toolbar-background, var(--color))}:host{--border-radius:4px;--padding-top:8px;--padding-bottom:8px;--padding-start:1.1em;--padding-end:1.1em;--transition:box-shadow 280ms cubic-bezier(.4, 0, .2, 1),
                background-color 15ms linear,
                color 15ms linear;-webkit-margin-start:2px;margin-inline-start:2px;-webkit-margin-end:2px;margin-inline-end:2px;margin-top:4px;margin-bottom:4px;min-height:36px;font-size:0.875rem;font-weight:500;letter-spacing:0.06em;text-transform:uppercase}:host(.button-solid){--background-activated:transparent;--background-hover:var(--ion-color-primary-contrast, #fff);--background-focused:var(--ion-color-primary-contrast, #fff);--background-activated-opacity:0;--background-focused-opacity:.24;--background-hover-opacity:.08;--box-shadow:0 3px 1px -2px rgba(0, 0, 0, 0.2), 0 2px 2px 0 rgba(0, 0, 0, 0.14), 0 1px 5px 0 rgba(0, 0, 0, 0.12)}:host(.button-solid.ion-activated){--box-shadow:0 5px 5px -3px rgba(0, 0, 0, 0.2), 0 8px 10px 1px rgba(0, 0, 0, 0.14), 0 3px 14px 2px rgba(0, 0, 0, 0.12)}:host(.button-outline){--border-width:2px;--border-style:solid;--box-shadow:none;--background-activated:transparent;--background-focused:var(--ion-color-primary, #0054e9);--background-hover:var(--ion-color-primary, #0054e9);--background-activated-opacity:0;--background-focused-opacity:.12;--background-hover-opacity:.04}:host(.button-outline.ion-activated.ion-color) .button-native{background:transparent}:host(.button-clear){--background-activated:transparent;--background-focused:var(--ion-color-primary, #0054e9);--background-hover:var(--ion-color-primary, #0054e9);--background-activated-opacity:0;--background-focused-opacity:.12;--background-hover-opacity:.04}:host(.button-round){--border-radius:999px;--padding-top:0;--padding-start:26px;--padding-end:26px;--padding-bottom:0}:host(.button-large){--padding-top:14px;--padding-start:1em;--padding-end:1em;--padding-bottom:14px;min-height:2.8em;font-size:1.25rem}:host(.button-small){--padding-top:4px;--padding-start:0.9em;--padding-end:0.9em;--padding-bottom:4px;min-height:2.1em;font-size:0.8125rem}:host(.button-strong){font-weight:bold}:host(.button-has-icon-only){--padding-top:0;--padding-bottom:var(--padding-top);--padding-end:var(--padding-top);--padding-start:var(--padding-end);min-width:clamp(30px, 2.86em, 60px);min-height:clamp(30px, 2.86em, 60px)}::slotted(ion-icon[slot=icon-only]){font-size:clamp(15.104px, 1.6em, 43.008px)}:host(.button-small.button-has-icon-only){min-width:clamp(23px, 2.16em, 54px);min-height:clamp(23px, 2.16em, 54px)}:host(.button-small) ::slotted(ion-icon[slot=icon-only]){font-size:clamp(13.002px, 1.23125em, 40.385px)}:host(.button-large.button-has-icon-only){min-width:clamp(46px, 2.5em, 78px);min-height:clamp(46px, 2.5em, 78px)}:host(.button-large) ::slotted(ion-icon[slot=icon-only]){font-size:clamp(15.008px, 1.4em, 43.008px)}:host(.button-solid.ion-color.ion-focused) .button-native::after{background:var(--ion-color-contrast)}:host(.button-clear.ion-color.ion-focused) .button-native::after,:host(.button-outline.ion-color.ion-focused) .button-native::after{background:var(--ion-color-base)}@media (any-hover: hover){:host(.button-solid.ion-color:hover) .button-native::after{background:var(--ion-color-contrast)}:host(.button-clear.ion-color:hover) .button-native::after,:host(.button-outline.ion-color:hover) .button-native::after{background:var(--ion-color-base)}}:host(.button-outline.ion-activated.in-toolbar:not(.ion-color):not(.in-toolbar-color)) .button-native{background:var(--ion-toolbar-background, var(--color));color:var(--ion-toolbar-color, var(--background), var(--ion-color-primary-contrast, #fff))}`,W0=Q(class extends Z{constructor(){super(),this.__registerHost(),this.__attachShadow(),this.ionFocus=N(this,"ionFocus",7),this.ionBlur=N(this,"ionBlur",7),this.inItem=!1,this.inListHeader=!1,this.inToolbar=!1,this.formButtonEl=null,this.formEl=null,this.inheritedAttributes={},this.isCircle=!1,this.buttonType="button",this.disabled=!1,this.routerDirection="forward",this.strong=!1,this.type="button",this.handleClick=t=>{let{el:n}=this;this.type==="button"?Bl(this.href,t,this.routerDirection,this.routerAnimation):nh(n)&&this.submitForm(t)},this.onFocus=()=>{this.ionFocus.emit()},this.onBlur=()=>{this.ionBlur.emit()},this.slotChanged=()=>{this.isCircle=this.hasIconOnly}}disabledChanged(){let{disabled:t}=this;this.formButtonEl&&(this.formButtonEl.disabled=t)}onAriaChanged(t,n,o){this.inheritedAttributes=Object.assign(Object.assign({},this.inheritedAttributes),{[o]:t}),ut(this)}renderHiddenButton(){let t=this.formEl=this.findForm();if(t){let{formButtonEl:n}=this;if(n!==null&&t.contains(n))return;let o=this.formButtonEl=document.createElement("button");o.type=this.type,o.style.display="none",o.disabled=this.disabled,t.appendChild(o)}}componentWillLoad(){this.inToolbar=!!this.el.closest("ion-buttons"),this.inListHeader=!!this.el.closest("ion-list-header"),this.inItem=!!this.el.closest("ion-item")||!!this.el.closest("ion-item-divider"),this.inheritedAttributes=bn(this.el)}get hasIconOnly(){return!!this.el.querySelector('[slot="icon-only"]')}get rippleType(){return(this.fill===void 0||this.fill==="clear")&&this.hasIconOnly&&this.inToolbar?"unbounded":"bounded"}findForm(){let{form:t}=this;if(t instanceof HTMLFormElement)return t;if(typeof t=="string"){let n=document.getElementById(t);return n?n instanceof HTMLFormElement?n:(Le(`[ion-button] - Form with selector: "#${t}" could not be found. Verify that the id is attached to a <form> element.`,this.el),null):(Le(`[ion-button] - Form with selector: "#${t}" could not be found. Verify that the id is correct and the form is rendered in the DOM.`,this.el),null)}return t!==void 0?(Le('[ion-button] - The provided "form" element is invalid. Verify that the form is a HTMLFormElement and rendered in the DOM.',this.el),null):this.el.closest("form")}submitForm(t){this.formEl&&this.formButtonEl&&(t.preventDefault(),this.formButtonEl.click())}render(){let t=F(this),{buttonType:n,type:o,disabled:i,rel:r,target:s,size:a,href:l,color:c,expand:u,hasIconOnly:d,shape:p,strong:f,inheritedAttributes:h}=this,y=a===void 0&&this.inItem?"small":a,g=l===void 0?"button":"a",w=g==="button"?{type:o}:{download:this.download,href:l,rel:r,target:s},P=this.fill;return P==null&&(P=this.inToolbar||this.inListHeader?"clear":"solid"),o!=="button"&&this.renderHiddenButton(),m(J,{key:"b105ad09215adb3ca2298acdadf0dc9154bbb9b0",onClick:this.handleClick,"aria-disabled":i?"true":null,class:et(c,{[t]:!0,[n]:!0,[`${n}-${u}`]:u!==void 0,[`${n}-${y}`]:y!==void 0,[`${n}-${p}`]:p!==void 0,[`${n}-${P}`]:!0,[`${n}-strong`]:f,"in-toolbar":ke("ion-toolbar",this.el),"in-toolbar-color":ke("ion-toolbar[color]",this.el),"in-buttons":ke("ion-buttons",this.el),"button-has-icon-only":d,"button-disabled":i,"ion-activatable":!0,"ion-focusable":!0})},m(g,Object.assign({key:"66b4e7112bcb9e41d5a723fbbadb0a3104f9ee1d"},w,{class:"button-native",part:"native",disabled:i,onFocus:this.onFocus,onBlur:this.onBlur},h),m("span",{key:"1439fc3da280221028dcf7ce8ec9dab273c4d4bb",class:"button-inner"},m("slot",{key:"d5269ae1afc87ec7b99746032f59cbae93720a9f",name:"icon-only",onSlotchange:this.slotChanged}),m("slot",{key:"461c83e97aa246aa86d83e14f1e15a288d35041e",name:"start"}),m("slot",{key:"807170d47101f9f6a333dd4ff489c89284f306fe"}),m("slot",{key:"e67f116dd0349a0d27893e4f3ff0ccef1d402f80",name:"end"})),t==="md"&&m("ion-ripple-effect",{key:"273f0bd9645a36c1bfd18a5c2ab4f81e22b7b989",type:this.rippleType})))}get el(){return this}static get watchers(){return{disabled:["disabledChanged"],"aria-checked":["onAriaChanged"],"aria-label":["onAriaChanged"]}}static get style(){return{ios:Nk,md:Ok}}},[33,"ion-button",{color:[513],buttonType:[1025,"button-type"],disabled:[516],expand:[513],fill:[1537],routerDirection:[1,"router-direction"],routerAnimation:[16,"router-animation"],download:[1],href:[1],rel:[1],shape:[513],size:[513],strong:[4],target:[1],type:[1],form:[1],isCircle:[32]},void 0,{disabled:["disabledChanged"],"aria-checked":["onAriaChanged"],"aria-label":["onAriaChanged"]}]);function Y0(){if(typeof customElements>"u")return;["ion-button","ion-ripple-effect"].forEach(t=>{switch(t){case"ion-button":customElements.get(t)||customElements.define(t,W0);break;case"ion-ripple-effect":customElements.get(t)||ec();break}})}var Z0=Y0;var Pk=':host{--background:var(--ion-background-color, #fff);--color:var(--ion-text-color, #000);--padding-top:0px;--padding-bottom:0px;--padding-start:0px;--padding-end:0px;--keyboard-offset:0px;--offset-top:0px;--offset-bottom:0px;--overflow:auto;display:block;position:relative;-ms-flex:1;flex:1;width:100%;height:100%;margin:0 !important;padding:0 !important;font-family:var(--ion-font-family, inherit);contain:size style}:host(.ion-color) .inner-scroll{background:var(--ion-color-base);color:var(--ion-color-contrast)}#background-content{left:0px;right:0px;top:calc(var(--offset-top) * -1);bottom:calc(var(--offset-bottom) * -1);position:absolute;background:var(--background)}.inner-scroll{left:0px;right:0px;top:calc(var(--offset-top) * -1);bottom:calc(var(--offset-bottom) * -1);-webkit-padding-start:var(--padding-start);padding-inline-start:var(--padding-start);-webkit-padding-end:var(--padding-end);padding-inline-end:var(--padding-end);padding-top:calc(var(--padding-top) + var(--offset-top));padding-bottom:calc(var(--padding-bottom) + var(--keyboard-offset) + var(--offset-bottom));position:absolute;color:var(--color);-webkit-box-sizing:border-box;box-sizing:border-box;overflow:hidden;-ms-touch-action:pan-x pan-y pinch-zoom;touch-action:pan-x pan-y pinch-zoom}.scroll-y,.scroll-x{-webkit-overflow-scrolling:touch;z-index:0;will-change:scroll-position}.scroll-y{overflow-y:var(--overflow);overscroll-behavior-y:contain}.scroll-x{overflow-x:var(--overflow);overscroll-behavior-x:contain}.overscroll::before,.overscroll::after{position:absolute;width:1px;height:1px;content:""}.overscroll::before{bottom:-1px}.overscroll::after{top:-1px}:host(.content-sizing){display:-ms-flexbox;display:flex;-ms-flex-direction:column;flex-direction:column;min-height:0;contain:none}:host(.content-sizing) .inner-scroll{position:relative;top:0;bottom:0;margin-top:calc(var(--offset-top) * -1);margin-bottom:calc(var(--offset-bottom) * -1)}.transition-effect{display:none;position:absolute;width:100%;height:100vh;opacity:0;pointer-events:none}:host(.content-ltr) .transition-effect{left:-100%;}:host(.content-rtl) .transition-effect{right:-100%;}.transition-cover{position:absolute;right:0;width:100%;height:100%;background:black;opacity:0.1}.transition-shadow{display:block;position:absolute;width:100%;height:100%;-webkit-box-shadow:inset -9px 0 9px 0 rgba(0, 0, 100, 0.03);box-shadow:inset -9px 0 9px 0 rgba(0, 0, 100, 0.03)}:host(.content-ltr) .transition-shadow{right:0;}:host(.content-rtl) .transition-shadow{left:0;-webkit-transform:scaleX(-1);transform:scaleX(-1)}::slotted([slot=fixed]){position:absolute;-webkit-transform:translateZ(0);transform:translateZ(0)}',Q0=Q(class extends Z{constructor(){super(),this.__registerHost(),this.__attachShadow(),this.ionScrollStart=N(this,"ionScrollStart",7),this.ionScroll=N(this,"ionScroll",7),this.ionScrollEnd=N(this,"ionScrollEnd",7),this.watchDog=null,this.isScrolling=!1,this.lastScroll=0,this.queued=!1,this.cTop=-1,this.cBottom=-1,this.isMainContent=!0,this.resizeTimeout=null,this.inheritedAttributes={},this.tabsElement=null,this.detail={scrollTop:0,scrollLeft:0,type:"scroll",event:void 0,startX:0,startY:0,startTime:0,currentX:0,currentY:0,velocityX:0,velocityY:0,deltaX:0,deltaY:0,currentTime:0,data:void 0,isScrolling:!0},this.fullscreen=!1,this.fixedSlotPlacement="after",this.scrollX=!1,this.scrollY=!0,this.scrollEvents=!1}componentWillLoad(){this.inheritedAttributes=bn(this.el)}connectedCallback(){if(this.isMainContent=this.el.closest("ion-menu, ion-popover, ion-modal")===null,Zt(this.el)){let t=this.tabsElement=this.el.closest("ion-tabs");t!==null&&(this.tabsLoadCallback=()=>this.resize(),t.addEventListener("ionTabBarLoaded",this.tabsLoadCallback))}}disconnectedCallback(){if(this.onScrollEnd(),Zt(this.el)){let{tabsElement:t,tabsLoadCallback:n}=this;t!==null&&n!==void 0&&t.removeEventListener("ionTabBarLoaded",n),this.tabsElement=null,this.tabsLoadCallback=void 0}}onResize(){this.resizeTimeout&&(clearTimeout(this.resizeTimeout),this.resizeTimeout=null),this.resizeTimeout=setTimeout(()=>{this.el.offsetParent!==null&&this.resize()},100)}shouldForceOverscroll(){let{forceOverscroll:t}=this,n=F(this);return t===void 0?n==="ios"&&Pe("ios"):t}resize(){ct.isBrowser&&(this.fullscreen?vn(()=>this.readDimensions()):(this.cTop!==0||this.cBottom!==0)&&(this.cTop=this.cBottom=0,ut(this)))}readDimensions(){let t=Lk(this.el),n=Math.max(this.el.offsetTop,0),o=Math.max(t.offsetHeight-n-this.el.offsetHeight,0);(n!==this.cTop||o!==this.cBottom)&&(this.cTop=n,this.cBottom=o,ut(this))}onScroll(t){let n=Date.now(),o=!this.isScrolling;this.lastScroll=n,o&&this.onScrollStart(),!this.queued&&this.scrollEvents&&(this.queued=!0,vn(i=>{this.queued=!1,this.detail.event=t,jk(this.detail,this.scrollEl,i,o),this.ionScroll.emit(this.detail)}))}getScrollElement(){return C(this,null,function*(){return this.scrollEl||(yield new Promise(t=>tt(this.el,t))),Promise.resolve(this.scrollEl)})}getBackgroundElement(){return C(this,null,function*(){return this.backgroundContentEl||(yield new Promise(t=>tt(this.el,t))),Promise.resolve(this.backgroundContentEl)})}scrollToTop(t=0){return this.scrollToPoint(void 0,0,t)}scrollToBottom(t=0){return C(this,null,function*(){let n=yield this.getScrollElement(),o=n.scrollHeight-n.clientHeight;return this.scrollToPoint(void 0,o,t)})}scrollByPoint(t,n,o){return C(this,null,function*(){let i=yield this.getScrollElement();return this.scrollToPoint(t+i.scrollLeft,n+i.scrollTop,o)})}scrollToPoint(t,n,o=0){return C(this,null,function*(){let i=yield this.getScrollElement();if(o<32){n!=null&&(i.scrollTop=n),t!=null&&(i.scrollLeft=t);return}let r,s=0,a=new Promise(f=>r=f),l=i.scrollTop,c=i.scrollLeft,u=n!=null?n-l:0,d=t!=null?t-c:0,p=f=>{let h=Math.min(1,(f-s)/o)-1,y=Math.pow(h,3)+1;u!==0&&(i.scrollTop=Math.floor(y*u+l)),d!==0&&(i.scrollLeft=Math.floor(y*d+c)),y<1?requestAnimationFrame(p):r()};return requestAnimationFrame(f=>{s=f,p(f)}),a})}onScrollStart(){this.isScrolling=!0,this.ionScrollStart.emit({isScrolling:!0}),this.watchDog&&clearInterval(this.watchDog),this.watchDog=setInterval(()=>{this.lastScroll<Date.now()-120&&this.onScrollEnd()},100)}onScrollEnd(){this.watchDog&&clearInterval(this.watchDog),this.watchDog=null,this.isScrolling&&(this.isScrolling=!1,this.ionScrollEnd.emit({isScrolling:!1}))}render(){let{fixedSlotPlacement:t,inheritedAttributes:n,isMainContent:o,scrollX:i,scrollY:r,el:s}=this,a=ah(s)?"rtl":"ltr",l=F(this),c=this.shouldForceOverscroll(),u=l==="ios";return this.resize(),m(J,Object.assign({key:"f2a24aa66dbf5c76f9d4b06f708eb73cadc239df",role:o?"main":void 0,class:et(this.color,{[l]:!0,"content-sizing":ke("ion-popover",this.el),overscroll:c,[`content-${a}`]:!0}),style:{"--offset-top":`${this.cTop}px`,"--offset-bottom":`${this.cBottom}px`}},n),m("div",{key:"6480ca7648b278abb36477b3838bccbcd4995e2a",ref:d=>this.backgroundContentEl=d,id:"background-content",part:"background"}),t==="before"?m("slot",{name:"fixed"}):null,m("div",{key:"29a23b663f5f0215bb000820c01e1814c0d55985",class:{"inner-scroll":!0,"scroll-x":i,"scroll-y":r,overscroll:(i||r)&&c},ref:d=>this.scrollEl=d,onScroll:this.scrollEvents?d=>this.onScroll(d):void 0,part:"scroll"},m("slot",{key:"0fe1bd05609a4b88ae2ce9addf5d5dc5dc1806f0"})),u?m("div",{class:"transition-effect"},m("div",{class:"transition-cover"}),m("div",{class:"transition-shadow"})):null,t==="after"?m("slot",{name:"fixed"}):null)}get el(){return this}static get style(){return Pk}},[1,"ion-content",{color:[513],fullscreen:[4],fixedSlotPlacement:[1,"fixed-slot-placement"],forceOverscroll:[1028,"force-overscroll"],scrollX:[4,"scroll-x"],scrollY:[4,"scroll-y"],scrollEvents:[4,"scroll-events"],getScrollElement:[64],getBackgroundElement:[64],scrollToTop:[64],scrollToBottom:[64],scrollByPoint:[64],scrollToPoint:[64]},[[9,"resize","onResize"]]]),Fk=e=>{var t;return e.parentElement?e.parentElement:!((t=e.parentNode)===null||t===void 0)&&t.host?e.parentNode.host:null},Lk=e=>{let t=e.closest("ion-tabs");if(t)return t;let n=e.closest("ion-app, ion-page, .ion-page, page-inner, .popover-content");return n||Fk(e)},jk=(e,t,n,o)=>{let i=e.currentX,r=e.currentY,s=e.currentTime,a=t.scrollLeft,l=t.scrollTop,c=n-s;if(o&&(e.startTime=n,e.startX=a,e.startY=l,e.velocityX=e.velocityY=0),e.currentTime=n,e.currentX=e.scrollLeft=a,e.currentY=e.scrollTop=l,e.deltaX=a-e.startX,e.deltaY=l-e.startY,c>0&&c<100){let u=(a-i)/c,d=(l-r)/c;e.velocityX=u*.7+e.velocityX*.3,e.velocityY=d*.7+e.velocityY*.3}};function K0(){if(typeof customElements>"u")return;["ion-content"].forEach(t=>{switch(t){case"ion-content":customElements.get(t)||customElements.define(t,Q0);break}})}var X0=K0;var Vk="all 0.2s ease-in-out",J0=e=>{let t=document.querySelector(`${e}.ion-cloned-element`);if(t!==null)return t;let n=document.createElement(e);return n.classList.add("ion-cloned-element"),n.style.setProperty("display","none"),document.body.appendChild(n),n},ew=e=>{if(!e)return;let t=e.querySelectorAll("ion-toolbar");return{el:e,toolbars:Array.from(t).map(n=>{let o=n.querySelector("ion-title");return{el:n,background:n.shadowRoot.querySelector(".toolbar-background"),ionTitleEl:o,innerTitleEl:o?o.shadowRoot.querySelector(".toolbar-title"):null,ionButtonsEl:Array.from(n.querySelectorAll("ion-buttons"))}})}},Bk=(e,t,n)=>{vn(()=>{let o=e.scrollTop,i=wn(1,1+-o/500,1.1);n.querySelector("ion-refresher.refresher-native")===null&&wt(()=>{zk(t.toolbars,i)})})},Pp=(e,t)=>{e.collapse!=="fade"&&(t===void 0?e.style.removeProperty("--opacity-scale"):e.style.setProperty("--opacity-scale",t.toString()))},Hk=(e,t,n)=>{if(!e[0].isIntersecting)return;let o=e[0].intersectionRatio>.9||n<=0?0:(1-e[0].intersectionRatio)*100/75;Pp(t.el,o===1?void 0:o)},$k=(e,t,n,o)=>{wt(()=>{let i=o.scrollTop;Hk(e,t,i);let r=e[0],s=r.intersectionRect,a=s.width*s.height,l=r.rootBounds.width*r.rootBounds.height,c=a===0&&l===0,u=Math.abs(s.left-r.boundingClientRect.left),d=Math.abs(s.right-r.boundingClientRect.right),p=a>0&&(u>=5||d>=5);c||p||(r.isIntersecting?(ss(t,!1),ss(n)):(s.x===0&&s.y===0||s.width!==0&&s.height!==0)&&i>0&&(ss(t),ss(n,!1),Pp(t.el)))})},ss=(e,t=!0)=>{let n=e.el,i=e.toolbars.map(r=>r.ionTitleEl);t?(n.classList.remove("header-collapse-condense-inactive"),i.forEach(r=>{r&&r.removeAttribute("aria-hidden")})):(n.classList.add("header-collapse-condense-inactive"),i.forEach(r=>{r&&r.setAttribute("aria-hidden","true")}))},zk=(e=[],t=1,n=!1)=>{e.forEach(o=>{let i=o.ionTitleEl,r=o.innerTitleEl;!i||i.size!=="large"||(r.style.transition=n?Vk:"",r.style.transform=`scale3d(${t}, ${t}, 1)`)})},tw=(e,t,n)=>{vn(()=>{let o=e.scrollTop,i=t.clientHeight,r=n?n.clientHeight:0;if(n!==null&&o<r){t.style.setProperty("--opacity-scale","0"),e.style.setProperty("clip-path",`inset(${i}px 0px 0px 0px)`);return}let s=o-r,l=wn(0,s/10,1);wt(()=>{e.style.removeProperty("clip-path"),t.style.setProperty("--opacity-scale",l.toString())})})},Uk="ion-header{display:block;position:relative;-ms-flex-order:-1;order:-1;width:100%;z-index:10}ion-header ion-toolbar:first-of-type{padding-top:var(--ion-safe-area-top, 0)}.header-ios ion-toolbar:last-of-type{--border-width:0 0 0.55px}@supports ((-webkit-backdrop-filter: blur(0)) or (backdrop-filter: blur(0))){.header-background{left:0;right:0;top:0;bottom:0;position:absolute;-webkit-backdrop-filter:saturate(180%) blur(20px);backdrop-filter:saturate(180%) blur(20px)}.header-translucent-ios ion-toolbar{--opacity:.8}.header-collapse-condense-inactive .header-background{-webkit-backdrop-filter:blur(20px);backdrop-filter:blur(20px)}}.header-ios.ion-no-border ion-toolbar:last-of-type{--border-width:0}.header-collapse-fade ion-toolbar{--opacity-scale:inherit}.header-collapse-condense{z-index:9}.header-collapse-condense ion-toolbar{position:-webkit-sticky;position:sticky;top:0}.header-collapse-condense ion-toolbar:first-of-type{padding-top:0px;z-index:1}.header-collapse-condense ion-toolbar{--background:var(--ion-background-color, #fff);z-index:0}.header-collapse-condense ion-toolbar:last-of-type{--border-width:0px}.header-collapse-condense ion-toolbar ion-searchbar{padding-top:0px;padding-bottom:13px}.header-collapse-main{--opacity-scale:1}.header-collapse-main ion-toolbar{--opacity-scale:inherit}.header-collapse-main ion-toolbar.in-toolbar ion-title,.header-collapse-main ion-toolbar.in-toolbar ion-buttons{-webkit-transition:all 0.2s ease-in-out;transition:all 0.2s ease-in-out}.header-collapse-condense-inactive:not(.header-collapse-condense) ion-toolbar.in-toolbar ion-title,.header-collapse-condense-inactive:not(.header-collapse-condense) ion-toolbar.in-toolbar ion-buttons.buttons-collapse{opacity:0;pointer-events:none}.header-collapse-condense-inactive.header-collapse-condense ion-toolbar.in-toolbar ion-title,.header-collapse-condense-inactive.header-collapse-condense ion-toolbar.in-toolbar ion-buttons.buttons-collapse{visibility:hidden}ion-header.header-ios:not(.header-collapse-main):has(~ion-content ion-header.header-ios[collapse=condense],~ion-content ion-header.header-ios.header-collapse-condense){opacity:0}",qk="ion-header{display:block;position:relative;-ms-flex-order:-1;order:-1;width:100%;z-index:10}ion-header ion-toolbar:first-of-type{padding-top:var(--ion-safe-area-top, 0)}.header-md{-webkit-box-shadow:0 2px 4px -1px rgba(0, 0, 0, 0.2), 0 4px 5px 0 rgba(0, 0, 0, 0.14), 0 1px 10px 0 rgba(0, 0, 0, 0.12);box-shadow:0 2px 4px -1px rgba(0, 0, 0, 0.2), 0 4px 5px 0 rgba(0, 0, 0, 0.14), 0 1px 10px 0 rgba(0, 0, 0, 0.12)}.header-collapse-condense{display:none}.header-md.ion-no-border{-webkit-box-shadow:none;box-shadow:none}",nw=Q(class extends Z{constructor(){super(),this.__registerHost(),this.inheritedAttributes={},this.translucent=!1,this.setupFadeHeader=(t,n)=>C(this,null,function*(){let o=this.scrollEl=yield vc(t);this.contentScrollCallback=()=>{tw(this.scrollEl,this.el,n)},o.addEventListener("scroll",this.contentScrollCallback),tw(this.scrollEl,this.el,n)})}componentWillLoad(){this.inheritedAttributes=bn(this.el)}componentDidLoad(){this.checkCollapsibleHeader()}componentDidUpdate(){this.checkCollapsibleHeader()}disconnectedCallback(){this.destroyCollapsibleHeader()}checkCollapsibleHeader(){return C(this,null,function*(){if(F(this)!=="ios")return;let{collapse:n}=this,o=n==="condense",i=n==="fade";if(this.destroyCollapsibleHeader(),o){let r=this.el.closest("ion-app,ion-page,.ion-page,page-inner"),s=r?Hi(r):null;wt(()=>{let a=J0("ion-title");a.size="large",J0("ion-back-button")}),yield this.setupCondenseHeader(s,r)}else if(i){let r=this.el.closest("ion-app,ion-page,.ion-page,page-inner"),s=r?Hi(r):null;if(!s){$i(this.el);return}let a=s.querySelector('ion-header[collapse="condense"]');yield this.setupFadeHeader(s,a)}})}destroyCollapsibleHeader(){this.intersectionObserver&&(this.intersectionObserver.disconnect(),this.intersectionObserver=void 0),this.scrollEl&&this.contentScrollCallback&&(this.scrollEl.removeEventListener("scroll",this.contentScrollCallback),this.contentScrollCallback=void 0),this.collapsibleMainHeader&&(this.collapsibleMainHeader.classList.remove("header-collapse-main"),this.collapsibleMainHeader=void 0)}setupCondenseHeader(t,n){return C(this,null,function*(){if(!t||!n){$i(this.el);return}if(typeof IntersectionObserver>"u")return;this.scrollEl=yield vc(t);let o=n.querySelectorAll("ion-header");if(this.collapsibleMainHeader=Array.from(o).find(a=>a.collapse!=="condense"),!this.collapsibleMainHeader)return;let i=ew(this.collapsibleMainHeader),r=ew(this.el);if(!i||!r)return;ss(i,!1),Pp(i.el,0);let s=a=>{$k(a,i,r,this.scrollEl)};this.intersectionObserver=new IntersectionObserver(s,{root:t,threshold:[.25,.3,.4,.5,.6,.7,.8,.9,1]}),this.intersectionObserver.observe(r.toolbars[r.toolbars.length-1].el),this.contentScrollCallback=()=>{Bk(this.scrollEl,r,t)},this.scrollEl.addEventListener("scroll",this.contentScrollCallback),wt(()=>{this.collapsibleMainHeader!==void 0&&this.collapsibleMainHeader.classList.add("header-collapse-main")})})}render(){let{translucent:t,inheritedAttributes:n}=this,o=F(this),i=this.collapse||"none",r=ke("ion-menu",this.el)?"none":"banner";return m(J,Object.assign({key:"b6cc27f0b08afc9fcc889683525da765d80ba672",role:r,class:{[o]:!0,[`header-${o}`]:!0,"header-translucent":this.translucent,[`header-collapse-${i}`]:!0,[`header-translucent-${o}`]:this.translucent}},n),o==="ios"&&t&&m("div",{key:"395766d4dcee3398bc91960db21f922095292f14",class:"header-background"}),m("slot",{key:"09a67ece27b258ff1248805d43d92a49b2c6859a"}))}get el(){return this}static get style(){return{ios:Uk,md:qk}}},[36,"ion-header",{collapse:[1],translucent:[4]}]);function ow(){if(typeof customElements>"u")return;["ion-header"].forEach(t=>{switch(t){case"ion-header":customElements.get(t)||customElements.define(t,nw);break}})}var iw=ow;var Gk=':host{--border-radius:0px;--border-width:0px;--border-style:solid;--padding-top:0px;--padding-bottom:0px;--padding-end:0px;--padding-start:0px;--inner-border-width:0px;--inner-padding-top:0px;--inner-padding-bottom:0px;--inner-padding-start:0px;--inner-padding-end:0px;--inner-box-shadow:none;--detail-icon-color:initial;--detail-icon-font-size:1.25em;--detail-icon-opacity:0.25;--color-activated:var(--color);--color-focused:var(--color);--color-hover:var(--color);--ripple-color:currentColor;-moz-osx-font-smoothing:grayscale;-webkit-font-smoothing:antialiased;display:block;position:relative;-ms-flex-align:center;align-items:center;-ms-flex-pack:justify;justify-content:space-between;outline:none;color:var(--color);font-family:var(--ion-font-family, inherit);text-align:initial;text-decoration:none;overflow:hidden;-webkit-box-sizing:border-box;box-sizing:border-box}:host(.ion-color) .item-native{background:var(--ion-color-base);color:var(--ion-color-contrast)}:host(.ion-color) .item-native,:host(.ion-color) .item-inner{border-color:var(--ion-color-shade)}:host(.ion-activated) .item-native{color:var(--color-activated)}:host(.ion-activated) .item-native::after{background:var(--background-activated);opacity:var(--background-activated-opacity)}:host(.ion-color.ion-activated) .item-native{color:var(--ion-color-contrast)}:host(.ion-focused) .item-native{color:var(--color-focused)}:host(.ion-focused) .item-native::after{background:var(--background-focused);opacity:var(--background-focused-opacity)}:host(.ion-color.ion-focused) .item-native{color:var(--ion-color-contrast)}:host(.ion-color.ion-focused) .item-native::after{background:var(--ion-color-contrast)}@media (any-hover: hover){:host(.ion-activatable:not(.ion-focused):hover) .item-native{color:var(--color-hover)}:host(.ion-activatable:not(.ion-focused):hover) .item-native::after{background:var(--background-hover);opacity:var(--background-hover-opacity)}:host(.ion-color.ion-activatable:not(.ion-focused):hover) .item-native{color:var(--ion-color-contrast)}:host(.ion-color.ion-activatable:not(.ion-focused):hover) .item-native::after{background:var(--ion-color-contrast)}}:host(.item-control-needs-pointer-cursor){cursor:pointer}:host(.item-interactive-disabled:not(.item-multiple-inputs)){cursor:default;pointer-events:none}:host(.item-disabled){cursor:default;opacity:0.3;pointer-events:none}.item-native{border-radius:var(--border-radius);margin-left:0;margin-right:0;margin-top:0;margin-bottom:0;padding-top:var(--padding-top);padding-bottom:var(--padding-bottom);font-family:inherit;font-size:inherit;font-style:inherit;font-weight:inherit;letter-spacing:inherit;text-decoration:inherit;text-indent:inherit;text-overflow:inherit;text-transform:inherit;text-align:inherit;white-space:inherit;color:inherit;padding-right:var(--padding-end);padding-left:calc(var(--padding-start) + var(--ion-safe-area-left, 0px));display:-ms-flexbox;display:flex;position:relative;-ms-flex-align:inherit;align-items:inherit;-ms-flex-pack:inherit;justify-content:inherit;width:100%;min-height:var(--min-height);-webkit-transition:var(--transition);transition:var(--transition);border-width:var(--border-width);border-style:var(--border-style);border-color:var(--border-color);outline:none;background:var(--background);overflow:inherit;z-index:1;-webkit-box-sizing:border-box;box-sizing:border-box}:host-context([dir=rtl]) .item-native{padding-right:calc(var(--padding-start) + var(--ion-safe-area-right, 0px));padding-left:var(--padding-end)}[dir=rtl] .item-native{padding-right:calc(var(--padding-start) + var(--ion-safe-area-right, 0px));padding-left:var(--padding-end)}@supports selector(:dir(rtl)){.item-native:dir(rtl){padding-right:calc(var(--padding-start) + var(--ion-safe-area-right, 0px));padding-left:var(--padding-end)}}.item-native::-moz-focus-inner{border:0}.item-native::after{left:0;right:0;top:0;bottom:0;position:absolute;content:"";opacity:0;-webkit-transition:var(--transition);transition:var(--transition);z-index:-1}button,a{cursor:pointer;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none;-webkit-user-drag:none}.item-inner{margin-left:0;margin-right:0;margin-top:0;margin-bottom:0;padding-top:var(--inner-padding-top);padding-bottom:var(--inner-padding-bottom);padding-right:calc(var(--ion-safe-area-right, 0px) + var(--inner-padding-end));padding-left:var(--inner-padding-start);display:-ms-flexbox;display:flex;position:relative;-ms-flex:1;flex:1;-ms-flex-direction:inherit;flex-direction:inherit;-ms-flex-align:inherit;align-items:inherit;-ms-flex-item-align:stretch;align-self:stretch;min-height:inherit;border-width:var(--inner-border-width);border-style:var(--border-style);border-color:var(--border-color);-webkit-box-shadow:var(--inner-box-shadow);box-shadow:var(--inner-box-shadow);overflow:inherit;-webkit-box-sizing:border-box;box-sizing:border-box}:host-context([dir=rtl]) .item-inner{padding-right:var(--inner-padding-start);padding-left:calc(var(--ion-safe-area-left, 0px) + var(--inner-padding-end))}[dir=rtl] .item-inner{padding-right:var(--inner-padding-start);padding-left:calc(var(--ion-safe-area-left, 0px) + var(--inner-padding-end))}@supports selector(:dir(rtl)){.item-inner:dir(rtl){padding-right:var(--inner-padding-start);padding-left:calc(var(--ion-safe-area-left, 0px) + var(--inner-padding-end))}}.item-detail-icon{-webkit-margin-start:calc(var(--inner-padding-end) / 2);margin-inline-start:calc(var(--inner-padding-end) / 2);-webkit-margin-end:-6px;margin-inline-end:-6px;color:var(--detail-icon-color);font-size:var(--detail-icon-font-size);opacity:var(--detail-icon-opacity)}::slotted(ion-icon){font-size:1.6em}::slotted(ion-button){--margin-top:0;--margin-bottom:0;--margin-start:0;--margin-end:0;z-index:1}::slotted(ion-label:not([slot=end])){-ms-flex:1;flex:1;width:-webkit-min-content;width:-moz-min-content;width:min-content;max-width:100%}:host(.item-input){-ms-flex-align:center;align-items:center}.input-wrapper{display:-ms-flexbox;display:flex;-ms-flex:1;flex:1;-ms-flex-direction:inherit;flex-direction:inherit;-ms-flex-align:inherit;align-items:inherit;-ms-flex-item-align:stretch;align-self:stretch;text-overflow:ellipsis;overflow:inherit;-webkit-box-sizing:border-box;box-sizing:border-box}:host(.item-label-stacked),:host(.item-label-floating){-ms-flex-align:start;align-items:start}:host(.item-label-stacked) .input-wrapper,:host(.item-label-floating) .input-wrapper{-ms-flex:1;flex:1;-ms-flex-direction:column;flex-direction:column}:host(.item-multiple-inputs) ::slotted(ion-checkbox),:host(.item-multiple-inputs) ::slotted(ion-datetime),:host(.item-multiple-inputs) ::slotted(ion-radio){position:relative}:host(.item-textarea){-ms-flex-align:stretch;align-items:stretch}::slotted(ion-reorder[slot]){margin-top:0;margin-bottom:0}ion-ripple-effect{color:var(--ripple-color)}:host{--min-height:44px;--transition:background-color 200ms linear, opacity 200ms linear;--padding-start:16px;--inner-padding-end:16px;--inner-border-width:0px 0px 0.55px 0px;--background:var(--ion-item-background, var(--ion-background-color, #fff));--background-activated:var(--ion-text-color, #000);--background-focused:var(--ion-text-color, #000);--background-hover:currentColor;--background-activated-opacity:.12;--background-focused-opacity:.15;--background-hover-opacity:.04;--border-color:var(--ion-item-border-color, var(--ion-border-color, var(--ion-color-step-250, var(--ion-background-color-step-250, #c8c7cc))));--color:var(--ion-item-color, var(--ion-text-color, #000));font-size:1rem}:host(.ion-activated){--transition:none}:host(.ion-color.ion-focused) .item-native::after{background:#000;opacity:0.15}:host(.ion-color.ion-activated) .item-native::after{background:#000;opacity:0.12}:host(.item-lines-full){--border-width:0px 0px 0.55px 0px}:host(.item-lines-inset){--inner-border-width:0px 0px 0.55px 0px}:host(.item-lines-inset),:host(.item-lines-none){--border-width:0px}:host(.item-lines-full),:host(.item-lines-none){--inner-border-width:0px}::slotted([slot=start]){-webkit-margin-start:0;margin-inline-start:0;-webkit-margin-end:16px;margin-inline-end:16px;margin-top:2px;margin-bottom:2px}::slotted(ion-icon[slot=start]),::slotted(ion-icon[slot=end]){margin-top:7px;margin-bottom:7px}::slotted(ion-toggle[slot=start]),::slotted(ion-toggle[slot=end]){margin-left:0;margin-right:0;margin-top:0;margin-bottom:0}:host(.item-label-stacked) ::slotted([slot=end]),:host(.item-label-floating) ::slotted([slot=end]){margin-top:7px;margin-bottom:7px}::slotted(.button-small){--padding-top:1px;--padding-bottom:1px;--padding-start:.5em;--padding-end:.5em;min-height:24px;font-size:0.8125rem}::slotted(ion-avatar){width:36px;height:36px}::slotted(ion-thumbnail){--size:56px}::slotted(ion-avatar[slot=end]),::slotted(ion-thumbnail[slot=end]){-webkit-margin-start:8px;margin-inline-start:8px;-webkit-margin-end:8px;margin-inline-end:8px;margin-top:8px;margin-bottom:8px}:host(.item-radio) ::slotted(ion-label),:host(.item-toggle) ::slotted(ion-label){-webkit-margin-start:0px;margin-inline-start:0px}::slotted(ion-label){-webkit-margin-start:0;margin-inline-start:0;-webkit-margin-end:8px;margin-inline-end:8px;margin-top:10px;margin-bottom:10px}:host(.item-label-floating),:host(.item-label-stacked){--min-height:68px}',Wk=':host{--border-radius:0px;--border-width:0px;--border-style:solid;--padding-top:0px;--padding-bottom:0px;--padding-end:0px;--padding-start:0px;--inner-border-width:0px;--inner-padding-top:0px;--inner-padding-bottom:0px;--inner-padding-start:0px;--inner-padding-end:0px;--inner-box-shadow:none;--detail-icon-color:initial;--detail-icon-font-size:1.25em;--detail-icon-opacity:0.25;--color-activated:var(--color);--color-focused:var(--color);--color-hover:var(--color);--ripple-color:currentColor;-moz-osx-font-smoothing:grayscale;-webkit-font-smoothing:antialiased;display:block;position:relative;-ms-flex-align:center;align-items:center;-ms-flex-pack:justify;justify-content:space-between;outline:none;color:var(--color);font-family:var(--ion-font-family, inherit);text-align:initial;text-decoration:none;overflow:hidden;-webkit-box-sizing:border-box;box-sizing:border-box}:host(.ion-color) .item-native{background:var(--ion-color-base);color:var(--ion-color-contrast)}:host(.ion-color) .item-native,:host(.ion-color) .item-inner{border-color:var(--ion-color-shade)}:host(.ion-activated) .item-native{color:var(--color-activated)}:host(.ion-activated) .item-native::after{background:var(--background-activated);opacity:var(--background-activated-opacity)}:host(.ion-color.ion-activated) .item-native{color:var(--ion-color-contrast)}:host(.ion-focused) .item-native{color:var(--color-focused)}:host(.ion-focused) .item-native::after{background:var(--background-focused);opacity:var(--background-focused-opacity)}:host(.ion-color.ion-focused) .item-native{color:var(--ion-color-contrast)}:host(.ion-color.ion-focused) .item-native::after{background:var(--ion-color-contrast)}@media (any-hover: hover){:host(.ion-activatable:not(.ion-focused):hover) .item-native{color:var(--color-hover)}:host(.ion-activatable:not(.ion-focused):hover) .item-native::after{background:var(--background-hover);opacity:var(--background-hover-opacity)}:host(.ion-color.ion-activatable:not(.ion-focused):hover) .item-native{color:var(--ion-color-contrast)}:host(.ion-color.ion-activatable:not(.ion-focused):hover) .item-native::after{background:var(--ion-color-contrast)}}:host(.item-control-needs-pointer-cursor){cursor:pointer}:host(.item-interactive-disabled:not(.item-multiple-inputs)){cursor:default;pointer-events:none}:host(.item-disabled){cursor:default;opacity:0.3;pointer-events:none}.item-native{border-radius:var(--border-radius);margin-left:0;margin-right:0;margin-top:0;margin-bottom:0;padding-top:var(--padding-top);padding-bottom:var(--padding-bottom);font-family:inherit;font-size:inherit;font-style:inherit;font-weight:inherit;letter-spacing:inherit;text-decoration:inherit;text-indent:inherit;text-overflow:inherit;text-transform:inherit;text-align:inherit;white-space:inherit;color:inherit;padding-right:var(--padding-end);padding-left:calc(var(--padding-start) + var(--ion-safe-area-left, 0px));display:-ms-flexbox;display:flex;position:relative;-ms-flex-align:inherit;align-items:inherit;-ms-flex-pack:inherit;justify-content:inherit;width:100%;min-height:var(--min-height);-webkit-transition:var(--transition);transition:var(--transition);border-width:var(--border-width);border-style:var(--border-style);border-color:var(--border-color);outline:none;background:var(--background);overflow:inherit;z-index:1;-webkit-box-sizing:border-box;box-sizing:border-box}:host-context([dir=rtl]) .item-native{padding-right:calc(var(--padding-start) + var(--ion-safe-area-right, 0px));padding-left:var(--padding-end)}[dir=rtl] .item-native{padding-right:calc(var(--padding-start) + var(--ion-safe-area-right, 0px));padding-left:var(--padding-end)}@supports selector(:dir(rtl)){.item-native:dir(rtl){padding-right:calc(var(--padding-start) + var(--ion-safe-area-right, 0px));padding-left:var(--padding-end)}}.item-native::-moz-focus-inner{border:0}.item-native::after{left:0;right:0;top:0;bottom:0;position:absolute;content:"";opacity:0;-webkit-transition:var(--transition);transition:var(--transition);z-index:-1}button,a{cursor:pointer;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none;-webkit-user-drag:none}.item-inner{margin-left:0;margin-right:0;margin-top:0;margin-bottom:0;padding-top:var(--inner-padding-top);padding-bottom:var(--inner-padding-bottom);padding-right:calc(var(--ion-safe-area-right, 0px) + var(--inner-padding-end));padding-left:var(--inner-padding-start);display:-ms-flexbox;display:flex;position:relative;-ms-flex:1;flex:1;-ms-flex-direction:inherit;flex-direction:inherit;-ms-flex-align:inherit;align-items:inherit;-ms-flex-item-align:stretch;align-self:stretch;min-height:inherit;border-width:var(--inner-border-width);border-style:var(--border-style);border-color:var(--border-color);-webkit-box-shadow:var(--inner-box-shadow);box-shadow:var(--inner-box-shadow);overflow:inherit;-webkit-box-sizing:border-box;box-sizing:border-box}:host-context([dir=rtl]) .item-inner{padding-right:var(--inner-padding-start);padding-left:calc(var(--ion-safe-area-left, 0px) + var(--inner-padding-end))}[dir=rtl] .item-inner{padding-right:var(--inner-padding-start);padding-left:calc(var(--ion-safe-area-left, 0px) + var(--inner-padding-end))}@supports selector(:dir(rtl)){.item-inner:dir(rtl){padding-right:var(--inner-padding-start);padding-left:calc(var(--ion-safe-area-left, 0px) + var(--inner-padding-end))}}.item-detail-icon{-webkit-margin-start:calc(var(--inner-padding-end) / 2);margin-inline-start:calc(var(--inner-padding-end) / 2);-webkit-margin-end:-6px;margin-inline-end:-6px;color:var(--detail-icon-color);font-size:var(--detail-icon-font-size);opacity:var(--detail-icon-opacity)}::slotted(ion-icon){font-size:1.6em}::slotted(ion-button){--margin-top:0;--margin-bottom:0;--margin-start:0;--margin-end:0;z-index:1}::slotted(ion-label:not([slot=end])){-ms-flex:1;flex:1;width:-webkit-min-content;width:-moz-min-content;width:min-content;max-width:100%}:host(.item-input){-ms-flex-align:center;align-items:center}.input-wrapper{display:-ms-flexbox;display:flex;-ms-flex:1;flex:1;-ms-flex-direction:inherit;flex-direction:inherit;-ms-flex-align:inherit;align-items:inherit;-ms-flex-item-align:stretch;align-self:stretch;text-overflow:ellipsis;overflow:inherit;-webkit-box-sizing:border-box;box-sizing:border-box}:host(.item-label-stacked),:host(.item-label-floating){-ms-flex-align:start;align-items:start}:host(.item-label-stacked) .input-wrapper,:host(.item-label-floating) .input-wrapper{-ms-flex:1;flex:1;-ms-flex-direction:column;flex-direction:column}:host(.item-multiple-inputs) ::slotted(ion-checkbox),:host(.item-multiple-inputs) ::slotted(ion-datetime),:host(.item-multiple-inputs) ::slotted(ion-radio){position:relative}:host(.item-textarea){-ms-flex-align:stretch;align-items:stretch}::slotted(ion-reorder[slot]){margin-top:0;margin-bottom:0}ion-ripple-effect{color:var(--ripple-color)}:host{--min-height:48px;--background:var(--ion-item-background, var(--ion-background-color, #fff));--background-activated:transparent;--background-focused:currentColor;--background-hover:currentColor;--background-activated-opacity:0;--background-focused-opacity:.12;--background-hover-opacity:.04;--border-color:var(--ion-item-border-color, var(--ion-border-color, var(--ion-color-step-150, var(--ion-background-color-step-150, rgba(0, 0, 0, 0.13)))));--color:var(--ion-item-color, var(--ion-text-color, #000));--transition:opacity 15ms linear, background-color 15ms linear;--padding-start:16px;--inner-padding-end:16px;--inner-border-width:0 0 1px 0;font-size:1rem;font-weight:normal;text-transform:none}:host(.ion-color.ion-activated) .item-native::after{background:transparent}:host(.item-interactive){--border-width:0 0 1px 0;--inner-border-width:0}:host(.item-lines-full){--border-width:0 0 1px 0}:host(.item-lines-inset){--inner-border-width:0 0 1px 0}:host(.item-lines-inset),:host(.item-lines-none){--border-width:0}:host(.item-lines-full),:host(.item-lines-none){--inner-border-width:0}:host(.item-multi-line) ::slotted([slot=start]),:host(.item-multi-line) ::slotted([slot=end]){margin-top:16px;margin-bottom:16px;-ms-flex-item-align:start;align-self:flex-start}::slotted([slot=start]){-webkit-margin-end:16px;margin-inline-end:16px}::slotted([slot=end]){-webkit-margin-start:16px;margin-inline-start:16px}::slotted(ion-icon){color:rgba(var(--ion-text-color-rgb, 0, 0, 0), 0.54);font-size:1.5em}:host(.ion-color) ::slotted(ion-icon){color:var(--ion-color-contrast)}::slotted(ion-icon[slot]){margin-top:12px;margin-bottom:12px}::slotted(ion-icon[slot=start]){-webkit-margin-end:32px;margin-inline-end:32px}::slotted(ion-icon[slot=end]){-webkit-margin-start:16px;margin-inline-start:16px}::slotted(ion-toggle[slot=start]),::slotted(ion-toggle[slot=end]){margin-left:0;margin-right:0;margin-top:0;margin-bottom:0}::slotted(ion-note){margin-left:0;margin-right:0;margin-top:0;margin-bottom:0;-ms-flex-item-align:start;align-self:flex-start;font-size:0.6875rem}::slotted(ion-note[slot]){padding-left:0;padding-right:0;padding-top:18px;padding-bottom:10px}::slotted(ion-avatar){width:40px;height:40px}::slotted(ion-thumbnail){--size:56px}::slotted(ion-avatar),::slotted(ion-thumbnail){margin-top:8px;margin-bottom:8px}::slotted(ion-avatar[slot=start]),::slotted(ion-thumbnail[slot=start]){-webkit-margin-end:16px;margin-inline-end:16px}::slotted(ion-avatar[slot=end]),::slotted(ion-thumbnail[slot=end]){-webkit-margin-start:16px;margin-inline-start:16px}::slotted(ion-label){margin-left:0;margin-right:0;margin-top:10px;margin-bottom:10px}:host(.item-label-stacked) ::slotted([slot=end]),:host(.item-label-floating) ::slotted([slot=end]){margin-top:7px;margin-bottom:7px}:host(.item-toggle) ::slotted(ion-label),:host(.item-radio) ::slotted(ion-label){-webkit-margin-start:0;margin-inline-start:0}::slotted(.button-small){--padding-top:2px;--padding-bottom:2px;--padding-start:.6em;--padding-end:.6em;min-height:25px;font-size:0.75rem}:host(.item-label-floating),:host(.item-label-stacked){--min-height:55px}:host(.ion-focused:not(.ion-color)) ::slotted(.label-stacked),:host(.ion-focused:not(.ion-color)) ::slotted(.label-floating),:host(.item-has-focus:not(.ion-color)) ::slotted(.label-stacked),:host(.item-has-focus:not(.ion-color)) ::slotted(.label-floating){color:var(--ion-color-primary, #0054e9)}',rw=Q(class extends Z{constructor(){super(),this.__registerHost(),this.__attachShadow(),this.labelColorStyles={},this.itemStyles=new Map,this.inheritedAriaAttributes={},this.multipleInputs=!1,this.focusable=!0,this.isInteractive=!1,this.button=!1,this.detailIcon=_p,this.disabled=!1,this.routerDirection="forward",this.type="button",this.updateInteractivityOnSlotChange=()=>{this.setIsInteractive(),this.setMultipleInputs()}}buttonChanged(){this.focusable=this.isFocusable()}labelColorChanged(t){let{color:n}=this;n===void 0&&(this.labelColorStyles=t.detail)}itemStyle(t){t.stopPropagation();let n=t.target.tagName,o=t.detail,i={},r=this.itemStyles.get(n)||{},s=!1;Object.keys(o).forEach(a=>{if(o[a]){let l=`item-${a}`;r[l]||(s=!0),i[l]=!0}}),!s&&Object.keys(i).length!==Object.keys(r).length&&(s=!0),s&&(this.itemStyles.set(n,i),ut(this))}connectedCallback(){this.hasStartEl()}componentWillLoad(){this.inheritedAriaAttributes=xo(this.el,["aria-label"])}componentDidLoad(){je(()=>{this.setMultipleInputs(),this.setIsInteractive(),this.focusable=this.isFocusable()})}totalNestedInputs(){let t=this.el.querySelectorAll("ion-checkbox, ion-datetime, ion-select, ion-radio"),n=this.el.querySelectorAll("ion-input, ion-range, ion-searchbar, ion-segment, ion-textarea, ion-toggle"),o=this.el.querySelectorAll("ion-router-link, ion-button, a, button");return{covers:t,inputs:n,clickables:o}}setMultipleInputs(){let{covers:t,inputs:n,clickables:o}=this.totalNestedInputs();this.multipleInputs=t.length+n.length>1||t.length+o.length>1||t.length>0&&this.isClickable()}setIsInteractive(){let{covers:t,inputs:n,clickables:o}=this.totalNestedInputs();this.isInteractive=t.length>0||n.length>0||o.length>0}hasCover(){return this.el.querySelectorAll("ion-checkbox, ion-datetime, ion-select, ion-radio").length===1&&!this.multipleInputs}isClickable(){return this.href!==void 0||this.button}canActivate(){return this.isClickable()||this.hasCover()}isFocusable(){let t=this.el.querySelector(".ion-focusable");return this.canActivate()||t!==null}hasStartEl(){this.el.querySelector('[slot="start"]')!==null&&this.el.classList.add("item-has-start-slot")}getFirstInteractive(){return this.el.querySelectorAll("ion-toggle:not([disabled]), ion-checkbox:not([disabled]), ion-radio:not([disabled]), ion-select:not([disabled]), ion-input:not([disabled]), ion-textarea:not([disabled])")[0]}render(){let{detail:t,detailIcon:n,download:o,labelColorStyles:i,lines:r,disabled:s,href:a,rel:l,target:c,routerAnimation:u,routerDirection:d,inheritedAriaAttributes:p,multipleInputs:f}=this,h={},y=F(this),g=this.isClickable(),w=this.canActivate(),P=g?a===void 0?"button":"a":"div",M=P==="button"?{type:this.type}:{download:o,href:a,rel:l,target:c},H={},$=this.getFirstInteractive();(g||$!==void 0&&!f)&&(H={onClick:he=>{if(g&&Bl(a,he,d,u),$!==void 0&&!f){let me=he.composedPath()[0];he.isTrusted&&this.el.shadowRoot.contains(me)&&(($.tagName==="ION-INPUT"||$.tagName==="ION-TEXTAREA")&&$.setFocus(),$.click(),he.stopImmediatePropagation())}}});let V=t!==void 0?t:y==="ios"&&g;this.itemStyles.forEach(he=>{Object.assign(h,he)});let ve=s||h["item-interactive-disabled"]?"true":null,ye=ke("ion-list",this.el)&&!ke("ion-radio-group",this.el),be=$!==void 0&&!["ION-INPUT","ION-TEXTAREA"].includes($.tagName);return m(J,{key:"24b59935bd8db8b0b7f940582455a42b82cbf762","aria-disabled":ve,class:Object.assign(Object.assign(Object.assign({},h),i),et(this.color,{item:!0,[y]:!0,"item-lines-default":r===void 0,[`item-lines-${r}`]:r!==void 0,"item-control-needs-pointer-cursor":be,"item-disabled":s,"in-list":ye,"item-multiple-inputs":this.multipleInputs,"ion-activatable":w,"ion-focusable":this.focusable,"item-rtl":document.dir==="rtl"})),role:ye?"listitem":null},m(P,Object.assign({key:"fd77b6e5f3eb2e1857a0cdd45562d71eabd30255"},M,p,{class:"item-native",part:"native",disabled:s},H),m("slot",{key:"8824ac8395aafa3d63c92f2128e947cac8393ac4",name:"start",onSlotchange:this.updateInteractivityOnSlotChange}),m("div",{key:"5c9127e388a432687766d86a9db91fd1663abf03",class:"item-inner"},m("div",{key:"9dc2d2f58c4067c0143b3963334c346c3c7f77df",class:"input-wrapper"},m("slot",{key:"8377d9e56dc4b1913f1346111b706e7f14c24d30",onSlotchange:this.updateInteractivityOnSlotChange})),m("slot",{key:"bc771e106174f4a84ee12e92d14df81ad7ed177d",name:"end",onSlotchange:this.updateInteractivityOnSlotChange}),V&&m("ion-icon",{key:"45336d121a097cbf71ee8a3f6b554745ba5e0bbf",icon:n,lazy:!1,class:"item-detail-icon",part:"detail-icon","aria-hidden":"true","flip-rtl":n===_p})),w&&y==="md"&&m("ion-ripple-effect",{key:"197e244ae3bffebfa6ac9bfe7658d12e1af0ecb1"})))}get el(){return this}static get watchers(){return{button:["buttonChanged"]}}static get style(){return{ios:Gk,md:Wk}}},[33,"ion-item",{color:[513],button:[4],detail:[4],detailIcon:[1,"detail-icon"],disabled:[516],download:[1],href:[1],rel:[1],lines:[1],routerAnimation:[16,"router-animation"],routerDirection:[1,"router-direction"],target:[1],type:[1],multipleInputs:[32],focusable:[32],isInteractive:[32]},[[0,"ionColor","labelColorChanged"],[0,"ionStyle","itemStyle"]],{button:["buttonChanged"]}]);function sw(){if(typeof customElements>"u")return;["ion-item","ion-icon","ion-ripple-effect"].forEach(t=>{switch(t){case"ion-item":customElements.get(t)||customElements.define(t,rw);break;case"ion-icon":customElements.get(t)||Jl();break;case"ion-ripple-effect":customElements.get(t)||ec();break}})}var aw=sw;var Yk=".item.sc-ion-label-ios-h,.item .sc-ion-label-ios-h{--color:initial;display:block;color:var(--color);font-family:var(--ion-font-family, inherit);font-size:inherit;text-overflow:ellipsis;-webkit-box-sizing:border-box;box-sizing:border-box}.ion-color.sc-ion-label-ios-h{color:var(--ion-color-base)}.ion-text-nowrap.sc-ion-label-ios-h{overflow:hidden}.item-interactive-disabled.sc-ion-label-ios-h:not(.item-multiple-inputs),.item-interactive-disabled:not(.item-multiple-inputs) .sc-ion-label-ios-h{cursor:default;opacity:0.3;pointer-events:none}.item-input.sc-ion-label-ios-h,.item-input .sc-ion-label-ios-h{-ms-flex:initial;flex:initial;max-width:200px;pointer-events:none}.item-textarea.sc-ion-label-ios-h,.item-textarea .sc-ion-label-ios-h{-ms-flex-item-align:baseline;align-self:baseline}.item-skeleton-text.sc-ion-label-ios-h,.item-skeleton-text .sc-ion-label-ios-h{overflow:hidden}.label-fixed.sc-ion-label-ios-h{-ms-flex:0 0 100px;flex:0 0 100px;width:100px;min-width:100px;max-width:200px}.label-stacked.sc-ion-label-ios-h,.label-floating.sc-ion-label-ios-h{margin-bottom:0;-ms-flex-item-align:stretch;align-self:stretch;width:auto;max-width:100%}.label-no-animate.label-floating.sc-ion-label-ios-h{-webkit-transition:none;transition:none}.sc-ion-label-ios-s h1,.sc-ion-label-ios-s h2,.sc-ion-label-ios-s h3,.sc-ion-label-ios-s h4,.sc-ion-label-ios-s h5,.sc-ion-label-ios-s h6{text-overflow:inherit;overflow:inherit}.ion-text-wrap.sc-ion-label-ios-h{font-size:0.875rem;line-height:1.5}.label-stacked.sc-ion-label-ios-h{margin-bottom:4px;font-size:0.875rem}.label-floating.sc-ion-label-ios-h{margin-bottom:0;-webkit-transform:translate(0, 29px);transform:translate(0, 29px);-webkit-transform-origin:left top;transform-origin:left top;-webkit-transition:-webkit-transform 150ms ease-in-out;transition:-webkit-transform 150ms ease-in-out;transition:transform 150ms ease-in-out;transition:transform 150ms ease-in-out, -webkit-transform 150ms ease-in-out}[dir=rtl].sc-ion-label-ios-h -no-combinator.label-floating.sc-ion-label-ios-h,[dir=rtl] .sc-ion-label-ios-h -no-combinator.label-floating.sc-ion-label-ios-h,[dir=rtl].label-floating.sc-ion-label-ios-h,[dir=rtl] .label-floating.sc-ion-label-ios-h{-webkit-transform-origin:right top;transform-origin:right top}@supports selector(:dir(rtl)){.label-floating.sc-ion-label-ios-h:dir(rtl){-webkit-transform-origin:right top;transform-origin:right top}}.item-textarea.label-floating.sc-ion-label-ios-h,.item-textarea .label-floating.sc-ion-label-ios-h{-webkit-transform:translate(0, 28px);transform:translate(0, 28px)}.item-has-focus.label-floating.sc-ion-label-ios-h,.item-has-focus .label-floating.sc-ion-label-ios-h,.item-has-placeholder.sc-ion-label-ios-h:not(.item-input).label-floating,.item-has-placeholder:not(.item-input) .label-floating.sc-ion-label-ios-h,.item-has-value.label-floating.sc-ion-label-ios-h,.item-has-value .label-floating.sc-ion-label-ios-h{-webkit-transform:scale(0.82);transform:scale(0.82)}.sc-ion-label-ios-s h1{margin-left:0;margin-right:0;margin-top:3px;margin-bottom:2px;font-size:1.375rem;font-weight:normal}.sc-ion-label-ios-s h2{margin-left:0;margin-right:0;margin-top:0;margin-bottom:2px;font-size:1.0625rem;font-weight:normal}.sc-ion-label-ios-s h3,.sc-ion-label-ios-s h4,.sc-ion-label-ios-s h5,.sc-ion-label-ios-s h6{margin-left:0;margin-right:0;margin-top:0;margin-bottom:3px;font-size:0.875rem;font-weight:normal;line-height:normal}.sc-ion-label-ios-s p{margin-left:0;margin-right:0;margin-top:0;margin-bottom:2px;font-size:0.875rem;line-height:normal;text-overflow:inherit;overflow:inherit}.sc-ion-label-ios-s>p{color:var(--ion-color-step-400, var(--ion-text-color-step-600, #999999))}.sc-ion-label-ios-h.in-item-color.sc-ion-label-ios-s>p{color:inherit}.sc-ion-label-ios-s h2:last-child,.sc-ion-label-ios-s h3:last-child,.sc-ion-label-ios-s h4:last-child,.sc-ion-label-ios-s h5:last-child,.sc-ion-label-ios-s h6:last-child,.sc-ion-label-ios-s p:last-child{margin-bottom:0}",Zk=".item.sc-ion-label-md-h,.item .sc-ion-label-md-h{--color:initial;display:block;color:var(--color);font-family:var(--ion-font-family, inherit);font-size:inherit;text-overflow:ellipsis;-webkit-box-sizing:border-box;box-sizing:border-box}.ion-color.sc-ion-label-md-h{color:var(--ion-color-base)}.ion-text-nowrap.sc-ion-label-md-h{overflow:hidden}.item-interactive-disabled.sc-ion-label-md-h:not(.item-multiple-inputs),.item-interactive-disabled:not(.item-multiple-inputs) .sc-ion-label-md-h{cursor:default;opacity:0.3;pointer-events:none}.item-input.sc-ion-label-md-h,.item-input .sc-ion-label-md-h{-ms-flex:initial;flex:initial;max-width:200px;pointer-events:none}.item-textarea.sc-ion-label-md-h,.item-textarea .sc-ion-label-md-h{-ms-flex-item-align:baseline;align-self:baseline}.item-skeleton-text.sc-ion-label-md-h,.item-skeleton-text .sc-ion-label-md-h{overflow:hidden}.label-fixed.sc-ion-label-md-h{-ms-flex:0 0 100px;flex:0 0 100px;width:100px;min-width:100px;max-width:200px}.label-stacked.sc-ion-label-md-h,.label-floating.sc-ion-label-md-h{margin-bottom:0;-ms-flex-item-align:stretch;align-self:stretch;width:auto;max-width:100%}.label-no-animate.label-floating.sc-ion-label-md-h{-webkit-transition:none;transition:none}.sc-ion-label-md-s h1,.sc-ion-label-md-s h2,.sc-ion-label-md-s h3,.sc-ion-label-md-s h4,.sc-ion-label-md-s h5,.sc-ion-label-md-s h6{text-overflow:inherit;overflow:inherit}.ion-text-wrap.sc-ion-label-md-h{line-height:1.5}.label-stacked.sc-ion-label-md-h,.label-floating.sc-ion-label-md-h{margin-left:0;margin-right:0;margin-top:0;margin-bottom:0;-webkit-transform-origin:top left;transform-origin:top left}.label-stacked.label-rtl.sc-ion-label-md-h,.label-floating.label-rtl.sc-ion-label-md-h{-webkit-transform-origin:top right;transform-origin:top right}.label-stacked.sc-ion-label-md-h{-webkit-transform:translateY(50%) scale(0.75);transform:translateY(50%) scale(0.75);-webkit-transition:color 150ms cubic-bezier(0.4, 0, 0.2, 1);transition:color 150ms cubic-bezier(0.4, 0, 0.2, 1)}.label-floating.sc-ion-label-md-h{-webkit-transform:translateY(96%);transform:translateY(96%);-webkit-transition:color 150ms cubic-bezier(0.4, 0, 0.2, 1), -webkit-transform 150ms cubic-bezier(0.4, 0, 0.2, 1);transition:color 150ms cubic-bezier(0.4, 0, 0.2, 1), -webkit-transform 150ms cubic-bezier(0.4, 0, 0.2, 1);transition:color 150ms cubic-bezier(0.4, 0, 0.2, 1), transform 150ms cubic-bezier(0.4, 0, 0.2, 1);transition:color 150ms cubic-bezier(0.4, 0, 0.2, 1), transform 150ms cubic-bezier(0.4, 0, 0.2, 1), -webkit-transform 150ms cubic-bezier(0.4, 0, 0.2, 1)}.ion-focused.label-floating.sc-ion-label-md-h,.ion-focused .label-floating.sc-ion-label-md-h,.item-has-focus.label-floating.sc-ion-label-md-h,.item-has-focus .label-floating.sc-ion-label-md-h,.item-has-placeholder.sc-ion-label-md-h:not(.item-input).label-floating,.item-has-placeholder:not(.item-input) .label-floating.sc-ion-label-md-h,.item-has-value.label-floating.sc-ion-label-md-h,.item-has-value .label-floating.sc-ion-label-md-h{-webkit-transform:translateY(50%) scale(0.75);transform:translateY(50%) scale(0.75)}.ion-focused.label-stacked.sc-ion-label-md-h:not(.ion-color),.ion-focused .label-stacked.sc-ion-label-md-h:not(.ion-color),.ion-focused.label-floating.sc-ion-label-md-h:not(.ion-color),.ion-focused .label-floating.sc-ion-label-md-h:not(.ion-color),.item-has-focus.label-stacked.sc-ion-label-md-h:not(.ion-color),.item-has-focus .label-stacked.sc-ion-label-md-h:not(.ion-color),.item-has-focus.label-floating.sc-ion-label-md-h:not(.ion-color),.item-has-focus .label-floating.sc-ion-label-md-h:not(.ion-color){color:var(--ion-color-primary, #0054e9)}.ion-focused.ion-color.label-stacked.sc-ion-label-md-h:not(.ion-color),.ion-focused.ion-color .label-stacked.sc-ion-label-md-h:not(.ion-color),.ion-focused.ion-color.label-floating.sc-ion-label-md-h:not(.ion-color),.ion-focused.ion-color .label-floating.sc-ion-label-md-h:not(.ion-color),.item-has-focus.ion-color.label-stacked.sc-ion-label-md-h:not(.ion-color),.item-has-focus.ion-color .label-stacked.sc-ion-label-md-h:not(.ion-color),.item-has-focus.ion-color.label-floating.sc-ion-label-md-h:not(.ion-color),.item-has-focus.ion-color .label-floating.sc-ion-label-md-h:not(.ion-color){color:var(--ion-color-contrast)}.ion-invalid.ion-touched.label-stacked.sc-ion-label-md-h:not(.ion-color),.ion-invalid.ion-touched .label-stacked.sc-ion-label-md-h:not(.ion-color),.ion-invalid.ion-touched.label-floating.sc-ion-label-md-h:not(.ion-color),.ion-invalid.ion-touched .label-floating.sc-ion-label-md-h:not(.ion-color){color:var(--highlight-color-invalid)}.sc-ion-label-md-s h1{margin-left:0;margin-right:0;margin-top:0;margin-bottom:2px;font-size:1.5rem;font-weight:normal}.sc-ion-label-md-s h2{margin-left:0;margin-right:0;margin-top:2px;margin-bottom:2px;font-size:1rem;font-weight:normal}.sc-ion-label-md-s h3,.sc-ion-label-md-s h4,.sc-ion-label-md-s h5,.sc-ion-label-md-s h6{margin-left:0;margin-right:0;margin-top:2px;margin-bottom:2px;font-size:0.875rem;font-weight:normal;line-height:normal}.sc-ion-label-md-s p{margin-left:0;margin-right:0;margin-top:0;margin-bottom:2px;font-size:0.875rem;line-height:1.25rem;text-overflow:inherit;overflow:inherit}.sc-ion-label-md-s>p{color:var(--ion-color-step-600, var(--ion-text-color-step-400, #666666))}.sc-ion-label-md-h.in-item-color.sc-ion-label-md-s>p{color:inherit}",lw=Q(class extends Z{constructor(){super(),this.__registerHost(),this.ionColor=N(this,"ionColor",7),this.ionStyle=N(this,"ionStyle",7),this.inRange=!1,this.noAnimate=!1}componentWillLoad(){this.inRange=!!this.el.closest("ion-range"),this.noAnimate=this.position==="floating",this.emitStyle(),this.emitColor()}componentDidLoad(){this.noAnimate&&setTimeout(()=>{this.noAnimate=!1},1e3)}colorChanged(){this.emitColor()}positionChanged(){this.emitStyle()}emitColor(){let{color:t}=this;this.ionColor.emit({"item-label-color":t!==void 0,[`ion-color-${t}`]:t!==void 0})}emitStyle(){let{inRange:t,position:n}=this;t||this.ionStyle.emit({label:!0,[`label-${n}`]:n!==void 0})}render(){let t=this.position,n=F(this);return m(J,{key:"d6fba1a97189acc8ddfd64a2f009954a3e46e188",class:et(this.color,{[n]:!0,"in-item-color":ke("ion-item.ion-color",this.el),[`label-${t}`]:t!==void 0,"label-no-animate":this.noAnimate,"label-rtl":document.dir==="rtl"})},m("slot",{key:"ce0ab50b5700398fdf50f36d02b7ad287eb71481"}))}get el(){return this}static get watchers(){return{color:["colorChanged"],position:["positionChanged"]}}static get style(){return{ios:Yk,md:Zk}}},[38,"ion-label",{color:[513],position:[1],noAnimate:[32]},void 0,{color:["colorChanged"],position:["positionChanged"]}]);function cw(){if(typeof customElements>"u")return;["ion-label"].forEach(t=>{switch(t){case"ion-label":customElements.get(t)||customElements.define(t,lw);break}})}var uw=cw;var Qk="ion-list{margin-left:0;margin-right:0;margin-top:0;margin-bottom:0;padding-left:0;padding-right:0;padding-top:0;padding-bottom:0;display:block;contain:content;list-style-type:none}ion-list.list-inset{-webkit-transform:translateZ(0);transform:translateZ(0);overflow:hidden}.list-ios{background:var(--ion-item-background, var(--ion-background-color, #fff))}.list-ios.list-inset{-webkit-margin-start:16px;margin-inline-start:16px;-webkit-margin-end:16px;margin-inline-end:16px;margin-top:16px;margin-bottom:16px;border-radius:10px}.list-ios.list-inset ion-item:only-child,.list-ios.list-inset ion-item:not(:only-of-type):last-of-type,.list-ios.list-inset ion-item-sliding:last-of-type ion-item{--border-width:0;--inner-border-width:0}.list-ios.list-inset+ion-list.list-inset{margin-top:0}.list-ios-lines-none .item-lines-default{--inner-border-width:0px;--border-width:0px}.list-ios-lines-full .item-lines-default{--inner-border-width:0px;--border-width:0 0 0.55px 0}.list-ios-lines-inset .item-lines-default{--inner-border-width:0 0 0.55px 0;--border-width:0px}ion-card .list-ios{margin-left:0;margin-right:0;margin-top:0;margin-bottom:0}",Kk="ion-list{margin-left:0;margin-right:0;margin-top:0;margin-bottom:0;padding-left:0;padding-right:0;padding-top:0;padding-bottom:0;display:block;contain:content;list-style-type:none}ion-list.list-inset{-webkit-transform:translateZ(0);transform:translateZ(0);overflow:hidden}.list-md{margin-left:0;margin-right:0;margin-top:0;margin-bottom:0;padding-left:0;padding-right:0;padding-top:8px;padding-bottom:8px;background:var(--ion-item-background, var(--ion-background-color, #fff))}.list-md>.input:last-child::after{inset-inline-start:0}.list-md.list-inset{-webkit-margin-start:16px;margin-inline-start:16px;-webkit-margin-end:16px;margin-inline-end:16px;margin-top:16px;margin-bottom:16px;border-radius:2px}.list-md.list-inset ion-item:not(:only-of-type):last-of-type,.list-md.list-inset ion-item-sliding:last-of-type ion-item{--border-width:0;--inner-border-width:0}.list-md.list-inset ion-item:only-child{--border-width:0;--inner-border-width:0}.list-md.list-inset+ion-list.list-inset{margin-top:0}.list-md-lines-none .item-lines-default{--inner-border-width:0px;--border-width:0px}.list-md-lines-full .item-lines-default{--inner-border-width:0px;--border-width:0 0 1px 0}.list-md-lines-inset .item-lines-default{--inner-border-width:0 0 1px 0;--border-width:0px}ion-card .list-md{margin-left:0;margin-right:0;margin-top:0;margin-bottom:0}",dw=Q(class extends Z{constructor(){super(),this.__registerHost(),this.inset=!1}closeSlidingItems(){return C(this,null,function*(){let t=this.el.querySelector("ion-item-sliding");return t?.closeOpened?t.closeOpened():!1})}render(){let t=F(this),{lines:n,inset:o}=this;return m(J,{key:"7f9943751542d2cbd49a4ad3f28e16d9949f70d4",role:"list",class:{[t]:!0,[`list-${t}`]:!0,"list-inset":o,[`list-lines-${n}`]:n!==void 0,[`list-${t}-lines-${n}`]:n!==void 0}})}get el(){return this}static get style(){return{ios:Qk,md:Kk}}},[32,"ion-list",{lines:[1],inset:[4],closeSlidingItems:[64]}]);function fw(){if(typeof customElements>"u")return;["ion-list"].forEach(t=>{switch(t){case"ion-list":customElements.get(t)||customElements.define(t,dw);break}})}var pw=fw;var Xk=":host{--color:initial;display:-ms-flexbox;display:flex;-ms-flex:1;flex:1;-ms-flex-align:center;align-items:center;-webkit-transform:translateZ(0);transform:translateZ(0);color:var(--color)}:host(.ion-color){color:var(--ion-color-base)}.toolbar-title{display:block;width:100%;text-overflow:ellipsis;white-space:nowrap;overflow:hidden;pointer-events:auto}:host(.title-small) .toolbar-title{white-space:normal}:host{top:0;-webkit-padding-start:90px;padding-inline-start:90px;-webkit-padding-end:90px;padding-inline-end:90px;padding-top:var(--padding-top);padding-bottom:var(--padding-bottom);position:absolute;width:100%;height:100%;-webkit-transform:translateZ(0);transform:translateZ(0);font-size:min(1.0625rem, 20.4px);font-weight:600;text-align:center;-webkit-box-sizing:border-box;box-sizing:border-box;pointer-events:none}:host{inset-inline-start:0}:host(.title-small){-webkit-padding-start:9px;padding-inline-start:9px;-webkit-padding-end:9px;padding-inline-end:9px;padding-top:6px;padding-bottom:16px;position:relative;font-size:min(0.8125rem, 23.4px);font-weight:normal}:host(.title-large){-webkit-padding-start:12px;padding-inline-start:12px;-webkit-padding-end:12px;padding-inline-end:12px;padding-top:2px;padding-bottom:4px;-webkit-transform-origin:left center;transform-origin:left center;position:static;-ms-flex-align:end;align-items:flex-end;min-width:100%;font-size:min(2.125rem, 61.2px);font-weight:700;text-align:start}:host(.title-large.title-rtl){-webkit-transform-origin:right center;transform-origin:right center}:host(.title-large.ion-cloned-element){--color:var(--ion-text-color, #000);font-family:var(--ion-font-family)}:host(.title-large) .toolbar-title{-webkit-transform-origin:inherit;transform-origin:inherit;width:auto}:host-context([dir=rtl]):host(.title-large) .toolbar-title,:host-context([dir=rtl]).title-large .toolbar-title{-webkit-transform-origin:calc(100% - inherit);transform-origin:calc(100% - inherit)}@supports selector(:dir(rtl)){:host(.title-large:dir(rtl)) .toolbar-title{-webkit-transform-origin:calc(100% - inherit);transform-origin:calc(100% - inherit)}}",Jk=":host{--color:initial;display:-ms-flexbox;display:flex;-ms-flex:1;flex:1;-ms-flex-align:center;align-items:center;-webkit-transform:translateZ(0);transform:translateZ(0);color:var(--color)}:host(.ion-color){color:var(--ion-color-base)}.toolbar-title{display:block;width:100%;text-overflow:ellipsis;white-space:nowrap;overflow:hidden;pointer-events:auto}:host(.title-small) .toolbar-title{white-space:normal}:host{-webkit-padding-start:20px;padding-inline-start:20px;-webkit-padding-end:20px;padding-inline-end:20px;padding-top:0;padding-bottom:0;font-size:1.25rem;font-weight:500;letter-spacing:0.0125em}:host(.title-small){width:100%;height:100%;font-size:0.9375rem;font-weight:normal}",hw=Q(class extends Z{constructor(){super(),this.__registerHost(),this.__attachShadow(),this.ionStyle=N(this,"ionStyle",7)}sizeChanged(){this.emitStyle()}connectedCallback(){this.emitStyle()}emitStyle(){let t=this.getSize();this.ionStyle.emit({[`title-${t}`]:!0})}getSize(){return this.size!==void 0?this.size:"default"}render(){let t=F(this),n=this.getSize();return m(J,{key:"e599c0bf1b0817df3fa8360bdcd6d787f751c371",class:et(this.color,{[t]:!0,[`title-${n}`]:!0,"title-rtl":document.dir==="rtl"})},m("div",{key:"6e7eee9047d6759876bb31d7305b76efc7c4338c",class:"toolbar-title"},m("slot",{key:"bf790eb4c83dd0af4f2fd1f85ab4af5819f46ff4"})))}get el(){return this}static get watchers(){return{size:["sizeChanged"]}}static get style(){return{ios:Xk,md:Jk}}},[33,"ion-title",{color:[513],size:[1]},void 0,{size:["sizeChanged"]}]);function mw(){if(typeof customElements>"u")return;["ion-title"].forEach(t=>{switch(t){case"ion-title":customElements.get(t)||customElements.define(t,hw);break}})}var eA=":host{--border-width:0;--border-style:solid;--opacity:1;--opacity-scale:1;-moz-osx-font-smoothing:grayscale;-webkit-font-smoothing:antialiased;display:block;position:relative;width:100%;padding-right:var(--ion-safe-area-right);padding-left:var(--ion-safe-area-left);color:var(--color);font-family:var(--ion-font-family, inherit);contain:content;z-index:10;-webkit-box-sizing:border-box;box-sizing:border-box}:host(.ion-color){color:var(--ion-color-contrast)}:host(.ion-color) .toolbar-background{background:var(--ion-color-base)}.toolbar-container{-webkit-padding-start:var(--padding-start);padding-inline-start:var(--padding-start);-webkit-padding-end:var(--padding-end);padding-inline-end:var(--padding-end);padding-top:var(--padding-top);padding-bottom:var(--padding-bottom);display:-ms-flexbox;display:flex;position:relative;-ms-flex-direction:row;flex-direction:row;-ms-flex-align:center;align-items:center;-ms-flex-pack:justify;justify-content:space-between;width:100%;min-height:var(--min-height);contain:content;overflow:hidden;z-index:10;-webkit-box-sizing:border-box;box-sizing:border-box}.toolbar-background{left:0;right:0;top:0;bottom:0;position:absolute;-webkit-transform:translateZ(0);transform:translateZ(0);border-width:var(--border-width);border-style:var(--border-style);border-color:var(--border-color);background:var(--background);contain:strict;opacity:calc(var(--opacity) * var(--opacity-scale));z-index:-1;pointer-events:none}::slotted(ion-progress-bar){left:0;right:0;bottom:0;position:absolute}:host{--background:var(--ion-toolbar-background, var(--ion-color-step-50, var(--ion-background-color-step-50, #f7f7f7)));--color:var(--ion-toolbar-color, var(--ion-text-color, #000));--border-color:var(--ion-toolbar-border-color, var(--ion-border-color, var(--ion-color-step-150, var(--ion-background-color-step-150, rgba(0, 0, 0, 0.2)))));--padding-top:3px;--padding-bottom:3px;--padding-start:4px;--padding-end:4px;--min-height:44px}.toolbar-content{-ms-flex:1;flex:1;-ms-flex-order:4;order:4;min-width:0}:host(.toolbar-segment) .toolbar-content{display:-ms-inline-flexbox;display:inline-flex}:host(.toolbar-searchbar) .toolbar-container{padding-top:0;padding-bottom:0}:host(.toolbar-searchbar) ::slotted(*){-ms-flex-item-align:start;align-self:start}:host(.toolbar-searchbar) ::slotted(ion-chip){margin-top:3px}::slotted(ion-buttons){min-height:38px}::slotted([slot=start]){-ms-flex-order:2;order:2}::slotted([slot=secondary]){-ms-flex-order:3;order:3}::slotted([slot=primary]){-ms-flex-order:5;order:5;text-align:end}::slotted([slot=end]){-ms-flex-order:6;order:6;text-align:end}:host(.toolbar-title-large) .toolbar-container{-ms-flex-wrap:wrap;flex-wrap:wrap;-ms-flex-align:start;align-items:flex-start}:host(.toolbar-title-large) .toolbar-content ion-title{-ms-flex:1;flex:1;-ms-flex-order:8;order:8;min-width:100%}",tA=":host{--border-width:0;--border-style:solid;--opacity:1;--opacity-scale:1;-moz-osx-font-smoothing:grayscale;-webkit-font-smoothing:antialiased;display:block;position:relative;width:100%;padding-right:var(--ion-safe-area-right);padding-left:var(--ion-safe-area-left);color:var(--color);font-family:var(--ion-font-family, inherit);contain:content;z-index:10;-webkit-box-sizing:border-box;box-sizing:border-box}:host(.ion-color){color:var(--ion-color-contrast)}:host(.ion-color) .toolbar-background{background:var(--ion-color-base)}.toolbar-container{-webkit-padding-start:var(--padding-start);padding-inline-start:var(--padding-start);-webkit-padding-end:var(--padding-end);padding-inline-end:var(--padding-end);padding-top:var(--padding-top);padding-bottom:var(--padding-bottom);display:-ms-flexbox;display:flex;position:relative;-ms-flex-direction:row;flex-direction:row;-ms-flex-align:center;align-items:center;-ms-flex-pack:justify;justify-content:space-between;width:100%;min-height:var(--min-height);contain:content;overflow:hidden;z-index:10;-webkit-box-sizing:border-box;box-sizing:border-box}.toolbar-background{left:0;right:0;top:0;bottom:0;position:absolute;-webkit-transform:translateZ(0);transform:translateZ(0);border-width:var(--border-width);border-style:var(--border-style);border-color:var(--border-color);background:var(--background);contain:strict;opacity:calc(var(--opacity) * var(--opacity-scale));z-index:-1;pointer-events:none}::slotted(ion-progress-bar){left:0;right:0;bottom:0;position:absolute}:host{--background:var(--ion-toolbar-background, var(--ion-background-color, #fff));--color:var(--ion-toolbar-color, var(--ion-text-color, #424242));--border-color:var(--ion-toolbar-border-color, var(--ion-border-color, var(--ion-color-step-150, var(--ion-background-color-step-150, #c1c4cd))));--padding-top:0;--padding-bottom:0;--padding-start:0;--padding-end:0;--min-height:56px}.toolbar-content{-ms-flex:1;flex:1;-ms-flex-order:3;order:3;min-width:0;max-width:100%}::slotted(.buttons-first-slot){-webkit-margin-start:4px;margin-inline-start:4px}::slotted(.buttons-last-slot){-webkit-margin-end:4px;margin-inline-end:4px}::slotted([slot=start]){-ms-flex-order:2;order:2}::slotted([slot=secondary]){-ms-flex-order:4;order:4}::slotted([slot=primary]){-ms-flex-order:5;order:5;text-align:end}::slotted([slot=end]){-ms-flex-order:6;order:6;text-align:end}",gw=Q(class extends Z{constructor(){super(),this.__registerHost(),this.__attachShadow(),this.childrenStyles=new Map}componentWillLoad(){let t=Array.from(this.el.querySelectorAll("ion-buttons")),n=t.find(r=>r.slot==="start");n&&n.classList.add("buttons-first-slot");let o=t.reverse(),i=o.find(r=>r.slot==="end")||o.find(r=>r.slot==="primary")||o.find(r=>r.slot==="secondary");i&&i.classList.add("buttons-last-slot")}childrenStyle(t){t.stopPropagation();let n=t.target.tagName,o=t.detail,i={},r=this.childrenStyles.get(n)||{},s=!1;Object.keys(o).forEach(a=>{let l=`toolbar-${a}`,c=o[a];c!==r[l]&&(s=!0),c&&(i[l]=!0)}),s&&(this.childrenStyles.set(n,i),ut(this))}render(){let t=F(this),n={};return this.childrenStyles.forEach(o=>{Object.assign(n,o)}),m(J,{key:"f6c4f669a6a61c5eac4cbb5ea0aa97c48ae5bd46",class:Object.assign(Object.assign({},n),et(this.color,{[t]:!0,"in-toolbar":ke("ion-toolbar",this.el)}))},m("div",{key:"9c81742ffa02de9ba7417025b077d05e67305074",class:"toolbar-background",part:"background"}),m("div",{key:"5fc96d166fa47894a062e41541a9beee38078a36",class:"toolbar-container",part:"container"},m("slot",{key:"b62c0d9d59a70176bdbf769aec6090d7a166853b",name:"start"}),m("slot",{key:"d01d3cc2c50e5aaa49c345b209fe8dbdf3d48131",name:"secondary"}),m("div",{key:"3aaa3a2810aedd38c37eb616158ec7b9638528fc",class:"toolbar-content",part:"content"},m("slot",{key:"357246690f8d5e1cc3ca369611d4845a79edf610"})),m("slot",{key:"06ed3cca4f7ebff4a54cd877dad3cc925ccf9f75",name:"primary"}),m("slot",{key:"e453d43d14a26b0d72f41e1b81a554bab8ece811",name:"end"})))}get el(){return this}static get style(){return{ios:eA,md:tA}}},[33,"ion-toolbar",{color:[513]},[[0,"ionStyle","childrenStyle"]]]);function vw(){if(typeof customElements>"u")return;["ion-toolbar"].forEach(t=>{switch(t){case"ion-toolbar":customElements.get(t)||customElements.define(t,gw);break}})}var yw=mw;var bw=vw;var ww=(e,t,n)=>{let o,i=()=>!(t()===void 0||e.label!==void 0||n()===null),r=()=>{i()&&je(()=>{s()})},s=()=>{let l=t();if(l===void 0)return;if(!i()){l.style.removeProperty("width");return}let c=n().scrollWidth;if(c===0&&l.offsetParent===null&&Ct!==void 0&&"IntersectionObserver"in Ct){if(o!==void 0)return;let u=o=new IntersectionObserver(d=>{d[0].intersectionRatio===1&&(s(),u.disconnect(),o=void 0)},{threshold:.01,root:e});u.observe(l);return}l.style.setProperty("width",`${c*.75}px`)};return{calculateNotchWidth:r,destroy:()=>{o&&(o.disconnect(),o=void 0)}}};var Cw=(e,t,n)=>{let o,i;if(Ct!==void 0&&"MutationObserver"in Ct){let l=Array.isArray(t)?t:[t];o=new MutationObserver(c=>{for(let u of c)for(let d of u.addedNodes)if(d.nodeType===Node.ELEMENT_NODE&&l.includes(d.slot)){n(),je(()=>r(d));return}}),o.observe(e,{childList:!0,subtree:!0})}let r=l=>{var c;i&&(i.disconnect(),i=void 0),i=new MutationObserver(u=>{n();for(let d of u)for(let p of d.removedNodes)p.nodeType===Node.ELEMENT_NODE&&p.slot===t&&a()}),i.observe((c=l.parentElement)!==null&&c!==void 0?c:l,{subtree:!0,childList:!0})},s=()=>{o&&(o.disconnect(),o=void 0),a()},a=()=>{i&&(i.disconnect(),i=void 0)};return{destroy:s}},Dw=(e,t,n)=>{let o=e==null?0:e.toString().length,i=nA(o,t);if(n===void 0)return i;try{return n(o,t)}catch(r){return Eo("[ion-input] - Exception in provided `counterFormatter`:",r),i}},nA=(e,t)=>`${e} / ${t}`;var oA=".sc-ion-input-ios-h{--placeholder-color:initial;--placeholder-font-style:initial;--placeholder-font-weight:initial;--placeholder-opacity:var(--ion-placeholder-opacity, 0.6);--padding-top:0px;--padding-end:0px;--padding-bottom:0px;--padding-start:0px;--background:transparent;--color:initial;--border-style:solid;--highlight-color-focused:var(--ion-color-primary, #0054e9);--highlight-color-valid:var(--ion-color-success, #2dd55b);--highlight-color-invalid:var(--ion-color-danger, #c5000f);--highlight-color:var(--highlight-color-focused);display:block;position:relative;width:100%;min-height:44px;padding:0 !important;color:var(--color);font-family:var(--ion-font-family, inherit);z-index:2}ion-item[slot=start].sc-ion-input-ios-h,ion-item [slot=start].sc-ion-input-ios-h,ion-item[slot=end].sc-ion-input-ios-h,ion-item [slot=end].sc-ion-input-ios-h{width:auto}.ion-color.sc-ion-input-ios-h{--highlight-color-focused:var(--ion-color-base)}.input-label-placement-floating.sc-ion-input-ios-h,.input-label-placement-stacked.sc-ion-input-ios-h{min-height:56px}.native-input.sc-ion-input-ios{padding-left:0;padding-right:0;padding-top:0;padding-bottom:0;font-family:inherit;font-size:inherit;font-style:inherit;font-weight:inherit;letter-spacing:inherit;text-decoration:inherit;text-indent:inherit;text-overflow:inherit;text-transform:inherit;text-align:inherit;white-space:inherit;color:inherit;display:inline-block;position:relative;-ms-flex:1;flex:1;width:100%;max-width:100%;height:100%;max-height:100%;border:0;outline:none;background:transparent;-webkit-box-sizing:border-box;box-sizing:border-box;-webkit-appearance:none;-moz-appearance:none;appearance:none;z-index:1}.native-input.sc-ion-input-ios::-webkit-input-placeholder{color:var(--placeholder-color);font-family:inherit;font-style:var(--placeholder-font-style);font-weight:var(--placeholder-font-weight);opacity:var(--placeholder-opacity)}.native-input.sc-ion-input-ios::-moz-placeholder{color:var(--placeholder-color);font-family:inherit;font-style:var(--placeholder-font-style);font-weight:var(--placeholder-font-weight);opacity:var(--placeholder-opacity)}.native-input.sc-ion-input-ios:-ms-input-placeholder{color:var(--placeholder-color);font-family:inherit;font-style:var(--placeholder-font-style);font-weight:var(--placeholder-font-weight);opacity:var(--placeholder-opacity)}.native-input.sc-ion-input-ios::-ms-input-placeholder{color:var(--placeholder-color);font-family:inherit;font-style:var(--placeholder-font-style);font-weight:var(--placeholder-font-weight);opacity:var(--placeholder-opacity)}.native-input.sc-ion-input-ios::placeholder{color:var(--placeholder-color);font-family:inherit;font-style:var(--placeholder-font-style);font-weight:var(--placeholder-font-weight);opacity:var(--placeholder-opacity)}.native-input.sc-ion-input-ios:-webkit-autofill{background-color:transparent}.native-input.sc-ion-input-ios:invalid{-webkit-box-shadow:none;box-shadow:none}.native-input.sc-ion-input-ios::-ms-clear{display:none}.cloned-input.sc-ion-input-ios{top:0;bottom:0;position:absolute;pointer-events:none}.cloned-input.sc-ion-input-ios{inset-inline-start:0}.cloned-input.sc-ion-input-ios:disabled{opacity:1}.input-clear-icon.sc-ion-input-ios{-webkit-margin-start:auto;margin-inline-start:auto;-webkit-margin-end:auto;margin-inline-end:auto;margin-top:auto;margin-bottom:auto;padding-left:0;padding-right:0;padding-top:0;padding-bottom:0;background-position:center;display:-ms-flexbox;display:flex;-ms-flex-align:center;align-items:center;-ms-flex-pack:center;justify-content:center;width:30px;height:30px;border:0;outline:none;background-color:transparent;background-repeat:no-repeat;color:var(--ion-color-step-600, var(--ion-text-color-step-400, #666666));visibility:hidden;-webkit-appearance:none;-moz-appearance:none;appearance:none}.in-item-color.sc-ion-input-ios-h .input-clear-icon.sc-ion-input-ios{color:inherit}.input-clear-icon.sc-ion-input-ios:focus{opacity:0.5}.has-value.sc-ion-input-ios-h .input-clear-icon.sc-ion-input-ios{visibility:visible}.input-wrapper.sc-ion-input-ios{-webkit-padding-start:var(--padding-start);padding-inline-start:var(--padding-start);-webkit-padding-end:var(--padding-end);padding-inline-end:var(--padding-end);padding-top:var(--padding-top);padding-bottom:var(--padding-bottom);border-radius:var(--border-radius);display:-ms-flexbox;display:flex;position:relative;-ms-flex-positive:1;flex-grow:1;-ms-flex-align:stretch;align-items:stretch;height:inherit;min-height:inherit;-webkit-transition:background-color 15ms linear;transition:background-color 15ms linear;background:var(--background);line-height:normal}.native-wrapper.sc-ion-input-ios{display:-ms-flexbox;display:flex;position:relative;-ms-flex-positive:1;flex-grow:1;-ms-flex-align:center;align-items:center;width:100%}.ion-touched.ion-invalid.sc-ion-input-ios-h{--highlight-color:var(--highlight-color-invalid)}.ion-valid.sc-ion-input-ios-h{--highlight-color:var(--highlight-color-valid)}.input-bottom.sc-ion-input-ios{-webkit-padding-start:var(--padding-start);padding-inline-start:var(--padding-start);-webkit-padding-end:var(--padding-end);padding-inline-end:var(--padding-end);padding-top:5px;padding-bottom:0;display:-ms-flexbox;display:flex;-ms-flex-pack:justify;justify-content:space-between;border-top:var(--border-width) var(--border-style) var(--border-color);font-size:0.75rem;white-space:normal}.has-focus.ion-valid.sc-ion-input-ios-h,.ion-touched.ion-invalid.sc-ion-input-ios-h{--border-color:var(--highlight-color)}.input-bottom.sc-ion-input-ios .error-text.sc-ion-input-ios{display:none;color:var(--highlight-color-invalid)}.input-bottom.sc-ion-input-ios .helper-text.sc-ion-input-ios{display:block;color:var(--ion-color-step-700, var(--ion-text-color-step-300, #4d4d4d))}.ion-touched.ion-invalid.sc-ion-input-ios-h .input-bottom.sc-ion-input-ios .error-text.sc-ion-input-ios{display:block}.ion-touched.ion-invalid.sc-ion-input-ios-h .input-bottom.sc-ion-input-ios .helper-text.sc-ion-input-ios{display:none}.input-bottom.sc-ion-input-ios .counter.sc-ion-input-ios{-webkit-margin-start:auto;margin-inline-start:auto;color:var(--ion-color-step-700, var(--ion-text-color-step-300, #4d4d4d));white-space:nowrap;-webkit-padding-start:16px;padding-inline-start:16px}.has-focus.sc-ion-input-ios-h input.sc-ion-input-ios{caret-color:var(--highlight-color)}.label-text-wrapper.sc-ion-input-ios{display:-ms-flexbox;display:flex;-ms-flex-align:center;align-items:center;max-width:200px;-webkit-transition:color 150ms cubic-bezier(0.4, 0, 0.2, 1), -webkit-transform 150ms cubic-bezier(0.4, 0, 0.2, 1);transition:color 150ms cubic-bezier(0.4, 0, 0.2, 1), -webkit-transform 150ms cubic-bezier(0.4, 0, 0.2, 1);transition:color 150ms cubic-bezier(0.4, 0, 0.2, 1), transform 150ms cubic-bezier(0.4, 0, 0.2, 1);transition:color 150ms cubic-bezier(0.4, 0, 0.2, 1), transform 150ms cubic-bezier(0.4, 0, 0.2, 1), -webkit-transform 150ms cubic-bezier(0.4, 0, 0.2, 1);pointer-events:none}.label-text.sc-ion-input-ios,.sc-ion-input-ios-s>[slot=label]{text-overflow:ellipsis;white-space:nowrap;overflow:hidden}.label-text-wrapper-hidden.sc-ion-input-ios,.input-outline-notch-hidden.sc-ion-input-ios{display:none}.input-wrapper.sc-ion-input-ios input.sc-ion-input-ios{-webkit-transition:opacity 150ms cubic-bezier(0.4, 0, 0.2, 1);transition:opacity 150ms cubic-bezier(0.4, 0, 0.2, 1)}.input-label-placement-start.sc-ion-input-ios-h .input-wrapper.sc-ion-input-ios{-ms-flex-direction:row;flex-direction:row}.input-label-placement-start.sc-ion-input-ios-h .label-text-wrapper.sc-ion-input-ios{-webkit-margin-start:0;margin-inline-start:0;-webkit-margin-end:16px;margin-inline-end:16px;margin-top:0;margin-bottom:0}.input-label-placement-end.sc-ion-input-ios-h .input-wrapper.sc-ion-input-ios{-ms-flex-direction:row-reverse;flex-direction:row-reverse}.input-label-placement-end.sc-ion-input-ios-h .label-text-wrapper.sc-ion-input-ios{-webkit-margin-start:16px;margin-inline-start:16px;-webkit-margin-end:0;margin-inline-end:0;margin-top:0;margin-bottom:0}.input-label-placement-fixed.sc-ion-input-ios-h .label-text-wrapper.sc-ion-input-ios{-webkit-margin-start:0;margin-inline-start:0;-webkit-margin-end:16px;margin-inline-end:16px;margin-top:0;margin-bottom:0}.input-label-placement-fixed.sc-ion-input-ios-h .label-text.sc-ion-input-ios{-ms-flex:0 0 100px;flex:0 0 100px;width:100px;min-width:100px;max-width:200px}.input-label-placement-stacked.sc-ion-input-ios-h .input-wrapper.sc-ion-input-ios,.input-label-placement-floating.sc-ion-input-ios-h .input-wrapper.sc-ion-input-ios{-ms-flex-direction:column;flex-direction:column;-ms-flex-align:start;align-items:start}.input-label-placement-stacked.sc-ion-input-ios-h .label-text-wrapper.sc-ion-input-ios,.input-label-placement-floating.sc-ion-input-ios-h .label-text-wrapper.sc-ion-input-ios{-webkit-transform-origin:left top;transform-origin:left top;max-width:100%;z-index:2}[dir=rtl].sc-ion-input-ios-h -no-combinator.input-label-placement-stacked.sc-ion-input-ios-h .label-text-wrapper.sc-ion-input-ios,[dir=rtl] .sc-ion-input-ios-h -no-combinator.input-label-placement-stacked.sc-ion-input-ios-h .label-text-wrapper.sc-ion-input-ios,[dir=rtl].input-label-placement-stacked.sc-ion-input-ios-h .label-text-wrapper.sc-ion-input-ios,[dir=rtl] .input-label-placement-stacked.sc-ion-input-ios-h .label-text-wrapper.sc-ion-input-ios,[dir=rtl].sc-ion-input-ios-h -no-combinator.input-label-placement-floating.sc-ion-input-ios-h .label-text-wrapper.sc-ion-input-ios,[dir=rtl] .sc-ion-input-ios-h -no-combinator.input-label-placement-floating.sc-ion-input-ios-h .label-text-wrapper.sc-ion-input-ios,[dir=rtl].input-label-placement-floating.sc-ion-input-ios-h .label-text-wrapper.sc-ion-input-ios,[dir=rtl] .input-label-placement-floating.sc-ion-input-ios-h .label-text-wrapper.sc-ion-input-ios{-webkit-transform-origin:right top;transform-origin:right top}@supports selector(:dir(rtl)){.input-label-placement-stacked.sc-ion-input-ios-h:dir(rtl) .label-text-wrapper.sc-ion-input-ios,.input-label-placement-floating.sc-ion-input-ios-h:dir(rtl) .label-text-wrapper.sc-ion-input-ios{-webkit-transform-origin:right top;transform-origin:right top}}.input-label-placement-stacked.sc-ion-input-ios-h input.sc-ion-input-ios,.input-label-placement-floating.sc-ion-input-ios-h input.sc-ion-input-ios{margin-left:0;margin-right:0;margin-top:1px;margin-bottom:0}.input-label-placement-floating.sc-ion-input-ios-h .label-text-wrapper.sc-ion-input-ios{-webkit-transform:translateY(100%) scale(1);transform:translateY(100%) scale(1)}.input-label-placement-floating.sc-ion-input-ios-h input.sc-ion-input-ios{opacity:0}.has-focus.input-label-placement-floating.sc-ion-input-ios-h input.sc-ion-input-ios,.has-value.input-label-placement-floating.sc-ion-input-ios-h input.sc-ion-input-ios{opacity:1}.label-floating.sc-ion-input-ios-h .label-text-wrapper.sc-ion-input-ios{-webkit-transform:translateY(50%) scale(0.75);transform:translateY(50%) scale(0.75);max-width:calc(100% / 0.75)}.sc-ion-input-ios-s>[slot=start]:last-of-type{-webkit-margin-end:16px;margin-inline-end:16px;-webkit-margin-start:0;margin-inline-start:0}.sc-ion-input-ios-s>[slot=end]:first-of-type{-webkit-margin-start:16px;margin-inline-start:16px;-webkit-margin-end:0;margin-inline-end:0}.sc-ion-input-ios-h[disabled].sc-ion-input-ios-s>ion-input-password-toggle,.sc-ion-input-ios-h[disabled] .sc-ion-input-ios-s>ion-input-password-toggle,.sc-ion-input-ios-h[readonly].sc-ion-input-ios-s>ion-input-password-toggle,.sc-ion-input-ios-h[readonly] .sc-ion-input-ios-s>ion-input-password-toggle{visibility:hidden}.sc-ion-input-ios-h{--border-width:0.55px;--border-color:var(--ion-item-border-color, var(--ion-border-color, var(--ion-color-step-250, var(--ion-background-color-step-250, #c8c7cc))));--highlight-height:0px;font-size:inherit}.input-clear-icon.sc-ion-input-ios ion-icon.sc-ion-input-ios{width:18px;height:18px}.input-disabled.sc-ion-input-ios-h{opacity:0.3}.sc-ion-input-ios-s>ion-button[slot=start].button-has-icon-only,.sc-ion-input-ios-s>ion-button[slot=end].button-has-icon-only{--border-radius:50%;--padding-start:0;--padding-end:0;--padding-top:0;--padding-bottom:0;aspect-ratio:1}",iA=".sc-ion-input-md-h{--placeholder-color:initial;--placeholder-font-style:initial;--placeholder-font-weight:initial;--placeholder-opacity:var(--ion-placeholder-opacity, 0.6);--padding-top:0px;--padding-end:0px;--padding-bottom:0px;--padding-start:0px;--background:transparent;--color:initial;--border-style:solid;--highlight-color-focused:var(--ion-color-primary, #0054e9);--highlight-color-valid:var(--ion-color-success, #2dd55b);--highlight-color-invalid:var(--ion-color-danger, #c5000f);--highlight-color:var(--highlight-color-focused);display:block;position:relative;width:100%;min-height:44px;padding:0 !important;color:var(--color);font-family:var(--ion-font-family, inherit);z-index:2}ion-item[slot=start].sc-ion-input-md-h,ion-item [slot=start].sc-ion-input-md-h,ion-item[slot=end].sc-ion-input-md-h,ion-item [slot=end].sc-ion-input-md-h{width:auto}.ion-color.sc-ion-input-md-h{--highlight-color-focused:var(--ion-color-base)}.input-label-placement-floating.sc-ion-input-md-h,.input-label-placement-stacked.sc-ion-input-md-h{min-height:56px}.native-input.sc-ion-input-md{padding-left:0;padding-right:0;padding-top:0;padding-bottom:0;font-family:inherit;font-size:inherit;font-style:inherit;font-weight:inherit;letter-spacing:inherit;text-decoration:inherit;text-indent:inherit;text-overflow:inherit;text-transform:inherit;text-align:inherit;white-space:inherit;color:inherit;display:inline-block;position:relative;-ms-flex:1;flex:1;width:100%;max-width:100%;height:100%;max-height:100%;border:0;outline:none;background:transparent;-webkit-box-sizing:border-box;box-sizing:border-box;-webkit-appearance:none;-moz-appearance:none;appearance:none;z-index:1}.native-input.sc-ion-input-md::-webkit-input-placeholder{color:var(--placeholder-color);font-family:inherit;font-style:var(--placeholder-font-style);font-weight:var(--placeholder-font-weight);opacity:var(--placeholder-opacity)}.native-input.sc-ion-input-md::-moz-placeholder{color:var(--placeholder-color);font-family:inherit;font-style:var(--placeholder-font-style);font-weight:var(--placeholder-font-weight);opacity:var(--placeholder-opacity)}.native-input.sc-ion-input-md:-ms-input-placeholder{color:var(--placeholder-color);font-family:inherit;font-style:var(--placeholder-font-style);font-weight:var(--placeholder-font-weight);opacity:var(--placeholder-opacity)}.native-input.sc-ion-input-md::-ms-input-placeholder{color:var(--placeholder-color);font-family:inherit;font-style:var(--placeholder-font-style);font-weight:var(--placeholder-font-weight);opacity:var(--placeholder-opacity)}.native-input.sc-ion-input-md::placeholder{color:var(--placeholder-color);font-family:inherit;font-style:var(--placeholder-font-style);font-weight:var(--placeholder-font-weight);opacity:var(--placeholder-opacity)}.native-input.sc-ion-input-md:-webkit-autofill{background-color:transparent}.native-input.sc-ion-input-md:invalid{-webkit-box-shadow:none;box-shadow:none}.native-input.sc-ion-input-md::-ms-clear{display:none}.cloned-input.sc-ion-input-md{top:0;bottom:0;position:absolute;pointer-events:none}.cloned-input.sc-ion-input-md{inset-inline-start:0}.cloned-input.sc-ion-input-md:disabled{opacity:1}.input-clear-icon.sc-ion-input-md{-webkit-margin-start:auto;margin-inline-start:auto;-webkit-margin-end:auto;margin-inline-end:auto;margin-top:auto;margin-bottom:auto;padding-left:0;padding-right:0;padding-top:0;padding-bottom:0;background-position:center;display:-ms-flexbox;display:flex;-ms-flex-align:center;align-items:center;-ms-flex-pack:center;justify-content:center;width:30px;height:30px;border:0;outline:none;background-color:transparent;background-repeat:no-repeat;color:var(--ion-color-step-600, var(--ion-text-color-step-400, #666666));visibility:hidden;-webkit-appearance:none;-moz-appearance:none;appearance:none}.in-item-color.sc-ion-input-md-h .input-clear-icon.sc-ion-input-md{color:inherit}.input-clear-icon.sc-ion-input-md:focus{opacity:0.5}.has-value.sc-ion-input-md-h .input-clear-icon.sc-ion-input-md{visibility:visible}.input-wrapper.sc-ion-input-md{-webkit-padding-start:var(--padding-start);padding-inline-start:var(--padding-start);-webkit-padding-end:var(--padding-end);padding-inline-end:var(--padding-end);padding-top:var(--padding-top);padding-bottom:var(--padding-bottom);border-radius:var(--border-radius);display:-ms-flexbox;display:flex;position:relative;-ms-flex-positive:1;flex-grow:1;-ms-flex-align:stretch;align-items:stretch;height:inherit;min-height:inherit;-webkit-transition:background-color 15ms linear;transition:background-color 15ms linear;background:var(--background);line-height:normal}.native-wrapper.sc-ion-input-md{display:-ms-flexbox;display:flex;position:relative;-ms-flex-positive:1;flex-grow:1;-ms-flex-align:center;align-items:center;width:100%}.ion-touched.ion-invalid.sc-ion-input-md-h{--highlight-color:var(--highlight-color-invalid)}.ion-valid.sc-ion-input-md-h{--highlight-color:var(--highlight-color-valid)}.input-bottom.sc-ion-input-md{-webkit-padding-start:var(--padding-start);padding-inline-start:var(--padding-start);-webkit-padding-end:var(--padding-end);padding-inline-end:var(--padding-end);padding-top:5px;padding-bottom:0;display:-ms-flexbox;display:flex;-ms-flex-pack:justify;justify-content:space-between;border-top:var(--border-width) var(--border-style) var(--border-color);font-size:0.75rem;white-space:normal}.has-focus.ion-valid.sc-ion-input-md-h,.ion-touched.ion-invalid.sc-ion-input-md-h{--border-color:var(--highlight-color)}.input-bottom.sc-ion-input-md .error-text.sc-ion-input-md{display:none;color:var(--highlight-color-invalid)}.input-bottom.sc-ion-input-md .helper-text.sc-ion-input-md{display:block;color:var(--ion-color-step-700, var(--ion-text-color-step-300, #4d4d4d))}.ion-touched.ion-invalid.sc-ion-input-md-h .input-bottom.sc-ion-input-md .error-text.sc-ion-input-md{display:block}.ion-touched.ion-invalid.sc-ion-input-md-h .input-bottom.sc-ion-input-md .helper-text.sc-ion-input-md{display:none}.input-bottom.sc-ion-input-md .counter.sc-ion-input-md{-webkit-margin-start:auto;margin-inline-start:auto;color:var(--ion-color-step-700, var(--ion-text-color-step-300, #4d4d4d));white-space:nowrap;-webkit-padding-start:16px;padding-inline-start:16px}.has-focus.sc-ion-input-md-h input.sc-ion-input-md{caret-color:var(--highlight-color)}.label-text-wrapper.sc-ion-input-md{display:-ms-flexbox;display:flex;-ms-flex-align:center;align-items:center;max-width:200px;-webkit-transition:color 150ms cubic-bezier(0.4, 0, 0.2, 1), -webkit-transform 150ms cubic-bezier(0.4, 0, 0.2, 1);transition:color 150ms cubic-bezier(0.4, 0, 0.2, 1), -webkit-transform 150ms cubic-bezier(0.4, 0, 0.2, 1);transition:color 150ms cubic-bezier(0.4, 0, 0.2, 1), transform 150ms cubic-bezier(0.4, 0, 0.2, 1);transition:color 150ms cubic-bezier(0.4, 0, 0.2, 1), transform 150ms cubic-bezier(0.4, 0, 0.2, 1), -webkit-transform 150ms cubic-bezier(0.4, 0, 0.2, 1);pointer-events:none}.label-text.sc-ion-input-md,.sc-ion-input-md-s>[slot=label]{text-overflow:ellipsis;white-space:nowrap;overflow:hidden}.label-text-wrapper-hidden.sc-ion-input-md,.input-outline-notch-hidden.sc-ion-input-md{display:none}.input-wrapper.sc-ion-input-md input.sc-ion-input-md{-webkit-transition:opacity 150ms cubic-bezier(0.4, 0, 0.2, 1);transition:opacity 150ms cubic-bezier(0.4, 0, 0.2, 1)}.input-label-placement-start.sc-ion-input-md-h .input-wrapper.sc-ion-input-md{-ms-flex-direction:row;flex-direction:row}.input-label-placement-start.sc-ion-input-md-h .label-text-wrapper.sc-ion-input-md{-webkit-margin-start:0;margin-inline-start:0;-webkit-margin-end:16px;margin-inline-end:16px;margin-top:0;margin-bottom:0}.input-label-placement-end.sc-ion-input-md-h .input-wrapper.sc-ion-input-md{-ms-flex-direction:row-reverse;flex-direction:row-reverse}.input-label-placement-end.sc-ion-input-md-h .label-text-wrapper.sc-ion-input-md{-webkit-margin-start:16px;margin-inline-start:16px;-webkit-margin-end:0;margin-inline-end:0;margin-top:0;margin-bottom:0}.input-label-placement-fixed.sc-ion-input-md-h .label-text-wrapper.sc-ion-input-md{-webkit-margin-start:0;margin-inline-start:0;-webkit-margin-end:16px;margin-inline-end:16px;margin-top:0;margin-bottom:0}.input-label-placement-fixed.sc-ion-input-md-h .label-text.sc-ion-input-md{-ms-flex:0 0 100px;flex:0 0 100px;width:100px;min-width:100px;max-width:200px}.input-label-placement-stacked.sc-ion-input-md-h .input-wrapper.sc-ion-input-md,.input-label-placement-floating.sc-ion-input-md-h .input-wrapper.sc-ion-input-md{-ms-flex-direction:column;flex-direction:column;-ms-flex-align:start;align-items:start}.input-label-placement-stacked.sc-ion-input-md-h .label-text-wrapper.sc-ion-input-md,.input-label-placement-floating.sc-ion-input-md-h .label-text-wrapper.sc-ion-input-md{-webkit-transform-origin:left top;transform-origin:left top;max-width:100%;z-index:2}[dir=rtl].sc-ion-input-md-h -no-combinator.input-label-placement-stacked.sc-ion-input-md-h .label-text-wrapper.sc-ion-input-md,[dir=rtl] .sc-ion-input-md-h -no-combinator.input-label-placement-stacked.sc-ion-input-md-h .label-text-wrapper.sc-ion-input-md,[dir=rtl].input-label-placement-stacked.sc-ion-input-md-h .label-text-wrapper.sc-ion-input-md,[dir=rtl] .input-label-placement-stacked.sc-ion-input-md-h .label-text-wrapper.sc-ion-input-md,[dir=rtl].sc-ion-input-md-h -no-combinator.input-label-placement-floating.sc-ion-input-md-h .label-text-wrapper.sc-ion-input-md,[dir=rtl] .sc-ion-input-md-h -no-combinator.input-label-placement-floating.sc-ion-input-md-h .label-text-wrapper.sc-ion-input-md,[dir=rtl].input-label-placement-floating.sc-ion-input-md-h .label-text-wrapper.sc-ion-input-md,[dir=rtl] .input-label-placement-floating.sc-ion-input-md-h .label-text-wrapper.sc-ion-input-md{-webkit-transform-origin:right top;transform-origin:right top}@supports selector(:dir(rtl)){.input-label-placement-stacked.sc-ion-input-md-h:dir(rtl) .label-text-wrapper.sc-ion-input-md,.input-label-placement-floating.sc-ion-input-md-h:dir(rtl) .label-text-wrapper.sc-ion-input-md{-webkit-transform-origin:right top;transform-origin:right top}}.input-label-placement-stacked.sc-ion-input-md-h input.sc-ion-input-md,.input-label-placement-floating.sc-ion-input-md-h input.sc-ion-input-md{margin-left:0;margin-right:0;margin-top:1px;margin-bottom:0}.input-label-placement-floating.sc-ion-input-md-h .label-text-wrapper.sc-ion-input-md{-webkit-transform:translateY(100%) scale(1);transform:translateY(100%) scale(1)}.input-label-placement-floating.sc-ion-input-md-h input.sc-ion-input-md{opacity:0}.has-focus.input-label-placement-floating.sc-ion-input-md-h input.sc-ion-input-md,.has-value.input-label-placement-floating.sc-ion-input-md-h input.sc-ion-input-md{opacity:1}.label-floating.sc-ion-input-md-h .label-text-wrapper.sc-ion-input-md{-webkit-transform:translateY(50%) scale(0.75);transform:translateY(50%) scale(0.75);max-width:calc(100% / 0.75)}.sc-ion-input-md-s>[slot=start]:last-of-type{-webkit-margin-end:16px;margin-inline-end:16px;-webkit-margin-start:0;margin-inline-start:0}.sc-ion-input-md-s>[slot=end]:first-of-type{-webkit-margin-start:16px;margin-inline-start:16px;-webkit-margin-end:0;margin-inline-end:0}.sc-ion-input-md-h[disabled].sc-ion-input-md-s>ion-input-password-toggle,.sc-ion-input-md-h[disabled] .sc-ion-input-md-s>ion-input-password-toggle,.sc-ion-input-md-h[readonly].sc-ion-input-md-s>ion-input-password-toggle,.sc-ion-input-md-h[readonly] .sc-ion-input-md-s>ion-input-password-toggle{visibility:hidden}.input-fill-solid.sc-ion-input-md-h{--background:var(--ion-color-step-50, var(--ion-background-color-step-50, #f2f2f2));--border-color:var(--ion-color-step-500, var(--ion-background-color-step-500, gray));--border-radius:4px;--padding-start:16px;--padding-end:16px;min-height:56px}.input-fill-solid.sc-ion-input-md-h .input-wrapper.sc-ion-input-md{border-bottom:var(--border-width) var(--border-style) var(--border-color)}.has-focus.input-fill-solid.ion-valid.sc-ion-input-md-h,.input-fill-solid.ion-touched.ion-invalid.sc-ion-input-md-h{--border-color:var(--highlight-color)}.input-fill-solid.sc-ion-input-md-h .input-bottom.sc-ion-input-md{border-top:none}@media (any-hover: hover){.input-fill-solid.sc-ion-input-md-h:hover{--background:var(--ion-color-step-100, var(--ion-background-color-step-100, #e6e6e6));--border-color:var(--ion-color-step-750, var(--ion-background-color-step-750, #404040))}}.input-fill-solid.has-focus.sc-ion-input-md-h{--background:var(--ion-color-step-150, var(--ion-background-color-step-150, #d9d9d9));--border-color:var(--ion-color-step-750, var(--ion-background-color-step-750, #404040))}.input-fill-solid.sc-ion-input-md-h .input-wrapper.sc-ion-input-md{border-start-start-radius:var(--border-radius);border-start-end-radius:var(--border-radius);border-end-end-radius:0px;border-end-start-radius:0px}.label-floating.input-fill-solid.input-label-placement-floating.sc-ion-input-md-h .label-text-wrapper.sc-ion-input-md{max-width:calc(100% / 0.75)}.input-fill-outline.sc-ion-input-md-h{--border-color:var(--ion-color-step-300, var(--ion-background-color-step-300, #b3b3b3));--border-radius:4px;--padding-start:16px;--padding-end:16px;min-height:56px}.input-fill-outline.input-shape-round.sc-ion-input-md-h{--border-radius:28px;--padding-start:32px;--padding-end:32px}.has-focus.input-fill-outline.ion-valid.sc-ion-input-md-h,.input-fill-outline.ion-touched.ion-invalid.sc-ion-input-md-h{--border-color:var(--highlight-color)}@media (any-hover: hover){.input-fill-outline.sc-ion-input-md-h:hover{--border-color:var(--ion-color-step-750, var(--ion-background-color-step-750, #404040))}}.input-fill-outline.has-focus.sc-ion-input-md-h{--border-width:var(--highlight-height);--border-color:var(--highlight-color)}.input-fill-outline.sc-ion-input-md-h .input-bottom.sc-ion-input-md{border-top:none}.input-fill-outline.sc-ion-input-md-h .input-wrapper.sc-ion-input-md{border-bottom:none}.input-fill-outline.input-label-placement-stacked.sc-ion-input-md-h .label-text-wrapper.sc-ion-input-md,.input-fill-outline.input-label-placement-floating.sc-ion-input-md-h .label-text-wrapper.sc-ion-input-md{-webkit-transform-origin:left top;transform-origin:left top;position:absolute;max-width:calc(100% - var(--padding-start) - var(--padding-end))}[dir=rtl].sc-ion-input-md-h -no-combinator.input-fill-outline.input-label-placement-stacked.sc-ion-input-md-h .label-text-wrapper.sc-ion-input-md,[dir=rtl] .sc-ion-input-md-h -no-combinator.input-fill-outline.input-label-placement-stacked.sc-ion-input-md-h .label-text-wrapper.sc-ion-input-md,[dir=rtl].input-fill-outline.input-label-placement-stacked.sc-ion-input-md-h .label-text-wrapper.sc-ion-input-md,[dir=rtl] .input-fill-outline.input-label-placement-stacked.sc-ion-input-md-h .label-text-wrapper.sc-ion-input-md,[dir=rtl].sc-ion-input-md-h -no-combinator.input-fill-outline.input-label-placement-floating.sc-ion-input-md-h .label-text-wrapper.sc-ion-input-md,[dir=rtl] .sc-ion-input-md-h -no-combinator.input-fill-outline.input-label-placement-floating.sc-ion-input-md-h .label-text-wrapper.sc-ion-input-md,[dir=rtl].input-fill-outline.input-label-placement-floating.sc-ion-input-md-h .label-text-wrapper.sc-ion-input-md,[dir=rtl] .input-fill-outline.input-label-placement-floating.sc-ion-input-md-h .label-text-wrapper.sc-ion-input-md{-webkit-transform-origin:right top;transform-origin:right top}@supports selector(:dir(rtl)){.input-fill-outline.input-label-placement-stacked.sc-ion-input-md-h:dir(rtl) .label-text-wrapper.sc-ion-input-md,.input-fill-outline.input-label-placement-floating.sc-ion-input-md-h:dir(rtl) .label-text-wrapper.sc-ion-input-md{-webkit-transform-origin:right top;transform-origin:right top}}.input-fill-outline.sc-ion-input-md-h .label-text-wrapper.sc-ion-input-md{position:relative}.label-floating.input-fill-outline.sc-ion-input-md-h .label-text-wrapper.sc-ion-input-md{-webkit-transform:translateY(-32%) scale(0.75);transform:translateY(-32%) scale(0.75);margin-left:0;margin-right:0;margin-top:0;margin-bottom:0;max-width:calc((100% - var(--padding-start) - var(--padding-end) - 8px) / 0.75)}.input-fill-outline.input-label-placement-stacked.sc-ion-input-md-h input.sc-ion-input-md,.input-fill-outline.input-label-placement-floating.sc-ion-input-md-h input.sc-ion-input-md{margin-left:0;margin-right:0;margin-top:6px;margin-bottom:6px}.input-fill-outline.sc-ion-input-md-h .input-outline-container.sc-ion-input-md{left:0;right:0;top:0;bottom:0;display:-ms-flexbox;display:flex;position:absolute;width:100%;height:100%}.input-fill-outline.sc-ion-input-md-h .input-outline-start.sc-ion-input-md,.input-fill-outline.sc-ion-input-md-h .input-outline-end.sc-ion-input-md{pointer-events:none}.input-fill-outline.sc-ion-input-md-h .input-outline-start.sc-ion-input-md,.input-fill-outline.sc-ion-input-md-h .input-outline-notch.sc-ion-input-md,.input-fill-outline.sc-ion-input-md-h .input-outline-end.sc-ion-input-md{border-top:var(--border-width) var(--border-style) var(--border-color);border-bottom:var(--border-width) var(--border-style) var(--border-color)}.input-fill-outline.sc-ion-input-md-h .input-outline-notch.sc-ion-input-md{max-width:calc(100% - var(--padding-start) - var(--padding-end))}.input-fill-outline.sc-ion-input-md-h .notch-spacer.sc-ion-input-md{-webkit-padding-end:8px;padding-inline-end:8px;font-size:calc(1em * 0.75);opacity:0;pointer-events:none;-webkit-box-sizing:content-box;box-sizing:content-box}.input-fill-outline.sc-ion-input-md-h .input-outline-start.sc-ion-input-md{border-start-start-radius:var(--border-radius);border-start-end-radius:0px;border-end-end-radius:0px;border-end-start-radius:var(--border-radius);-webkit-border-start:var(--border-width) var(--border-style) var(--border-color);border-inline-start:var(--border-width) var(--border-style) var(--border-color);width:calc(var(--padding-start) - 4px)}.input-fill-outline.sc-ion-input-md-h .input-outline-end.sc-ion-input-md{-webkit-border-end:var(--border-width) var(--border-style) var(--border-color);border-inline-end:var(--border-width) var(--border-style) var(--border-color);border-start-start-radius:0px;border-start-end-radius:var(--border-radius);border-end-end-radius:var(--border-radius);border-end-start-radius:0px;-ms-flex-positive:1;flex-grow:1}.label-floating.input-fill-outline.sc-ion-input-md-h .input-outline-notch.sc-ion-input-md{border-top:none}.sc-ion-input-md-h{--border-width:1px;--border-color:var(--ion-item-border-color, var(--ion-border-color, var(--ion-color-step-150, var(--ion-background-color-step-150, rgba(0, 0, 0, 0.13)))));--highlight-height:2px;font-size:inherit}.input-clear-icon.sc-ion-input-md ion-icon.sc-ion-input-md{width:22px;height:22px}.input-disabled.sc-ion-input-md-h{opacity:0.38}.has-focus.ion-valid.sc-ion-input-md-h,.ion-touched.ion-invalid.sc-ion-input-md-h{--border-color:var(--highlight-color)}.input-bottom.sc-ion-input-md .counter.sc-ion-input-md{letter-spacing:0.0333333333em}.input-label-placement-floating.has-focus.sc-ion-input-md-h .label-text-wrapper.sc-ion-input-md,.input-label-placement-stacked.has-focus.sc-ion-input-md-h .label-text-wrapper.sc-ion-input-md{color:var(--highlight-color)}.has-focus.input-label-placement-floating.ion-valid.sc-ion-input-md-h .label-text-wrapper.sc-ion-input-md,.input-label-placement-floating.ion-touched.ion-invalid.sc-ion-input-md-h .label-text-wrapper.sc-ion-input-md,.has-focus.input-label-placement-stacked.ion-valid.sc-ion-input-md-h .label-text-wrapper.sc-ion-input-md,.input-label-placement-stacked.ion-touched.ion-invalid.sc-ion-input-md-h .label-text-wrapper.sc-ion-input-md{color:var(--highlight-color)}.input-highlight.sc-ion-input-md{bottom:-1px;position:absolute;width:100%;height:var(--highlight-height);-webkit-transform:scale(0);transform:scale(0);-webkit-transition:-webkit-transform 200ms;transition:-webkit-transform 200ms;transition:transform 200ms;transition:transform 200ms, -webkit-transform 200ms;background:var(--highlight-color)}.input-highlight.sc-ion-input-md{inset-inline-start:0}.has-focus.sc-ion-input-md-h .input-highlight.sc-ion-input-md{-webkit-transform:scale(1);transform:scale(1)}.in-item.sc-ion-input-md-h .input-highlight.sc-ion-input-md{bottom:0}.in-item.sc-ion-input-md-h .input-highlight.sc-ion-input-md{inset-inline-start:0}.input-shape-round.sc-ion-input-md-h{--border-radius:16px}.sc-ion-input-md-s>ion-button[slot=start].button-has-icon-only,.sc-ion-input-md-s>ion-button[slot=end].button-has-icon-only{--border-radius:50%;--padding-start:8px;--padding-end:8px;--padding-top:8px;--padding-bottom:8px;aspect-ratio:1;min-height:40px}",rA=Q(class extends Z{constructor(){super(),this.__registerHost(),this.ionInput=N(this,"ionInput",7),this.ionChange=N(this,"ionChange",7),this.ionBlur=N(this,"ionBlur",7),this.ionFocus=N(this,"ionFocus",7),this.inputId=`ion-input-${sA++}`,this.helperTextId=`${this.inputId}-helper-text`,this.errorTextId=`${this.inputId}-error-text`,this.inheritedAttributes={},this.isComposing=!1,this.didInputClearOnEdit=!1,this.hasFocus=!1,this.autocapitalize="off",this.autocomplete="off",this.autocorrect="off",this.autofocus=!1,this.clearInput=!1,this.counter=!1,this.disabled=!1,this.labelPlacement="start",this.name=this.inputId,this.readonly=!1,this.required=!1,this.spellcheck=!1,this.type="text",this.value="",this.onInput=t=>{let n=t.target;n&&(this.value=n.value||""),this.emitInputChange(t)},this.onChange=t=>{this.emitValueChange(t)},this.onBlur=t=>{this.hasFocus=!1,this.focusedValue!==this.value&&this.emitValueChange(t),this.didInputClearOnEdit=!1,this.ionBlur.emit(t)},this.onFocus=t=>{this.hasFocus=!0,this.focusedValue=this.value,this.ionFocus.emit(t)},this.onKeydown=t=>{this.checkClearOnEdit(t)},this.onCompositionStart=()=>{this.isComposing=!0},this.onCompositionEnd=()=>{this.isComposing=!1},this.clearTextInput=t=>{this.clearInput&&!this.readonly&&!this.disabled&&t&&(t.preventDefault(),t.stopPropagation(),this.setFocus()),this.value="",this.emitInputChange(t)},this.onLabelClick=t=>{t.target===t.currentTarget&&t.stopPropagation()}}debounceChanged(){let{ionInput:t,debounce:n,originalIonInput:o}=this;this.ionInput=n===void 0?o??t:oh(t,n)}onTypeChange(){let t=this.el.querySelector("ion-input-password-toggle");t&&(t.type=this.type)}valueChanged(){let t=this.nativeInput,n=this.getValue();t&&t.value!==n&&!this.isComposing&&(t.value=n)}onDirChanged(t){this.inheritedAttributes=Object.assign(Object.assign({},this.inheritedAttributes),{dir:t}),ut(this)}onClickCapture(t){let n=this.nativeInput;n&&t.target===n&&(t.stopPropagation(),this.el.click())}componentWillLoad(){this.inheritedAttributes=Object.assign(Object.assign({},bn(this.el)),xo(this.el,["tabindex","title","data-form-type","dir"]))}connectedCallback(){let{el:t}=this;this.slotMutationController=Cw(t,["label","start","end"],()=>ut(this)),this.notchController=ww(t,()=>this.notchSpacerEl,()=>this.labelSlot),this.debounceChanged(),ct.isBrowser&&document.dispatchEvent(new CustomEvent("ionInputDidLoad",{detail:this.el}))}componentDidLoad(){this.originalIonInput=this.ionInput,this.onTypeChange(),this.debounceChanged()}componentDidRender(){var t;(t=this.notchController)===null||t===void 0||t.calculateNotchWidth()}disconnectedCallback(){ct.isBrowser&&document.dispatchEvent(new CustomEvent("ionInputDidUnload",{detail:this.el})),this.slotMutationController&&(this.slotMutationController.destroy(),this.slotMutationController=void 0),this.notchController&&(this.notchController.destroy(),this.notchController=void 0)}setFocus(){return C(this,null,function*(){this.nativeInput&&this.nativeInput.focus()})}getInputElement(){return C(this,null,function*(){return this.nativeInput||(yield new Promise(t=>tt(this.el,t))),Promise.resolve(this.nativeInput)})}emitValueChange(t){let{value:n}=this,o=n==null?n:n.toString();this.focusedValue=o,this.ionChange.emit({value:o,event:t})}emitInputChange(t){let{value:n}=this,o=n==null?n:n.toString();this.ionInput.emit({value:o,event:t})}shouldClearOnEdit(){let{type:t,clearOnEdit:n}=this;return n===void 0?t==="password":n}getValue(){return typeof this.value=="number"?this.value.toString():(this.value||"").toString()}checkClearOnEdit(t){if(!this.shouldClearOnEdit())return;let o=["Enter","Tab","Shift","Meta","Alt","Control"].includes(t.key);!this.didInputClearOnEdit&&this.hasValue()&&!o&&(this.value="",this.emitInputChange(t)),o||(this.didInputClearOnEdit=!0)}hasValue(){return this.getValue().length>0}renderHintText(){let{helperText:t,errorText:n,helperTextId:o,errorTextId:i}=this;return[m("div",{id:o,class:"helper-text"},t),m("div",{id:i,class:"error-text"},n)]}getHintTextID(){let{el:t,helperText:n,errorText:o,helperTextId:i,errorTextId:r}=this;if(t.classList.contains("ion-touched")&&t.classList.contains("ion-invalid")&&o)return r;if(n)return i}renderCounter(){let{counter:t,maxlength:n,counterFormatter:o,value:i}=this;if(!(t!==!0||n===void 0))return m("div",{class:"counter"},Dw(i,n,o))}renderBottomContent(){let{counter:t,helperText:n,errorText:o,maxlength:i}=this;if(!(!(n||o)&&!(t===!0&&i!==void 0)))return m("div",{class:"input-bottom"},this.renderHintText(),this.renderCounter())}renderLabel(){let{label:t}=this;return m("div",{class:{"label-text-wrapper":!0,"label-text-wrapper-hidden":!this.hasLabel}},t===void 0?m("slot",{name:"label"}):m("div",{class:"label-text"},t))}get labelSlot(){return this.el.querySelector('[slot="label"]')}get hasLabel(){return this.label!==void 0||this.labelSlot!==null}renderLabelContainer(){return F(this)==="md"&&this.fill==="outline"?[m("div",{class:"input-outline-container"},m("div",{class:"input-outline-start"}),m("div",{class:{"input-outline-notch":!0,"input-outline-notch-hidden":!this.hasLabel}},m("div",{class:"notch-spacer","aria-hidden":"true",ref:o=>this.notchSpacerEl=o},this.label)),m("div",{class:"input-outline-end"})),this.renderLabel()]:this.renderLabel()}render(){let{disabled:t,fill:n,readonly:o,shape:i,inputId:r,labelPlacement:s,el:a,hasFocus:l,clearInputIcon:c}=this,u=F(this),d=this.getValue(),p=ke("ion-item",this.el),f=u==="md"&&n!=="outline"&&!p,y=c??(u==="ios"?C0:D0),g=this.hasValue(),w=a.querySelector('[slot="start"], [slot="end"]')!==null,P=s==="stacked"||s==="floating"&&(g||l||w);return m(J,{key:"41b2526627e7d2773a80f011b123284203a71ca0",class:et(this.color,{[u]:!0,"has-value":g,"has-focus":l,"label-floating":P,[`input-fill-${n}`]:n!==void 0,[`input-shape-${i}`]:i!==void 0,[`input-label-placement-${s}`]:!0,"in-item":p,"in-item-color":ke("ion-item.ion-color",this.el),"input-disabled":t})},m("label",{key:"9ab078363e32528102b441ad1791d83f86fdcbdc",class:"input-wrapper",htmlFor:r,onClick:this.onLabelClick},this.renderLabelContainer(),m("div",{key:"e34b594980ec62e4c618e827fadf7669a39ad0d8",class:"native-wrapper",onClick:this.onLabelClick},m("slot",{key:"12dc04ead5502e9e5736240e918bf9331bf7b5d9",name:"start"}),m("input",Object.assign({key:"df356eb4ced23109b2c0242f36dc043aba8782d6",class:"native-input",ref:M=>this.nativeInput=M,id:r,disabled:t,autoCapitalize:this.autocapitalize,autoComplete:this.autocomplete,autoCorrect:this.autocorrect,autoFocus:this.autofocus,enterKeyHint:this.enterkeyhint,inputMode:this.inputmode,min:this.min,max:this.max,minLength:this.minlength,maxLength:this.maxlength,multiple:this.multiple,name:this.name,pattern:this.pattern,placeholder:this.placeholder||"",readOnly:o,required:this.required,spellcheck:this.spellcheck,step:this.step,type:this.type,value:d,onInput:this.onInput,onChange:this.onChange,onBlur:this.onBlur,onFocus:this.onFocus,onKeyDown:this.onKeydown,onCompositionstart:this.onCompositionStart,onCompositionend:this.onCompositionEnd,"aria-describedby":this.getHintTextID(),"aria-invalid":this.getHintTextID()===this.errorTextId},this.inheritedAttributes)),this.clearInput&&!o&&!t&&m("button",{key:"f79f68cabcd4ea99419331174a377827db0c0741","aria-label":"reset",type:"button",class:"input-clear-icon",onPointerDown:M=>{M.preventDefault()},onClick:this.clearTextInput},m("ion-icon",{key:"237ec07ec2e10f08818a332bb596578c2c49f770","aria-hidden":"true",icon:y})),m("slot",{key:"1f0a3624aa3e8dc3c307a6762230ab698768a5e5",name:"end"})),f&&m("div",{key:"8a8cbb82695a722a0010b53dd0b1f1f97534a20b",class:"input-highlight"})),this.renderBottomContent())}get el(){return this}static get watchers(){return{debounce:["debounceChanged"],type:["onTypeChange"],value:["valueChanged"],dir:["onDirChanged"]}}static get style(){return{ios:oA,md:iA}}},[38,"ion-input",{color:[513],autocapitalize:[1],autocomplete:[1],autocorrect:[1],autofocus:[4],clearInput:[4,"clear-input"],clearInputIcon:[1,"clear-input-icon"],clearOnEdit:[4,"clear-on-edit"],counter:[4],counterFormatter:[16,"counter-formatter"],debounce:[2],disabled:[516],enterkeyhint:[1],errorText:[1,"error-text"],fill:[1],inputmode:[1],helperText:[1,"helper-text"],label:[1],labelPlacement:[1,"label-placement"],max:[8],maxlength:[2],min:[8],minlength:[2],multiple:[4],name:[1],pattern:[1],placeholder:[1],readonly:[516],required:[4],shape:[1],spellcheck:[4],step:[1],type:[1],value:[1032],hasFocus:[32],setFocus:[64],getInputElement:[64]},[[2,"click","onClickCapture"]],{debounce:["debounceChanged"],type:["onTypeChange"],value:["valueChanged"],dir:["onDirChanged"]}]),sA=0;function aA(){if(typeof customElements>"u")return;["ion-input","ion-icon"].forEach(t=>{switch(t){case"ion-input":customElements.get(t)||customElements.define(t,rA);break;case"ion-icon":customElements.get(t)||Jl();break}})}var Iw=aA;var lA=["outletContent"],kt=["*"];var Dq=(()=>{let e=class ic extends v0{parentOutlet;outletContent;constructor(n,o,i,r,s,a,l,c){super(n,o,i,r,s,a,l,c),this.parentOutlet=c}static \u0275fac=function(o){return new(o||ic)(an("name"),an("tabs"),D(Tt),D(le),D(vt),D(W),D(Qe),D(ic,12))};static \u0275cmp=Ye({type:ic,selectors:[["ion-router-outlet"]],viewQuery:function(o,i){if(o&1&&Ka(lA,7,mt),o&2){let r;Cr(r=Dr())&&(i.outletContent=r.first)}},features:[Ht],ngContentSelectors:kt,decls:3,vars:0,consts:[["outletContent",""]],template:function(o,i){o&1&&(rt(),Qa(0,null,0),st(2),hf())},encapsulation:2})};return e=dt([d0({defineCustomElementFn:_0})],e),e})();var cA=(e,t)=>{let n=e.prototype;t.forEach(o=>{Object.defineProperty(n,o,{get(){return this.el[o]},set(i){this.z.runOutsideAngular(()=>this.el[o]=i)},configurable:!0})})},uA=(e,t)=>{let n=e.prototype;t.forEach(o=>{n[o]=function(){let i=arguments;return this.z.runOutsideAngular(()=>this.el[o].apply(this.el,i))}})},Wp=(e,t,n)=>{n.forEach(o=>e[o]=Uo(t,o))};function Wt(e){return function(n){let{defineCustomElementFn:o,inputs:i,methods:r}=e;return o!==void 0&&o(),i&&cA(n,i),r&&uA(n,r),n}}var Iq=(()=>{let e=class Fp{z;el;constructor(n,o,i){this.z=i,n.detach(),this.el=o.nativeElement}static \u0275fac=function(o){return new(o||Fp)(D(Te),D(le),D(W))};static \u0275cmp=Ye({type:Fp,selectors:[["ion-app"]],ngContentSelectors:kt,decls:1,vars:0,template:function(o,i){o&1&&(rt(),st(0))},encapsulation:2,changeDetection:0})};return e=dt([Wt({defineCustomElementFn:G0,methods:["setFocus"]})],e),e})();var Eq=(()=>{let e=class Lp{z;el;constructor(n,o,i){this.z=i,n.detach(),this.el=o.nativeElement,Wp(this,this.el,["ionFocus","ionBlur"])}static \u0275fac=function(o){return new(o||Lp)(D(Te),D(le),D(W))};static \u0275cmp=Ye({type:Lp,selectors:[["ion-button"]],inputs:{buttonType:"buttonType",color:"color",disabled:"disabled",download:"download",expand:"expand",fill:"fill",form:"form",href:"href",mode:"mode",rel:"rel",routerAnimation:"routerAnimation",routerDirection:"routerDirection",shape:"shape",size:"size",strong:"strong",target:"target",type:"type"},ngContentSelectors:kt,decls:1,vars:0,template:function(o,i){o&1&&(rt(),st(0))},encapsulation:2,changeDetection:0})};return e=dt([Wt({defineCustomElementFn:Z0,inputs:["buttonType","color","disabled","download","expand","fill","form","href","mode","rel","routerAnimation","routerDirection","shape","size","strong","target","type"]})],e),e})();var xq=(()=>{let e=class jp{z;el;constructor(n,o,i){this.z=i,n.detach(),this.el=o.nativeElement,Wp(this,this.el,["ionScrollStart","ionScroll","ionScrollEnd"])}static \u0275fac=function(o){return new(o||jp)(D(Te),D(le),D(W))};static \u0275cmp=Ye({type:jp,selectors:[["ion-content"]],inputs:{color:"color",fixedSlotPlacement:"fixedSlotPlacement",forceOverscroll:"forceOverscroll",fullscreen:"fullscreen",scrollEvents:"scrollEvents",scrollX:"scrollX",scrollY:"scrollY"},ngContentSelectors:kt,decls:1,vars:0,template:function(o,i){o&1&&(rt(),st(0))},encapsulation:2,changeDetection:0})};return e=dt([Wt({defineCustomElementFn:X0,inputs:["color","fixedSlotPlacement","forceOverscroll","fullscreen","scrollEvents","scrollX","scrollY"],methods:["getScrollElement","scrollToTop","scrollToBottom","scrollByPoint","scrollToPoint"]})],e),e})();var Sq=(()=>{let e=class Vp{z;el;constructor(n,o,i){this.z=i,n.detach(),this.el=o.nativeElement}static \u0275fac=function(o){return new(o||Vp)(D(Te),D(le),D(W))};static \u0275cmp=Ye({type:Vp,selectors:[["ion-header"]],inputs:{collapse:"collapse",mode:"mode",translucent:"translucent"},ngContentSelectors:kt,decls:1,vars:0,template:function(o,i){o&1&&(rt(),st(0))},encapsulation:2,changeDetection:0})};return e=dt([Wt({defineCustomElementFn:iw,inputs:["collapse","mode","translucent"]})],e),e})();var _q=(()=>{let e=class Bp{z;el;constructor(n,o,i){this.z=i,n.detach(),this.el=o.nativeElement}static \u0275fac=function(o){return new(o||Bp)(D(Te),D(le),D(W))};static \u0275cmp=Ye({type:Bp,selectors:[["ion-item"]],inputs:{button:"button",color:"color",detail:"detail",detailIcon:"detailIcon",disabled:"disabled",download:"download",href:"href",lines:"lines",mode:"mode",rel:"rel",routerAnimation:"routerAnimation",routerDirection:"routerDirection",target:"target",type:"type"},ngContentSelectors:kt,decls:1,vars:0,template:function(o,i){o&1&&(rt(),st(0))},encapsulation:2,changeDetection:0})};return e=dt([Wt({defineCustomElementFn:aw,inputs:["button","color","detail","detailIcon","disabled","download","href","lines","mode","rel","routerAnimation","routerDirection","target","type"]})],e),e})();var Tq=(()=>{let e=class Hp{z;el;constructor(n,o,i){this.z=i,n.detach(),this.el=o.nativeElement}static \u0275fac=function(o){return new(o||Hp)(D(Te),D(le),D(W))};static \u0275cmp=Ye({type:Hp,selectors:[["ion-label"]],inputs:{color:"color",mode:"mode",position:"position"},ngContentSelectors:kt,decls:1,vars:0,template:function(o,i){o&1&&(rt(),st(0))},encapsulation:2,changeDetection:0})};return e=dt([Wt({defineCustomElementFn:uw,inputs:["color","mode","position"]})],e),e})(),Mq=(()=>{let e=class $p{z;el;constructor(n,o,i){this.z=i,n.detach(),this.el=o.nativeElement}static \u0275fac=function(o){return new(o||$p)(D(Te),D(le),D(W))};static \u0275cmp=Ye({type:$p,selectors:[["ion-list"]],inputs:{inset:"inset",lines:"lines",mode:"mode"},ngContentSelectors:kt,decls:1,vars:0,template:function(o,i){o&1&&(rt(),st(0))},encapsulation:2,changeDetection:0})};return e=dt([Wt({defineCustomElementFn:pw,inputs:["inset","lines","mode"],methods:["closeSlidingItems"]})],e),e})();var kq=(()=>{let e=class zp{z;el;constructor(n,o,i){this.z=i,n.detach(),this.el=o.nativeElement}static \u0275fac=function(o){return new(o||zp)(D(Te),D(le),D(W))};static \u0275cmp=Ye({type:zp,selectors:[["ion-title"]],inputs:{color:"color",size:"size"},ngContentSelectors:kt,decls:1,vars:0,template:function(o,i){o&1&&(rt(),st(0))},encapsulation:2,changeDetection:0})};return e=dt([Wt({defineCustomElementFn:yw,inputs:["color","size"]})],e),e})();var Aq=(()=>{let e=class Up{z;el;constructor(n,o,i){this.z=i,n.detach(),this.el=o.nativeElement}static \u0275fac=function(o){return new(o||Up)(D(Te),D(le),D(W))};static \u0275cmp=Ye({type:Up,selectors:[["ion-toolbar"]],inputs:{color:"color",mode:"mode"},ngContentSelectors:kt,decls:1,vars:0,template:function(o,i){o&1&&(rt(),st(0))},encapsulation:2,changeDetection:0})};return e=dt([Wt({defineCustomElementFn:bw,inputs:["color","mode"]})],e),e})();var dA=(()=>{class e extends is{angularDelegate=v(Ql);injector=v(Ee);environmentInjector=v(ae);constructor(){super(yp),L0()}create(n){return super.create(L(b({},n),{delegate:this.angularDelegate.create(this.environmentInjector,this.injector,"modal")}))}static \u0275fac=function(o){return new(o||e)};static \u0275prov=I({token:e,factory:e.\u0275fac})}return e})(),qp=class extends is{angularDelegate=v(Ql);injector=v(Ee);environmentInjector=v(ae);constructor(){super(bp),q0()}create(t){return super.create(L(b({},t),{delegate:this.angularDelegate.create(this.environmentInjector,this.injector,"popover")}))}},Rq=(e={})=>Yo([{provide:Zl,useValue:e},{provide:qa,useFactory:fA,multi:!0,deps:[Zl,Ce]},b0(),Ql,dA,qp]),fA=(e,t)=>()=>{t.documentElement.classList.add("ion-ce"),gp(e)};var pA=["accept","autocapitalize","autocomplete","autocorrect","autofocus","clearInput","clearOnEdit","color","counter","counterFormatter","debounce","disabled","enterkeyhint","errorText","fill","helperText","inputmode","label","labelPlacement","max","maxlength","min","minlength","mode","multiple","name","pattern","placeholder","readonly","required","shape","size","spellcheck","step","type","value"],hA={provide:Pl,useExisting:tn(()=>mA),multi:!0},mA=(()=>{let e=class Gp extends w0{z;el;constructor(n,o,i,r){super(r,o),this.z=i,n.detach(),this.el=o.nativeElement,Wp(this,this.el,["ionInput","ionChange","ionBlur","ionFocus"])}handleIonInput(n){this.handleValueChange(n,n.value)}registerOnChange(n){super.registerOnChange(o=>{this.type==="number"?n(o===""?null:parseFloat(o)):n(o)})}static \u0275fac=function(o){return new(o||Gp)(D(Te),D(le),D(W),D(Ee))};static \u0275cmp=Ye({type:Gp,selectors:[["ion-input"]],hostBindings:function(o,i){o&1&&cn("ionInput",function(s){return i.handleIonInput(s.target)})},inputs:{accept:"accept",autocapitalize:"autocapitalize",autocomplete:"autocomplete",autocorrect:"autocorrect",autofocus:"autofocus",clearInput:"clearInput",clearOnEdit:"clearOnEdit",color:"color",counter:"counter",counterFormatter:"counterFormatter",debounce:"debounce",disabled:"disabled",enterkeyhint:"enterkeyhint",errorText:"errorText",fill:"fill",helperText:"helperText",inputmode:"inputmode",label:"label",labelPlacement:"labelPlacement",max:"max",maxlength:"maxlength",min:"min",minlength:"minlength",mode:"mode",multiple:"multiple",name:"name",pattern:"pattern",placeholder:"placeholder",readonly:"readonly",required:"required",shape:"shape",size:"size",spellcheck:"spellcheck",step:"step",type:"type",value:"value"},features:[pi([hA]),Ht],ngContentSelectors:kt,decls:1,vars:0,template:function(o,i){o&1&&(rt(),st(0))},encapsulation:2,changeDetection:0})};return e=dt([Wt({defineCustomElementFn:Iw,inputs:pA,methods:["setFocus","getInputElement"]})],e),e})();var Ew=uh("CapacitorSQLite",{web:()=>import("./chunk-VNBY24TM.js").then(e=>new e.CapacitorSQLiteWeb),electron:()=>window.CapacitorCustomPlatform.plugins.CapacitorSQLite});var $q=(()=>{let t=class t{initDB(){return C(this,null,function*(){let o=yield Ew.createConnection({database:"my_database",version:1,encrypted:!1,mode:"no-encryption"});this.db=o,yield this.db.open(),yield this.db.execute(`
      CREATE TABLE IF NOT EXISTS users (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        name TEXT,
        email TEXT
      );
    `)})}getAllUsers(){return C(this,null,function*(){return(yield this.db.query("SELECT * FROM users")).values||[]})}addUser(o,i){return C(this,null,function*(){yield this.db.run("INSERT INTO users (name,email) VALUES (?,?)",[o,i])})}updateUser(o,i,r){return C(this,null,function*(){yield this.db.run("UPDATE users SET name=?, email=? WHERE id=?",[i,r,o])})}deleteUser(o){return C(this,null,function*(){yield this.db.run("DELETE FROM users WHERE id=?",[o])})}};t.\u0275fac=function(i){return new(i||t)},t.\u0275prov=I({token:t,factory:t.\u0275fac,providedIn:"root"});let e=t;return e})();export{Ou as a,Pu as b,Qg as c,D as d,Ye as e,rf as f,ff as g,Wa as h,Ya as i,Za as j,$v as k,cn as l,qv as m,sx as n,Gv as o,mf as p,Wv as q,ux as r,Yv as s,yy as t,Mf as u,iS as v,yb as w,B_ as x,$_ as y,Y_ as z,PU as A,IT as B,LU as C,Sp as D,Dq as E,Iq as F,Eq as G,xq as H,Sq as I,_q as J,Tq as K,Mq as L,kq as M,Aq as N,Rq as O,mA as P,$q as Q};
