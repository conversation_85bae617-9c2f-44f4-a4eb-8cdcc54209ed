import { Injectable } from '@angular/core';
import { CapacitorSQLite, SQLiteDBConnection } from '@capacitor-community/sqlite';

@Injectable({
  providedIn: 'root'
})
export class SqliteCrudopService {
  private db!: SQLiteDBConnection;

  async initDB() {
    try {
      console.log('Starting initDB');
      const isWeb = (window as any).Capacitor?.getPlatform() === 'web';
      console.log('Platform is web:', isWeb);

      if (isWeb) {
        // For web platform, initialize the jeep-sqlite web component
        const jeepSqlite = document.querySelector('jeep-sqlite');
        if (jeepSqlite) {
          if (typeof (jeepSqlite as any).initWebStore === 'function') {
            await (jeepSqlite as any).initWebStore();
            console.log('Initialized jeep-sqlite web store');
          } else {
            console.log('initWebStore method not found on jeep-sqlite, skipping');
          }
        } else {
          console.error('jeep-sqlite element not found in DOM');
        }
      }

      const connection = (await CapacitorSQLite.createConnection({
        database: 'my_database',
        version: 1,
        encrypted: false,
        mode: 'no-encryption'
      })) as unknown as SQLiteDBConnection;

      console.log('Connection object:', connection);

      this.db = connection;
      await this.db.open();

      const createTableQuery = 
        "CREATE TABLE IF NOT EXISTS users (" +
        "id INTEGER PRIMARY KEY AUTOINCREMENT," +
        "name TEXT," +
        "email TEXT" +
        ");";

      await this.db.execute(createTableQuery);
      console.log('Database initialized successfully');
    } catch (error) {
      console.error('Error initializing database:', error);
    }
  }

  async getAllUsers() {
    try {
      const res = await this.db.query('SELECT * FROM users');
      return res.values || [];
    } catch (error) {
      console.error('Error fetching users:', error);
      return [];
    }
  }

  async addUser(name: string, email: string) {
    try {
      await this.db.run('INSERT INTO users (name,email) VALUES (?,?)', [name, email]);
    } catch (error) {
      console.error('Error adding user:', error);
    }
  }

  async updateUser(id: number, name: string, email: string) {
    try {
      await this.db.run('UPDATE users SET name=?, email=? WHERE id=?', [name, email, id]);
    } catch (error) {
      console.error('Error updating user:', error);
    }
  }

  async deleteUser(id: number) {
    try {
      await this.db.run('DELETE FROM users WHERE id=?', [id]);
    } catch (error) {
      console.error('Error deleting user:', error);
    }
  }
}
