import { Injectable } from '@angular/core';
import {
  CapacitorSQLite,
  SQLiteConnection,
  SQLiteDBConnection
} from '@capacitor-community/sqlite';

@Injectable({ providedIn: 'root' })
export class SqliteService {
  private sqlite: SQLiteConnection;
  private db!: SQLiteDBConnection;

  constructor() {
    // ✅ Create SQLiteConnection object
    this.sqlite = new SQLiteConnection(CapacitorSQLite);
  }

  async initDB() {
    try {
      // ✅ Create connection properly
      const conn: SQLiteDBConnection = await this.sqlite.createConnection(
        'mydb',       // database name
        false,        // encrypted
        'no-encryption',
        1             // version
      );

      await conn.open(); // ✅ Open the DB

      this.db = conn;

      // ✅ Create table
      await this.db.execute(`
        CREATE TABLE IF NOT EXISTS users (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          name TEXT,
          email TEXT
        );
      `);

      console.log('✅ Database initialized');
    } catch (err) {
      console.error('❌ DB Init Error:', err);
    }
  }

  async addUser(name: string, email: string) {
    await this.db.run('INSERT INTO users (name,email) VALUES (?,?)', [name, email]);
  }

  async getUsers() {
    const result = await this.db.query('SELECT * FROM users');
    return result.values ?? [];
  }

  async updateUser(id: number, name: string, email: string) {
    await this.db.run('UPDATE users SET name=?, email=? WHERE id=?', [name, email, id]);
  }

  async deleteUser(id: number) {
    await this.db.run('DELETE FROM users WHERE id=?', [id]);
  }
}
