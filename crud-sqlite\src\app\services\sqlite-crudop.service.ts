import { Injectable } from '@angular/core';
import { CapacitorSQLite, SQLiteDBConnection } from '@capacitor-community/sqlite';

@Injectable({ providedIn: 'root' })
export class SqliteCrudopService {
  private db: SQLiteDBConnection | null = null;
  private isInitialized = false;

  async initDB(): Promise<void> {
    if (this.isInitialized) {
      return;
    }

    try {
      console.log('Initializing SQLite database...');

      // Wait a bit for jeep-sqlite to be fully ready on web platform
      const isWeb = (window as any).Capacitor?.getPlatform() === 'web';
      if (isWeb) {
        await new Promise(resolve => setTimeout(resolve, 100));
      }

      // Create connection using the correct method
      const result = await CapacitorSQLite.createConnection({
        database: 'mydb',
        version: 1,
        encrypted: false,
        mode: 'no-encryption'
      });

      this.db = result as unknown as SQLiteDBConnection;
      await this.db.open();

      // Create table
      const createTableQuery = `
        CREATE TABLE IF NOT EXISTS users (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          name TEXT NOT NULL,
          email TEXT NOT NULL
        );
      `;

      await this.db.execute(createTableQuery);
      this.isInitialized = true;
      console.log('Database initialized successfully');
    } catch (error) {
      console.error('Error initializing database:', error);
      throw error;
    }
  }

  private async ensureInitialized(): Promise<void> {
    if (!this.isInitialized || !this.db) {
      await this.initDB();
    }
  }

  async getAllUsers(): Promise<any[]> {
    try {
      await this.ensureInitialized();
      if (!this.db) throw new Error('Database not initialized');

      const result = await this.db.query('SELECT * FROM users');
      return result.values || [];
    } catch (error) {
      console.error('Error fetching users:', error);
      return [];
    }
  }

  async addUser(name: string, email: string): Promise<void> {
    try {
      await this.ensureInitialized();
      if (!this.db) throw new Error('Database not initialized');

      await this.db.run('INSERT INTO users (name, email) VALUES (?, ?)', [name, email]);
    } catch (error) {
      console.error('Error adding user:', error);
      throw error;
    }
  }

  async updateUser(id: number, name: string, email: string): Promise<void> {
    try {
      await this.ensureInitialized();
      if (!this.db) throw new Error('Database not initialized');

      await this.db.run('UPDATE users SET name = ?, email = ? WHERE id = ?', [name, email, id]);
    } catch (error) {
      console.error('Error updating user:', error);
      throw error;
    }
  }

  async deleteUser(id: number): Promise<void> {
    try {
      await this.ensureInitialized();
      if (!this.db) throw new Error('Database not initialized');

      await this.db.run('DELETE FROM users WHERE id = ?', [id]);
    } catch (error) {
      console.error('Error deleting user:', error);
      throw error;
    }
  }
}
