io.ionic.starter.app-sqlite-framework-2.4.0-0 C:\Users\<USER>\.gradle\caches\8.13\transforms\0c74dc3dc468678fcded4dbd7e17823a\transformed\sqlite-framework-2.4.0\res
io.ionic.starter.app-appcompat-resources-1.7.0-1 C:\Users\<USER>\.gradle\caches\8.13\transforms\0d0aa8469f3a45927795e5e14c85ad05\transformed\appcompat-resources-1.7.0\res
io.ionic.starter.app-lifecycle-livedata-core-2.6.2-2 C:\Users\<USER>\.gradle\caches\8.13\transforms\11589e438a78fb6de52e886596aaaa7b\transformed\lifecycle-livedata-core-2.6.2\res
io.ionic.starter.app-lifecycle-viewmodel-savedstate-2.6.2-3 C:\Users\<USER>\.gradle\caches\8.13\transforms\11c075f13e834455872b5f2e6a7d1f09\transformed\lifecycle-viewmodel-savedstate-2.6.2\res
io.ionic.starter.app-core-runtime-2.2.0-4 C:\Users\<USER>\.gradle\caches\8.13\transforms\13d35c67cb3a04e7e3e833e3d793d5df\transformed\core-runtime-2.2.0\res
io.ionic.starter.app-emoji2-views-helper-1.3.0-5 C:\Users\<USER>\.gradle\caches\8.13\transforms\14165bf8eec30afa3e583ffa928fb932\transformed\emoji2-views-helper-1.3.0\res
io.ionic.starter.app-activity-1.9.2-6 C:\Users\<USER>\.gradle\caches\8.13\transforms\1851baad953a72983870260c24fb7d2a\transformed\activity-1.9.2\res
io.ionic.starter.app-lifecycle-viewmodel-2.6.2-7 C:\Users\<USER>\.gradle\caches\8.13\transforms\1a40dcf1dbe43aa4d3fcc9c9bef1a483\transformed\lifecycle-viewmodel-2.6.2\res
io.ionic.starter.app-startup-runtime-1.1.1-8 C:\Users\<USER>\.gradle\caches\8.13\transforms\381b4363b5175c342310fb1bad1257c8\transformed\startup-runtime-1.1.1\res
io.ionic.starter.app-tracing-1.2.0-9 C:\Users\<USER>\.gradle\caches\8.13\transforms\3ee10dd1fe31539ff0654f253cb5fcd3\transformed\tracing-1.2.0\res
io.ionic.starter.app-savedstate-1.2.1-10 C:\Users\<USER>\.gradle\caches\8.13\transforms\4ee185d8f5138a37e2052cba61bdfce2\transformed\savedstate-1.2.1\res
io.ionic.starter.app-core-splashscreen-1.0.1-11 C:\Users\<USER>\.gradle\caches\8.13\transforms\53f40aa6f6d840a54b93ab2747ad6132\transformed\core-splashscreen-1.0.1\res
io.ionic.starter.app-coordinatorlayout-1.2.0-12 C:\Users\<USER>\.gradle\caches\8.13\transforms\7d963e06227f9c96672f16e0a743fb69\transformed\coordinatorlayout-1.2.0\res
io.ionic.starter.app-lifecycle-process-2.6.2-13 C:\Users\<USER>\.gradle\caches\8.13\transforms\7fcaa2d94568f1111aa0d45f264971fb\transformed\lifecycle-process-2.6.2\res
io.ionic.starter.app-sqlcipher-android-4.6.1-14 C:\Users\<USER>\.gradle\caches\8.13\transforms\8844262158cc7282a5b059e3855a2ef7\transformed\sqlcipher-android-4.6.1\res
io.ionic.starter.app-lifecycle-livedata-2.6.2-15 C:\Users\<USER>\.gradle\caches\8.13\transforms\88653deaec603d633e4afa694d403d76\transformed\lifecycle-livedata-2.6.2\res
io.ionic.starter.app-profileinstaller-1.3.1-16 C:\Users\<USER>\.gradle\caches\8.13\transforms\9004adbda7bfb1b3286dec38d922dd38\transformed\profileinstaller-1.3.1\res
io.ionic.starter.app-fragment-1.8.4-17 C:\Users\<USER>\.gradle\caches\8.13\transforms\914c90e97af2aebbb6598590579e1b39\transformed\fragment-1.8.4\res
io.ionic.starter.app-lifecycle-runtime-2.6.2-18 C:\Users\<USER>\.gradle\caches\8.13\transforms\99cf7c220a2a9f2a8cb7b1ae85acd02c\transformed\lifecycle-runtime-2.6.2\res
io.ionic.starter.app-core-ktx-1.15.0-19 C:\Users\<USER>\.gradle\caches\8.13\transforms\a2eff8b0468781740ada75bfca18b6fc\transformed\core-ktx-1.15.0\res
io.ionic.starter.app-room-runtime-2.6.1-20 C:\Users\<USER>\.gradle\caches\8.13\transforms\bc6d32dbdad3a4b2c0ef6b4bde666a61\transformed\room-runtime-2.6.1\res
io.ionic.starter.app-webkit-1.12.1-21 C:\Users\<USER>\.gradle\caches\8.13\transforms\bcb73a203268f8e615381d06480e2c0c\transformed\webkit-1.12.1\res
io.ionic.starter.app-appcompat-1.7.0-22 C:\Users\<USER>\.gradle\caches\8.13\transforms\e2542f07e03e83966d61d2ce34c14b7c\transformed\appcompat-1.7.0\res
io.ionic.starter.app-annotation-experimental-1.4.1-23 C:\Users\<USER>\.gradle\caches\8.13\transforms\e55552fb3405c226de11947ae024032c\transformed\annotation-experimental-1.4.1\res
io.ionic.starter.app-emoji2-1.3.0-24 C:\Users\<USER>\.gradle\caches\8.13\transforms\e9683f504198d3d554b1474ed6ab66f7\transformed\emoji2-1.3.0\res
io.ionic.starter.app-core-1.15.0-25 C:\Users\<USER>\.gradle\caches\8.13\transforms\ea82f0e4740fea90ca38a885280cdee8\transformed\core-1.15.0\res
io.ionic.starter.app-biometric-1.1.0-26 C:\Users\<USER>\.gradle\caches\8.13\transforms\eaaca94d50affd3c3a86a8212751e60b\transformed\biometric-1.1.0\res
io.ionic.starter.app-sqlite-2.4.0-27 C:\Users\<USER>\.gradle\caches\8.13\transforms\ee7d53abeb21c3cdf634393d4f175d4b\transformed\sqlite-2.4.0\res
io.ionic.starter.app-security-crypto-1.1.0-alpha06-28 C:\Users\<USER>\.gradle\caches\8.13\transforms\fe76c8ca9816e5969645718f260e9a6f\transformed\security-crypto-1.1.0-alpha06\res
io.ionic.starter.app-pngs-29 C:\Users\<USER>\OneDrive\Desktop\FRONTEND DEVELOPER\crud-sqlite\android\app\build\generated\res\pngs\debug
io.ionic.starter.app-resValues-30 C:\Users\<USER>\OneDrive\Desktop\FRONTEND DEVELOPER\crud-sqlite\android\app\build\generated\res\resValues\debug
io.ionic.starter.app-packageDebugResources-31 C:\Users\<USER>\OneDrive\Desktop\FRONTEND DEVELOPER\crud-sqlite\android\app\build\intermediates\incremental\debug\packageDebugResources\merged.dir
io.ionic.starter.app-packageDebugResources-32 C:\Users\<USER>\OneDrive\Desktop\FRONTEND DEVELOPER\crud-sqlite\android\app\build\intermediates\incremental\debug\packageDebugResources\stripped.dir
io.ionic.starter.app-debug-33 C:\Users\<USER>\OneDrive\Desktop\FRONTEND DEVELOPER\crud-sqlite\android\app\build\intermediates\merged_res\debug\mergeDebugResources
io.ionic.starter.app-debug-34 C:\Users\<USER>\OneDrive\Desktop\FRONTEND DEVELOPER\crud-sqlite\android\app\src\debug\res
io.ionic.starter.app-main-35 C:\Users\<USER>\OneDrive\Desktop\FRONTEND DEVELOPER\crud-sqlite\android\app\src\main\res
io.ionic.starter.app-debug-36 C:\Users\<USER>\OneDrive\Desktop\FRONTEND DEVELOPER\crud-sqlite\android\capacitor-cordova-android-plugins\build\intermediates\packaged_res\debug\packageDebugResources
io.ionic.starter.app-debug-37 C:\Users\<USER>\OneDrive\Desktop\FRONTEND DEVELOPER\crud-sqlite\node_modules\@capacitor-community\sqlite\android\build\intermediates\packaged_res\debug\packageDebugResources
io.ionic.starter.app-debug-38 C:\Users\<USER>\OneDrive\Desktop\FRONTEND DEVELOPER\crud-sqlite\node_modules\@capacitor\android\capacitor\build\intermediates\packaged_res\debug\packageDebugResources
io.ionic.starter.app-debug-39 C:\Users\<USER>\OneDrive\Desktop\FRONTEND DEVELOPER\crud-sqlite\node_modules\@capacitor\app\android\build\intermediates\packaged_res\debug\packageDebugResources
io.ionic.starter.app-debug-40 C:\Users\<USER>\OneDrive\Desktop\FRONTEND DEVELOPER\crud-sqlite\node_modules\@capacitor\haptics\android\build\intermediates\packaged_res\debug\packageDebugResources
io.ionic.starter.app-debug-41 C:\Users\<USER>\OneDrive\Desktop\FRONTEND DEVELOPER\crud-sqlite\node_modules\@capacitor\keyboard\android\build\intermediates\packaged_res\debug\packageDebugResources
io.ionic.starter.app-debug-42 C:\Users\<USER>\OneDrive\Desktop\FRONTEND DEVELOPER\crud-sqlite\node_modules\@capacitor\status-bar\android\build\intermediates\packaged_res\debug\packageDebugResources
